import React, { useState } from 'react';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { AlertTriangle, CheckCircle, TrendingUp, TrendingDown, Filter, ChevronDown, Search, Settings } from 'lucide-react';

const mockTradeData = [
  {
    id: 1,
    date: '03/31/2025',
    model: 'RD-Cont.',
    session: 'NY Open',
    setup: 'Simple FVG-RD',
    entry: '09:57:00',
    exit: '10:01:00',
    direction: 'Long',
    market: 'MNQ',
    rMultiple: 0.80,
    patternQuality: 4.5,
    win: true,
    entryPrice: 19103.75,
    exitPrice: 19130.75,
    risk: 34,
    pnl: 54.00,
    dolTarget: 'FVG Target (Macro-FVG)',
    rdType: 'True-RD',
    entryVersion: 'Simple-Entry',
    drawOnLiquidity: 'REQH/L, MNOR-FVG, AM-FPFVG'
  },
  {
    id: 2,
    date: '03/31/2025',
    model: 'RD-Cont.',
    session: 'Lunch Macro',
    setup: 'Complex-Entry',
    entry: '11:48:00',
    exit: '13:07:00',
    direction: 'Long',
    market: 'MNQ',
    rMultiple: -1.01,
    patternQuality: 3.7,
    win: false,
    entryPrice: 19225.75,
    exitPrice: 19195.75,
    risk: 30,
    pnl: -60.00,
    dolTarget: 'FVG Target (Same Day - AM)',
    rdType: null,
    entryVersion: 'Complex-Entry',
    drawOnLiquidity: 'LN-H/L, REQH/L, Strong-FVG'
  },
  {
    id: 3,
    date: '03/31/2025',
    model: 'FVG-RD',
    session: 'Lunch Macro',
    setup: 'Complex FVG-RD',
    entry: '13:16:00',
    exit: '13:21:00',
    direction: 'Short',
    market: 'MNQ',
    rMultiple: 1.63,
    patternQuality: 3.17,
    win: true,
    entryPrice: 19277.00,
    exitPrice: 19228.00,
    risk: 30,
    pnl: 98.00,
    dolTarget: 'RD Target (Same Day - AM)',
    rdType: null,
    entryVersion: 'Complex-Entry',
    drawOnLiquidity: 'Macro-FVG'
  },
  {
    id: 4,
    date: '04/01/2025',
    model: 'RD-Cont.',
    session: 'MOC',
    setup: 'Multi-Array Confluence Setup',
    entry: '15:44:00',
    exit: '16:18:00',
    direction: 'Long',
    market: 'MNQ',
    rMultiple: 1.88,
    patternQuality: 3.0,
    win: true,
    entryPrice: 19545.00,
    exitPrice: 19601.50,
    risk: 30,
    pnl: 113.00,
    dolTarget: 'Liquidity Target (09:30-Opening-Range-H/L)',
    rdType: 'IMM-RD',
    entryVersion: 'Complex-Entry',
    drawOnLiquidity: 'News-FVG, Lunch-H/L, PM-FVG, Asia-H/L'
  },
  {
    id: 5,
    date: '04/02/2025',
    model: 'RD-Cont.',
    session: 'Pre-Market',
    setup: 'True-RD Continuation',
    entry: '07:52:00',
    exit: '08:02:00',
    direction: 'Short',
    market: 'MNQ',
    rMultiple: 2.13,
    patternQuality: 3.5,
    win: true,
    entryPrice: 19448.75,
    exitPrice: 19413.00,
    risk: 17,
    pnl: 71.50,
    dolTarget: 'RD Target',
    rdType: 'True-RD',
    entryVersion: 'Complex-Entry',
    drawOnLiquidity: 'NDOG, Prev-3Days-KeyFVG'
  }
];

// Performance chart data
const performanceData = [
  { date: '03/29', pnl: 72.5, cumulative: 72.5 },
  { date: '03/30', pnl: 92, cumulative: 164.5 },
  { date: '03/31', pnl: 92, cumulative: 256.5 },
  { date: '04/01', pnl: 113, cumulative: 369.5 },
  { date: '04/02', pnl: 102.5, cumulative: 472 },
];

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('summary');
  
  // Calculate summary statistics
  const totalTrades = mockTradeData.length;
  const winningTrades = mockTradeData.filter(trade => trade.win).length;
  const winRate = (winningTrades / totalTrades * 100).toFixed(1);
  const totalPnl = mockTradeData.reduce((sum, trade) => sum + trade.pnl, 0);
  const avgRMultiple = (mockTradeData.reduce((sum, trade) => sum + trade.rMultiple, 0) / totalTrades).toFixed(2);
  
  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white font-sans">
      {/* Header */}
      <div className="bg-red-600 py-2 px-4 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <h1 className="text-xl font-bold">TRADING 2025</h1>
          <span className="text-sm font-light">SESSION 1</span>
        </div>
        <div className="flex space-x-6">
          <button className="flex items-center">
            <LineChart className="w-6 h-6 mr-2" />
            DASHBOARD
          </button>
          <button className="flex items-center">
            <TrendingUp className="w-6 h-6 mr-2" />
            PERFORMANCE
          </button>
          <button className="flex items-center">
            <Search className="w-6 h-6 mr-2" />
            ANALYSIS
          </button>
        </div>
        <div className="flex space-x-4">
          <button><Settings className="w-6 h-6" /></button>
          <button><ChevronDown className="w-6 h-6" /></button>
        </div>
      </div>
      
      {/* Status area */}
      <div className="bg-gray-800 py-1 px-4 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <span className="text-gray-400">LIVE</span>
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
        </div>
        <div className="flex space-x-12">
          <button onClick={() => setActiveTab('summary')} className={`${activeTab === 'summary' ? 'border-b-2 border-red-500' : ''}`}>
            Summary
          </button>
          <button onClick={() => setActiveTab('trades')} className={`${activeTab === 'trades' ? 'border-b-2 border-red-500' : ''}`}>
            Trades
          </button>
          <button onClick={() => setActiveTab('setups')} className={`${activeTab === 'setups' ? 'border-b-2 border-red-500' : ''}`}>
            Setups
          </button>
          <button onClick={() => setActiveTab('analytics')} className={`${activeTab === 'analytics' ? 'border-b-2 border-red-500' : ''}`}>
            Analysis
          </button>
        </div>
        <div>
          <span className="text-gray-400">04/03/2025</span>
        </div>
      </div>
      
      {/* Alert banner */}
      <div className="bg-red-500 py-1 px-4 flex justify-center items-center font-bold">
        <AlertTriangle className="w-5 h-5 mr-2" />
        NEWS EVENT: FOMC MINUTES 14:00 EST
      </div>
      
      {/* Main content */}
      <div className="flex-1 overflow-auto">
        {activeTab === 'summary' && (
          <div className="p-4">
            {/* Performance metrics */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-800 p-4 rounded-md">
                <h3 className="text-gray-400 text-sm">WIN RATE</h3>
                <div className="text-2xl font-bold">{winRate}%</div>
                <div className="text-green-500 text-sm">+3.2% from last week</div>
              </div>
              <div className="bg-gray-800 p-4 rounded-md">
                <h3 className="text-gray-400 text-sm">TOTAL P&L</h3>
                <div className="text-2xl font-bold">${totalPnl}</div>
                <div className="text-green-500 text-sm">+$215 from last week</div>
              </div>
              <div className="bg-gray-800 p-4 rounded-md">
                <h3 className="text-gray-400 text-sm">AVG R-MULTIPLE</h3>
                <div className="text-2xl font-bold">{avgRMultiple}</div>
                <div className="text-green-500 text-sm">+0.3 from last week</div>
              </div>
              <div className="bg-gray-800 p-4 rounded-md">
                <h3 className="text-gray-400 text-sm">BEST SETUP</h3>
                <div className="text-lg font-bold">RD-Cont. (MOC)</div>
                <div className="text-green-500 text-sm">3.2 avg. R-multiple</div>
              </div>
            </div>
            
            {/* Performance chart */}
            <div className="bg-gray-800 p-4 rounded-md mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="font-bold">PERFORMANCE CHART</h2>
                <div className="flex space-x-4">
                  <button className="text-sm bg-gray-700 px-3 py-1 rounded">Daily</button>
                  <button className="text-sm bg-gray-900 px-3 py-1 rounded">Weekly</button>
                  <button className="text-sm bg-gray-900 px-3 py-1 rounded">Monthly</button>
                </div>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                    <XAxis dataKey="date" stroke="#888" />
                    <YAxis stroke="#888" />
                    <Tooltip 
                      contentStyle={{ backgroundColor: '#222', borderColor: '#444' }}
                      itemStyle={{ color: '#fff' }}
                      labelStyle={{ color: '#ccc' }}
                    />
                    <Line type="monotone" dataKey="cumulative" stroke="#ff0000" strokeWidth={2} dot={{ stroke: '#ff0000', strokeWidth: 2, r: 4 }} />
                    <Line type="monotone" dataKey="pnl" stroke="#00ff00" strokeWidth={2} dot={{ stroke: '#00ff00', strokeWidth: 2, r: 4 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
            
            {/* Recent trades */}
            <div className="bg-gray-800 p-4 rounded-md">
              <div className="flex justify-between items-center mb-4">
                <h2 className="font-bold">RECENT TRADES</h2>
                <button className="text-sm bg-red-600 px-3 py-1 rounded">View All</button>
              </div>
              <table className="w-full">
                <thead className="border-b border-gray-700">
                  <tr className="text-sm text-gray-400">
                    <th className="text-left py-2">TIME</th>
                    <th className="text-left py-2">SETUP</th>
                    <th className="text-left py-2">DIRECTION</th>
                    <th className="text-left py-2">QUALITY</th>
                    <th className="text-right py-2">R-MULT</th>
                    <th className="text-right py-2">P&L</th>
                  </tr>
                </thead>
                <tbody>
                  {mockTradeData.map(trade => (
                    <tr key={trade.id} className={`border-b border-gray-700 ${trade.win ? 'text-green-400' : 'text-red-400'}`}>
                      <td className="py-3">{trade.entry}</td>
                      <td className="py-3">{trade.setup}</td>
                      <td className="py-3 flex items-center">
                        {trade.direction === 'Long' ? 
                          <TrendingUp className="w-4 h-4 mr-1" /> : 
                          <TrendingDown className="w-4 h-4 mr-1" />
                        }
                        {trade.direction}
                      </td>
                      <td className="py-3">
                        <div className="flex items-center">
                          <span className="mr-2">{trade.patternQuality}</span>
                          <div className="w-16 h-2 bg-gray-700 rounded-full">
                            <div 
                              className={`h-2 rounded-full ${trade.patternQuality >= 4 ? 'bg-green-500' : trade.patternQuality >= 3 ? 'bg-yellow-500' : 'bg-red-500'}`}
                              style={{ width: `${trade.patternQuality * 20}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 text-right">{trade.rMultiple.toFixed(2)}</td>
                      <td className="py-3 text-right">${trade.pnl}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        
        {activeTab === 'trades' && (
          <div className="p-4">
            {/* Filter row */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex space-x-2">
                <button className="bg-gray-800 px-3 py-1 rounded flex items-center">
                  <Filter className="w-4 h-4 mr-1" />
                  Filter
                </button>
                <button className="bg-gray-800 px-3 py-1 rounded">All Setups</button>
                <button className="bg-gray-800 px-3 py-1 rounded">RD-Cont.</button>
                <button className="bg-gray-800 px-3 py-1 rounded">FVG-RD</button>
              </div>
              <div className="flex items-center bg-gray-800 px-3 py-1 rounded">
                <Search className="w-4 h-4 mr-2" />
                <input 
                  type="text" 
                  placeholder="Search trades..." 
                  className="bg-transparent border-none outline-none text-white w-40"
                />
              </div>
            </div>
            
            {/* Trades table */}
            <div className="bg-gray-800 p-4 rounded-md overflow-x-auto">
              <table className="w-full min-w-max">
                <thead>
                  <tr className="text-left text-gray-400 border-b border-gray-700">
                    <th className="p-2">DATE</th>
                    <th className="p-2">MODEL</th>
                    <th className="p-2">SETUP</th>
                    <th className="p-2">QUALITY</th>
                    <th className="p-2">ENTRY</th>
                    <th className="p-2">EXIT</th>
                    <th className="p-2">DIRECTION</th>
                    <th className="p-2">DOL TARGET</th>
                    <th className="p-2 text-right">R-MULT</th>
                    <th className="p-2 text-right">P&L</th>
                  </tr>
                </thead>
                <tbody>
                  {mockTradeData.map(trade => (
                    <tr 
                      key={trade.id} 
                      className={`border-b border-gray-700 hover:bg-gray-700`}
                    >
                      <td className="p-2">{trade.date}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          trade.model === 'RD-Cont.' ? 'bg-blue-900 text-blue-300' : 
                          trade.model === 'FVG-RD' ? 'bg-purple-900 text-purple-300' : 
                          'bg-gray-700'
                        }`}>
                          {trade.model}
                        </span>
                      </td>
                      <td className="p-2">{trade.setup}</td>
                      <td className="p-2">
                        <div className="flex items-center">
                          <span className={`mr-2 ${
                            trade.patternQuality >= 4 ? 'text-green-400' : 
                            trade.patternQuality >= 3 ? 'text-yellow-400' : 
                            'text-red-400'
                          }`}>
                            {trade.patternQuality.toFixed(1)}
                          </span>
                          <div className="w-16 h-2 bg-gray-700 rounded-full">
                            <div 
                              className={`h-2 rounded-full ${
                                trade.patternQuality >= 4 ? 'bg-green-500' : 
                                trade.patternQuality >= 3 ? 'bg-yellow-500' : 
                                'bg-red-500'
                              }`}
                              style={{ width: `${trade.patternQuality * 20}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="p-2">{trade.entry}</td>
                      <td className="p-2">{trade.exit}</td>
                      <td className="p-2">
                        <span className={`flex items-center ${
                          trade.direction === 'Long' ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {trade.direction === 'Long' ? 
                            <TrendingUp className="w-4 h-4 mr-1" /> : 
                            <TrendingDown className="w-4 h-4 mr-1" />
                          }
                          {trade.direction}
                        </span>
                      </td>
                      <td className="p-2">{trade.dolTarget}</td>
                      <td className={`p-2 text-right font-bold ${
                        trade.rMultiple > 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {trade.rMultiple.toFixed(2)}
                      </td>
                      <td className={`p-2 text-right font-bold ${
                        trade.pnl > 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        ${trade.pnl.toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        
        {activeTab === 'setups' && (
          <div className="p-4">
            <div className="grid grid-cols-2 gap-4">
              {/* Setup performance */}
              <div className="bg-gray-800 p-4 rounded-md">
                <h2 className="font-bold mb-4">SETUP PERFORMANCE</h2>
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-gray-400 border-b border-gray-700">
                      <th className="p-2">SETUP TYPE</th>
                      <th className="p-2 text-center">WIN %</th>
                      <th className="p-2 text-center">AVG QUALITY</th>
                      <th className="p-2 text-right">AVG R-MULT</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Simple FVG-RD</td>
                      <td className="p-2 text-center text-green-400">86%</td>
                      <td className="p-2 text-center">4.2</td>
                      <td className="p-2 text-right text-green-400">1.72</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">True-RD Continuation</td>
                      <td className="p-2 text-center text-green-400">75%</td>
                      <td className="p-2 text-center">3.8</td>
                      <td className="p-2 text-right text-green-400">1.63</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Complex FVG-RD</td>
                      <td className="p-2 text-center text-yellow-400">67%</td>
                      <td className="p-2 text-center">3.5</td>
                      <td className="p-2 text-right text-green-400">1.27</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Multi-Array Confluence</td>
                      <td className="p-2 text-center text-yellow-400">65%</td>
                      <td className="p-2 text-center">3.3</td>
                      <td className="p-2 text-right text-green-400">1.19</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">NWOG Reaction</td>
                      <td className="p-2 text-center text-red-400">52%</td>
                      <td className="p-2 text-center">2.9</td>
                      <td className="p-2 text-right text-red-400">0.76</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              {/* DOL performance */}
              <div className="bg-gray-800 p-4 rounded-md">
                <h2 className="font-bold mb-4">DOL TARGET PERFORMANCE</h2>
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-gray-400 border-b border-gray-700">
                      <th className="p-2">DOL TYPE</th>
                      <th className="p-2 text-center">HIT RATE</th>
                      <th className="p-2 text-center">AVG MOVES</th>
                      <th className="p-2 text-right">CLUSTERING</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">FVG Target (Macro-FVG)</td>
                      <td className="p-2 text-center text-green-400">92%</td>
                      <td className="p-2 text-center">1.4R</td>
                      <td className="p-2 text-right">Strict</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Liquidity Target (09:30-H/L)</td>
                      <td className="p-2 text-center text-green-400">88%</td>
                      <td className="p-2 text-center">1.6R</td>
                      <td className="p-2 text-right">Nearby</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">RD Target</td>
                      <td className="p-2 text-center text-yellow-400">76%</td>
                      <td className="p-2 text-center">1.2R</td>
                      <td className="p-2 text-right">Nearby</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Parent-FVG (NWOG)</td>
                      <td className="p-2 text-center text-yellow-400">71%</td>
                      <td className="p-2 text-center">1.8R</td>
                      <td className="p-2 text-right">Loose</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Liquidity Target (Lunch-H/L)</td>
                      <td className="p-2 text-center text-red-400">58%</td>
                      <td className="p-2 text-center">0.9R</td>
                      <td className="p-2 text-right">Mixed</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              {/* Time block performance */}
              <div className="bg-gray-800 p-4 rounded-md">
                <h2 className="font-bold mb-4">SESSION PERFORMANCE</h2>
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-gray-400 border-b border-gray-700">
                      <th className="p-2">TIME BLOCK</th>
                      <th className="p-2 text-center">WIN %</th>
                      <th className="p-2 text-center">TRADES</th>
                      <th className="p-2 text-right">AVG R-MULT</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">NY Open</td>
                      <td className="p-2 text-center text-green-400">87%</td>
                      <td className="p-2 text-center">15</td>
                      <td className="p-2 text-right text-green-400">1.86</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">MOC</td>
                      <td className="p-2 text-center text-green-400">82%</td>
                      <td className="p-2 text-center">11</td>
                      <td className="p-2 text-right text-green-400">1.73</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">10:50-11:10 Macro</td>
                      <td className="p-2 text-center text-yellow-400">72%</td>
                      <td className="p-2 text-center">18</td>
                      <td className="p-2 text-right text-green-400">1.48</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Lunch Macro</td>
                      <td className="p-2 text-center text-yellow-400">65%</td>
                      <td className="p-2 text-center">23</td>
                      <td className="p-2 text-right text-green-400">1.21</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">PM Opening Range</td>
                      <td className="p-2 text-center text-red-400">54%</td>
                      <td className="p-2 text-center">13</td>
                      <td className="p-2 text-right text-red-400">0.84</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              {/* News impact */}
              <div className="bg-gray-800 p-4 rounded-md">
                <h2 className="font-bold mb-4">NEWS IMPACT ANALYSIS</h2>
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-gray-400 border-b border-gray-700">
                      <th className="p-2">NEWS TYPE</th>
                      <th className="p-2 text-center">IMPACT</th>
                      <th className="p-2 text-center">VOLATILITY</th>
                      <th className="p-2 text-right">BEST SETUP</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">FOMC</td>
                      <td className="p-2 text-center">
                        <span className="px-2 py-1 bg-red-900 text-red-300 rounded-full text-xs">HIGH</span>
                      </td>
                      <td className="p-2 text-center">+320%</td>
                      <td className="p-2 text-right">Simple FVG-RD</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Non-Farm Payrolls</td>
                      <td className="p-2 text-center">
                        <span className="px-2 py-1 bg-red-900 text-red-300 rounded-full text-xs">HIGH</span>
                      </td>
                      <td className="p-2 text-center">+280%</td>
                      <td className="p-2 text-right">True-RD Cont.</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">GDP</td>
                      <td className="p-2 text-center">
                        <span className="px-2 py-1 bg-yellow-900 text-yellow-300 rounded-full text-xs">MEDIUM</span>
                      </td>
                      <td className="p-2 text-center">+180%</td>
                      <td className="p-2 text-right">Multi-Array</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">CPI/Inflation</td>
                      <td className="p-2 text-center">
                        <span className="px-2 py-1 bg-yellow-900 text-yellow-300 rounded-full text-xs">MEDIUM</span>
                      </td>
                      <td className="p-2 text-center">+160%</td>
                      <td className="p-2 text-right">Complex FVG-RD</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Retail Sales</td>
                      <td className="p-2 text-center">
                        <span className="px-2 py-1 bg-green-900 text-green-300 rounded-full text-xs">LOW</span>
                      </td>
                      <td className="p-2 text-center">+70%</td>
                      <td className="p-2 text-right">NWOG Reaction</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'analytics' && (
          <div className="p-4">
            <div className="grid grid-cols-2 gap-4 mb-4">
              {/* Market chart placeholder */}
              <div className="bg-gray-800 p-4 rounded-md">
                <h2 className="font-bold mb-2">PRICE CHART WITH ENTRIES</h2>
                <div className="bg-gray-900 h-64 rounded flex items-center justify-center">
                  <span className="text-gray-500">Price chart with entry/exit points would appear here</span>
                </div>
              </div>
              
              {/* Pattern analysis */}
              <div className="bg-gray-800 p-4 rounded-md">
                <h2 className="font-bold mb-2">PATTERN QUALITY CORRELATION</h2>
                <div className="bg-gray-900 h-64 rounded flex items-center justify-center">
                  <span className="text-gray-500">Pattern quality vs. R-multiple chart would appear here</span>
                </div>
              </div>
            </div>
            
            {/* Trade entry form */}
            <div className="bg-gray-800 p-4 rounded-md">
              <h2 className="font-bold mb-4">NEW TRADE ENTRY</h2>
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Date</label>
                  <input type="date" className="w-full bg-gray-700 p-2 rounded text-white" />
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Model Type</label>
                  <select className="w-full bg-gray-700 p-2 rounded text-white">
                    <option>RD-Cont.</option>
                    <option>FVG-RD</option>
                    <option>FVG-RD, RD-Cont.</option>
                  </select>
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Session</label>
                  <select className="w-full bg-gray-700 p-2 rounded text-white">
                    <option>NY Open</option>
                    <option>Lunch Macro</option>
                    <option>10:50-11:10 Macro</option>
                    <option>MOC</option>
                  </select>
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Setup</label>
                  <select className="w-full bg-gray-700 p-2 rounded text-white">
                    <option>Simple FVG-RD</option>
                    <option>Complex FVG-RD</option>
                    <option>True-RD Continuation</option>
                    <option>Multi-Array Confluence</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-4 gap-4 mt-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Entry Time</label>
                  <input type="time" className="w-full bg-gray-700 p-2 rounded text-white" />
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Exit Time</label>
                  <input type="time" className="w-full bg-gray-700 p-2 rounded text-white" />
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Direction</label>
                  <select className="w-full bg-gray-700 p-2 rounded text-white">
                    <option>Long</option>
                    <option>Short</option>
                  </select>
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Market</label>
                  <select className="w-full bg-gray-700 p-2 rounded text-white">
                    <option>MNQ</option>
                    <option>ES</option>
                    <option>MES</option>
                    <option>NQ</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-4 gap-4 mt-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Entry Price</label>
                  <input type="number" className="w-full bg-gray-700 p-2 rounded text-white" />
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Exit Price</label>
                  <input type="number" className="w-full bg-gray-700 p-2 rounded text-white" />
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Risk (Points)</label>
                  <input type="number" className="w-full bg-gray-700 p-2 rounded text-white" />
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Pattern Quality</label>
                  <div className="flex items-center bg-gray-700 p-2 rounded">
                    <input type="range" min="1" max="5" step="0.1" className="w-full" />
                    <span className="ml-2 text-white">4.2</span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-1">DOL Target Type</label>
                  <select className="w-full bg-gray-700 p-2 rounded text-white">
                    <option>FVG Target (Macro-FVG)</option>
                    <option>RD Target</option>
                    <option>Liquidity Target (09:30-Opening-Range-H/L)</option>
                    <option>Parent-FVG (NWOG)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Draw On Liquidity</label>
                  <select className="w-full bg-gray-700 p-2 rounded text-white">
                    <option>REQH/L, MNOR-FVG, AM-FPFVG</option>
                    <option>LN-H/L, REQH/L, Strong-FVG</option>
                    <option>Macro-FVG</option>
                    <option>None</option>
                  </select>
                </div>
              </div>
              
              <div className="mt-4">
                <label className="block text-gray-400 text-sm mb-1">Notes</label>
                <textarea className="w-full bg-gray-700 p-2 rounded text-white h-20"></textarea>
              </div>
              
              <div className="mt-4 flex justify-end">
                <button className="bg-red-600 text-white px-4 py-2 rounded">Save Trade</button>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Status bar */}
      <div className="bg-gray-800 py-2 px-4 flex justify-between items-center text-sm">
        <div className="flex items-center space-x-2">
          <span className="text-red-600 font-bold">P1</span>
          <span>04:32:15</span>
        </div>
        <div className="flex items-center space-x-4">
          <span>Win Streak: 3</span>
          <span>Session P&L: +$472.00</span>
          <span>Contracts: 1</span>
        </div>
        <div className="flex items-center space-x-2">
          <span>Market: MNQ</span>
          <span className="text-green-400">+0.32%</span>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
