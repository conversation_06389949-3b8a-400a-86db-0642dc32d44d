{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@adhd-trading-dashboard/shared": ["packages/shared/src"], "@adhd-trading-dashboard/shared/*": ["packages/shared/src/*"], "@adhd-trading-dashboard/dashboard": ["packages/dashboard/src"], "@adhd-trading-dashboard/dashboard/*": ["packages/dashboard/src/*"]}, "outDir": "./dist", "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": true, "isolatedModules": true}, "exclude": ["node_modules", "dist", "scripts", "**/node_modules", "**/dist"], "references": [{"path": "./packages/shared"}, {"path": "./packages/dashboard"}]}