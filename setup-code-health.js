#!/usr/bin/env node

/**
 * Setup script for Code Health Tools
 * Checks dependencies and provides installation instructions
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log(`
╔═══════════════════════════════════════════════╗
║         Code Health Tools Setup               ║
╚═══════════════════════════════════════════════╝
`);

// Check if we're in the right directory
const currentDir = process.cwd();
const codeHealthDir = path.join(currentDir, 'code-health');

if (!fs.existsSync(codeHealthDir)) {
  console.error('❌ Error: code-health directory not found!');
  console.log('\nPlease run this from your project root directory.');
  process.exit(1);
}

// Check for required files
const requiredFiles = [
  'enhanced-refactor-analyzer.js',
  'dynamic-scripts-cleanup.js',
  'enhanced-analyze-codebase.js',
  'orchestrate-health.js',
];

console.log('📋 Checking for required files...\n');

const missingFiles = [];
requiredFiles.forEach((file) => {
  const filePath = path.join(codeHealthDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.error('\n❌ Missing required files. Please ensure all code-health scripts are in place.');
  process.exit(1);
}

// Check for dependencies
console.log('\n📦 Checking dependencies...\n');

const dependencies = {
  '@babel/parser': '^7.24.0',
  '@babel/traverse': '^7.24.0',
  commander: '^12.0.0',
};

const missingDeps = [];

// In ES modules, we can't use require.resolve directly
// Instead, we'll check if the package is in node_modules
Object.keys(dependencies).forEach((dep) => {
  try {
    const depPath = path.join(currentDir, 'node_modules', dep);
    if (fs.existsSync(depPath)) {
      console.log(`  ✅ ${dep}`);
    } else {
      throw new Error('Not installed');
    }
  } catch (e) {
    console.log(`  ❌ ${dep} - NOT INSTALLED`);
    missingDeps.push(dep);
  }
});

if (missingDeps.length > 0) {
  console.log('\n📦 Installing missing dependencies...\n');

  const installCmd = `yarn add --dev ${missingDeps.map((d) => d + '@latest').join(' ')}`;
  console.log(`Running: ${installCmd}\n`);

  try {
    execSync(installCmd, { stdio: 'inherit' });
    console.log('\n✅ Dependencies installed successfully!');
  } catch (error) {
    console.error('\n❌ Failed to install dependencies automatically.');
    console.log('\nPlease run manually:');
    console.log(`  ${installCmd}`);
    process.exit(1);
  }
}

// Make scripts executable
console.log('\n🔧 Making scripts executable...\n');

requiredFiles.forEach((file) => {
  const filePath = path.join(codeHealthDir, file);
  try {
    fs.chmodSync(filePath, '755');
    console.log(`  ✅ ${file}`);
  } catch (error) {
    console.log(`  ⚠️  Could not make ${file} executable`);
  }
});

// Test basic functionality
console.log('\n🧪 Testing basic functionality...\n');

try {
  // In ES modules, we can't dynamically require modules
  // Instead, we'll just check if the files exist and are readable
  const testPath = path.join(codeHealthDir, 'enhanced-refactor-analyzer.js');
  const content = fs.readFileSync(testPath, 'utf-8');

  if (content.includes('export default')) {
    console.log('  ✅ Modules using ES module syntax correctly');
  } else {
    console.log('  ⚠️  Modules may not be using ES module syntax');
  }
} catch (error) {
  console.error('  ❌ Error checking modules:', error.message);
  process.exit(1);
}

// Add convenience scripts to package.json
console.log('\n📝 Updating package.json scripts...\n');

try {
  const packagePath = path.join(currentDir, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));

  const healthScripts = {
    health: 'node code-health/orchestrate-health.js',
    'health:analyze': 'node code-health/enhanced-analyze-codebase.js',
    'health:scripts': 'node code-health/enhanced-refactor-analyzer.js ./scripts',
    'health:cleanup': 'node code-health/dynamic-scripts-cleanup.js',
    'health:cleanup:dry': 'node code-health/dynamic-scripts-cleanup.js --dry-run',
  };

  if (!packageJson.scripts) {
    packageJson.scripts = {};
  }

  let added = false;
  Object.entries(healthScripts).forEach(([key, value]) => {
    if (!packageJson.scripts[key]) {
      packageJson.scripts[key] = value;
      added = true;
      console.log(`  ✅ Added script: ${key}`);
    }
  });

  if (added) {
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log('\n  ✅ package.json updated');
  } else {
    console.log('  ℹ️  Scripts already exist in package.json');
  }
} catch (error) {
  console.log('  ⚠️  Could not update package.json:', error.message);
}

// Success!
console.log(`
✨ Setup complete! ✨

You can now use the code health tools:

1. Run a full analysis:
   npm run health

2. Analyze scripts directory:
   npm run health:scripts

3. Preview cleanup changes:
   npm run health:cleanup:dry

4. Execute cleanup:
   npm run health:cleanup

For more options:
   node code-health/orchestrate-health.js --help

Happy coding! 🚀
`);

// Create a test script directory if it doesn't exist
if (!fs.existsSync(path.join(currentDir, 'scripts'))) {
  console.log(
    '\n⚠️  Note: No scripts directory found. Create one with some JavaScript files to analyze.'
  );
}
