#!/usr/bin/env node

/**
 * AST-based TypeScript Parser
 * Provides accurate code analysis using TypeScript compiler API
 */

import ts from 'typescript';
import fs from 'fs';
import path from 'path';

export class TypeScriptASTParser {
  constructor() {
    this.compilerOptions = {
      target: ts.ScriptTarget.ES2020,
      module: ts.ModuleKind.ESNext,
      jsx: ts.JsxEmit.React,
      allowJs: true,
      checkJs: false,
      noEmit: true,
      esModuleInterop: true,
      skipLibCheck: true,
      moduleResolution: ts.ModuleResolutionKind.NodeJs,
    };
  }

  /**
   * Parse a TypeScript/JavaScript file and extract AST information
   */
  parseFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const sourceFile = ts.createSourceFile(
      filePath,
      content,
      this.compilerOptions.target,
      true,
      ts.ScriptKind.TSX
    );

    return {
      sourceFile,
      imports: this.extractImports(sourceFile),
      exports: this.extractExports(sourceFile),
      typeDeclarations: this.extractTypeDeclarations(sourceFile),
      functionDeclarations: this.extractFunctionDeclarations(sourceFile),
      classDeclarations: this.extractClassDeclarations(sourceFile),
      dependencies: this.analyzeDependencies(sourceFile),
    };
  }

  /**
   * Extract all import statements
   */
  extractImports(sourceFile) {
    const imports = [];
    
    const visit = (node) => {
      if (ts.isImportDeclaration(node)) {
        const moduleSpecifier = node.moduleSpecifier?.text;
        const importClause = node.importClause;
        
        if (importClause) {
          const namedImports = [];
          const defaultImport = importClause.name?.text;
          
          if (importClause.namedBindings) {
            if (ts.isNamedImports(importClause.namedBindings)) {
              importClause.namedBindings.elements.forEach(element => {
                namedImports.push({
                  name: element.name.text,
                  alias: element.propertyName?.text,
                });
              });
            } else if (ts.isNamespaceImport(importClause.namedBindings)) {
              namedImports.push({
                namespace: importClause.namedBindings.name.text,
              });
            }
          }
          
          imports.push({
            line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
            module: moduleSpecifier,
            defaultImport,
            namedImports,
            isTypeOnly: node.importClause?.isTypeOnly || false,
          });
        }
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return imports;
  }

  /**
   * Extract all export statements
   */
  extractExports(sourceFile) {
    const exports = [];
    
    const visit = (node) => {
      if (ts.isExportDeclaration(node)) {
        const moduleSpecifier = node.moduleSpecifier?.text;
        exports.push({
          line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
          module: moduleSpecifier,
          isTypeOnly: node.isTypeOnly || false,
          isReExport: !!moduleSpecifier,
        });
      } else if (ts.isExportAssignment(node)) {
        exports.push({
          line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
          isDefault: true,
        });
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return exports;
  }

  /**
   * Extract type declarations (interfaces, types, enums)
   */
  extractTypeDeclarations(sourceFile) {
    const declarations = [];
    
    const visit = (node) => {
      if (ts.isInterfaceDeclaration(node)) {
        declarations.push({
          kind: 'interface',
          name: node.name.text,
          line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
          isExported: this.hasExportModifier(node),
          genericParams: node.typeParameters?.map(p => p.name.text),
          extends: node.heritageClauses?.flatMap(clause => 
            clause.types.map(type => type.expression.getText(sourceFile))
          ),
        });
      } else if (ts.isTypeAliasDeclaration(node)) {
        declarations.push({
          kind: 'type',
          name: node.name.text,
          line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
          isExported: this.hasExportModifier(node),
          genericParams: node.typeParameters?.map(p => p.name.text),
        });
      } else if (ts.isEnumDeclaration(node)) {
        declarations.push({
          kind: 'enum',
          name: node.name.text,
          line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
          isExported: this.hasExportModifier(node),
          members: node.members.map(m => m.name?.getText(sourceFile)),
        });
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return declarations;
  }

  /**
   * Extract function declarations
   */
  extractFunctionDeclarations(sourceFile) {
    const functions = [];
    
    const visit = (node) => {
      if (ts.isFunctionDeclaration(node) && node.name) {
        functions.push({
          name: node.name.text,
          line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
          isExported: this.hasExportModifier(node),
          isAsync: !!node.modifiers?.some(m => m.kind === ts.SyntaxKind.AsyncKeyword),
          parameters: node.parameters.map(p => ({
            name: p.name.getText(sourceFile),
            type: p.type?.getText(sourceFile),
            optional: !!p.questionToken,
          })),
        });
      } else if (ts.isVariableStatement(node)) {
        node.declarationList.declarations.forEach(decl => {
          if (ts.isArrowFunction(decl.initializer) || ts.isFunctionExpression(decl.initializer)) {
            functions.push({
              name: decl.name.getText(sourceFile),
              line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
              isExported: this.hasExportModifier(node),
              isArrow: ts.isArrowFunction(decl.initializer),
            });
          }
        });
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return functions;
  }

  /**
   * Extract class declarations
   */
  extractClassDeclarations(sourceFile) {
    const classes = [];
    
    const visit = (node) => {
      if (ts.isClassDeclaration(node) && node.name) {
        const methods = [];
        const properties = [];
        
        node.members.forEach(member => {
          if (ts.isMethodDeclaration(member)) {
            methods.push({
              name: member.name?.getText(sourceFile),
              isStatic: !!member.modifiers?.some(m => m.kind === ts.SyntaxKind.StaticKeyword),
              isPrivate: !!member.modifiers?.some(m => m.kind === ts.SyntaxKind.PrivateKeyword),
            });
          } else if (ts.isPropertyDeclaration(member)) {
            properties.push({
              name: member.name?.getText(sourceFile),
              type: member.type?.getText(sourceFile),
              isStatic: !!member.modifiers?.some(m => m.kind === ts.SyntaxKind.StaticKeyword),
            });
          }
        });
        
        classes.push({
          name: node.name.text,
          line: sourceFile.getLineAndCharacterOfPosition(node.pos).line + 1,
          isExported: this.hasExportModifier(node),
          extends: node.heritageClauses?.find(c => c.token === ts.SyntaxKind.ExtendsKeyword)
            ?.types[0]?.expression.getText(sourceFile),
          implements: node.heritageClauses?.find(c => c.token === ts.SyntaxKind.ImplementsKeyword)
            ?.types.map(t => t.expression.getText(sourceFile)),
          methods,
          properties,
        });
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    return classes;
  }

  /**
   * Analyze dependencies and type usage
   */
  analyzeDependencies(sourceFile) {
    const dependencies = {
      usedTypes: new Set(),
      referencedIdentifiers: new Set(),
    };
    
    const visit = (node) => {
      // Collect type references
      if (ts.isTypeReferenceNode(node)) {
        const typeName = node.typeName.getText(sourceFile);
        dependencies.usedTypes.add(typeName);
      }
      
      // Collect identifier references (excluding declarations)
      if (ts.isIdentifier(node) && !ts.isDeclaration(node.parent)) {
        dependencies.referencedIdentifiers.add(node.text);
      }
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    
    return {
      usedTypes: Array.from(dependencies.usedTypes),
      referencedIdentifiers: Array.from(dependencies.referencedIdentifiers),
    };
  }

  /**
   * Check if node has export modifier
   */
  hasExportModifier(node) {
    return !!node.modifiers?.some(m => m.kind === ts.SyntaxKind.ExportKeyword);
  }

  /**
   * Find specific type declarations by name
   */
  findTypeDeclaration(sourceFile, typeName) {
    const declarations = this.extractTypeDeclarations(sourceFile);
    return declarations.filter(decl => decl.name === typeName);
  }

  /**
   * Check if file imports from a specific module
   */
  importsFromModule(sourceFile, moduleName) {
    const imports = this.extractImports(sourceFile);
    return imports.some(imp => imp.module === moduleName);
  }

  /**
   * Get all imported types from a module
   */
  getImportedTypes(sourceFile, moduleName) {
    const imports = this.extractImports(sourceFile);
    const moduleImports = imports.filter(imp => imp.module === moduleName);
    
    const types = [];
    moduleImports.forEach(imp => {
      if (imp.defaultImport) types.push(imp.defaultImport);
      imp.namedImports.forEach(named => types.push(named.name));
    });
    
    return types;
  }
}

// Export for use in other modules
export default TypeScriptASTParser;