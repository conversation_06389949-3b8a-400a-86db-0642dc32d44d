{"name": "adhd-trading-dashboard-monorepo", "version": "1.0.0", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "npm run build --workspaces", "start:core": "npm run start -w packages/core", "start:dashboard": "npm run start -w packages/dashboard", "deploy": "npm run build && node scripts/deploy.js", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces"}, "author": "", "license": "MIT"}