# ADHD Trading Dashboard Build System

This document describes the new build system for the ADHD Trading Dashboard monorepo.

## Project Structure

The project is organized as a monorepo with three main packages:

- **shared**: Common components, utilities, and theme system
- **core**: Google Apps Script integration
- **dashboard**: Main React application

## Prerequisites

- Node.js v18 LTS (recommended)
- Yarn package manager

## Setup

1. Install dependencies:

```bash
yarn install
```

2. Build all packages:

```bash
yarn build
```

## Development Workflow

### Starting the Development Server

```bash
yarn start
```

This will start the React development server for the dashboard package.

### Building Packages

Build all packages:

```bash
yarn build
```

Build individual packages:

```bash
yarn build:shared
yarn build:core
yarn build:dashboard
```

Build in development mode:

```bash
yarn build:dev
```

### Type Checking

Run TypeScript type checking:

```bash
yarn type-check
```

Watch mode for type checking:

```bash
yarn type-check:watch
```

### Testing

Run tests:

```bash
yarn test
```

Run tests in watch mode:

```bash
yarn test:watch
```

Run tests with coverage:

```bash
yarn test:coverage
```

### Linting

Lint all packages:

```bash
yarn lint
```

### Cleaning

Clean build artifacts:

```bash
yarn clean
```

Deep clean (including node_modules):

```bash
yarn clean:deep
```

## Build System Architecture

### TypeScript Configuration

The project uses TypeScript project references to manage dependencies between packages. The root `tsconfig.json` defines common settings, and each package extends these settings with package-specific configurations.

### Babel Configuration

Babel is used for transpilation with the following presets:

- `@babel/preset-env`: For ES features
- `@babel/preset-react`: For JSX
- `@babel/preset-typescript`: For TypeScript

And plugins:

- `babel-plugin-styled-components`: For styled-components
- `@babel/plugin-proposal-class-properties`: For class properties
- `@babel/plugin-syntax-dynamic-import`: For dynamic imports
- `@babel/plugin-transform-runtime`: For runtime helpers

### Webpack Configuration

Each package has its own webpack configuration for bundling. The configurations are optimized for:

- Development: Source maps, fast builds
- Production: Minification, optimization

### Jest Configuration

Jest is configured for testing with:

- React Testing Library
- TypeScript support
- Coverage reporting

## Deployment

Deploy to Google Apps Script:

```bash
yarn deploy
```

Deploy to GitHub Pages:

```bash
yarn deploy:gh-pages
```

## Troubleshooting

If you encounter issues with the build system:

1. Try cleaning the build artifacts: `yarn clean`
2. Check TypeScript version consistency: `yarn check-versions`
3. Align package versions: `yarn align-versions`
4. If all else fails, try a deep clean: `yarn clean:deep` followed by `yarn install`
