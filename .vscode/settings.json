{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.updateImportsOnFileMove.enabled": "always", "javascript.preferences.importModuleSpecifier": "relative", "javascript.updateImportsOnFileMove.enabled": "always", "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": false, "**/dist": false}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/**": true, "**/dist/**": true}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.singleQuote": true, "prettier.trailingComma": "es5", "prettier.printWidth": 100}