#!/usr/bin/env node

/**
 * Code Health Diagnostic Tool
 * Debugs why code health tools aren't detecting improvements
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

class HealthDiagnostic {
  async run() {
    console.log('🩺 CODE HEALTH DIAGNOSTIC\n');

    await this.checkYarnCommands();
    await this.checkPreviousReports();
    await this.checkFileChanges();
    await this.checkAnalysisScope();
    await this.provideSolutions();
  }

  async checkYarnCommands() {
    console.log('📦 CHECKING YARN COMMANDS');

    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      const scripts = packageJson.scripts || {};

      console.log('Available health commands:');
      Object.entries(scripts)
        .filter(([name]) => name.includes('health'))
        .forEach(([name, command]) => {
          console.log(`  ${name}: ${command}`);
        });

      if (!scripts.health) {
        console.log('❌ No "health" script found in package.json');
        console.log("   This might be why you're not seeing updates");
      }
    } catch (error) {
      console.log('❌ Could not read package.json');
    }
    console.log('');
  }

  async checkPreviousReports() {
    console.log('📊 CHECKING PREVIOUS REPORTS');

    const reportFiles = [
      'code-health-previous-report.json',
      'scripts-analysis-previous.json',
      'code-health-report.json',
      'scripts-analysis.json',
    ];

    for (const file of reportFiles) {
      try {
        const stat = await fs.stat(file);
        const data = JSON.parse(await fs.readFile(file, 'utf8'));

        console.log(`✅ ${file}:`);
        console.log(`   Last modified: ${stat.mtime.toLocaleString()}`);
        console.log(
          `   Health score: ${data.healthScore || data.currentHealth?.score || 'Not found'}`
        );
        console.log(`   Issues: ${data.totalIssues || data.summary?.totalIssues || 'Not found'}`);
        console.log(`   Timestamp: ${data.timestamp || 'Not found'}`);
      } catch (error) {
        console.log(`❌ ${file}: Not found or invalid`);
      }
    }
    console.log('');
  }

  async checkFileChanges() {
    console.log('📁 CHECKING RECENT FILE CHANGES');

    try {
      // Check git status to see what's been modified recently
      const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' });
      if (gitStatus.trim()) {
        console.log('Uncommitted changes found:');
        console.log(gitStatus);
      } else {
        console.log('No uncommitted changes');
      }

      // Check recent commits
      const recentCommits = execSync('git log --oneline -5', { encoding: 'utf8' });
      console.log('\nRecent commits:');
      console.log(recentCommits);
    } catch (error) {
      console.log('❌ Could not check git status (not a git repo?)');
    }
    console.log('');
  }

  async checkAnalysisScope() {
    console.log('🔍 CHECKING ANALYSIS SCOPE');

    // Check what files would be analyzed
    const patterns = [
      'packages/*/src/**/*.{ts,tsx,js,jsx}',
      'src/**/*.{ts,tsx,js,jsx}',
      'scripts/**/*.js',
    ];

    for (const pattern of patterns) {
      try {
        const files = await this.findFiles(pattern);
        console.log(`${pattern}: ${files.length} files`);
        if (files.length > 0) {
          console.log(
            `   Sample: ${files
              .slice(0, 3)
              .map((f) => path.relative('.', f))
              .join(', ')}`
          );
        }
      } catch (error) {
        console.log(`${pattern}: Error scanning`);
      }
    }

    // Check if the refactored areas are being analyzed
    const refactoredAreas = [
      'packages/shared/src/types/trading.ts',
      'packages/dashboard/src/features/trade-journal',
      'packages/shared/src/services/tradeStorage.ts',
    ];

    console.log('\nChecking refactored areas:');
    for (const area of refactoredAreas) {
      try {
        await fs.access(area);
        const stat = await fs.stat(area);
        console.log(`✅ ${area} (modified: ${stat.mtime.toLocaleDateString()})`);
      } catch (error) {
        console.log(`❌ ${area} not found`);
      }
    }
    console.log('');
  }

  async provideSolutions() {
    console.log('💡 SOLUTIONS TO TRY:');

    console.log('1. Force fresh analysis:');
    console.log('   rm code-health-*.json scripts-analysis*.json');
    console.log('   yarn health');
    console.log('');

    console.log('2. Run individual tools to isolate issue:');
    console.log('   node code-health/enhanced-analyze-codebase.js packages/');
    console.log('   node code-health/enhanced-refactor-analyzer.js scripts/');
    console.log('');

    console.log('3. Check if tools are analyzing right directories:');
    console.log('   yarn health --verbose');
    console.log('');

    console.log('4. Manual validation - check if your recent changes actually fixed issues:');
    console.log('   - Did you remove old API patterns?');
    console.log('   - Did you consolidate duplicate Trade interfaces?');
    console.log('   - Are TypeScript errors gone?');
    console.log('');

    console.log('5. Update analysis scope to focus on your refactored areas:');
    console.log('   node code-health/enhanced-analyze-codebase.js packages/dashboard/src/');
    console.log('   node code-health/enhanced-analyze-codebase.js packages/shared/src/');
  }

  async findFiles(pattern) {
    // Simple file finder
    const files = [];

    async function walk(dir) {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
            await walk(fullPath);
          } else if (entry.isFile() && this.matchesPattern(entry.name, pattern)) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    }

    await walk.call(this, '.');
    return files;
  }

  matchesPattern(filename, pattern) {
    // Extract file extensions from pattern like "*.{ts,tsx,js,jsx}"
    const extMatch = pattern.match(/\{([^}]+)\}/);
    if (extMatch) {
      const extensions = extMatch[1].split(',');
      const fileExt = path.extname(filename).slice(1);
      return extensions.includes(fileExt);
    }

    // Simple wildcard matching
    const regex = pattern.replace(/\*/g, '.*').replace(/\?/g, '.');
    return new RegExp(regex).test(filename);
  }
}

// Run diagnostic
const diagnostic = new HealthDiagnostic();
diagnostic.run().catch(console.error);
