# Running the ADHD Trading Dashboard

This guide provides step-by-step instructions for running the ADHD Trading Dashboard application.

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

## Step 1: Install Dependencies

First, install the dependencies for the monorepo:

```bash
npm install
```

## Step 2: Link Packages

Link the packages in the monorepo together:

```bash
npm run link-packages
```

This script will:
1. Build each package in the correct order
2. Create npm links between the packages
3. Link the dependencies correctly

## Step 3: Start the Dashboard

Start the dashboard application:

```bash
npm run dashboard
```

This will start the React development server for the dashboard package. The application will be available at http://localhost:3000.

## Troubleshooting

### Package Dependency Issues

If you encounter dependency issues, try installing with the `--legacy-peer-deps` flag:

```bash
npm install --legacy-peer-deps
```

### TypeScript Errors

If you encounter TypeScript errors, try running the type checker:

```bash
npm run type-check
```

Fix any errors that are reported, then try running the application again.

### Build Errors

If you encounter build errors, try cleaning the build directories:

```bash
# Remove node_modules and dist directories
find . -name "node_modules" -type d -prune -exec rm -rf '{}' +
find . -name "dist" -type d -prune -exec rm -rf '{}' +

# Reinstall dependencies
npm install

# Link packages
npm run link-packages
```

## Deployment

To deploy the application to Google Apps Script:

```bash
npm run build-and-deploy
```

This will build the application and deploy it to Google Apps Script.

## Development Workflow

1. Make changes to the code
2. Run `npm run link-packages` to rebuild and link the packages
3. Run `npm run dashboard` to start the development server
4. Test your changes
5. Repeat
