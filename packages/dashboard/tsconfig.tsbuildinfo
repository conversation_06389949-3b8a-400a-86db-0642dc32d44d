{"program": {"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/typescript/lib/lib.es2022.d.ts", "../../node_modules/typescript/lib/lib.esnext.d.ts", "../../node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.array.d.ts", "../../node_modules/typescript/lib/lib.es2022.error.d.ts", "../../node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.object.d.ts", "../../node_modules/typescript/lib/lib.es2022.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2022.string.d.ts", "../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/@types/react/global.d.ts", "../../node_modules/csstype/index.d.ts", "../../node_modules/@types/prop-types/index.d.ts", "../../node_modules/@types/react/index.d.ts", "../../node_modules/@types/react/jsx-runtime.d.ts", "../../node_modules/@remix-run/router/dist/history.d.ts", "../../node_modules/@remix-run/router/dist/utils.d.ts", "../../node_modules/@remix-run/router/dist/router.d.ts", "../../node_modules/@remix-run/router/dist/index.d.ts", "../../node_modules/react-router/dist/lib/context.d.ts", "../../node_modules/react-router/dist/lib/components.d.ts", "../../node_modules/react-router/dist/lib/hooks.d.ts", "../../node_modules/react-router/dist/index.d.ts", "../../node_modules/react-router-dom/dist/dom.d.ts", "../../node_modules/react-router-dom/dist/index.d.ts", "../shared/dist/api/types/trading.d.ts", "../shared/dist/api/types/index.d.ts", "../shared/dist/api/gasapi.d.ts", "../shared/dist/api/index.d.ts", "../shared/dist/hooks/useerrorhandler.d.ts", "../shared/dist/hooks/index.d.ts", "../shared/dist/monitoring/index.d.ts", "../shared/dist/services/tradestorage.d.ts", "../shared/dist/services/index.d.ts", "../shared/dist/index.d.ts", "../../node_modules/@types/hoist-non-react-statics/index.d.ts", "../../node_modules/@types/styled-components/index.d.ts", "./src/layouts/sidebar.tsx", "./src/layouts/header.tsx", "./src/layouts/mainlayout.tsx", "./src/components/molecules/loadingscreen.tsx", "./src/features/performance-dashboard/components/metricspanel.tsx", "./src/features/performance-dashboard/components/performancechart.tsx", "./src/features/performance-dashboard/components/recenttradespanel.tsx", "./src/features/performance-dashboard/hooks/usedashboarddata.ts", "./src/features/performance-dashboard/dashboard.tsx", "./src/features/daily-guide/types.ts", "./src/features/daily-guide/components/marketoverview.tsx", "./src/features/daily-guide/components/tradingplan.tsx", "./src/features/daily-guide/components/keylevels.tsx", "./src/features/daily-guide/api/dailyguideapi.ts", "./src/features/daily-guide/context/dailyguidecontext.tsx", "./src/features/daily-guide/components/marketnews.tsx", "./src/features/daily-guide/components/ui/sectioncard.tsx", "./src/features/daily-guide/components/ui/sentimentbadge.tsx", "./src/features/daily-guide/components/ui/prioritytag.tsx", "./src/features/daily-guide/components/ui/index.ts", "./src/features/daily-guide/dailyguide.tsx", "./src/features/trade-journal/types/index.ts", "./src/features/trade-journal/components/tradelist.tsx", "./src/services/tradestorage.ts", "./src/features/trade-journal/hooks/usetradejournal.ts", "./src/features/trade-journal/tradejournal.tsx", "./src/features/trade-analysis/types.ts", "./src/features/trade-analysis/api/tradeanalysisapi.ts", "./src/features/trade-analysis/context/tradeanalysiscontext.tsx", "./src/features/trade-analysis/components/filterpanel.tsx", "./src/features/trade-analysis/components/performancesummary.tsx", "./src/features/trade-analysis/components/tradestable.tsx", "./src/features/trade-analysis/components/categoryperformancechart.tsx", "./src/features/trade-analysis/components/timeperformancechart.tsx", "./src/features/trade-analysis/components/tradedetail.tsx", "./src/features/trade-analysis/tradeanalysis.tsx", "./src/features/trade-journal/hooks/usetradeformdata.ts", "./src/features/trade-journal/hooks/usetradevalidation.ts", "./src/features/trade-journal/hooks/usetradecalculations.ts", "./src/features/trade-journal/constants/patternquality.ts", "./src/features/trade-journal/hooks/usetradesubmission.ts", "./src/features/trade-journal/hooks/usetradeform.ts", "./src/features/trade-journal/components/tabpanel.tsx", "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "./src/features/trade-journal/constants/dolanalysis.ts", "./src/features/trade-journal/components/dol-analysis/doltypeselector.tsx", "./src/features/trade-journal/components/dol-analysis/dolstrengthselector.tsx", "./src/features/trade-journal/components/dol-analysis/dolreactionselector.tsx", "./src/features/trade-journal/components/dol-analysis/dolcontextselector.tsx", "./src/features/trade-journal/components/dol-analysis/doldetailedanalysis.tsx", "./src/features/trade-journal/components/dol-analysis/doleffectivenessrating.tsx", "./src/features/trade-journal/components/dol-analysis/dolanalysis.tsx", "./src/features/trade-journal/components/form/tradeformheader.tsx", "./src/features/trade-journal/components/form/tradeformbasicfields.tsx", "./src/features/trade-journal/hooks/index.ts", "./src/features/trade-journal/components/timepicker.tsx", "./src/features/trade-journal/components/selectdropdown.tsx", "./src/features/trade-journal/components/form/tradeformtimingfields.tsx", "./src/features/trade-journal/components/form/tradeformriskfields.tsx", "./src/features/trade-journal/constants/setupclassification.ts", "./src/features/trade-journal/components/setup-classification/primarysetupselector.tsx", "./src/features/trade-journal/components/setup-classification/secondarysetupselector.tsx", "./src/features/trade-journal/components/setup-classification/liquidityselector.tsx", "./src/features/trade-journal/components/setup-classification/fvgselector.tsx", "./src/features/trade-journal/components/setup-classification/doltargetselector.tsx", "./src/features/trade-journal/components/setup-classification/parentpdarrayselector.tsx", "./src/features/trade-journal/components/setup-classification/setupclassificationsection.tsx", "./src/features/trade-journal/components/form/tradeformstrategyfields.tsx", "./src/features/trade-journal/components/form/tradeformactions.tsx", "./src/features/trade-journal/components/form/tradeformmessages.tsx", "./src/features/trade-journal/components/form/tradeformloading.tsx", "./src/features/trade-journal/components/form/index.ts", "./src/features/trade-journal/tradeform.tsx", "./src/features/settings/hooks/usesettings.ts", "./src/features/settings/settings.tsx", "./src/components/notfound.tsx", "./src/routes/routes.tsx", "./src/routes/index.ts", "./src/components/apperrorboundary.tsx", "./src/app.tsx", "./src/minimalapp.tsx", "./src/simpleapp.tsx", "./src/testapp.tsx", "./src/devtools-config.js", "../../node_modules/@types/react-dom/client.d.ts", "../../node_modules/web-vitals/dist/modules/types.d.ts", "../../node_modules/web-vitals/dist/modules/getcls.d.ts", "../../node_modules/web-vitals/dist/modules/getfcp.d.ts", "../../node_modules/web-vitals/dist/modules/getfid.d.ts", "../../node_modules/web-vitals/dist/modules/getlcp.d.ts", "../../node_modules/web-vitals/dist/modules/getttfb.d.ts", "../../node_modules/web-vitals/dist/modules/index.d.ts", "./src/reportwebvitals.ts", "./src/index.tsx", "./src/simple-index.tsx", "./src/components/featureerrorboundary.tsx", "./src/features/daily-guide/state/dailyguidestate.ts", "./src/features/daily-guide/state/dailyguideselectors.ts", "./src/features/daily-guide/state/index.ts", "./src/features/daily-guide/hooks/usedailyguide.ts", "./src/features/daily-guide/hooks/index.ts", "./src/features/daily-guide/components/dailyguide.tsx", "./src/features/daily-guide/components/index.ts", "./src/features/daily-guide/index.ts", "./src/features/performance-dashboard/index.ts", "./src/features/settings/components/settingssection.tsx", "./src/features/settings/components/settingitem.tsx", "./src/features/settings/components/toggleswitch.tsx", "./src/features/settings/index.ts", "./src/features/trade-analysis/state/tradeanalysisstate.ts", "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "./src/features/trade-analysis/components/tradeanalysistable.tsx", "./src/features/trade-analysis/components/tradeanalysissummary.tsx", "./src/features/trade-analysis/components/tradeanalysischarts.tsx", "./src/features/trade-analysis/components/tradeanalysis.tsx", "./src/features/trade-analysis/index.ts", "./src/features/trade-analysis/components/distributionchart.tsx", "./src/features/trade-analysis/components/equitycurve.tsx", "./src/features/trade-analysis/components/metricspanel.tsx", "./src/features/trade-analysis/types/index.ts", "./src/features/trade-journal/index.ts", "./src/features/trading-dashboard/types/index.ts", "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "./src/features/trading-dashboard/components/metricspanel.tsx", "./node_modules/recharts/types/container/surface.d.ts", "./node_modules/recharts/types/container/layer.d.ts", "../../node_modules/@types/d3-time/index.d.ts", "../../node_modules/@types/d3-scale/index.d.ts", "../../node_modules/victory-vendor/d3-scale.d.ts", "./node_modules/recharts/types/cartesian/xaxis.d.ts", "./node_modules/recharts/types/cartesian/yaxis.d.ts", "./node_modules/recharts/types/util/types.d.ts", "./node_modules/recharts/types/component/defaultlegendcontent.d.ts", "./node_modules/recharts/types/util/payload/getuniqpayload.d.ts", "./node_modules/recharts/types/component/legend.d.ts", "./node_modules/recharts/types/component/defaulttooltipcontent.d.ts", "./node_modules/recharts/types/component/tooltip.d.ts", "./node_modules/recharts/types/component/responsivecontainer.d.ts", "./node_modules/recharts/types/component/cell.d.ts", "./node_modules/recharts/types/component/text.d.ts", "./node_modules/recharts/types/component/label.d.ts", "./node_modules/recharts/types/component/labellist.d.ts", "./node_modules/recharts/types/component/customized.d.ts", "./node_modules/recharts/types/shape/sector.d.ts", "../../node_modules/@types/d3-path/index.d.ts", "../../node_modules/@types/d3-shape/index.d.ts", "../../node_modules/victory-vendor/d3-shape.d.ts", "./node_modules/recharts/types/shape/curve.d.ts", "./node_modules/recharts/types/shape/rectangle.d.ts", "./node_modules/recharts/types/shape/polygon.d.ts", "./node_modules/recharts/types/shape/dot.d.ts", "./node_modules/recharts/types/shape/cross.d.ts", "./node_modules/recharts/types/shape/symbols.d.ts", "./node_modules/recharts/types/polar/polargrid.d.ts", "./node_modules/recharts/types/polar/polarradiusaxis.d.ts", "./node_modules/recharts/types/polar/polarangleaxis.d.ts", "./node_modules/recharts/types/polar/pie.d.ts", "./node_modules/recharts/types/polar/radar.d.ts", "./node_modules/recharts/types/polar/radialbar.d.ts", "./node_modules/recharts/types/cartesian/brush.d.ts", "./node_modules/recharts/types/util/ifoverflowmatches.d.ts", "./node_modules/recharts/types/cartesian/referenceline.d.ts", "./node_modules/recharts/types/cartesian/referencedot.d.ts", "./node_modules/recharts/types/cartesian/referencearea.d.ts", "./node_modules/recharts/types/cartesian/cartesianaxis.d.ts", "./node_modules/recharts/types/cartesian/cartesiangrid.d.ts", "./node_modules/recharts/types/cartesian/line.d.ts", "./node_modules/recharts/types/cartesian/area.d.ts", "./node_modules/recharts/types/util/barutils.d.ts", "./node_modules/recharts/types/cartesian/bar.d.ts", "./node_modules/recharts/types/cartesian/zaxis.d.ts", "./node_modules/recharts/types/cartesian/errorbar.d.ts", "./node_modules/recharts/types/cartesian/scatter.d.ts", "../../node_modules/@types/lodash/common/common.d.ts", "../../node_modules/@types/lodash/common/array.d.ts", "../../node_modules/@types/lodash/common/collection.d.ts", "../../node_modules/@types/lodash/common/date.d.ts", "../../node_modules/@types/lodash/common/function.d.ts", "../../node_modules/@types/lodash/common/lang.d.ts", "../../node_modules/@types/lodash/common/math.d.ts", "../../node_modules/@types/lodash/common/number.d.ts", "../../node_modules/@types/lodash/common/object.d.ts", "../../node_modules/@types/lodash/common/seq.d.ts", "../../node_modules/@types/lodash/common/string.d.ts", "../../node_modules/@types/lodash/common/util.d.ts", "../../node_modules/@types/lodash/index.d.ts", "./node_modules/recharts/types/util/getlegendprops.d.ts", "./node_modules/recharts/types/util/chartutils.d.ts", "./node_modules/recharts/types/chart/accessibilitymanager.d.ts", "./node_modules/recharts/types/chart/types.d.ts", "./node_modules/recharts/types/chart/generatecategoricalchart.d.ts", "./node_modules/recharts/types/chart/linechart.d.ts", "./node_modules/recharts/types/chart/barchart.d.ts", "./node_modules/recharts/types/chart/piechart.d.ts", "./node_modules/recharts/types/chart/treemap.d.ts", "./node_modules/recharts/types/chart/sankey.d.ts", "./node_modules/recharts/types/chart/radarchart.d.ts", "./node_modules/recharts/types/chart/scatterchart.d.ts", "./node_modules/recharts/types/chart/areachart.d.ts", "./node_modules/recharts/types/chart/radialbarchart.d.ts", "./node_modules/recharts/types/chart/composedchart.d.ts", "./node_modules/recharts/types/chart/sunburstchart.d.ts", "./node_modules/recharts/types/shape/trapezoid.d.ts", "./node_modules/recharts/types/numberaxis/funnel.d.ts", "./node_modules/recharts/types/chart/funnelchart.d.ts", "./node_modules/recharts/types/util/global.d.ts", "./node_modules/recharts/types/index.d.ts", "./src/features/trading-dashboard/components/performancechart.tsx", "./src/features/trading-dashboard/components/recenttradestable.tsx", "./src/features/trading-dashboard/components/setupanalysis.tsx", "./src/features/trading-dashboard/tradingdashboard.tsx", "./src/features/trading-dashboard/index.ts", "./src/layouts/index.ts", "./src/pages/dailyguide.tsx", "./src/pages/dashboard.tsx", "./src/pages/notfound.tsx", "./src/pages/settings.tsx", "./src/pages/tradeanalysis.tsx", "./src/pages/tradeform.tsx", "./src/pages/tradejournal.tsx", "./src/routes/components/molecules/loadingscreen.tsx", "./src/routes/layouts/mainlayout.tsx", "../../node_modules/@vitest/utils/dist/types.d.ts", "../../node_modules/@vitest/utils/dist/helpers.d.ts", "../../node_modules/pretty-format/build/types.d.ts", "../../node_modules/pretty-format/build/index.d.ts", "../../node_modules/@vitest/utils/dist/index.d.ts", "../../node_modules/@vitest/runner/dist/tasks-c965d7f6.d.ts", "../../node_modules/@vitest/runner/dist/runner-3b8473ea.d.ts", "../../node_modules/@vitest/runner/dist/index.d.ts", "../../node_modules/@types/chai/index.d.ts", "../../node_modules/@vitest/expect/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment-38cdead3.d.ts", "../../node_modules/@vitest/snapshot/dist/index-6461367c.d.ts", "../../node_modules/@vitest/snapshot/dist/index.d.ts", "../../node_modules/@types/node/assert.d.ts", "../../node_modules/@types/node/assert/strict.d.ts", "../../node_modules/@types/node/globals.d.ts", "../../node_modules/@types/node/async_hooks.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/child_process.d.ts", "../../node_modules/@types/node/cluster.d.ts", "../../node_modules/@types/node/console.d.ts", "../../node_modules/@types/node/constants.d.ts", "../../node_modules/@types/node/crypto.d.ts", "../../node_modules/@types/node/dgram.d.ts", "../../node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/@types/node/dns.d.ts", "../../node_modules/@types/node/dns/promises.d.ts", "../../node_modules/@types/node/domain.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/fs/promises.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/http2.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/inspector.d.ts", "../../node_modules/@types/node/module.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/os.d.ts", "../../node_modules/@types/node/path.d.ts", "../../node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/@types/node/process.d.ts", "../../node_modules/@types/node/punycode.d.ts", "../../node_modules/@types/node/querystring.d.ts", "../../node_modules/@types/node/readline.d.ts", "../../node_modules/@types/node/repl.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/stream/promises.d.ts", "../../node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/@types/node/stream/web.d.ts", "../../node_modules/@types/node/string_decoder.d.ts", "../../node_modules/@types/node/test.d.ts", "../../node_modules/@types/node/timers.d.ts", "../../node_modules/@types/node/timers/promises.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/trace_events.d.ts", "../../node_modules/@types/node/tty.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/v8.d.ts", "../../node_modules/@types/node/vm.d.ts", "../../node_modules/@types/node/wasi.d.ts", "../../node_modules/@types/node/worker_threads.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/globals.global.d.ts", "../../node_modules/@types/node/index.d.ts", "../../node_modules/esbuild/lib/main.d.ts", "../../node_modules/vitest/node_modules/vite/types/metadata.d.ts", "../../node_modules/vitest/node_modules/vite/types/hmrpayload.d.ts", "../../node_modules/vitest/node_modules/vite/types/customevent.d.ts", "../../node_modules/vitest/node_modules/rollup/dist/rollup.d.ts", "../../node_modules/vitest/node_modules/vite/types/importglob.d.ts", "../../node_modules/source-map-js/source-map.d.ts", "../../node_modules/postcss/lib/previous-map.d.ts", "../../node_modules/postcss/lib/input.d.ts", "../../node_modules/postcss/lib/css-syntax-error.d.ts", "../../node_modules/postcss/lib/declaration.d.ts", "../../node_modules/postcss/lib/root.d.ts", "../../node_modules/postcss/lib/warning.d.ts", "../../node_modules/postcss/lib/lazy-result.d.ts", "../../node_modules/postcss/lib/no-work-result.d.ts", "../../node_modules/postcss/lib/processor.d.ts", "../../node_modules/postcss/lib/result.d.ts", "../../node_modules/postcss/lib/document.d.ts", "../../node_modules/postcss/lib/rule.d.ts", "../../node_modules/postcss/lib/node.d.ts", "../../node_modules/postcss/lib/comment.d.ts", "../../node_modules/postcss/lib/container.d.ts", "../../node_modules/postcss/lib/at-rule.d.ts", "../../node_modules/postcss/lib/list.d.ts", "../../node_modules/postcss/lib/postcss.d.ts", "../../node_modules/vitest/node_modules/vite/dist/node/index.d.ts", "../../node_modules/@vitest/runner/dist/types.d.ts", "../../node_modules/@vitest/runner/types.d.ts", "../../node_modules/@vitest/utils/dist/diff.d.ts", "../../node_modules/@vitest/utils/diff.d.ts", "../../node_modules/@vitest/runner/dist/utils.d.ts", "../../node_modules/@vitest/runner/utils.d.ts", "../../node_modules/tinybench/dist/index.d.cts", "../../node_modules/vite-node/dist/types.d-1e7e3fdf.d.ts", "../../node_modules/vite-node/dist/types-c39b64bb.d.ts", "../../node_modules/vite-node/dist/client.d.ts", "../../node_modules/@vitest/snapshot/dist/manager.d.ts", "../../node_modules/@vitest/snapshot/manager.d.ts", "../../node_modules/vite-node/dist/index.d.ts", "../../node_modules/source-map/source-map.d.ts", "../../node_modules/vite-node/node_modules/vite/dist/node/index.d.ts", "../../node_modules/vite-node/dist/server.d.ts", "../../node_modules/vitest/dist/types-e3c9754d.d.ts", "../../node_modules/tinyspy/dist/index.d.ts", "../../node_modules/@vitest/spy/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment.d.ts", "../../node_modules/@vitest/snapshot/environment.d.ts", "../../node_modules/vitest/dist/config.d.ts", "../../node_modules/vitest/dist/index.d.ts", "../../node_modules/vitest/globals.d.ts", "../../node_modules/@types/node/ts5.6/buffer.buffer.d.ts", "../../node_modules/vite/dist/node/index.d.ts"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "4b421cbfb3a38a27c279dec1e9112c3d1da296f77a1a85ddadf7e7a425d45d18", "1fc5ab7a764205c68fa10d381b08417795fc73111d6dd16b5b1ed36badb743d9", "746d62152361558ea6d6115cf0da4dd10ede041d14882ede3568bce5dc4b4f1f", "d11a03592451da2d1065e09e61f4e2a9bf68f780f4f6623c18b57816a9679d17", "aea179452def8a6152f98f63b191b84e7cbd69b0e248c91e61fb2e52328abe8c", {"version": "3aafcb693fe5b5c3bd277bd4c3a617b53db474fe498fc5df067c5603b1eebde7", "affectsGlobalScope": true}, {"version": "f3d4da15233e593eacb3965cde7960f3fddf5878528d882bcedd5cbaba0193c7", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "81cac4cbc92c0c839c70f8ffb94eb61e2d32dc1c3cf6d95844ca099463cf37ea", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "6e7997ef61de3132e4d4b2250e75343f487903ddf5370e7ce33cf1b9db9a63ed", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "5e5e095c4470c8bab227dbbc61374878ecead104c74ab9960d3adcccfee23205", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "d7f680a43f8cd12a6b6122c07c54ba40952b0c8aa140dcfcf32eb9e6cb028596", "affectsGlobalScope": true}, {"version": "3787b83e297de7c315d55d4a7c546ae28e5f6c0a361b7a1dcec1f1f50a54ef11", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "2768ef564cfc0689a1b76106c421a2909bdff0acbe87da010785adab80efdd5c", "affectsGlobalScope": true}, {"version": "b248e32ca52e8f5571390a4142558ae4f203ae2f94d5bac38a3084d529ef4e58", "affectsGlobalScope": true}, {"version": "6c55633c733c8378db65ac3da7a767c3cf2cf3057f0565a9124a16a3a2019e87", "affectsGlobalScope": true}, {"version": "fb4416144c1bf0323ccbc9afb0ab289c07312214e8820ad17d709498c865a3fe", "affectsGlobalScope": true}, {"version": "5b0ca94ec819d68d33da516306c15297acec88efeb0ae9e2b39f71dbd9685ef7", "affectsGlobalScope": true}, {"version": "34c839eaaa6d78c8674ae2c37af2236dee6831b13db7b4ef4df3ec889a04d4f2", "affectsGlobalScope": true}, {"version": "34478567f8a80171f88f2f30808beb7da15eac0538ae91282dd33dce928d98ed", "affectsGlobalScope": true}, {"version": "ab7d58e6161a550ff92e5aff755dc37fe896245348332cd5f1e1203479fe0ed1", "affectsGlobalScope": true}, {"version": "6bda95ea27a59a276e46043b7065b55bd4b316c25e70e29b572958fa77565d43", "affectsGlobalScope": true}, {"version": "aedb8de1abb2ff1095c153854a6df7deae4a5709c37297f9d6e9948b6806fa66", "affectsGlobalScope": true}, {"version": "a4da0551fd39b90ca7ce5f68fb55d4dc0c1396d589b612e1902f68ee090aaada", "affectsGlobalScope": true}, {"version": "11ffe3c281f375fff9ffdde8bbec7669b4dd671905509079f866f2354a788064", "affectsGlobalScope": true}, {"version": "52d1bb7ab7a3306fd0375c8bff560feed26ed676a5b0457fa8027b563aecb9a4", "affectsGlobalScope": true}, {"version": "bbdf156fea2fabed31a569445835aeedcc33643d404fcbaa54541f06c109df3f", "affectsGlobalScope": true}, "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "65ff5a0aefd7817a03c1ad04fee85c9cdd3ec415cc3c9efec85d8008d4d5e4ee", {"version": "7fb6faf1006c3503d44e4922c8c65772ddc98e92bad7c2ae2d5db123f3e297b3", "affectsGlobalScope": true}, "af7fd2870746deed40e130fc0a3966de74e8f52a97ec114d0fbb35876ab05ca9", "036b2bdb6931917cd8c03e4448d26d14b760035ff0cc636f582eedbac84cdb8c", "31172fc1c2ff8750789aa6ca42dbb19869ba33acea9df7141e6a65154f49d31b", "adb0acb5deb823b8a300cbb9453cee23d5acdebfc3569cdb2947cfba8e465711", "e9f9b0b6a912d089ea1e008db41892a1a7dedfe37ed3603da7aabcd596b62176", "b620e82a2a4b595315172015f8a7ef55710c05e4dd3ca50835597a4d4196f3ae", "c86b1dc09268220a591c811f103cebdedeffe99c5394241cc7a9fa96d01e168b", "b147482273abaf18f285b79d9d8bfad9a20a0c8a4fd46e4c03978299f19ee518", "adad2f34346602d079ba8572f05bfd67619c1b91497adae041bbb2faaee5b99b", "202767e06b68e590b65afba715122116259e4dc500592cf05e320d6383c4dffb", {"version": "c217e646d1decd693b2e119c2a7741145704546e78e784ec9d62f4997c3c4abe", "affectsGlobalScope": true}, "63125fef16a45fbe6d0ba29356ed881ace70f4a76445e10f3207b562e3541f82", "fb33c732c5a21c6642e913178af7e60aab196e0601f795f5f28a10c91c8ec5a8", "a540f5d6fee57c60e042b84a1a345feb1f2d061d47b7c4112b76be0265ea8d6d", "e4e0b34083e0477ad9b1ae836bbbb1b06ba4611e3236c2d4413550fcfe921f67", "551234b86c9a2e2a161d6ac71a5dc7948a8d8548062aedba0256f2a2959b3727", "de8696b758c999728b29e27acbd0314fdb3823c4f8f44cb7a2e61bb155d81707", "7cbdbbc59e9b464c9b8041a0516cb099b31c7158f89cccc6ae0878adaa8ebfb5", "23507f404ecd955f1855c645c33fe9e2f03dca1fd27516cc8bcdef2685d5ae61", "e40fc0febb5d85bcf5657e3ffb7010b6a000df22b78b816c6afdb16a4a89a7bb", "aceb8a7e047cfe09a117c6c29deb4b4c29d7ac2c7629144b443404dc59b6c3b2", "b2d0630483bf337ef9dac326c3334a245aa4946e9f60f12baf7da5be44beafbb", {"version": "356701ea5df9eea3bf62b0f29857cb950d95eec9b9063f85c17be705926cdd2a", "affectsGlobalScope": true}, {"version": "ac9cfb319c33b0317c36296462eb6deed68a1612b33fd26167990f7e5f84b5fb", "signature": "dc5f149997b58549ff2c8085049c9f2e8983b8347cda91ca9d8912a36a0df962"}, {"version": "584f041367c5910b8c0b8c85e15819342cb94832ee0b0b03a0d8dfd0cb43a644", "signature": "f5328d9d09a135be62bbbd577511a967e5e0f3b633d96b1c314edbd407cd8d3a"}, {"version": "54b51820422d37f23d62bbaad774cf181fdc5a6d91116f403c11562fdf4e9077", "signature": "08fd22931fa2ff638debc6cb64638b45fe901ed5b34c42fc8120cc87a7f3c91f"}, {"version": "2d81c7d3ff6e7c00e9ecd1d58f6109ef579b13b8a956f268ad5f55555c7a0553", "signature": "350d59818bbc7c74258419aac86cb52a5a1e3fe5daf2aa5ddc5609ac29077c37"}, {"version": "7289b0f801fa6546eb60b16a9e57c6e0742ccc32724e32563b1b153b564941b4", "signature": "af898fa56c457099a6dd1d8b3a304ddc40f004ffd56140c08fb2190d6cff1cd1"}, {"version": "a463e539fb7637f43bfc6f2a23182a9c9c929ad6e17514928eeb544863f0a90a", "signature": "a43b57a8bec795499823bb1412f6390fa94eb4582f1da89021fa402b39782159"}, {"version": "2d191b6be01d7fe638b98ecf71e67e24b9543a310edae15d6aa9c3c878d69130", "signature": "eee3162a1bcf46d0d2cbe026cae41dda311557d34bbeb4a2568b24d899f0d95e"}, {"version": "8a42608ccb7e8a6a1c2a3d54d6a7df9a60279b6c484ea2c5d0e3965e68f2f719", "signature": "ccbf86c896b57fca25f342b3a8db91b62df74c1ce442b9fce50535ac3847d1e9"}, {"version": "486f0d226b3387baba7e970e8a967f6af79c83aafcce8b35334d17878030c77c", "signature": "3789b482cdd1e3b95965c9eeb116da588b2b8e9073e2e077b0b20eafdce9ccd4"}, {"version": "73e07cb21743cdddec3bd43d98d3dc9297981d5e6c132f52dc4e56a0cecc10e5", "signature": "388a025c8124b2e31a4480f059160e2cb25573752cfbf2de04064bb3d7338893"}, {"version": "4d5b6408b641409db90b69a0e7e0190fc823fe2dbedbd2503d0a4392e61b3750", "signature": "8d0ba11b90db7b4bbbe5493f8269d8c263af310bec68ba3a97adbc40c41a6ad9"}, {"version": "f899d998eadb8db2692ae7ea47244d9bbba24ac1baa283e356f7743e8c1c4a81", "signature": "f54891c82314113fe135ae8116bd9dfd60196099305d2c31cba89dd8f1665195"}, {"version": "e47b6f9e647622042b7f840594022e5acfca55a6e69accfb5dcfca932061eee5", "signature": "d3bf0b7e522962b891fb165f781e4c78964fdf10360c04a7ac29cde8a5c1ed9b"}, {"version": "e60e61545ded6ec34e8a4d8404701be339d2917d3ae78a5f7a3f4041e5f43916", "signature": "dae7514854d2624d6b793b4f9d19d274376a79ee57f2a171cfd3b608b35fcbe8"}, {"version": "78daa0250c76940bb06266742b561f9484d9ef4b90d9ca8060b2b220e7d0b8a8", "signature": "990d6d364a17014799b16380bcfdd49f8c321b82f5bc8da1f8c386ad6958dd32"}, {"version": "355df60fc6b4b2785af906c2c19f1f1c7c9581812c25536ff9e05dd9345f7d90", "signature": "fec06d67eae0876f74f74a5bfc1d9e27839897964e3010c2b17a6ac04f93944f"}, {"version": "4fb73c9aae1884a67317de55e5c65f7494afd8693f7c385524446e76b6388df0", "signature": "11ac11ee16c56a6e1333d17d6bb5c334bcc5b2a45d183f147c60273fc36ad4bc"}, {"version": "4fd559bab2bc5907ba8977c87c4a09028147193fd7b2baff3ebda8e19791819a", "signature": "8a07a1cbdb060e1103966afee15126750e6ae8011f0fc5fe5631d37bbd181177"}, {"version": "548dd9a5f21738f6ef3709823fe6540fbb9f163bb7046214235929ad06b69175", "signature": "039df669133bfc1f6190409183de1720d1b21970e072e9b807554e8db89a9059"}, {"version": "a8cafaafb985f82e89293a24140f3ba32dcf80ace105f8e6d1fd72df46e58751", "signature": "70ebc0b228ac83b2b859b4db4d49918138ad035c90d9c8b2b2e336e4c16c9beb"}, {"version": "b06a89fddd54928fb2982e5f340a4c3c85ad6a1342f23a5fba299997e0a8e7ec", "signature": "ea4dedea402bf55a0a3e76529dc7e75174b686fa9a8e1dcad91e7e6e93938fcf"}, {"version": "409c941e5c6e19f16ed144b3e3a4eec1fc071afe4521ea5802079b198befb790", "signature": "38c1d46868f6d2186750a2e49733d909df0cf935f21d5e7c4055757352897c95"}, {"version": "219f428bb747f3c13a4478f3196e1edba5facd2de1ed0ff481d7aafa5d9577d4", "signature": "4d7a49be2b7c0a3669fa07a7e37f56b25fa7904c842e55c4720660ba08896b62"}, {"version": "a15ea47f41dfa387925e986218dc6c25f59a2864f298e1e2bc5fbda0f12ed002", "signature": "ff2629e4acb9f54cf5fd8c967b4efe5e622ba286dd4da0b46a19e786fed36885"}, {"version": "09999dede6a4d37f29f2095a5735492f300697894330763bbc34f705ca0f2c85", "signature": "cce5a5feae2b66542043b6a3bda2a120981cf366280bc9cadb153ee3a836de4c"}, {"version": "79850c1286601b09ae9641eb9743718d890f953866aa0163bd5ade79b9df5998", "signature": "0e665076fb7d542b669da6ae7ceee8e5c8176b81e8e796518e614fbc20b433ea"}, {"version": "6db6910741d65b81fd15c03fb7b35f71255c7a79d4659426994ec4f0d526993b", "signature": "4a088f1b403791be30904f96a7048012f62bfc60ad571c3031f13b409351b8f0"}, {"version": "5ada4cf89a33d5f257e579f37b22017fd5941b8cb77f3cbba68e944308673d8f", "signature": "076645c76c5adc1aae21fec3a45d6dac4a1ae4f05d3b1c633917beed770f1b39"}, {"version": "7fd93794d4940e6e650a575044d34ab1fca1a0aaf323b6c124085471412544d1", "signature": "d9a2165aac2dc2a9d72c991133a9b52762a47ce919b82609506eceb42cfcec88"}, {"version": "c23470465e67a4294423a5bc1cb1940902bdb3295db379e18693ccc08f663489", "signature": "b922dc02a083fb54b89eeac6fdeb65db063485db805049b2e3d757c76b1622bd"}, {"version": "12f04a2ba8a09f073eaec72f06d443bfc0a27444086f6040bc126649be9c471e", "signature": "08f107bdb5a87c19c7f97038f9a48c176c4d57388964a85655a12988bab7883a"}, {"version": "40d4df43f6ac2f1c76652583fd3ca4ad03899e3839f150e76113862d91f7b09d", "signature": "5b0f8caca0e296331727f12717162969ac88b45d22e81c7298f479b26d06451a"}, {"version": "8404fdfb84cc0e8894c52672675ed20fae65d88b59a0870f41d9ccf9c1c4f091", "signature": "80770d683d60893ad2cc24ea6d584fce7a1f834b573eca2d6d4e62686b50c4d5"}, {"version": "93a83021793d9270cc86fc9ee4a7c7fa05cbedea6736a6f2ecd67cec52f0e55a", "signature": "007859fee5a297cf8c832bc4172ae3801343a1c20f9c9c2e6461b4192d7a34d5"}, {"version": "4b0facb99ed0702430afd84ab346a7b9a1711b22ae1426d636f349c33b2b18e4", "signature": "6467bb2c0551ba97674a89cd2e17dd3102119badd75e45730d6ab18dbc0209b7"}, {"version": "9717a7a8432ef9618800a4b8f2a944e88d0493d23e1f436352013fbed54be532", "signature": "6b81a3c1aefb95f43a718be7cc2906fe9e661380640daa2144b14a36720832c9"}, {"version": "0911bc23c41b6afcf062d39d6503d940517b6bde1a9f888c0a5d892c0ab6b960", "signature": "9b85ee11a37272940e058a790eaf5e98450d7fcc1850c0f9b046e24239c6d13d"}, {"version": "5958c86166f6774b9bc5cb5196650de71ea338d7dcdc8a683ddd51c42218e113", "signature": "b828d82178c5a3901a2b0bc311252437999647f15a893268279cf56d8f01243e"}, {"version": "1beb7988e33ab2eee4758adcde0094a4fb39fa21a01e69b27d348ab9599fc4f6", "signature": "8e72774646b24a5c834b0391db4933e24bce7df6228d6ae9298e342b39d128c6"}, {"version": "0736ff24e8d4af1bc8a53fd38247ee9dade70a1240257ccc502ccba2be03e244", "signature": "cb19e63846f7ac0645d8978ff18d51f695432072d3fa224cd1eb01cd57dd7611"}, {"version": "a5d2a766e44d060e29686cbcdf7a3885235c9df485559b15515dfbb8407c4f98", "signature": "76baa851171990e9a1e275837dee7e7bcf3c6a97c0cdd7c46ea9d448a345b3c5"}, {"version": "e65f2dc1c647dd5a4d8761ba4546af43b6195aef4ba8e18cd2dc4cfbaff1b10e", "signature": "ccfbcb8b2a9e6ac89873ec89c00347000f6a831a286ae8be22ba2d5db9de01c3"}, {"version": "f17de8f86995601f21db443f9da339cfd0ce9cc0b2f7d8515c262503fadc7580", "signature": "99d786ab622c9b56adcb58f9f0538172bcded1e8b068423d496eeaec27d90220"}, {"version": "34d05aeeb72fce9b19f3b0e357fabf7fcdd964b01cdc75841a2170ae6ff5014a", "signature": "bc97e287525a89f8383d31b293a5864fe407a5c90a4d04102c3b9c9c1c8323cd"}, {"version": "37d47df5cb8a71d19f280cd1376b0cf275c8ef5b703c1edee2bff0698d68aa59", "signature": "90fd983ea7aba602945fb86159a96d671818a9340a09e36edeb069b187ce8a0d"}, {"version": "4da6a84fcf0f3a58d8b73298d4e5665288b1a593032c99511cc8e8d5551e383e", "signature": "1cacf783e1851ec49e29a26d18ac819790bae3323752df7e3b1199f9c8d7ad78"}, {"version": "2c34533555f022bf0367657ef78a7ae5d13d3a7aedb6d6044966b536f5560861", "signature": "16386df1fbaeccbcf0337d3e482661761b35e1e7a1fc2439d225afedd1bde445"}, {"version": "c7c7f2cf80fc23b2d9b81a599e66d4ff5eb195bb9ea39c8c4493fee409bea524", "signature": "6665138a546417854e2e4052c4ad623491c10d2db87e93b50a6c6e56d88493e0"}, {"version": "4bc0af1811177d089e8c5ac42c9ba4deb830abdf4ad73448dc613fecb3a7623f", "signature": "93e1b6020078a55235ca74a8fc69cb23708ce4782d4e92c6feb9bfc7657f568c"}, {"version": "8edbbe77ed76d1fd34473e6eee32842ad924125a0e1f4d830168db2fa0e4586a", "signature": "84ceab1f99c513c5c5a9fba45e4c81ed0db4ee4ba39d044bc618d4c63d3cc493"}, {"version": "1ad5cd08861e9baba6bc79f3366490bac1262b7e8bf14f5e85a480a61ead56b0", "signature": "2e1de782466c173dd37d52346d138d6e977f3357ccd2859e6b0fa1e2c48d95bd"}, {"version": "84f21c3c5fd5a18eda20a686b75a50d2d9ceaa403f4e231fb0600b4e3eff35cc", "signature": "8246da6759ef4efe159d91009fc7c53d6dc819dba8d27428ba8d50d420b7679c"}, {"version": "a2f728a4ac9c04478fa5369658d6c8539240d8cf2d490a534406fc822c611b35", "signature": "ba41642922250cc4bc3d4357abf263cabe583f2a6623170c2e32ea41b614d093"}, {"version": "fe9fab4ebf5ec5ddeffc32bbb1ee2fd6f97dbf9e496114d55d49fb2e8d75b294", "signature": "67246f72bfabdb9ec95695e7cd266bdce51829aa50451f43c834931a5c345c13"}, {"version": "ffbfee8f0253b44563e1d67d58fab233334e7795f9c1f2f140abe3faa460d0b4", "signature": "c77fbaf95c79520bc28462575d27d3175a92d95d9bda827d7e7cb1b79c10c9d1"}, {"version": "ff953696eceae0c9554706556120b7eab06aa06cb458a6c9cece241ed2b768c8", "signature": "da5e742b0e4bc8b4aee90fb644a811c694dfb21769054b0b615a7f02fede046c"}, {"version": "4a568319899914004ee04d5a406baf1b0cc7758d1fa8861d5c44e335b7487820", "signature": "5783c7bc7e2f935270dbcb674c47b947a06d02aa08cee2c43defb52975b0b5cf"}, {"version": "d0b17a9119a82817973e7bc148233cd47fdd93170134a3f82e877707cd9eff5c", "signature": "ff10ea19e886fdea6e821f8bae3a3b2f9efde13cfce6411fcea2189f77dc90dc"}, {"version": "e4bff2afd3b0ab6b020950495b67da8be110ebd527a1a96c6b73108d021c47c7", "signature": "97f0e0bf25f2efbeb0c1a2e1dc7dce39f9ae67bfefd0201279518360c9d775e8"}, {"version": "ca6f14ab23fe4355a44654ed021a3f754bb2e1e1234f577a913cc8c28da69103", "signature": "acdd4bbd9bc6307c4069e07678fdb554a81b126471fcf422d0235c59c8c7843a"}, {"version": "70d6322f426d8692b186f9bad7fd714f19f644957a97ea896b3d972cc31d8693", "signature": "083f81f27279cf3bc71c7dc3f2490e0e2f98a167db3a7349a34a9ac5c6a1f32f"}, {"version": "3b5cc449a490bcf2ad20d19d8c7cf49aaf43c0e99f983ea030f4d016667318de", "signature": "70c225304a3e295fc5f3a12696e35a9088179252438cfe0bba0df41ce4c82b15"}, {"version": "262671818d41386f1ceb990bc4fbb5d34bce7d37514c7702268a3a02e191398c", "signature": "98604a7b2d61fb13fc636f9bd4440dbec7b635bf4c0b0179fe29a353f1c41b94"}, {"version": "9b1f6ed99f4d40ac24e529e16d25f84d848d3e6a2ac6631434f54fc1465049a6", "signature": "7bfa1bc25d6d19426c56f5fd0b448ae007cf94d570bd8524ceb9c0170ecbbf21"}, {"version": "efb903cbcbe8604ea6d13f2233d1f6dcb20f20f5deebda2594af62423f2a5fac", "signature": "aef77375bac953388684d4d36d94932f68311ec0c5d33e068aa3dfadbe87c7fa"}, {"version": "0a7402b2a95ae5064616b6211f2b37b5f16a215ede87f97c69a048a210865334", "signature": "0846db15e4df2e4ae51c197f90d18d3c7d187f6452a2cae0e2b7dd01190b698b"}, {"version": "71511cfb3032991d4cd604bfe72f3b8beda6ed9e2dbd7e0bad647cf5317ed856", "signature": "c5065b45bb379eb29c1a4d26c1172488dfbc19932151196fd90eec5e61647044"}, {"version": "3f1321b51e35e16825ca5c700decea30c3970de37ab7ce9562313181b26ec5ca", "signature": "dfc91ce2f3302d76ab31bc26a5bca17b0930483af768dfdb53f6207b0725b547"}, {"version": "849a55e5794eab9f34aaa4df356362735c882d33015fec9345550660610df639", "signature": "b357574c6cad3ed368356d17f980db9eefbd6bde3810ac3f22b7c866a324ae98"}, {"version": "6321dc647441d476279f5a7e782922cef6e6491b7710b6c4a4100b059e140b0d", "signature": "3d8a668aac5b5a165f2fc02896aa8e6ea80c7f0dc865f1e0b4c414590ff7d774"}, {"version": "e699994c1136753032c2744f88cba0535fa01775b7a7d4d695c3b3bd604fd814", "signature": "27361f53ea94cbe3c98b6c8fdb619a9ae40ea7729516fe5354dfa7fecec93a1e"}, {"version": "6fddda1a263d02c96ea02d960dfef9a0660e7beb73b10df20a91aa774d31819d", "signature": "4b151bd99f67b3841de035aec954fa5b4f471f9b453b3833fce9a7a07a86da80"}, {"version": "2530dde2eeea9c3a2480a840ce8141f580434724ecf5f275fa8dcf958b8fbc69", "signature": "572908b1c86c56b216cf041d2edd654f0966b296561ae0b5e3a3b09ae7bd4598"}, {"version": "2a61e860d78ae16819605664f7b7feca192b628ee0e76196493da169c25e4393", "signature": "2021be1ed16b739dff6794dd0421da3d5ceeffaaf40e1f19b6df840dc04eac0d"}, {"version": "ab0328b70167ba40d364246343b088622f32efd6a818119ab59b799ece96f4f7", "signature": "e94a372e7350675877f9547072eaa91d44b23ccaf74fafba2b379696b25dd967"}, {"version": "bfa4fe9a1dd763c2393ba2f3b16e8c3f594092b1dc889e73817a11759bac061c", "signature": "95586103ffdb61c995122676216f7de1e7425e447729561a829e04ebfbafdaf1"}, {"version": "5f98e58589cfb3b0296ff4989c7364405abf9b5d2c6d74aae7aa10c07abfa5ca", "signature": "e955b0f4edddc076b686fd478d96bd62df91ed88b774d4d7972912f532891958"}, {"version": "1c8ae36bc9acb61ae4511e3dea346c287762bc12162e2b63f18fd6157122488f", "signature": "3c4c9f74724e743951d7e117b25e86c60d3cb78e65bdf379a0476921c0553eee"}, {"version": "e23540c598fd9e4548cee450ff93e73eb823dd86c35cf860792460a5c2e8f49a", "signature": "b7c6cc6ebc4191a0739f76c97d51890e5798c07b889b921db304ac02bd0b8f09"}, {"version": "33fe8d1005cf9f915feecc660ec8bc79f50b109727bd91c427f4b389e816f952", "signature": "633e661b422e08d7f74e13bf7fa8848ff045230aeaafa2025aa71641e965f694"}, {"version": "ce83b4e707a5d914e30c169b7314e1bbc8ffd935c65055e13b75b9a0e4851721", "signature": "646ffdd61f99892108b3c026b02e0b3f2b3fd55150f552bb8b57ae9764f796d7"}, {"version": "6feba102d6c6bdf21b014061133ae44541b48b2a22b6d1c764e9cd805f862caa", "signature": "716bce3d042d4c46cf3fa979c4577b6a23d5859a79a907ef55139f17e8195ad2"}, {"version": "0aa6cc8333ed1e5d0f067f6e296c92fc5c2d4bd4769bea663f573129456c05cc", "signature": "9917b371f53b7838810d6c170c5ba590f43b60b2925a82acb55b9c8ef2221f96"}, {"version": "5aa882be52b887f656af7a6f7e6d5ca971f07d8e277ab272b65bec34cbb6fa56", "signature": "ec89de6c4914ecab4385756a26409a8526df923dd0a9767c818cf42824c7b545"}, {"version": "4d7a0c8e2da53fd5b0d98c85039f0ade6354a7ed4662e919c3730c34820fdae6", "signature": "f8e5e23c57153a10f1b33bcf66b3b6402482996c528344dfd54c4114b115789d"}, "83e27bbd7304ea67f9afa1535f1d4fdb15866089f0d893c784cbb5b1c6fb3386", {"version": "6231095d0b356894ceec8856ce4e354a38a773a62067978f3af9c478acbcf2e9", "affectsGlobalScope": true}, "66754e21a2ac3ffc0eb3fc55cf5c33deb2c115b726fb2c4368b870c5e85106ce", "ccd9f8f7c8760d8eb0c0c112c772a9756d59365a61fd5cc102a889fed79d0076", "d7ccbcf0e92d0209f499729768c791fbc278ecad02c3f596db5b519eb4f5d496", "85542ea0f889f8993e6b4631c541640c17c230bf5cf18d483184de9105536b4d", "8cc2eed7caa0e187fae67a1e7fdb4da79079489744c6e5be9e0839d258fd5995", "aaf2cc4016c33c836fcca0dbe20d7a97bf17cafeb581a57a62267b1c38930bd4", {"version": "b56f87a14cc2ca254c01b08da27fb4d33d1d6a84148575af34a51185cb206b8a", "signature": "22e2ce0dbe0b43a105afc528395f86a8c758a4efa0a1ab2aa3956bcc9624b8d9"}, {"version": "80f4b99d577887214270884a40cdb328c1954a18ae2de1f1db0822d98d3d7278", "signature": "85e1cdcaa35e580205336a696020943072a7285f894d3d546927f3c95cfaa3e3"}, {"version": "3f6eae5e1255ed7145a5d3b86fdd946aa49e595d8c661f0c8b7aedce450c4841", "signature": "9c0d91c8dd07033d2bdc78c408507d4cd5c5f96be51b89fbf7931bc2df7752a0"}, {"version": "e51c4e755a9002e36179512ac648dbd11af6a244f1143f1339a8a73e590012ce", "signature": "a8ce1f67cf1af5cf481828bc9eff67efb4c1475b14f52c5f0c3bd0f7589a664d"}, {"version": "8b32e44312f0e8c0da9f415c2be8cb513d954e308e0e46bc6bd40b24508fb3d5", "signature": "792c10fed6f74a98906fdeca6e8890a77fa5676826c7ec4caa6c67ed5dac4901"}, {"version": "6e2506a65839fe525922f094b67c7a7d5afb2d94e70a5659e82bd184bc4eac47", "signature": "cfcff22783c747b3f15f187e28a807b9f91cf8d8cd1091c222b6e6d7ee117c48"}, {"version": "cc5110831fe610aece076220cfdc163817fc295ff4f898a52464c17461f20eae", "signature": "a7601b4dcb10a9be5babb7a5ba56c48e1dc6d9a53f61e472ec3f12ae016c5191"}, {"version": "38bccc1f09383eb4a7d7aee2a6e2cfd8fc3287c177bf3a9e77b81a2464cfc578", "signature": "0c6250908c25dc76182c78dba74570857f2ee5e64de4e632c89dc1a98f364c49"}, {"version": "9891c9c20f63447b3c3f757c7dc4f058c91107232ec18056ad55d5911ac4b940", "signature": "6ffac02737acffadbae66f13aceaa74c525600d2f565af8b7d2f3a36cb82da4b"}, {"version": "ac4fb1a0af0eb93f5d3612b91bfe99cce3641fb258e218f95a2ad6cabb66fd3c", "signature": "b3f559d8a8cd73299d508a459e0fd5b4cfc9208c814ca53f2830fdb748db4a8a"}, {"version": "12158b45b18483114d9c4d6b31cae9c7fed6de3a5482348973bc82a1272b6b93", "signature": "7a2999a4f3fb2f512865ea7a19860ed20347ab22bac0d9aa0a4d5d996c0be97b"}, {"version": "ed8f18925a664eb9943a0f4c8d9e73a24890dd0d0d9f63e7f0250062edc4f9e8", "signature": "e697418da584fc270971cc340be7a5239094a90eb32c0da3503db05cc0cdd220"}, {"version": "425c8e9f4bf4c5e7d611bbc99464e723a4490a97dfa568dd9a885e33f9dddac9", "signature": "96f7d35cf46a4caedd1f7e9f326bfe3b3a7507d28cc63d09df1555992402bce0"}, {"version": "24ba46e305519c126156b89b597c67f3e5d50a1695bf07b04b99ba8ddbb5f12b", "signature": "bf8bb3155417be61d573eee2e0ba945e8884e8590ddb4e081e16ba7d949a0b27"}, {"version": "812b8c063db0007ab1020258d1fce6846d272a09fcd85bc7de100e2e5c85870f", "signature": "ecd12ffa345eec9cccefeb1b7113bd42b362440965adadfc648c523d6f8f3bd1"}, {"version": "653a4fe259b55c32cd5a67cb7fb467d99a0994c02a6e271715b1c0390762efb7", "signature": "68d07ee170f6a3b98e66a967cda122255171bb477c5819fe27c9759298e5c3fc"}, {"version": "a8b729499c700fc6ae6187eda9a4de77f8810f803ed1005410c6ac5177845c51", "signature": "59f44b5f3bb7e5d28a57aed81f63a3a76c2170ed1d702c4ee3a6920b44bb7e8d"}, {"version": "9f4177810acb07f9f6462279c4776b76caa773ea17864018f120dad6fe33283e", "signature": "602453052c3ffad4cdcf7997493f3ab3f48d6d25fbff712a1a0a2ca00bcff55f"}, {"version": "f0c7d88b968e0e03526dd80fb811242e35a7a5aae9f6589a8f1e503e3ac6bc6d", "signature": "deb3a39f2dd8621c0a5bd3af3146a1bc37cac6065e5b7e72f6103205c2b93f9a"}, {"version": "46532c6f7a4050488007f21ecd64d87adfdc783d544b54892a7176d125d5f2aa", "signature": "19e0c89ba6925d5b05bee8636d05e34091c76f5e6d773ad16c326618a1506599"}, {"version": "505af209221559373a6f39da1084b5b75261fd9936628a17619aec4035c99365", "signature": "303dd23cda708d3eeba3ced38044f1e7075bb50b4e1f8b2b3ab2f2d6c4aa1522"}, {"version": "3acdbe54b18a522546fc6e622d0232d1d2ea082db3b838001c5cbb4218ea8e14", "signature": "419416682191008fd4eee29bf9d8fb71b8f3982e299b97314db32a41969f264a"}, {"version": "f21422e95538bb44411cf01a1140e7e4a2f094694e37ead8967eed1347dc29a7", "signature": "708d31baa2361105516ec2b499dd2e41000368b50149379412c13d284ffb9872"}, {"version": "8475d4d3428b13ba4da7d1488312adef837553f3ee8b21bc4b1a326bee6cf378", "signature": "55ac7dc7905c3c426157c8448b95147135b7178589c7bc4cdde17956fb53ef8c"}, {"version": "b8d6b7bff4763563bbfc2a5c02afcd4a3534fd33c753704e7c85b9034ce2c917", "signature": "9de329bb9003063e86cba200d061ac28b3f47894b4316bd78c4d8ab2ad56d34f"}, {"version": "f8fd67f9f99793030825995be097c154a9dcf4d2bb23b5fb53ae4e17f13425ee", "signature": "b364b8b4d478e0d1b0046517960b1eff346d88c5f5ea09d8ee7f9786e94cbe64"}, {"version": "246fc1f8490f6076b86a4470fd67bf9ef3cb15662d9f3f4a2c59e827a8f7b901", "signature": "a3f70b6206119f911a56b70329567cf372d783fe6f07acc6f8d063ab66c84f19"}, {"version": "43bb434f5993ccc55ecee6d6032176c934d6ccb12256d9922dd1377ca3758cec", "signature": "981853a477a44999a1d661c10e8fc5e3e295cd6838a78c8db6ffb22bef881178"}, {"version": "1ec94f45222fe53300dae99c6ff1be8eb12d18ebf0b1dd11e7e0689efacc1021", "signature": "1ec2e6838d25ad35b740a0f3132f2359676ec5aacade71df03f335aedacc1fda"}, {"version": "c8580591bd0e37defff3d147284513eb5c13cc7c380e9bd9fb6724652e110c81", "signature": "1fb8aae5b5aab68c64de79ef9d849d6472f5235768698be1cad568eaadde0fb0"}, {"version": "a36c6934422539639851727d96c37ffdf998db32a4fdff11200f6d7cbf6508e7", "signature": "1454d8d6a1451a4185f8e6c92ea0198c23e7aba227349b3201c05e300a4d929b"}, {"version": "e9e88ea3c780fc0876e569b40c47752ccbd31193b28bfcf9a21550c67f4722f0", "signature": "29be795c0c260e9751f976e218cd1dae3b7e3234aaf88faeaeb9f55c742b9f62"}, {"version": "4d4bd08699eb53f61f65cf9c8e343180188ae48d727f6e6986f669f6701660ce", "signature": "5edfe012215dd16742fc615493b450a5aace028d6608327cadc60f66f93b311b"}, "7e3373dde2bba74076250204bd2af3aa44225717435e46396ef076b1954d2729", "1c3dfad66ff0ba98b41c98c6f41af096fc56e959150bc3f44b2141fb278082fd", "56208c500dcb5f42be7e18e8cb578f257a1a89b94b3280c506818fed06391805", "0c94c2e497e1b9bcfda66aea239d5d36cd980d12a6d9d59e66f4be1fa3da5d5a", "eb9271b3c585ea9dc7b19b906a921bf93f30f22330408ffec6df6a22057f3296", "82b7bf38f1bc606dc662c35b8c80905e40956e4c2212d523402ae925bd75de63", "81be14ad77be99cea7343fdc92a0f4058bcdebaa789d944e04ce4f86f0ca5fbb", "9f1e00eab512de990ba27afa8634ca07362192063315be1f8166bc3dcc7f0e0f", "9674788d4c5fcbd55c938e6719177ac932c304c94e0906551cc57a7942d2b53b", "86dac6ce3fcd0a069b67a1ac9abdbce28588ea547fd2b42d73c1a2b7841cf182", "4d34fbeadba0009ed3a1a5e77c99a1feedec65d88c4d9640910ff905e4e679f7", "9d90361f495ed7057462bcaa9ae8d8dbad441147c27716d53b3dfeaea5bb7fc8", "8fcc5571404796a8fe56e5c4d05049acdeac9c7a72205ac15b35cb463916d614", "a3b3a1712610260c7ab96e270aad82bd7b28a53e5776f25a9a538831057ff44c", "33a2af54111b3888415e1d81a7a803d37fada1ed2f419c427413742de3948ff5", "d5a4fca3b69f2f740e447efb9565eecdbbe4e13f170b74dd4a829c5c9a5b8ebf", "56f1e1a0c56efce87b94501a354729d0a0898508197cb50ab3e18322eb822199", "8960e8c1730aa7efb87fcf1c02886865229fdbf3a8120dd08bb2305d2241bd7e", "27bf82d1d38ea76a590cbe56873846103958cae2b6f4023dc59dd8282b66a38a", "0daaab2afb95d5e1b75f87f59ee26f85a5f8d3005a799ac48b38976b9b521e69", "2c378d9368abcd2eba8c29b294d40909845f68557bc0b38117e4f04fc56e5f9c", {"version": "bb220eaac1677e2ad82ac4e7fd3e609a0c7b6f2d6d9c673a35068c97f9fcd5cd", "affectsGlobalScope": true}, "c60b14c297cc569c648ddaea70bc1540903b7f4da416edd46687e88a543515a1", "d03cf6cd011da250c9a67c35a3378de326f6136c4192a90dd11f3a84627b4ef6", "9c0217750253e3bf9c7e3821e51cff04551c00e63258d5e190cf8bd3181d5d4a", "5c2e7f800b757863f3ddf1a98d7521b8da892a95c1b2eafb48d652a782891677", "73ed3ff18ca862b9d7272de3b0d137d284a0c40e1c94cbf37acd5270ce9b7cd6", "c61d8275c35a76cb12c271b5fa8707bb46b1e5778a370fd6037c244c4df6a725", "c7793cb5cd2bef461059ca340fbcd19d7ddac7ab3dcc6cd1c90432fca260a6ae", "fd3bf6d545e796ebd31acc33c3b20255a5bc61d963787fc8473035ea1c09d870", "c7af51101b509721c540c86bb5fc952094404d22e8a18ced30c38a79619916fa", "59c8f7d68f79c6e3015f8aee218282d47d3f15b85e5defc2d9d1961b6ffed7a0", "93a2049cbc80c66aa33582ec2648e1df2df59d2b353d6b4a97c9afcbb111ccab", "d04d359e40db3ae8a8c23d0f096ad3f9f73a9ef980f7cb252a1fdc1e7b3a2fb9", "84aa4f0c33c729557185805aae6e0df3bd084e311da67a10972bbcf400321ff0", "cf6cbe50e3f87b2f4fd1f39c0dc746b452d7ce41b48aadfdb724f44da5b6f6ed", "3cf494506a50b60bf506175dead23f43716a088c031d3aa00f7220b3fbcd56c9", "f2d47126f1544c40f2b16fc82a66f97a97beac2085053cf89b49730a0e34d231", "724ac138ba41e752ae562072920ddee03ba69fe4de5dafb812e0a35ef7fb2c7e", "e4eb3f8a4e2728c3f2c3cb8e6b60cadeb9a189605ee53184d02d265e2820865c", "f16cb1b503f1a64b371d80a0018949135fbe06fb4c5f78d4f637b17921a49ee8", "f4808c828723e236a4b35a1415f8f550ff5dec621f81deea79bf3a051a84ffd0", "3b810aa3410a680b1850ab478d479c2f03ed4318d1e5bf7972b49c4d82bacd8d", "0ce7166bff5669fcb826bc6b54b246b1cf559837ea9cc87c3414cc70858e6097", "90ae889ba2396d54fe9c517fcb0d5a8923d3023c3e6cbd44676748045853d433", "3549400d56ee2625bb5cc51074d3237702f1f9ffa984d61d9a2db2a116786c22", "5ffe02488a8ffd06804b75084ecc66b512f85186508e7c9b57b5335283b1f487", "b60f6734309d20efb9b0e0c7e6e68282ee451592b9c079dd1a988bb7a5eeb5e7", "f4187a4e2973251fd9655598aa7e6e8bba879939a73188ee3290bb090cc46b15", "7220461ab7f6d600b313ce621346c315c3a0ebc65b5c6f268488c5c55b68d319", "b14c272987c82d49f0f12184c9d8d07a7f71767be99cb76faa125b777c70e962", "fcf79300e5257a23ed3bacaa6861d7c645139c6f7ece134d15e6669447e5e6db", "187119ff4f9553676a884e296089e131e8cc01691c546273b1d0089c3533ce42", "aa2c18a1b5a086bbcaae10a4efba409cc95ba7287d8cf8f2591b53704fea3dea", "b88749bdb18fc1398370e33aa72bc4f88274118f4960e61ce26605f9b33c5ba2", "0aaef8cded245bf5036a7a40b65622dd6c4da71f7a35343112edbe112b348a1e", "00baffbe8a2f2e4875367479489b5d43b5fc1429ecb4a4cc98cfc3009095f52a", "a873c50d3e47c21aa09fbe1e2023d9a44efb07cc0cb8c72f418bf301b0771fd3", "7c14ccd2eaa82619fffc1bfa877eb68a012e9fb723d07ee98db451fadb618906", "49c36529ee09ea9ce19525af5bb84985ea8e782cb7ee8c493d9e36d027a3d019", "df996e25faa505f85aeb294d15ebe61b399cf1d1e49959cdfaf2cc0815c203f9", "4f6a12044ee6f458db11964153830abbc499e73d065c51c329ec97407f4b13dd", "44c1a26f578277f8ccef3215a4bd642a0a4fbbaf187cf9ae3053591c891fdc9c", "a5989cd5e1e4ca9b327d2f93f43e7c981f25ee12a81c2ebde85ec7eb30f34213", "f65b8fa1532dfe0ef2c261d63e72c46fe5f089b28edcd35b3526328d42b412b8", "1060083aacfc46e7b7b766557bff5dafb99de3128e7bab772240877e5bfe849d", "d61a3fa4243c8795139e7352694102315f7a6d815ad0aeb29074cfea1eb67e93", "1f66b80bad5fa29d9597276821375ddf482c84cfb12e8adb718dc893ffce79e0", "1ed8606c7b3612e15ff2b6541e5a926985cbb4d028813e969c1976b7f4133d73", "c086ab778e9ba4b8dbb2829f42ef78e2b28204fc1a483e42f54e45d7a96e5737", "dd0b9b00a39436c1d9f7358be8b1f32571b327c05b5ed0e88cc91f9d6b6bc3c9", "a951a7b2224a4e48963762f155f5ad44ca1145f23655dde623ae312d8faeb2f2", "cd960c347c006ace9a821d0a3cffb1d3fbc2518a4630fb3d77fe95f7fd0758b8", "fe1f3b21a6cc1a6bc37276453bd2ac85910a8bdc16842dc49b711588e89b1b77", "1a6a21ff41d509ab631dbe1ea14397c518b8551f040e78819f9718ef80f13975", "0a55c554e9e858e243f714ce25caebb089e5cc7468d5fd022c1e8fa3d8e8173d", "3a5e0fe9dcd4b1a9af657c487519a3c39b92a67b1b21073ff20e37f7d7852e32", "977aeb024f773799d20985c6817a4c0db8fed3f601982a52d4093e0c60aba85f", "d59cf5116848e162c7d3d954694f215b276ad10047c2854ed2ee6d14a481411f", "50098be78e7cbfc324dfc04983571c80539e55e11a0428f83a090c13c41824a2", "08e767d9d3a7e704a9ea5f057b0f020fd5880bc63fbb4aa6ffee73be36690014", "dd6051c7b02af0d521857069c49897adb8595d1f0e94487d53ebc157294ef864", "79c6a11f75a62151848da39f6098549af0dd13b22206244961048326f451b2a8", {"version": "64b99a241a46e573f21a7f753f35ad3bd8af0f31a1354568d01a4f4aedfb886f", "signature": "3b6c787b451f30597e435a3ca84398762f0c88e594eccca365e8e2e99cb7805a"}, {"version": "78927523e9e018033e6870d085b28f9dc33b53dc33834271ab9c0694c0ea858f", "signature": "0fea03bdfdad040c4778fe4b506c06262d835dd2605f6290070918ad3619bc90"}, {"version": "974a54efd113b2737e3d36ba8a04d4a676f335bcd84432793673f6d37a0798c8", "signature": "69ea7afc755c65ee4d1b162ecd5802d23734d16e60ed9b927e9df65785793326"}, {"version": "7b4e2cf75678a943c99319eb4f6c5fc4a2f489178cc7924c959a58fc586e3166", "signature": "f6fadd9797e8f82fcf93858942edb7efe8c3b81ecad4a44cb65885b2ddcac386"}, {"version": "620a5351326d8e94abeed9785195ae0f4c057db4fa2c1730b4305b3050a5f596", "signature": "582299407aed7241327f72cff215a91a914a8a8f2965decd6f5a9e315c42ff99"}, {"version": "1ea76f9ca11a86a1a2d4adfa7753139d70f5bb3be390b3297751bcf22aa21fb7", "signature": "6fe9fd42d86fd49c85e717dafaea3e8ac2a5aee992a3a422cd27c606bb743d40"}, {"version": "3c12545c3495552d7c8d3b65d28b7eb952d787f81402945a83a4c6c7eb8bfc1e", "signature": "9a2373d0a5162a5abbaff359f02a8399ff45fcbae63aacf0a386b942cd363472"}, {"version": "4eb49f78e0f38b229f141fd50fe6d7b66037bc57292eb5904bfbda363f8264ba", "signature": "93eeef004631133d6ccd289df70099fe33f4eaf4c5923e0cdbe1aad789fc8486"}, {"version": "901f8cbe7714844625c3a8dd5ea80f369a7f6217056e271514a7dc9ac4abd6c3", "signature": "44ed844582052d9d07f6c0d36c46b9a9bb4e1fbaa6b9df91d28ff0fd3f6d3b8f"}, {"version": "1e4f1d67be003a7fcd7ff5233b8db813903b2a3bd7c0d64eb1743fe4eca2cd33", "signature": "c61c1f4c5b971501c028e1398a7749e0bf6a1cc4eda9459f033a69b06517176e"}, {"version": "04eb0d46c106586193550266be2153fb8d5a988fdd439c9a3cfd8a8a19a2e5b8", "signature": "975330316fb64cc62d7a7e98732337ab5ea57c06c45f9809b6ed23433507bd85"}, {"version": "7fd6a6bc5906feabfdce83b81269e3d9e6bd44a78e852af19f2c0b3ccbd44e69", "signature": "7861c472817e23e8e8569ef8a49d2891d39c7b1c277f620fba58ca36332de135"}, {"version": "5b04f43f3c465681fde1f787dec39f4ff56dea30519942e345160842ebfb4c8a", "signature": "66ad5d5919ff36b51d9650b2a3c39a16fc63697f3f8fb595fe22dfda2a998f9d"}, {"version": "d01d28b18673372f59b1d54b5eb04d7dc8882d853268b457b3751c340346c0bf", "signature": "90e5fcd8a17d123144e6cf65a93bc2f0c12306572512c4a5e62e8b21af94b2f2"}, {"version": "8e64938da757c1cda6a517989bf8b9e237f66cbe78984750bf4628ca8627e708", "signature": "b03122126494020f4e0194825a105ccdab306cf050eefe0e38d13e89c208defe"}, "19ce9ec982b542ef6d04d29ce678aad2fa52a67d8087e9c6cd95a4d6d98784c8", "4af47b2f19621ce8f638167a32f141a3a2c0e71ce8ebf51384393ca1c2654e60", "462bccdf75fcafc1ae8c30400c9425e1a4681db5d605d1a0edb4f990a54d8094", "5923d8facbac6ecf7c84739a5c701a57af94a6f6648d6229a6c768cf28f0f8cb", "0beeeb5964025d8564c03cb0bf1a4b60dc40c01f065ad1a4e9030415f5bc7bc2", "370d29706526cf66ee767a4a3ee4218c9544a252ce22f231648414348704cb4c", "6bf53608a27a76ef8580f9577618174f0dd5325142cafb8b3a19aa4850319afb", "821fe27bd167988c3cc518af8d9591ac1bd8d9e9d231ee9eac7b82786dd9f3a6", {"version": "eef204f061321360559bd19235ea32a9d55b3ec22a362cc78d14ef50d4db4490", "affectsGlobalScope": true}, "2ea3c07887a34c982f1d902139569a86bfa4fbf5ab79c3397aec80b2ceb20b05", "73b67d2e87ac7d7baaca64ca33fd1523c0b3c850cb7db5b9c014f1be7996bed1", "5d5ae61fce1581fd6424269790a9071e3f8e69b029f5d0fcb46ce618c5dbc565", "38a0ccc7106312a9f60e034e7cd8ac218774d8aa65f832cee3363a7e65f99325", "4911d4c3a7f7c11bad0e2cec329a19a385d10ea83b0b69c76e032359e388f624", "a69c09dbea52352f479d3e7ac949fde3d17b195abe90b045d619f747b38d6d1a", {"version": "4f6463a60e5754bbc4a864b2aaf8fecb7706b96a21b88f27b534589b801978b6", "affectsGlobalScope": true}, "56d13f223ab40f71840795f5bef2552a397a70666ee60878222407f3893fb8d0", {"version": "4ffef5c4698e94e49dcf150e3270bad2b24a2aeab48b24acbe7c1366edff377d", "affectsGlobalScope": true}, "2534e46a52653b55dfb5a41ce427ec430c4afbaaf3bfcb1ae09b185c5d6bf169", "afc6e96061af46bcff47246158caee7e056f5288783f2d83d6858cd25be1c565", {"version": "34f5bcac12b36d70304b73de5f5aab3bb91bd9919f984be80579ebcad03a624e", "affectsGlobalScope": true}, "82408ed3e959ddc60d3e9904481b5a8dc16469928257af22a3f7d1a3bc7fd8c4", "3f2478baf49cf27aa1335ba5299e2394131284e9d50a3845e3f95e52664ff518", "f50c975ab7b50e25a69e3d8a3773894125b44e9698924105f23b812bf7488baf", "8bd106053ee0345dde7f626ed1f6100a89fb85f13ea65352627cf78c5f30c553", "76650408392bf49a8fbf3e2b6b302712a92d76af77b06e2da1cc8077359c4409", "0af3121e68297b2247dd331c0d24dba599e50736a7517a5622d5591aae4a3122", "06ccebc2c2db57d6bdbca63b71c4ae5e6ddc42d972fd8f122d4c1a28aa111b25", {"version": "81e8508d1e82278f5d3fee936f267e00c308af36219bfcee2631f9513c9c4017", "affectsGlobalScope": true}, "413a4be7f94f631235bbc83dad36c4d15e5a2ff02bca1efdbd03636d6454631b", "20c468256fd68d3ef1fa53526e76d51d6aa91711e84d72c0343589b99238287e", "4198acced75d48a039c078734c4efca7788ff8c78609c270a2b63ec20e3e1676", "8d4c16a26d59e3ce49741a7d4a6e8206b884e226cf308667c7778a0b2c0fee7f", "288dd0c774a5c6e3964084c7a2bc8cc6b746d70f44a9892d028d04f915cf7ebc", "d61c7c41eb1960b1285e242fd102c162b65c0522985b839fadda59874308a170", {"version": "e8b18c6385ff784228a6f369694fcf1a6b475355ba89090a88de13587a9391d5", "affectsGlobalScope": true}, "1805e0e4d1ed00f6361db25dff6887c7fa9b5b39f32599a34e8551da7daaa9c2", "abc1c425b2ad6720433f40f1877abfa4223f0f3dd486c9c28c492179ca183cb6", "fb0989383c6109f20281b3d31265293daefdd76d0d30551782c1654e93704f48", "a4210a84a82b3e7a8cec5b2f3616e46d523f4f10cc1576d8f2fb89d0987b341e", {"version": "8207e7e6db9aa5fc7e61c8f17ba74cf9c115d26f51f91ee93f790815a7ea9dfb", "affectsGlobalScope": true}, "9f1069b9e2c051737b1f9b4f1baf50e4a63385a6a89c32235549ae87fc3d5492", "22d48bfb37261136423ac687f1fa7bd4dda3083f767416d409a8260cf92bc8fc", "29c2706fa0cc49a2bd90c83234da33d08bb9554ecec675e91c1f85087f5a5324", "0acbf26bf958f9e80c1ffa587b74749d2697b75b484062d36e103c137c562bc3", "95518ff86843e226b62a800f679f6968ad8dac8ccbe30fbfe63de3afb13761a2", "1b952304137851e45bc009785de89ada562d9376177c97e37702e39e60c2f1ff", "698ab660b477b9c2cd5ccbd99e7e7df8b4a6134c1f5711fa615ed7aab51cb7f7", "33eee034727baf564056b4ea719075c23d3b4767d0b5f9c6933b81f3d77774d2", "c33a6ea7147af60d8e98f1ac127047f4b0d4e2ce28b8f08ff3de07ca7cc00637", "a4471d2bdba495b2a6a30b8765d5e0282fa7009d88345a9528f73c37869d3b93", {"version": "aee7013623e7632fba449d4df1da92925b27d9b816cb05546044dbfe54c88ef4", "affectsGlobalScope": true}, "e10177274a35a9d07c825615340b2fcde2f610f53f3fb40269fd196b4288dda6", "c9d70d3d7191a66a81cb554557f8ed1cf736ea8397c44a864fe52689de18865a", "998a3de5237518c0b3ac00a11b3b4417affb008aa20aedee52f3fdae3cb86151", "ad41008ffe077206e1811fc873f4d9005b5fd7f6ab52bb6118fef600815a5cb4", {"version": "1aad825534c73852a1f3275e527d729a2c0640f539198fdfdfeb83b839851910", "affectsGlobalScope": true}, "badae0df9a8016ac36994b0a0e7b82ba6aaa3528e175a8c3cb161e4683eec03e", "c3db860bcaaaeb3bbc23f353bbda1f8ab82756c8d5e973bebb3953cb09ea68f2", "235a53595bd20b0b0eeb1a29cb2887c67c48375e92f03749b2488fbd46d0b1a0", "bc09393cd4cd13f69cf1366d4236fbae5359bb550f0de4e15767e9a91d63dfb1", "9c266243b01545e11d2733a55ad02b4c00ecdbda99c561cd1674f96e89cdc958", "c71155c05fc76ff948a4759abc1cb9feec036509f500174bc18dad4c7827a60c", {"version": "ab9b9a36e5284fd8d3bf2f7d5fcbc60052f25f27e4d20954782099282c60d23e", "affectsGlobalScope": true}, "88003d9ab15507806f41b120be6d407c1afe566c2f6689ebe3a034dd5ec0c8dc", "850040826cfa77593d44f44487133af21917f4f21507258bd4269501b80d32f0", "8f07f2b6514744ac96e51d7cb8518c0f4de319471237ea10cf688b8d0e9d0225", "bcb6ea18f23dae2c48459d7b86d3adccd6898f824fcbf9da08b935f559896580", "1363ba7d52f2353d0c4306d0ecdaf171bf4509c0148842f9fd8d3986c098a2eb", "3a24f4a428f24cad90b83fab329a620c4adbace083a8eda62c63365065b79e73", "739c2c46edc112421fc023c24b4898b1f413f792bb6a02b40ba182c648e56c2f", "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "33f3718dababfc26dfd9832c150149ea4e934f255130f8c118a59ae69e5ed441", "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "1bb61aa2f08ab4506d41dbe16c5f3f5010f014bbf46fa3d715c0cbe3b00f4e1c", "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "e42820cd611b15910c204cd133f692dcd602532b39317d4f2a19389b27e6f03d", "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "f234315aeb08f02d74769341155afa47ef6ec6873607f55c6a9104d50fc27383", "1c53e1884dc6550ce179c68e9e3086f54af258fff39eb70274ea9294eb7ce6df", "2d57bdaafc7cd0ebc006f0e77c869d6fe6148809c08e8b5677aef4150cf4a7c7", "05c7aef6a4e496b93c2e682cced8903c0dfe6340d04f3fe616176e2782193435", "67856637713bace00feca7f0d4a9907e6a85bcceeb507e07df852cb5f6467834", "e666e31d323fef5642f87db0da48a83e58f0aaf9e3823e87eabd8ec7e0441a36", "69bf2422313487956e4dacf049f30cb91b34968912058d244cb19e4baa24da97", "d12ab69ace581804d4f264eabc71094ca8dadfa70ae2bf5ccd54a8d6105ab84b", "973af20e33ebed2f6c3af36062c214b03daf2a0a3193554f6538ea928228b671", "ca179564af22b92d588ce07d527042767d37bacce79fb78cd6fc7d8ff8c1f329", "e72396ce544667ab49df38ffb91cb3f80ff17d2ad3df903ec30b1d2ca8ea68de", "b7e28e06011460436d5c2ec2996846ac0c451e135357fc5a7269e5665a32fbd7", "d422e50d00562af6bb419fca3a81c8437391acc13f52672dcffdfc3da2a93125", "2887592574fcdfd087647c539dcb0fbe5af2521270dad4a37f9d17c16190d579", "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "632d6fe34a09b481d3007779ad03e4202e4ed4f73bee02fdfb6b23e51ca4aebd", {"version": "7c4d59b36618af873cc489404906c46e2a2f0629a8416ee08b711f5f02096f3f", "affectsGlobalScope": true}, "3fa571018b674c0cdc74584b04f32c421829c409236e1332af4a87ad904b504d", "2d37a18dbc84466a72a4b00d0293ecfe3170fc664ca32126db0b7eace05824d5", "f63e23230f3960b712450cf08f0f31e56acdae3ce65e0bf31bfdd6442b29d115", "f2d1a59a658165341b0e2b7879aa2e19ea6a709146b2d3f70ee8a07159d3d08e", "5309c17206a98ed2bdd130eb25a213e864697f5b017f774141c12030e82db573", "8a3ff3da0cc09f4c5523f6e336635cd0e2cd5cc7e5297186b44b6a6b06e3ef96", {"version": "829325a03054bf6c70506fa5cfcd997944faf73c54c9285d1a2d043d239f4565", "affectsGlobalScope": true}], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "jsx": 4, "module": 99, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "strictNullChecks": true, "target": 7}, "fileIdsList": [[365], [62, 63, 64, 365], [62, 63, 365], [62, 365], [212, 365], [230, 365], [60, 365], [259, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 365], [259, 260, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 365], [260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 365], [259, 260, 261, 263, 264, 265, 266, 267, 268, 269, 270, 271, 365], [259, 260, 261, 262, 264, 265, 266, 267, 268, 269, 270, 271, 365], [259, 260, 261, 262, 263, 265, 266, 267, 268, 269, 270, 271, 365], [259, 260, 261, 262, 263, 264, 266, 267, 268, 269, 270, 271, 365], [259, 260, 261, 262, 263, 264, 265, 267, 268, 269, 270, 271, 365], [259, 260, 261, 262, 263, 264, 265, 266, 268, 269, 270, 271, 365], [259, 260, 261, 262, 263, 264, 265, 266, 267, 269, 270, 271, 365], [259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 270, 271, 365], [259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 271, 365], [259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 365], [321, 365], [324, 365], [325, 330, 356, 365], [326, 336, 337, 344, 353, 364, 365], [326, 327, 336, 344, 365], [328, 365], [329, 330, 337, 345, 365], [330, 353, 361, 365], [331, 333, 336, 344, 365], [332, 365], [333, 334, 365], [335, 336, 365], [336, 365], [336, 337, 338, 353, 364, 365], [336, 337, 338, 353, 365], [339, 344, 353, 364, 365], [336, 337, 339, 340, 344, 353, 361, 364, 365], [339, 341, 353, 361, 364, 365], [321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371], [336, 342, 365], [343, 364, 365], [333, 336, 344, 353, 365], [345, 365], [346, 365], [324, 347, 365], [348, 363, 365, 369], [349, 365], [350, 365], [336, 351, 365], [351, 352, 365, 367], [325, 336, 353, 354, 355, 365], [325, 353, 355, 365], [353, 354, 365], [356, 365], [357, 365], [336, 359, 360, 365], [359, 360, 365], [330, 344, 361, 365], [362, 365], [344, 363, 365], [325, 339, 350, 364, 365], [330, 365], [353, 365, 366], [365, 367], [365, 368], [325, 330, 336, 338, 347, 353, 364, 365, 367, 369], [353, 365, 370], [57, 58, 59, 365], [58, 60, 82, 365], [312, 316, 365], [312, 313, 314, 365], [313, 365], [312, 365], [312, 313, 365, 402], [365, 399], [365, 403], [318, 365], [311, 318, 365], [311, 318, 319, 365], [365, 418], [365, 409], [365, 416], [365, 401], [308, 365], [308, 309, 311, 365], [365, 394], [365, 392, 394], [365, 383, 391, 392, 393, 395], [365, 381], [365, 384, 389, 394, 397], [365, 380, 397], [365, 384, 385, 388, 389, 390, 397], [365, 384, 385, 386, 388, 389, 397], [365, 381, 382, 383, 384, 385, 389, 390, 391, 393, 394, 395, 397], [365, 379, 381, 382, 383, 384, 385, 386, 388, 389, 390, 391, 392, 393, 394, 395, 396], [365, 379, 397], [365, 384, 386, 387, 389, 390, 397], [365, 388, 397], [365, 389, 390, 394, 397], [365, 382, 392], [310, 365], [65, 69, 365], [60, 65, 69, 70, 365], [65, 66, 67, 68, 365], [60, 65, 66, 365], [60, 65, 365], [213, 365], [231, 365], [365, 406, 407], [365, 398, 406, 407, 415], [365, 406], [336, 337, 339, 341, 344, 353, 361, 364, 365, 370, 372, 373, 374, 375, 376, 377, 378, 397], [312, 315, 316, 317, 320, 337, 365, 369, 398, 400, 404, 405, 408, 410, 411, 412, 414, 415], [312, 315, 316, 317, 320, 337, 365, 369, 398, 400, 404, 405, 408, 410, 411, 412, 414, 415, 417, 419, 420], [365, 421], [365, 375], [365, 377], [170, 365], [170, 171, 172, 173, 174, 175, 365], [60, 215, 216, 217, 233, 236, 365], [60, 215, 216, 217, 226, 234, 254, 365], [60, 214, 217, 365], [60, 217, 365], [60, 215, 216, 217, 365], [60, 215, 216, 217, 252, 255, 258, 365], [60, 215, 216, 217, 226, 233, 236, 365], [60, 215, 216, 217, 226, 234, 246, 365], [60, 215, 216, 217, 226, 236, 246, 365], [60, 215, 216, 217, 226, 246, 365], [60, 215, 216, 217, 221, 227, 233, 238, 256, 257, 365], [217, 365], [60, 217, 271, 274, 275, 276, 365], [60, 217, 271, 273, 274, 275, 365], [60, 217, 234, 365], [60, 217, 273, 365], [60, 217, 226, 365], [60, 217, 218, 219, 365], [60, 217, 219, 221, 365], [210, 211, 215, 216, 217, 218, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 247, 248, 249, 250, 251, 252, 253, 255, 256, 257, 258, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 365], [60, 217, 288, 365], [60, 217, 229, 365], [60, 217, 236, 240, 241, 365], [60, 217, 227, 229, 365], [60, 217, 232, 365], [60, 217, 255, 365], [60, 217, 232, 272, 365], [60, 220, 273, 365], [60, 214, 215, 216, 365], [60, 61, 71, 81, 162, 163, 365], [60, 61, 81, 365], [60, 61, 83, 365], [60, 61, 71, 83, 365], [61, 365], [61, 93, 365], [60, 61, 81, 83, 94, 95, 96, 183, 185, 365], [61, 94, 95, 96, 186, 365], [60, 61, 81, 83, 93, 365], [60, 61, 83, 98, 365], [61, 100, 101, 102, 365], [60, 61, 83, 93, 365], [60, 61, 93, 97, 365], [60, 61, 81, 83, 94, 95, 96, 98, 99, 103, 365], [61, 184, 365], [60, 61, 93, 183, 365], [61, 93, 98, 99, 103, 104, 183, 185, 187, 365], [61, 81, 93, 365], [61, 181, 182, 365], [60, 61, 83, 88, 89, 90, 91, 365], [60, 61, 365], [61, 92, 365], [61, 158, 159, 190, 191, 192, 365], [60, 61, 83, 158, 365], [61, 110, 365], [60, 61, 83, 110, 112, 365], [60, 61, 83, 110, 365], [60, 61, 81, 83, 110, 112, 365], [60, 61, 81, 194, 195, 196, 197, 198, 199, 365], [60, 61, 81, 110, 365], [60, 61, 81, 194, 365], [60, 61, 81, 110, 111, 365], [60, 61, 81, 110, 194, 365], [61, 110, 112, 113, 114, 115, 116, 117, 118, 119, 194, 195, 196, 197, 198, 199, 200, 365], [61, 81, 365], [60, 61, 81, 83, 112, 113, 114, 115, 116, 117, 118, 365], [60, 61, 83, 130, 131, 132, 133, 134, 135, 365], [60, 61, 83, 129, 365], [61, 137, 138, 142, 143, 152, 153, 154, 155, 365], [60, 61, 83, 105, 121, 365], [60, 61, 83, 105, 365], [60, 61, 83, 105, 121, 139, 141, 151, 365], [60, 61, 83, 105, 121, 139, 140, 141, 365], [60, 61, 83, 105, 123, 365], [60, 61, 83, 105, 123, 127, 365], [60, 61, 83, 144, 365], [60, 61, 83, 145, 146, 147, 148, 149, 150, 365], [60, 61, 71, 83, 105, 365], [61, 105, 365], [61, 120, 121, 122, 124, 125, 365], [60, 61, 105, 365], [60, 61, 71, 120, 121, 122, 124, 365], [60, 61, 105, 107, 365], [60, 61, 71, 105, 107, 123, 365], [61, 109, 157, 365], [60, 61, 71, 83, 125, 126, 128, 136, 156, 365], [60, 61, 71, 83, 105, 106, 108, 365], [60, 61, 83, 207, 365], [60, 61, 83, 207, 292, 365], [60, 61, 207, 365], [61, 207, 208, 209, 293, 294, 295, 296, 365], [60, 61, 83, 208, 209, 293, 294, 295, 365], [60, 61, 164, 168, 169, 177, 365], [61, 84, 85, 86, 365], [60, 61, 71, 83, 84, 85, 365], [60, 61, 297, 365], [60, 61, 71, 180, 201, 365], [60, 61, 71, 157, 365], [61, 176, 365], [61, 161, 365], [60, 61, 71, 86, 87, 92, 104, 109, 119, 157, 159, 160, 365], [60, 61, 166, 168, 169, 365], [73, 74, 365], [72, 365], [76, 365], [75, 77, 78, 80, 365], [79, 365], [73, 365], [325, 423], [212, 325, 423], [230, 325, 423], [60, 325, 423], [259, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 325, 423], [259, 260, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 325, 423], [260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 325, 423], [259, 260, 261, 263, 264, 265, 266, 267, 268, 269, 270, 271, 325, 423], [259, 260, 261, 262, 264, 265, 266, 267, 268, 269, 270, 271, 325, 423], [259, 260, 261, 262, 263, 265, 266, 267, 268, 269, 270, 271, 325, 423], [259, 260, 261, 262, 263, 264, 266, 267, 268, 269, 270, 271, 325, 423], [259, 260, 261, 262, 263, 264, 265, 267, 268, 269, 270, 271, 325, 423], [259, 260, 261, 262, 263, 264, 265, 266, 268, 269, 270, 271, 325, 423], [259, 260, 261, 262, 263, 264, 265, 266, 267, 269, 270, 271, 325, 423], [259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 270, 271, 325, 423], [259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 271, 325, 423], [259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 325, 423], [57, 58, 59, 325, 423], [58, 60, 82, 325, 423], [312, 316, 325, 423], [312, 313, 314, 325, 423], [313, 325, 423], [312, 325, 423], [312, 313, 325, 402, 423], [325, 399, 423], [325, 403, 423], [318, 325, 423], [311, 318, 325, 423], [311, 318, 319, 325, 423], [325, 418, 423], [325, 409, 423], [325, 416, 423], [325, 401, 423], [308, 325, 423], [308, 309, 311, 325, 365, 423], [325, 394, 423], [325, 392, 394, 423], [325, 383, 391, 392, 393, 395, 423], [325, 381, 423], [325, 384, 389, 394, 397, 423], [325, 380, 397, 423], [325, 384, 385, 388, 389, 390, 397, 423], [325, 384, 385, 386, 388, 389, 397, 423], [325, 381, 382, 383, 384, 385, 389, 390, 391, 393, 394, 395, 397, 423], [325, 379, 381, 382, 383, 384, 385, 386, 388, 389, 390, 391, 392, 393, 394, 395, 396, 423], [325, 379, 397, 423], [325, 384, 386, 387, 389, 390, 397, 423], [325, 388, 397, 423], [325, 389, 390, 394, 397, 423], [325, 382, 392, 423], [310, 325, 423], [213, 325, 423], [231, 325, 423], [325, 406, 407, 423], [325, 406, 407, 415, 423, 424], [325, 406, 423], [312, 315, 316, 317, 320, 325, 337, 369, 400, 404, 405, 408, 410, 411, 412, 414, 415, 423, 424], [312, 315, 316, 317, 320, 325, 337, 369, 400, 404, 405, 408, 410, 411, 412, 414, 415, 417, 419, 420, 423, 424], [325, 421, 423], [170, 325, 423], [170, 171, 172, 173, 174, 175, 325, 423], [60], [93], [94, 95, 96, 186], [60, 93], [100, 101, 102], [184], [93, 98, 99, 103, 104, 183, 185, 187], [181, 182], [92], [158, 159, 190, 191, 192], [110], [60, 110], [60, 194], [110, 112, 113, 114, 115, 116, 117, 118, 119, 194, 195, 196, 197, 198, 199, 200], [137, 138, 142, 143, 152, 153, 154, 155], [60, 105, 121], [60, 105], [60, 105, 123], [105], [120, 121, 122, 124, 125], [109, 157], [60, 207], [207], [207, 208, 209, 293, 294, 295, 296], [168], [84, 85, 86], [176], [161]], "referencedMap": [[62, 1], [65, 2], [64, 3], [63, 4], [316, 1], [230, 1], [213, 5], [231, 6], [212, 1], [82, 7], [260, 8], [261, 9], [259, 10], [262, 11], [263, 12], [264, 13], [265, 14], [266, 15], [267, 16], [268, 17], [269, 18], [270, 19], [271, 20], [321, 21], [322, 21], [324, 22], [325, 23], [326, 24], [327, 25], [328, 26], [329, 27], [330, 28], [331, 29], [332, 30], [333, 31], [334, 31], [335, 32], [336, 33], [337, 34], [338, 35], [323, 1], [371, 1], [339, 36], [340, 37], [341, 38], [372, 39], [342, 40], [343, 41], [344, 42], [345, 43], [346, 44], [347, 45], [348, 46], [349, 47], [350, 48], [351, 49], [352, 50], [353, 51], [355, 52], [354, 53], [356, 54], [357, 55], [358, 1], [359, 56], [360, 57], [361, 58], [362, 59], [363, 60], [364, 61], [365, 62], [366, 63], [367, 64], [368, 65], [369, 66], [370, 67], [59, 1], [169, 7], [57, 1], [60, 68], [61, 7], [83, 69], [317, 70], [315, 71], [314, 72], [313, 73], [399, 71], [403, 74], [400, 75], [404, 76], [318, 1], [418, 77], [319, 78], [320, 79], [409, 79], [419, 80], [410, 81], [417, 82], [402, 83], [401, 1], [309, 84], [312, 85], [308, 1], [58, 1], [373, 1], [395, 86], [393, 87], [394, 88], [382, 89], [383, 87], [390, 90], [381, 91], [386, 92], [396, 1], [387, 93], [392, 94], [397, 95], [380, 96], [388, 97], [389, 98], [384, 99], [391, 86], [385, 100], [311, 101], [310, 1], [70, 102], [71, 103], [69, 104], [67, 105], [66, 106], [68, 105], [379, 1], [412, 1], [405, 1], [416, 1], [11, 1], [12, 1], [14, 1], [13, 1], [2, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [20, 1], [21, 1], [22, 1], [3, 1], [4, 1], [26, 1], [23, 1], [24, 1], [25, 1], [27, 1], [28, 1], [29, 1], [5, 1], [30, 1], [31, 1], [32, 1], [33, 1], [6, 1], [37, 1], [34, 1], [35, 1], [36, 1], [38, 1], [7, 1], [39, 1], [44, 1], [45, 1], [40, 1], [41, 1], [42, 1], [43, 1], [8, 1], [49, 1], [46, 1], [47, 1], [48, 1], [50, 1], [9, 1], [51, 1], [52, 1], [53, 1], [54, 1], [55, 1], [1, 1], [10, 1], [56, 1], [214, 107], [232, 108], [408, 109], [411, 109], [414, 110], [407, 111], [406, 1], [413, 112], [420, 113], [421, 114], [415, 113], [422, 115], [377, 1], [398, 112], [376, 116], [375, 1], [378, 1], [374, 117], [171, 118], [172, 118], [173, 118], [174, 118], [175, 118], [176, 119], [170, 1], [253, 120], [255, 121], [245, 122], [250, 123], [251, 124], [257, 125], [252, 126], [249, 127], [248, 128], [247, 129], [258, 130], [215, 123], [216, 123], [256, 123], [274, 131], [284, 132], [278, 132], [286, 132], [290, 132], [276, 133], [277, 132], [279, 132], [282, 132], [285, 132], [281, 134], [283, 132], [287, 7], [280, 123], [275, 135], [224, 7], [228, 7], [218, 123], [221, 7], [226, 123], [227, 136], [220, 137], [223, 7], [225, 7], [222, 138], [211, 7], [210, 7], [292, 139], [289, 140], [242, 141], [241, 123], [239, 7], [240, 123], [243, 142], [244, 143], [237, 7], [233, 144], [236, 123], [235, 123], [234, 123], [229, 123], [238, 144], [288, 123], [254, 145], [273, 146], [272, 147], [291, 1], [246, 1], [219, 1], [217, 148], [164, 149], [163, 150], [180, 150], [87, 151], [160, 152], [168, 153], [97, 154], [186, 155], [187, 156], [96, 157], [99, 158], [94, 157], [95, 157], [103, 159], [102, 160], [100, 151], [101, 160], [98, 161], [104, 162], [185, 163], [184, 164], [188, 165], [182, 166], [181, 166], [183, 167], [93, 153], [88, 151], [89, 151], [90, 151], [92, 168], [91, 169], [189, 170], [191, 151], [190, 151], [192, 151], [158, 150], [193, 171], [159, 172], [111, 173], [116, 174], [202, 175], [203, 175], [113, 176], [204, 175], [114, 174], [117, 174], [200, 177], [199, 178], [196, 179], [198, 178], [197, 179], [118, 176], [115, 176], [112, 180], [195, 181], [201, 182], [194, 183], [119, 184], [110, 153], [205, 153], [136, 185], [133, 186], [134, 186], [135, 186], [132, 186], [131, 186], [130, 186], [156, 187], [153, 152], [138, 188], [137, 189], [155, 151], [154, 151], [143, 188], [152, 190], [142, 191], [127, 192], [128, 193], [141, 151], [149, 194], [148, 194], [147, 194], [150, 194], [145, 194], [146, 194], [151, 195], [126, 151], [140, 151], [106, 196], [129, 197], [123, 197], [144, 197], [139, 198], [122, 199], [125, 200], [120, 201], [108, 201], [124, 202], [121, 199], [206, 203], [157, 204], [109, 205], [105, 153], [209, 206], [293, 207], [294, 206], [295, 206], [208, 208], [297, 209], [296, 210], [207, 153], [178, 211], [85, 151], [298, 212], [86, 213], [84, 152], [165, 169], [299, 151], [300, 214], [301, 152], [302, 151], [303, 215], [304, 216], [305, 151], [177, 217], [306, 151], [162, 218], [307, 152], [161, 219], [107, 197], [179, 220], [166, 169], [167, 169], [74, 1], [75, 221], [73, 222], [72, 1], [77, 223], [76, 1], [81, 224], [78, 1], [80, 225], [79, 226]], "exportedModulesMap": [[62, 1], [65, 2], [64, 3], [63, 4], [316, 227], [230, 227], [213, 228], [231, 229], [212, 227], [82, 230], [260, 231], [261, 232], [259, 233], [262, 234], [263, 235], [264, 236], [265, 237], [266, 238], [267, 239], [268, 240], [269, 241], [270, 242], [271, 243], [321, 21], [322, 21], [324, 22], [325, 23], [326, 24], [327, 25], [328, 26], [329, 27], [330, 28], [331, 29], [332, 30], [333, 31], [334, 31], [335, 32], [336, 33], [337, 34], [338, 35], [323, 1], [371, 1], [339, 36], [340, 37], [341, 38], [372, 39], [342, 40], [343, 41], [344, 42], [345, 43], [346, 44], [347, 45], [348, 46], [349, 47], [350, 48], [351, 49], [352, 50], [353, 51], [355, 52], [354, 53], [356, 54], [357, 55], [358, 1], [359, 56], [360, 57], [361, 58], [362, 59], [363, 60], [364, 61], [365, 62], [366, 63], [367, 64], [368, 65], [369, 66], [370, 67], [59, 227], [169, 230], [57, 227], [60, 244], [61, 230], [83, 245], [317, 246], [315, 247], [314, 248], [313, 249], [399, 247], [403, 250], [400, 251], [404, 252], [318, 227], [418, 253], [319, 254], [320, 255], [409, 255], [419, 256], [410, 257], [417, 258], [402, 259], [401, 227], [309, 260], [312, 261], [308, 227], [58, 227], [373, 227], [395, 262], [393, 263], [394, 264], [382, 265], [383, 263], [390, 266], [381, 267], [386, 268], [396, 227], [387, 269], [392, 270], [397, 271], [380, 272], [388, 273], [389, 274], [384, 275], [391, 262], [385, 276], [311, 277], [310, 227], [70, 102], [71, 103], [69, 104], [67, 105], [66, 106], [68, 105], [379, 227], [412, 227], [405, 227], [416, 227], [11, 227], [12, 227], [14, 227], [13, 227], [2, 227], [15, 227], [16, 227], [17, 227], [18, 227], [19, 227], [20, 227], [21, 227], [22, 227], [3, 227], [4, 227], [26, 227], [23, 227], [24, 227], [25, 227], [27, 227], [28, 227], [29, 227], [5, 227], [30, 227], [31, 227], [32, 227], [33, 227], [6, 227], [37, 227], [34, 227], [35, 227], [36, 227], [38, 227], [7, 227], [39, 227], [44, 227], [45, 227], [40, 227], [41, 227], [42, 227], [43, 227], [8, 227], [49, 227], [46, 227], [47, 227], [48, 227], [50, 227], [9, 227], [51, 227], [52, 227], [53, 227], [54, 227], [55, 227], [1, 227], [10, 227], [56, 227], [214, 278], [232, 279], [408, 280], [411, 280], [414, 281], [407, 282], [406, 227], [413, 112], [420, 283], [421, 284], [415, 283], [422, 285], [377, 1], [398, 112], [376, 116], [375, 1], [378, 1], [374, 117], [171, 286], [172, 286], [173, 286], [174, 286], [175, 286], [176, 287], [170, 227], [253, 120], [255, 121], [245, 122], [250, 123], [251, 124], [257, 125], [252, 126], [249, 127], [248, 128], [247, 129], [258, 130], [215, 123], [216, 123], [256, 123], [274, 131], [284, 132], [278, 132], [286, 132], [290, 132], [276, 133], [277, 132], [279, 132], [282, 132], [285, 132], [281, 134], [283, 132], [287, 7], [280, 123], [275, 135], [224, 7], [228, 7], [218, 123], [221, 7], [226, 123], [227, 136], [220, 137], [223, 7], [225, 7], [222, 138], [211, 7], [210, 7], [292, 139], [289, 140], [242, 141], [241, 123], [239, 7], [240, 123], [243, 142], [244, 143], [237, 7], [233, 144], [236, 123], [235, 123], [234, 123], [229, 123], [238, 144], [288, 123], [254, 145], [273, 146], [272, 147], [291, 1], [246, 1], [219, 1], [217, 148], [163, 288], [180, 288], [87, 288], [160, 288], [97, 289], [186, 288], [187, 290], [96, 291], [99, 288], [94, 291], [95, 291], [103, 292], [102, 291], [100, 288], [101, 291], [98, 291], [104, 288], [185, 293], [184, 289], [188, 294], [182, 289], [181, 289], [183, 295], [88, 288], [89, 288], [90, 288], [92, 288], [189, 296], [191, 288], [190, 288], [192, 288], [193, 297], [159, 288], [111, 298], [116, 288], [202, 299], [203, 299], [113, 288], [204, 299], [114, 288], [117, 288], [200, 288], [199, 299], [196, 300], [198, 299], [197, 300], [118, 288], [115, 288], [112, 299], [195, 298], [201, 301], [119, 288], [136, 288], [133, 288], [134, 288], [135, 288], [132, 288], [131, 288], [130, 288], [156, 302], [153, 288], [138, 303], [137, 304], [155, 288], [154, 288], [143, 303], [152, 303], [142, 303], [127, 305], [128, 288], [141, 288], [149, 288], [148, 288], [147, 288], [150, 288], [145, 288], [146, 288], [151, 288], [126, 288], [140, 288], [106, 304], [123, 306], [139, 307], [122, 306], [125, 303], [120, 304], [108, 306], [124, 306], [121, 304], [206, 308], [157, 288], [109, 288], [209, 309], [293, 309], [294, 309], [295, 309], [208, 310], [297, 311], [296, 288], [178, 312], [85, 288], [298, 313], [86, 288], [84, 288], [165, 288], [299, 288], [300, 288], [301, 288], [302, 288], [303, 288], [304, 288], [305, 288], [177, 314], [306, 288], [162, 315], [307, 288], [161, 288], [107, 306], [179, 312], [166, 288], [167, 288], [74, 1], [75, 221], [73, 222], [72, 1], [77, 223], [76, 1], [81, 224], [78, 1], [80, 225], [79, 226]], "semanticDiagnosticsPerFile": [62, 65, 64, 63, 316, 230, 213, 231, 212, 82, 260, 261, 259, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 321, 322, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 323, 371, 339, 340, 341, 372, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 355, 354, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 59, 169, 57, 60, 61, 83, 317, 315, 314, 313, 399, 403, 400, 404, 318, 418, 319, 320, 409, 419, 410, 417, 402, 401, 309, 312, 308, 58, 373, 395, 393, 394, 382, 383, 390, 381, 386, 396, 387, 392, 397, 380, 388, 389, 384, 391, 385, 311, 310, 70, 71, 69, 67, 66, 68, 379, 412, 405, 416, 11, 12, 14, 13, 2, 15, 16, 17, 18, 19, 20, 21, 22, 3, 4, 26, 23, 24, 25, 27, 28, 29, 5, 30, 31, 32, 33, 6, 37, 34, 35, 36, 38, 7, 39, 44, 45, 40, 41, 42, 43, 8, 49, 46, 47, 48, 50, 9, 51, 52, 53, 54, 55, 1, 10, 56, 214, 232, 408, 411, 414, 407, 406, 413, 420, 421, 415, 422, 377, 398, 376, 375, 378, 374, 171, 172, 173, 174, 175, 176, 170, 253, 255, 245, 250, 251, 257, 252, 249, 248, 247, 258, 215, 216, 256, 274, 284, 278, 286, 290, 276, 277, 279, 282, 285, 281, 283, 287, 280, 275, 224, 228, 218, 221, 226, 227, 220, 223, 225, 222, 211, 210, 292, 289, 242, 241, 239, 240, 243, 244, 237, 233, 236, 235, 234, 229, 238, 288, 254, 273, 272, 291, 246, 219, 217, [164, [{"file": "./src/app.tsx", "start": 7, "length": 5, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/app.tsx", "start": 111, "length": 13, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'ThemeProvider'.", "category": 1, "code": 2305}, {"file": "./src/app.tsx", "start": 1367, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ position: string; top: number; left: number; padding: string; background: string; color: string; zIndex: number; fontSize: string; fontFamily: string; }' is not assignable to type 'Properties<string | number, string & {}>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'position' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type 'Position | undefined'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "../../node_modules/@types/react/index.d.ts", "start": 84031, "length": 5, "messageText": "The expected type comes from property 'style' which is declared here on type 'DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement>'", "category": 3, "code": 6500}]}]], [163, [{"file": "./src/components/apperrorboundary.tsx", "start": 206, "length": 16, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'AppErrorBoundary'.", "category": 1, "code": 2305}]], [180, [{"file": "./src/components/featureerrorboundary.tsx", "start": 194, "length": 20, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'FeatureErrorBoundary'.", "category": 1, "code": 2305}]], 87, 160, 168, [97, [{"file": "./src/features/daily-guide/api/dailyguideapi.ts", "start": 4763, "length": 11, "code": 2739, "category": 1, "messageText": "Type '{ id: string; description: string; priority: TradingPlanPriority; completed: boolean; }[]' is missing the following properties from type 'TradingPlan': items, strategy, riskManagement, notes", "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3655, "length": 11, "messageText": "The expected type comes from property 'tradingPlan' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}]], [186, [{"file": "./src/features/daily-guide/components/dailyguide.tsx", "start": 123, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/dailyguide.tsx", "start": 129, "length": 6, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Button'.", "category": 1, "code": 2305}]], 187, [96, [{"file": "./src/features/daily-guide/components/keylevels.tsx", "start": 119, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/keylevels.tsx", "start": 125, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Badge'.", "category": 1, "code": 2305}]], [99, [{"file": "./src/features/daily-guide/components/marketnews.tsx", "start": 2482, "length": 10, "messageText": "Property 'marketNews' does not exist on type 'DailyGuideContextType'.", "category": 1, "code": 2339}, {"file": "./src/features/daily-guide/components/marketnews.tsx", "start": 3253, "length": 4, "messageText": "Parameter 'news' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [94, [{"file": "./src/features/daily-guide/components/marketoverview.tsx", "start": 135, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/marketoverview.tsx", "start": 141, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Badge'.", "category": 1, "code": 2305}]], [95, [{"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 146, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 152, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Badge'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 159, "length": 6, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Button'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 167, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Input'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 174, "length": 9, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'FormField'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 9790, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 103, 102, 100, 101, [98, [{"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 225, "length": 16, "messageText": "'\"../types\"' has no exported member named 'DailyGuideAction'. Did you mean 'DailyGuideData'?", "category": 1, "code": 2724}, {"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 555, "length": 16, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ marketData: null; tradingPlan: never[]; keyLevels: never[]; marketNews: never[]; isLoading: false; error: null; currentDate: string; }' is not assignable to type 'DailyGuideState'.", "category": 1, "code": 2322, "next": [{"messageText": "Object literal may only specify known properties, and 'marketData' does not exist in type 'DailyGuideState'.", "category": 1, "code": 2353}]}}, {"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 1423, "length": 152, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ tradingPlan: any; data: DailyGuideData; isLoading: boolean; error: string | null; selectedDate: string; }' is not assignable to type 'DailyGuideState'.", "category": 1, "code": 2322, "next": [{"messageText": "Object literal may only specify known properties, and 'tradingPlan' does not exist in type 'DailyGuideState'.", "category": 1, "code": 2353}]}}, {"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 1442, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'tradingPlan' does not exist on type 'DailyGuideState'."}, {"file": "./src/features/daily-guide/context/dailyguidecontext.tsx", "start": 1459, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [104, [{"file": "./src/features/daily-guide/dailyguide.tsx", "start": 174, "length": 6, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Button'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 1421, "length": 11, "messageText": "Property 'currentDate' does not exist on type 'DailyGuideContextType'.", "category": 1, "code": 2339}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 2137, "length": 14, "code": 2741, "category": 1, "messageText": "Property 'marketOverview' is missing in type '{}' but required in type 'MarketOverviewProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/marketoverview.tsx", "start": 386, "length": 14, "messageText": "'marketOverview' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 2334, "length": 11, "code": 2741, "category": 1, "messageText": "Property 'tradingPlan' is missing in type '{}' but required in type 'TradingPlanProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/tradingplan.tsx", "start": 432, "length": 11, "messageText": "'tradingPlan' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/daily-guide/dailyguide.tsx", "start": 2526, "length": 9, "code": 2741, "category": 1, "messageText": "Property 'keyLevels' is missing in type '{}' but required in type 'KeyLevelsProps'.", "relatedInformation": [{"file": "./src/features/daily-guide/components/keylevels.tsx", "start": 321, "length": 9, "messageText": "'keyLevels' is declared here.", "category": 3, "code": 2728}]}]], 185, [184, [{"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 114, "length": 8, "messageText": "'useState' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5211, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ sentiment: \"bullish\" | \"bearish\" | \"neutral\"; summary: string; indices: { symbol: string; name: string; value: number; change: number; changePercent: number; }[]; economicEvents: ({ title: string; ... 4 more ...; actual?: undefined; } | { ...; })[]; news: { ...; }[]; lastUpdated: string; }' is not assignable to type 'MarketOverview'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'economicEvents' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '({ title: string; time: string; importance: string; expected: string; previous: string; actual?: undefined; } | { title: string; time: string; importance: string; expected: string; previous: string; actual: string; })[]' is not assignable to type 'EconomicEvent[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ title: string; time: string; importance: string; expected: string; previous: string; actual?: undefined; } | { title: string; time: string; importance: string; expected: string; previous: string; actual: string; }' is not assignable to type 'EconomicEvent'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ title: string; time: string; importance: string; expected: string; previous: string; actual?: undefined; }' is not assignable to type 'EconomicEvent'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'importance' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type '\"medium\" | \"high\" | \"low\"'.", "category": 1, "code": 2322}]}]}]}]}]}]}, "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3588, "length": 14, "messageText": "The expected type comes from property 'marketOverview' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5251, "length": 11, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ items: { id: string; description: string; priority: string; completed: boolean; }[]; strategy: string; riskManagement: { maxRiskPerTrade: number; maxDailyLoss: number; maxTrades: number; positionSizing: string; }; notes: string; }' is not assignable to type 'TradingPlan'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'items' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ id: string; description: string; priority: string; completed: boolean; }[]' is not assignable to type 'TradingPlanItem[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; description: string; priority: string; completed: boolean; }' is not assignable to type 'TradingPlanItem'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'priority' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type 'TradingPlanPriority'.", "category": 1, "code": 2322}]}]}]}]}]}, "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3655, "length": 11, "messageText": "The expected type comes from property 'tradingPlan' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5355, "length": 10, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: string; title: string; source: string; timestamp: string; url: string; impact: string; }[]' is not assignable to type 'MarketNewsItem[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: string; title: string; source: string; timestamp: string; url: string; impact: string; }' is not assignable to type 'MarketNewsItem'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'impact' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type '\"medium\" | \"high\" | \"low\"'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": [{"file": "./src/features/daily-guide/types.ts", "start": 3835, "length": 10, "messageText": "The expected type comes from property 'marketNews' which is declared here on type 'DailyGuideData'", "category": 3, "code": 6500}]}, {"file": "./src/features/daily-guide/hooks/usedailyguide.ts", "start": 5603, "length": 9, "messageText": "'state' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [188, [{"file": "./src/features/daily-guide/index.ts", "start": 235, "length": 24, "messageText": "Module './components' has already exported a member named 'MarketOverview'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"file": "./src/features/daily-guide/index.ts", "start": 235, "length": 24, "messageText": "Module './components' has already exported a member named 'TradingPlan'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}]], [182, [{"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 87, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'createSelector'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1078, "length": 11, "messageText": "Parameter 'tradingPlan' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1215, "length": 5, "messageText": "Parameter 'items' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1238, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1359, "length": 5, "messageText": "Parameter 'items' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1382, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1534, "length": 8, "messageText": "Parameter 'allItems' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1544, "length": 14, "messageText": "Parameter 'completedItems' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1759, "length": 5, "messageText": "Parameter 'items' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1914, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 1930, "length": 21, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type 'Record<TradingPlanPriority, any>'."}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2082, "length": 14, "messageText": "Parameter 'marketOverview' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2214, "length": 7, "messageText": "Parameter 'indices' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2241, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2351, "length": 7, "messageText": "Parameter 'indices' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2378, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2489, "length": 14, "messageText": "Parameter 'marketOverview' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2648, "length": 14, "messageText": "Parameter 'marketOverview' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2780, "length": 14, "messageText": "Parameter 'marketOverview' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2929, "length": 6, "messageText": "Parameter 'events' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 2954, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3083, "length": 6, "messageText": "Parameter 'levels' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3177, "length": 5, "messageText": "Parameter 'level' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3341, "length": 9, "messageText": "Parameter 'watchlist' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3444, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3609, "length": 4, "messageText": "Parameter 'news' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3630, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3743, "length": 4, "messageText": "Parameter 'news' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 3828, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 4161, "length": 14, "messageText": "Parameter 'marketOverview' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 4177, "length": 11, "messageText": "Parameter 'tradingPlan' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 4190, "length": 14, "messageText": "Parameter 'keyPriceLevels' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/daily-guide/state/dailyguideselectors.ts", "start": 4370, "length": 14, "messageText": "Parameter 'marketOverview' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [181, [{"file": "./src/features/daily-guide/state/dailyguidestate.ts", "start": 618, "length": 18, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'createStoreContext'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/state/dailyguidestate.ts", "start": 638, "length": 12, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'persistState'.", "category": 1, "code": 2305}, {"file": "./src/features/daily-guide/state/dailyguidestate.ts", "start": 6875, "length": 5, "messageText": "Parameter 'state' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 183, 93, 88, 89, 90, 92, [91, [{"file": "./src/features/performance-dashboard/hooks/usedashboarddata.ts", "start": 474, "length": 10, "messageText": "'setMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 189, 191, 190, 192, [158, [{"file": "./src/features/settings/hooks/usesettings.ts", "start": 124, "length": 8, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'useTheme'.", "category": 1, "code": 2305}]], 193, 159, 111, 116, 202, 203, [113, [{"file": "./src/features/trade-analysis/components/filterpanel.tsx", "start": 364, "length": 6, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Button'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/filterpanel.tsx", "start": 372, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/filterpanel.tsx", "start": 378, "length": 3, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Tag'.", "category": 1, "code": 2305}]], 204, [114, [{"file": "./src/features/trade-analysis/components/performancesummary.tsx", "start": 166, "length": 46, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [117, [{"file": "./src/features/trade-analysis/components/timeperformancechart.tsx", "start": 183, "length": 43, "messageText": "'TimePerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/timeperformancechart.tsx", "start": 2710, "length": 5, "messageText": "'title' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [200, [{"file": "./src/features/trade-analysis/components/tradeanalysis.tsx", "start": 144, "length": 6, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Button'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysis.tsx", "start": 3326, "length": 12, "messageText": "'title' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [199, [{"file": "./src/features/trade-analysis/components/tradeanalysischarts.tsx", "start": 135, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}]], [196, [{"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 149, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 158, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Input'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 168, "length": 6, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Select'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 179, "length": 6, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Button'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 190, "length": 9, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'FormField'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 1963, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 2277, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 2586, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 3103, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 3432, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/components/tradeanalysisfilter.tsx", "start": 3831, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [198, [{"file": "./src/features/trade-analysis/components/tradeanalysissummary.tsx", "start": 145, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}]], [197, [{"file": "./src/features/trade-analysis/components/tradeanalysistable.tsx", "start": 146, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Table'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysistable.tsx", "start": 156, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysistable.tsx", "start": 165, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Badge'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradeanalysistable.tsx", "start": 3446, "length": 8, "messageText": "Parameter 'columnId' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/components/tradeanalysistable.tsx", "start": 3456, "length": 9, "messageText": "Parameter 'direction' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/components/tradeanalysistable.tsx", "start": 3604, "length": 3, "messageText": "Parameter 'row' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [118, [{"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 162, "length": 33, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 273, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Badge'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 280, "length": 4, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Card'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradedetail.tsx", "start": 286, "length": 3, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Tag'.", "category": 1, "code": 2305}]], [115, [{"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 195, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 326, "length": 5, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Badge'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/components/tradestable.tsx", "start": 333, "length": 3, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'Tag'.", "category": 1, "code": 2305}]], [112, [{"file": "./src/features/trade-analysis/context/tradeanalysiscontext.tsx", "start": 313, "length": 17, "messageText": "'TradeAnalysisData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [195, [{"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 784, "length": 8, "messageText": "'dispatch' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 2356, "length": 11, "messageText": "Block-scoped variable 'fetchTrades' used before its declaration.", "category": 1, "code": 2448, "relatedInformation": [{"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 5753, "length": 11, "messageText": "'fetchTrades' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 2356, "length": 11, "messageText": "Variable 'fetchTrades' is used before being assigned.", "category": 1, "code": 2454}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 2514, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 4720, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 5033, "length": 610, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ winRate: any; profitFactor: any; averageWin: any; averageLoss: any; totalTrades: any; netProfit: any; expectancy: number; maxDrawdown: number; sharpeRatio: number; successStreak: number; }' is not assignable to parameter of type 'SetStateAction<PerformanceMetrics | null>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ winRate: any; profitFactor: any; averageWin: any; averageLoss: any; totalTrades: any; netProfit: any; expectancy: number; maxDrawdown: number; sharpeRatio: number; successStreak: number; }' is missing the following properties from type 'PerformanceMetrics': winningTrades, losingTrades, breakeven, totalProfitLoss, and 3 more.", "category": 1, "code": 2740}]}}, {"file": "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "start": 6517, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [201, [{"file": "./src/features/trade-analysis/index.ts", "start": 1162, "length": 24, "messageText": "Module './state/tradeAnalysisState' has already exported a member named 'Trade'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"file": "./src/features/trade-analysis/index.ts", "start": 1162, "length": 24, "messageText": "Module './state/tradeAnalysisState' has already exported a member named 'TradeAnalysisAction'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"file": "./src/features/trade-analysis/index.ts", "start": 1162, "length": 24, "messageText": "Module './state/tradeAnalysisState' has already exported a member named 'TradeAnalysisState'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}]], [194, [{"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 120, "length": 18, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'createStoreContext'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 140, "length": 14, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'createSelector'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 156, "length": 12, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'persistState'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 4349, "length": 5, "messageText": "Parameter 'state' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 6670, "length": 6, "messageText": "Parameter 'trades' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 6678, "length": 6, "messageText": "Parameter 'filter' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 6717, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 7647, "length": 3, "messageText": "Parameter 'tag' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 7859, "length": 6, "messageText": "Parameter 'trades' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 7867, "length": 4, "messageText": "Parameter 'sort' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8343, "length": 6, "messageText": "Parameter 'trades' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8351, "length": 4, "messageText": "Parameter 'page' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8357, "length": 8, "messageText": "Parameter 'pageSize' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8579, "length": 6, "messageText": "Parameter 'trades' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8587, "length": 8, "messageText": "Parameter 'pageSize' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8735, "length": 6, "messageText": "Parameter 'trades' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8827, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8901, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 8978, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9059, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9064, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9135, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9140, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9363, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9398, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9403, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9533, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9568, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/features/trade-analysis/state/tradeanalysisstate.ts", "start": 9573, "length": 5, "messageText": "Parameter 'trade' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [119, [{"file": "./src/features/trade-analysis/tradeanalysis.tsx", "start": 187, "length": 8, "messageText": "Module '\"@adhd-trading-dashboard/shared\"' has no exported member 'DataCard'.", "category": 1, "code": 2305}, {"file": "./src/features/trade-analysis/tradeanalysis.tsx", "start": 2662, "length": 11, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'ViewType' is not assignable to type '\"summary\" | \"trades\" | \"charts\" | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"time\"' is not assignable to type '\"summary\" | \"trades\" | \"charts\" | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./src/features/trade-analysis/types.ts", "start": 3325, "length": 11, "messageText": "The expected type comes from property 'defaultView' which is declared here on type 'Partial<UserPreferences>'", "category": 3, "code": 6500}]}]], 110, 205, 136, 133, 134, [135, [{"file": "./src/features/trade-journal/components/dol-analysis/doleffectivenessrating.tsx", "start": 192, "length": 25, "messageText": "'DOL_EFFECTIVENESS_OPTIONS' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 132, 131, 130, 156, 153, 138, 137, 155, 154, [143, [{"file": "./src/features/trade-journal/components/form/tradeformriskfields.tsx", "start": 1597, "length": 16, "messageText": "'validationErrors' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [152, [{"file": "./src/features/trade-journal/components/form/tradeformstrategyfields.tsx", "start": 119, "length": 8, "messageText": "'useState' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 142, 127, [128, [{"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 4814, "length": 12, "messageText": "This comparison appears to be unintentional because the types 'ScoreRange' and '\"\"' have no overlap.", "category": 1, "code": 2367}, {"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 6138, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 6329, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 6523, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 6708, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 6889, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 7077, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/components/pattern-quality/patternqualityassessment.tsx", "start": 7268, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}]], 141, 149, 148, 147, 150, 145, [146, [{"file": "./src/features/trade-journal/components/setup-classification/secondarysetupselector.tsx", "start": 3034, "length": 12, "messageText": "'primarySetup' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [151, [{"file": "./src/features/trade-journal/components/setup-classification/setupclassificationsection.tsx", "start": 170, "length": 8, "messageText": "'useState' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 126, 140, [106, [{"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 3001, "length": 12, "messageText": "'ExpandButton' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [129, [{"file": "./src/features/trade-journal/constants/dolanalysis.ts", "start": 101, "length": 73, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], 123, [144, [{"file": "./src/features/trade-journal/constants/setupclassification.ts", "start": 97, "length": 160, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], 139, 122, [125, [{"file": "./src/features/trade-journal/hooks/usetradeform.ts", "start": 105, "length": 9, "messageText": "'useEffect' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradeform.ts", "start": 2423, "length": 8, "messageText": "'navigate' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [120, [{"file": "./src/features/trade-journal/hooks/usetradeformdata.ts", "start": 557, "length": 10, "messageText": "'isNewTrade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [108, [{"file": "./src/features/trade-journal/hooks/usetradejournal.ts", "start": 153, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [124, [{"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 991, "length": 10, "messageText": "'isNewTrade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 3298, "length": 18, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ clarity: string; confluence: string; context: string; risk: string; reward: string; timeframe: string; volume: string; }' is not assignable to parameter of type 'Record<string, ScoreRange>'.", "category": 1, "code": 2345, "next": [{"messageText": "Property 'clarity' is incompatible with index signature.", "category": 1, "code": 2530, "next": [{"messageText": "Type 'string' is not assignable to type 'ScoreRange'.", "category": 1, "code": 2322}]}]}}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 3471, "length": 19, "code": 2551, "category": 1, "messageText": "Property 'patternQualityScore' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string | undefined; takeProfit?: string | undefined; profit: string; ... 40 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'. Did you mean 'patternQuality'?", "relatedInformation": [{"file": "./src/features/trade-journal/types/index.ts", "start": 6699, "length": 14, "messageText": "'patternQuality' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 3839, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'dolAnalysis' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string | undefined; takeProfit?: string | undefined; profit: string; ... 40 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'."}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 5325, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | undefined' is not assignable to type 'SetupType | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'SetupType | undefined'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./src/features/trade-journal/types/index.ts", "start": 4988, "length": 5, "messageText": "The expected type comes from property 'setup' which is declared here on type 'Omit<Trade, \"id\">'", "category": 3, "code": 6500}]}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 5905, "length": 19, "code": 2551, "category": 1, "messageText": "Property 'patternQualityScore' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string | undefined; takeProfit?: string | undefined; profit: string; ... 40 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'. Did you mean 'patternQuality'?", "relatedInformation": [{"file": "./src/features/trade-journal/types/index.ts", "start": 6699, "length": 14, "messageText": "'patternQuality' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 5966, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'dolAnalysis' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string | undefined; takeProfit?: string | undefined; profit: string; ... 40 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'."}, {"file": "./src/features/trade-journal/hooks/usetradesubmission.ts", "start": 6978, "length": 16, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; symbol: string; size: number; strategy: string; notes: string; date: string; direction: \"Long\" | \"Short\"; entryTime?: string | undefined; exitTime?: string | undefined; profitLoss: number; ... 25 more ...; images?: string[] | undefined; }' is not assignable to parameter of type 'Omit<Trade, \"id\">'.", "category": 1, "code": 2345, "next": [{"messageText": "Object literal may only specify known properties, and 'id' does not exist in type 'Omit<Trade, \"id\">'.", "category": 1, "code": 2353}]}}]], 121, 206, 157, [109, [{"file": "./src/features/trade-journal/tradejournal.tsx", "start": 299, "length": 11, "messageText": "'TradeColumn' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/tradejournal.tsx", "start": 403, "length": 32, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/features/trade-journal/tradejournal.tsx", "start": 8984, "length": 6, "code": 2677, "category": 1, "messageText": {"messageText": "A type predicate's type must be assignable to its parameter's type.", "category": 1, "code": 2677, "next": [{"messageText": "Type 'string' is not assignable to type 'SetupType | undefined'.", "category": 1, "code": 2322}]}}, {"file": "./src/features/trade-journal/tradejournal.tsx", "start": 9241, "length": 6, "code": 2677, "category": 1, "messageText": {"messageText": "A type predicate's type must be assignable to its parameter's type.", "category": 1, "code": 2677, "next": [{"messageText": "Type 'string' is not assignable to type 'ModelType | undefined'.", "category": 1, "code": 2322}]}}, {"file": "./src/features/trade-journal/tradejournal.tsx", "start": 10089, "length": 6, "code": 2677, "category": 1, "messageText": {"messageText": "A type predicate's type must be assignable to its parameter's type.", "category": 1, "code": 2677, "next": [{"messageText": "Type 'string' is not assignable to type 'LiquidityType | undefined'.", "category": 1, "code": 2322}]}}, {"file": "./src/features/trade-journal/tradejournal.tsx", "start": 10409, "length": 6, "code": 2677, "category": 1, "messageText": {"messageText": "A type predicate's type must be assignable to its parameter's type.", "category": 1, "code": 2677, "next": [{"messageText": "Type 'string' is not assignable to type 'DOLType | undefined'.", "category": 1, "code": 2322}]}}]], 105, 209, 293, 294, 295, 208, 297, 296, 207, 178, 85, 298, 86, 84, 165, 299, 300, 301, 302, 303, 304, 305, 177, 306, 162, 307, 161, [107, [{"file": "./src/services/tradestorage.ts", "start": 2007, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'Omit<Trade, \"id\">'."}]], 179, 166, 167, 74, 75, 73, 72, 77, 76, 81, 78, 80, 79], "latestChangedDtsFile": "./dist/features/trade-journal/components/form/TradeFormStrategyFields.d.ts"}, "version": "4.9.4"}