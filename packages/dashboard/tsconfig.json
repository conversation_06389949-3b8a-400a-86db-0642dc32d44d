{"extends": "../../tsconfig.json", "compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "baseUrl": "src", "composite": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "sourceMap": true, "noEmit": false, "types": ["vitest/globals"], "paths": {"@api/*": ["api/*"], "@components/*": ["components/*"], "@context/*": ["context/*"], "@hooks/*": ["hooks/*"], "@layouts/*": ["layouts/*"], "@pages/*": ["pages/*"], "@routes/*": ["routes/*"], "@services/*": ["services/*"], "@styles/*": ["styles/*"], "@theme/*": ["theme/*"], "@utils/*": ["utils/*"], "@features/*": ["features/*"], "@adhd-trading-dashboard/shared": ["../../shared/src"], "@adhd-trading-dashboard/shared/*": ["../../shared/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/__tests__/*", "**/*.bak"], "references": [{"path": "../shared"}]}