{"name": "@adhd-trading-dashboard/dashboard", "version": "0.1.0", "private": true, "dependencies": {"@adhd-trading-dashboard/shared": "1.0.0", "@anthropic-ai/sdk": "0.52.0", "@babel/parser": "7.27.2", "@babel/traverse": "7.27.1", "canvas": "3.1.0", "express": "4.21.2", "idb": "8.0.3", "react-router-dom": "6.6.2"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0", "recharts": "2.10.3", "styled-components": "5.3.6"}, "scripts": {"prestart": "node scripts/manage-assets.js", "start": "vite", "predev": "node scripts/manage-assets.js", "dev": "vite", "prebuild": "node scripts/manage-assets.js", "build": "tsc && vite build", "build:dev": "tsc && vite build --mode development", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .js,.jsx,.ts,.tsx"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react": "18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.1", "recharts": "2.10.3", "styled-components": "5.3.6", "typescript": "4.9.4", "vite-plugin-pwa": "1.0.0", "web-vitals": "2.1.4"}}