/**
 * Simple script to generate placeholder assets for the ADHD Trading Dashboard
 * This creates basic SVG files for the favicon and logos
 */

const fs = require('fs');
const path = require('path');

// Ensure the public directory exists
const publicDir = path.join(__dirname, '..');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Create a simple F1-themed favicon SVG
const faviconSvg = `
<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" fill="#1a1f2c" />
  <rect y="22" width="32" height="10" fill="#e10600" />
  <text x="16" y="16" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">A</text>
</svg>
`;

// Create a simple F1-themed logo SVG (192x192)
const logo192Svg = `
<svg width="192" height="192" xmlns="http://www.w3.org/2000/svg">
  <rect width="192" height="192" fill="#1a1f2c" />
  <rect y="134" width="192" height="58" fill="#e10600" />
  <text x="96" y="70" font-family="Arial" font-size="60" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">ADHD</text>
  <text x="96" y="110" font-family="Arial" font-size="30" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">TRADING</text>
</svg>
`;

// Create a simple F1-themed logo SVG (512x512)
const logo512Svg = `
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" fill="#1a1f2c" />
  <rect y="358" width="512" height="154" fill="#e10600" />
  <text x="256" y="180" font-family="Arial" font-size="160" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">ADHD</text>
  <text x="256" y="280" font-family="Arial" font-size="80" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">TRADING</text>
</svg>
`;

// Write the SVG files
fs.writeFileSync(path.join(publicDir, 'favicon.svg'), faviconSvg);
fs.writeFileSync(path.join(publicDir, 'logo192.svg'), logo192Svg);
fs.writeFileSync(path.join(publicDir, 'logo512.svg'), logo512Svg);

// Create simple copies as .ico and .png files (these are just renamed SVGs for demonstration)
fs.copyFileSync(path.join(publicDir, 'favicon.svg'), path.join(publicDir, 'favicon.ico'));
fs.copyFileSync(path.join(publicDir, 'logo192.svg'), path.join(publicDir, 'logo192.png'));
fs.copyFileSync(path.join(publicDir, 'logo512.svg'), path.join(publicDir, 'logo512.png'));

console.log('Generated placeholder assets:');
console.log('- favicon.ico (SVG)');
console.log('- logo192.png (SVG)');
console.log('- logo512.png (SVG)');
console.log('Note: These are actually SVG files renamed for demonstration purposes.');
