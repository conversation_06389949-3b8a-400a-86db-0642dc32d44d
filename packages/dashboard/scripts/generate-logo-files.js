/**
 * <PERSON><PERSON><PERSON> to generate proper logo files for the ADHD Trading Dashboard
 * This creates actual PNG files for the logos
 */

const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Directories
const rootDir = path.resolve(__dirname, '..');
const publicDir = path.resolve(rootDir, 'public');

// Ensure directories exist
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
  console.log(`Created directory: ${publicDir}`);
}

// Function to create a logo PNG
function createLogoPNG(width, height, filename) {
  // Create canvas
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Draw background
  ctx.fillStyle = '#1a1f2c';
  ctx.fillRect(0, 0, width, height);

  // Draw red bar at bottom (F1 style)
  ctx.fillStyle = '#e10600';
  ctx.fillRect(0, height * 0.7, width, height * 0.3);

  // Draw text
  ctx.fillStyle = 'white';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // ADHD text
  ctx.font = `bold ${width * 0.3}px Arial`;
  ctx.fillText('ADHD', width / 2, height * 0.35);

  // TRADING text
  ctx.font = `bold ${width * 0.15}px Arial`;
  ctx.fillText('TRADING', width / 2, height * 0.55);

  // Save to file
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(publicDir, filename), buffer);
  console.log(`Created ${filename}`);

  // Also save to root directory for development
  fs.writeFileSync(path.join(rootDir, filename), buffer);
  console.log(`Created ${filename} in root directory for development`);
}

// Function to create a favicon
function createFavicon(filename) {
  // For a proper favicon, we would use a library like 'favicon'
  // For now, we'll create a simple PNG as a placeholder
  const size = 32;
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Draw background
  ctx.fillStyle = '#1a1f2c';
  ctx.fillRect(0, 0, size, size);

  // Draw red bar at bottom (F1 style)
  ctx.fillStyle = '#e10600';
  ctx.fillRect(0, size * 0.7, size, size * 0.3);

  // Draw text
  ctx.fillStyle = 'white';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.font = `bold ${size * 0.5}px Arial`;
  ctx.fillText('A', size / 2, size * 0.4);

  // Save to file
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(publicDir, filename), buffer);
  console.log(`Created ${filename}`);

  // Also save to root directory for development
  fs.writeFileSync(path.join(rootDir, filename), buffer);
  console.log(`Created ${filename} in root directory for development`);
}

try {
  // Remove any existing files first to ensure clean generation
  const files = ['logo192.png', 'logo512.png', 'favicon.ico'];
  files.forEach((file) => {
    const publicFilePath = path.join(publicDir, file);
    const rootFilePath = path.join(rootDir, file);

    if (fs.existsSync(publicFilePath)) {
      fs.unlinkSync(publicFilePath);
      console.log(`Removed existing file: ${publicFilePath}`);
    }

    if (fs.existsSync(rootFilePath)) {
      fs.unlinkSync(rootFilePath);
      console.log(`Removed existing file: ${rootFilePath}`);
    }
  });

  // Create logo files
  createLogoPNG(192, 192, 'logo192.png');
  createLogoPNG(512, 512, 'logo512.png');
  createFavicon('favicon.ico');

  // Verify file sizes to ensure they're not just renamed SVGs
  files.forEach((file) => {
    const publicFilePath = path.join(publicDir, file);
    const rootFilePath = path.join(rootDir, file);

    if (fs.existsSync(publicFilePath)) {
      const stats = fs.statSync(publicFilePath);
      console.log(`${file} size: ${stats.size} bytes`);

      // PNG files should be larger than 1KB
      if (file.endsWith('.png') && stats.size < 1024) {
        console.warn(
          `Warning: ${file} is suspiciously small (${stats.size} bytes). It might not be a proper PNG file.`
        );
      }
    }
  });

  console.log('Logo files generated successfully!');
} catch (error) {
  console.error('Error generating logo files:', error);
}
