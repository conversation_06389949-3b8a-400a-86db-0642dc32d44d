/**
 * This script ensures that all necessary assets are available in both the public directory
 * and the root directory for the build process.
 */

const fs = require('fs');
const path = require('path');

// Directories
const rootDir = path.resolve(__dirname, '..');
const publicDir = path.resolve(rootDir, 'public');
const distDir = path.resolve(rootDir, 'dist');

// Ensure directories exist
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
  console.log(`Created directory: ${publicDir}`);
}

if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
  console.log(`Created directory: ${distDir}`);
}

// List of essential files
const essentialFiles = [
  'favicon.ico',
  'logo192.png',
  'logo512.png',
  'manifest.json'
];

// Check and copy files between root and public
essentialFiles.forEach(file => {
  const rootFilePath = path.join(rootDir, file);
  const publicFilePath = path.join(publicDir, file);
  
  // If file exists in root but not in public, copy to public
  if (fs.existsSync(rootFilePath) && !fs.existsSync(publicFilePath)) {
    fs.copyFileSync(rootFilePath, publicFilePath);
    console.log(`Copied ${file} from root to public directory`);
  }
  
  // If file exists in public but not in root, copy to root
  if (fs.existsSync(publicFilePath) && !fs.existsSync(rootFilePath)) {
    fs.copyFileSync(publicFilePath, rootFilePath);
    console.log(`Copied ${file} from public to root directory`);
  }
  
  // If file doesn't exist in either location, warn
  if (!fs.existsSync(rootFilePath) && !fs.existsSync(publicFilePath)) {
    console.warn(`Warning: ${file} not found in either root or public directory`);
  }
});

console.log('Asset verification complete!');
