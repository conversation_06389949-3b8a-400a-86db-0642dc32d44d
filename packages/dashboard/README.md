# ADHD Trading Dashboard Package

This package contains the React application for the ADHD Trading Dashboard.

## Architecture

The dashboard package is organized by features and provides the user interface for the ADHD Trading Dashboard:

- **Features**: Feature-specific components and logic
- **Layouts**: Layout components for the application
- **Routes**: Route definitions for the application

## Features

The dashboard includes the following features:

- **Dashboard**: Overview of trading performance and key metrics
- **Daily Guide**: Daily trading plan and market overview
- **Trade Journal**: Record and review trades
- **Trade Analysis**: Analyze trading performance and patterns
- **Settings**: Configure dashboard settings

## Usage

### Running the Dashboard

```bash
# Start the development server
npm run start

# Build for production
npm run build

# Run tests
npm run test
```

### Importing Components

```tsx
import { Dashboard, DailyGuide } from "@adhd-trading-dashboard/dashboard";

const App = () => {
  return (
    <div>
      <Dashboard />
      <DailyGuide />
    </div>
  );
};
```

## Planned Improvements

In the future, we plan to reorganize the dashboard package to be feature-based rather than component-type based:

```
packages/dashboard/src/
  ├── features/
  │   ├── daily-guide/    # Daily guide feature
  │   ├── trade-journal/  # Trade journal feature
  │   └── trade-analysis/ # Trade analysis feature
  ├── layouts/            # Layout components
  └── routes/             # Route definitions
```

This will improve code organization and make it easier to add new features.
