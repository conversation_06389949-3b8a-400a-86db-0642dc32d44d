/* 
 * ADHD Trading Dashboard - CSS Variables
 * F1-inspired theme variables
 */

:root {
  /* F1 Theme Color Variables */
  --f1-red: #e10600;
  --f1-black: #15151e;
  --f1-white: #f8f4f4;
  --f1-gray: #38383f;
  --f1-blue: #0090d0;
  --f1-yellow: #ffb800;
  --f1-green: #27f59b;
  
  /* Background Colors */
  --bg-primary: var(--f1-black);
  --bg-secondary: #1a1f2c;
  --bg-tertiary: #2a2f3c;
  --bg-card: #242936;
  --bg-hover: #2f3441;
  
  /* Text Colors */
  --text-primary: var(--f1-white);
  --text-secondary: #b0b7c3;
  --text-tertiary: #7a8194;
  --text-accent: var(--f1-red);
  --text-success: var(--f1-green);
  --text-warning: var(--f1-yellow);
  --text-danger: var(--f1-red);
  
  /* Border Colors */
  --border-light: rgba(255, 255, 255, 0.1);
  --border-medium: rgba(255, 255, 255, 0.15);
  --border-dark: rgba(0, 0, 0, 0.2);
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-secondary: 'Formula1', 'Titillium Web', sans-serif;
  --font-family-mono: 'Roboto Mono', monospace;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-md: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 2.5rem;   /* 40px */
  --spacing-3xl: 3rem;     /* 48px */
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.5rem;   /* 8px */
  --border-radius-lg: 0.75rem;  /* 12px */
  --border-radius-xl: 1rem;     /* 16px */
  --border-radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Z-index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  
  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 250ms;
  --transition-slow: 350ms;
  --transition-ease: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Chart Colors */
  --chart-color-1: var(--f1-red);
  --chart-color-2: var(--f1-blue);
  --chart-color-3: var(--f1-green);
  --chart-color-4: var(--f1-yellow);
  --chart-color-5: #9747FF;
  --chart-color-6: #00D2C8;
  --chart-color-7: #FF5722;
  --chart-color-8: #4CAF50;
  
  /* Grid */
  --container-padding: 1rem;
  --container-max-width: 1280px;
  --grid-gap: 1rem;
}

/* Dark mode overrides (default) */
:root {
  color-scheme: dark;
}

/* Media queries for responsive design */
@media (min-width: 768px) {
  :root {
    --container-padding: 2rem;
    --grid-gap: 1.5rem;
  }
}
