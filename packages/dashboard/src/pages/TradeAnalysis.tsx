/**
 * Trade Analysis Page
 *
 * Displays analysis of trading performance
 */

import React from 'react';
import { TradeAnalysis as TradeAnalysisFeature } from '../features/trade-analysis';
import FeatureErrorBoundary from '../components/FeatureErrorBoundary';
import { useNavigate } from 'react-router-dom';

/**
 * TradeAnalysis Page
 *
 * Displays analysis of trading performance
 */
const TradeAnalysis: React.FC = () => {
  const navigate = useNavigate();

  const handleError = (error: Error) => {
    // Log the error to the console with additional context
    console.error('Trade Analysis Feature Error:', error);

    // Here you could also log to an error tracking service
    // if (window.Sentry) {
    //   window.Sentry.captureException(error, {
    //     tags: { feature: 'trade-analysis' }
    //   });
    // }
  };

  const handleSkip = () => {
    // Navigate to the dashboard if the user skips this feature
    navigate('/');
  };

  return (
    <FeatureErrorBoundary featureName="Trade Analysis" onError={handleError} onSkip={handleSkip}>
      <TradeAnalysisFeature />
    </FeatureErrorBoundary>
  );
};

export default TradeAnalysis;
