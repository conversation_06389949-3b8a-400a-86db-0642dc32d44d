/**
 * Trade Journal Page
 *
 * Displays a journal of trades
 */

import React from 'react';
import styled from 'styled-components';

const PageContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const PlaceholderText = styled.p`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

/**
 * TradeJournal Page
 * 
 * Displays a journal of trades
 */
const TradeJournal: React.FC = () => {
  return (
    <PageContainer>
      <Title>Trade Journal</Title>
      <PlaceholderText>
        This page will contain the trade journal.
      </PlaceholderText>
    </PageContainer>
  );
};

export default TradeJournal;
