/**
 * Main Layout Component
 *
 * This component provides the main layout structure for the application.
 */

import React, { useState } from "react";
import { Outlet } from "react-router-dom";
import styled from "styled-components";
import Sidebar from "./Sidebar";
import Header from "./Header";

// Main container
const LayoutContainer = styled.div`
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: ${({ theme }) => theme.colors.background};
`;

// Sidebar container
const SidebarContainer = styled.div<{ isOpen: boolean }>`
  width: ${({ isOpen }) => (isOpen ? "240px" : "64px")};
  height: 100%;
  transition: width ${({ theme }) => theme.transitions.normal};
  flex-shrink: 0;

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    position: fixed;
    z-index: ${({ theme }) => theme.zIndex.fixed};
    width: ${({ isOpen }) => (isOpen ? "240px" : "0")};
    box-shadow: ${({ isOpen, theme }) => (isOpen ? theme.shadows.lg : "none")};
  }
`;

// Main content container
const ContentContainer = styled.div<{ sidebarOpen: boolean }>`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-left ${({ theme }) => theme.transitions.normal};

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    margin-left: 0;
    width: 100%;
  }
`;

// Header container
const HeaderContainer = styled.div`
  height: 64px;
  flex-shrink: 0;
`;

// Main content
const MainContent = styled.main`
  flex: 1;
  overflow: auto;
  padding: ${({ theme }) => theme.spacing.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing.md};
  }
`;

// Overlay for mobile sidebar
const Overlay = styled.div<{ isVisible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: ${({ theme }) => theme.zIndex.modal - 1};
  opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
  visibility: ${({ isVisible }) => (isVisible ? "visible" : "hidden")};
  transition: opacity ${({ theme }) => theme.transitions.normal},
    visibility ${({ theme }) => theme.transitions.normal};

  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {
    display: none;
  }
`;

/**
 * Main Layout Component
 */
const MainLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Close sidebar (for mobile)
  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  return (
    <LayoutContainer>
      {/* Sidebar */}
      <SidebarContainer isOpen={sidebarOpen}>
        <Sidebar isOpen={sidebarOpen} />
      </SidebarContainer>

      {/* Overlay for mobile */}
      <Overlay isVisible={sidebarOpen} onClick={closeSidebar} />

      {/* Main content */}
      <ContentContainer sidebarOpen={sidebarOpen}>
        <HeaderContainer>
          <Header toggleSidebar={toggleSidebar} sidebarOpen={sidebarOpen} />
        </HeaderContainer>

        <MainContent>
          <Outlet />
        </MainContent>
      </ContentContainer>
    </LayoutContainer>
  );
};

export default MainLayout;
