/**
 * Header Component
 *
 * This component displays the application header with navigation controls.
 */

import React from "react";
import styled from "styled-components";

// Header container
const HeaderContainer = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

// Left section
const LeftSection = styled.div`
  display: flex;
  align-items: center;
`;

// Hamburger menu button
const MenuButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: ${({ theme }) => theme.colors.textPrimary};
  cursor: pointer;
  transition: color ${({ theme }) => theme.transitions.fast};

  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

// Logo
const Logo = styled.div`
  display: flex;
  align-items: center;
  margin-left: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: bold;
`;

// Right section
const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`;

// User menu
const UserMenu = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

// Avatar
const Avatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
`;

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
}

/**
 * Header Component
 */
const Header: React.FC<HeaderProps> = ({ toggleSidebar, sidebarOpen }) => {
  return (
    <HeaderContainer>
      <LeftSection>
        <MenuButton onClick={toggleSidebar} aria-label="Toggle sidebar">
          {sidebarOpen ? <span>☰</span> : <span>☰</span>}
        </MenuButton>
        <Logo>ADHD Trading Dashboard</Logo>
      </LeftSection>

      <RightSection>
        <UserMenu>
          <Avatar>JD</Avatar>
        </UserMenu>
      </RightSection>
    </HeaderContainer>
  );
};

export default Header;
