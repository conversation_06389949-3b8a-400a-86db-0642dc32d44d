/**
 * Trade Journal Component
 *
 * This component displays a list of recorded trades with filtering options
 * and allows adding new trades.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { useTradeJournal } from './hooks/useTradeJournal';
import { useTradeFilters } from './hooks/useTradeFilters';
import { TradeJournalHeader, TradeJournalContent } from './components/trade-journal';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

/**
 * TradeJournal Component
 *
 * Main component for the Trade Journal feature that displays trade history
 * with filtering options and provides navigation to add new trades.
 */
const TradeJournal: React.FC = () => {
  const { trades, isLoading, error, refreshTrades } = useTradeJournal();
  const [showFilters, setShowFilters] = useState(false);

  const {
    filters,
    handleFilterChange,
    resetFilters,
    filteredTrades,
    uniqueSetups,
    uniqueModelTypes,
    uniquePrimarySetupTypes,
    uniqueSecondarySetupTypes,
    uniqueLiquidityTypes,
    uniqueDOLTypes,
  } = useTradeFilters(trades);

  return (
    <PageContainer>
      <TradeJournalHeader
        refreshTrades={refreshTrades}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
      />

      <TradeJournalContent
        error={error}
        showFilters={showFilters}
        filteredTrades={filteredTrades}
        isLoading={isLoading}
        filters={filters}
        handleFilterChange={handleFilterChange}
        resetFilters={resetFilters}
        uniqueSetups={uniqueSetups}
        uniqueModelTypes={uniqueModelTypes}
        uniquePrimarySetupTypes={uniquePrimarySetupTypes}
        uniqueSecondarySetupTypes={uniqueSecondarySetupTypes}
        uniqueLiquidityTypes={uniqueLiquidityTypes}
        uniqueDOLTypes={uniqueDOLTypes}
      />
    </PageContainer>
  );
};

export default TradeJournal;
