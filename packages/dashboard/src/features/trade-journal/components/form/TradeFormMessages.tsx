/**
 * Trade Form Messages Component
 *
 * Displays error and success messages for the trade form
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight || 'rgba(244, 67, 54, 0.1)'};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

const SuccessMessage = styled.div`
  color: ${({ theme }) => theme.colors.success};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

const TabInfo = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
`;

interface TradeFormMessagesProps {
  error: string | null;
  success: string | null;
  showTabInfo?: boolean;
}

/**
 * Trade Form Messages Component
 */
const TradeFormMessages: React.FC<TradeFormMessagesProps> = ({
  error,
  success,
  showTabInfo = true,
}) => {
  return (
    <>
      {error && <ErrorMessage>{error}</ErrorMessage>}
      {success && <SuccessMessage>{success}</SuccessMessage>}
      {showTabInfo && <TabInfo>* Required tab for saving trade</TabInfo>}
    </>
  );
};

export default TradeFormMessages;
