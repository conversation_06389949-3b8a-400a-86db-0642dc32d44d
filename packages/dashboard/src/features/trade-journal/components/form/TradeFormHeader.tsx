/**
 * Trade Form Header Component
 *
 * Displays the header for the trade form with title and actions
 */

import React from 'react';
import styled from 'styled-components';
import { TradeFormValues } from '../../types';

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

interface TradeFormHeaderProps {
  isNewTrade: boolean;
  formValues: TradeFormValues;
}

/**
 * Trade Form Header Component
 */
const TradeFormHeader: React.FC<TradeFormHeaderProps> = ({ isNewTrade, formValues }) => {
  return (
    <PageHeader>
      <Title>
        {isNewTrade ? 'Add New Trade' : `Edit Trade: ${formValues.symbol} (${formValues.date})`}
      </Title>
    </PageHeader>
  );
};

export default TradeFormHeader;
