/**
 * Trade Form Actions Component
 *
 * Displays the action buttons for the trade form
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

const Button = styled.button`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 500;
  cursor: pointer;
  transition: background-color ${({ theme }) => theme.transitions.fast};
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.textSecondary};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const SubmitButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.primary};
  border: none;
  color: white;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

interface TradeFormActionsProps {
  isSubmitting: boolean;
  isLoading: boolean;
  isNewTrade: boolean;
}

/**
 * Trade Form Actions Component
 */
const TradeFormActions: React.FC<TradeFormActionsProps> = ({
  isSubmitting,
  isLoading,
  isNewTrade,
}) => {
  const navigate = useNavigate();

  return (
    <ButtonGroup>
      <CancelButton
        type="button"
        onClick={() => {
          console.log('Cancel button clicked, navigating to journal');
          navigate('/journal');
        }}
      >
        Cancel
      </CancelButton>
      <SubmitButton
        type="submit"
        disabled={isSubmitting || isLoading}
        data-testid={isNewTrade ? 'add-trade-button' : 'update-trade-button'}
      >
        {isSubmitting ? 'Saving...' : isNewTrade ? 'Add Trade' : 'Update Trade'}
      </SubmitButton>
    </ButtonGroup>
  );
};

export default TradeFormActions;
