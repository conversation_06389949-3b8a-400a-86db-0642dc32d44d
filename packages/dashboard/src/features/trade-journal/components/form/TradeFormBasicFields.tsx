/**
 * Trade Form Basic Fields Component
 *
 * Displays the basic information fields for the trade form
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const ValidationError = styled.span`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: 2px;
`;

interface TradeFormBasicFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
  calculateProfitLoss?: () => void;
}

/**
 * Trade Form Basic Fields Component
 */
const TradeFormBasicFields: React.FC<TradeFormBasicFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
  calculateProfitLoss,
}) => {
  // Handle changes that should trigger profit calculation
  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
    if (calculateProfitLoss) {
      // Use setTimeout to ensure the form values are updated before calculation
      setTimeout(calculateProfitLoss, 0);
    }
  };

  return (
    <>
      <FormRow>
        <FormGroup>
          <Label htmlFor="date">Date</Label>
          <Input
            id="date"
            name="date"
            type="date"
            value={formValues.date}
            onChange={handleChange}
            required
          />
          {validationErrors.date && <ValidationError>{validationErrors.date}</ValidationError>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="symbol">Symbol</Label>
          <Input
            id="symbol"
            name="symbol"
            type="text"
            value={formValues.symbol}
            onChange={handleChange}
            required
          />
          {validationErrors.symbol && <ValidationError>{validationErrors.symbol}</ValidationError>}
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="direction">Direction</Label>
          <Select
            id="direction"
            name="direction"
            value={formValues.direction}
            onChange={handleChange}
            required
          >
            <option value="long">Long</option>
            <option value="short">Short</option>
          </Select>
        </FormGroup>

        <FormGroup>
          <Label htmlFor="result">Result</Label>
          <Select
            id="result"
            name="result"
            value={formValues.result}
            onChange={handleChange}
            required
          >
            <option value="win">Win</option>
            <option value="loss">Loss</option>
            <option value="breakeven">Breakeven</option>
          </Select>
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="entryPrice">Entry Price</Label>
          <Input
            id="entryPrice"
            name="entryPrice"
            type="number"
            step="0.01"
            value={formValues.entryPrice}
            onChange={handlePriceChange}
            required
          />
          {validationErrors.entryPrice && (
            <ValidationError>{validationErrors.entryPrice}</ValidationError>
          )}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="exitPrice">Exit Price</Label>
          <Input
            id="exitPrice"
            name="exitPrice"
            type="number"
            step="0.01"
            value={formValues.exitPrice}
            onChange={handlePriceChange}
            required
          />
          {validationErrors.exitPrice && (
            <ValidationError>{validationErrors.exitPrice}</ValidationError>
          )}
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="quantity">Quantity</Label>
          <Input
            id="quantity"
            name="quantity"
            type="number"
            value={formValues.quantity}
            onChange={handlePriceChange}
            required
          />
          {validationErrors.quantity && (
            <ValidationError>{validationErrors.quantity}</ValidationError>
          )}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="profit">Profit/Loss ($)</Label>
          <Input
            id="profit"
            name="profit"
            type="number"
            step="0.01"
            value={formValues.profit}
            onChange={handleChange}
            required
          />
        </FormGroup>
      </FormRow>
    </>
  );
};

export default TradeFormBasicFields;
