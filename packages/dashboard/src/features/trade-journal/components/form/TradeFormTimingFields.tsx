/**
 * Trade Form Timing Fields Component
 *
 * Displays the timing fields for the trade form
 */

import React from 'react';
import styled from 'styled-components';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { SESSION_OPTIONS, MARKET_OPTIONS } from '../../hooks';
import TimePicker from '../TimePicker';
import SelectDropdown from '../SelectDropdown';

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const ValidationError = styled.span`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: 2px;
`;

interface TradeFormTimingFieldsProps {
  formValues: TradeFormValues;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  validationErrors: ValidationErrors;
}

/**
 * Trade Form Timing Fields Component
 */
const TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
}) => {
  return (
    <>
      <FormRow>
        <FormGroup>
          <TimePicker
            id="rdTime"
            name="rdTime"
            label="Risk/Decision Time"
            value={formValues.rdTime || ''}
            onChange={handleChange}
          />
          {validationErrors.rdTime && <ValidationError>{validationErrors.rdTime}</ValidationError>}
        </FormGroup>

        <FormGroup>
          <TimePicker
            id="entryTime"
            name="entryTime"
            label="Entry Time"
            value={formValues.entryTime || ''}
            onChange={handleChange}
          />
          {validationErrors.entryTime && (
            <ValidationError>{validationErrors.entryTime}</ValidationError>
          )}
        </FormGroup>

        <FormGroup>
          <TimePicker
            id="exitTime"
            name="exitTime"
            label="Exit Time"
            value={formValues.exitTime || ''}
            onChange={handleChange}
          />
          {validationErrors.exitTime && (
            <ValidationError>{validationErrors.exitTime}</ValidationError>
          )}
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <SelectDropdown
            id="session"
            name="session"
            label="Session (Time Block)"
            value={formValues.session || ''}
            onChange={handleChange}
            options={SESSION_OPTIONS}
            placeholder="Select Session"
          />
        </FormGroup>

        <FormGroup>
          <SelectDropdown
            id="market"
            name="market"
            label="Market"
            value={formValues.market || 'Stocks'}
            onChange={handleChange}
            options={MARKET_OPTIONS}
          />
        </FormGroup>
      </FormRow>
    </>
  );
};

export default TradeFormTimingFields;
