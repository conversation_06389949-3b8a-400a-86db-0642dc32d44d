/**
 * Trade List Empty Component
 *
 * Displays a message when there are no trades to show
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

const EmptyContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing.xl};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  text-align: center;
`;

const EmptyTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const EmptyDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
  max-width: 500px;
`;

const AddTradeButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 500;
  text-decoration: none;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

interface TradeListEmptyProps {
  filtered?: boolean;
}

/**
 * Trade List Empty Component
 */
const TradeListEmpty: React.FC<TradeListEmptyProps> = ({ filtered }) => {
  return (
    <EmptyContainer>
      <EmptyTitle>{filtered ? 'No matching trades found' : 'No trades yet'}</EmptyTitle>
      <EmptyDescription>
        {filtered
          ? 'Try adjusting your filters to see more results.'
          : 'Start tracking your trades to gain insights into your trading performance.'}
      </EmptyDescription>
      {!filtered && <AddTradeButton to="/trade/new">+ Add Your First Trade</AddTradeButton>}
    </EmptyContainer>
  );
};

export default TradeListEmpty;
