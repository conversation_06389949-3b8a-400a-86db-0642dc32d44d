/**
 * Trade List Row Component
 *
 * Displays a row in the trade list
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { TradeColumn } from '../TradeList';

const TradeItem = styled.div<{ expanded?: boolean }>`
  display: grid;
  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.cardBackground};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: all ${({ theme }) => theme.transitions.fast};
  cursor: ${({ expanded }) => (expanded !== undefined ? 'pointer' : 'default')};
  position: relative;

  &:hover {
    background-color: ${({ theme }) => theme.colors.chartGrid};
  }
`;

interface TradeListRowProps {
  trade: Trade;
  visibleColumns: TradeColumn[];
  expanded?: boolean;
  toggleRowExpansion: (tradeId: string) => void;
}

/**
 * Trade List Row Component
 */
const TradeListRow: React.FC<TradeListRowProps> = ({
  trade,
  visibleColumns,
  expanded,
  toggleRowExpansion,
}) => {
  return (
    <TradeItem expanded={expanded} onClick={() => toggleRowExpansion(trade.id)}>
      {visibleColumns.map((column) => (
        <React.Fragment key={column.id}>{column.accessor(trade)}</React.Fragment>
      ))}
    </TradeItem>
  );
};

export default TradeListRow;
