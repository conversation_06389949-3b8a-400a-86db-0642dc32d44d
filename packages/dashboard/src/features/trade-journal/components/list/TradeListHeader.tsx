/**
 * Trade List Header Component
 *
 * Displays the header for the trade list
 */

import React from 'react';
import styled from 'styled-components';
import { TradeColumn } from '../TradeList';

const TradeHeader = styled.div`
  display: grid;
  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));
  align-items: center;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textSecondary};
  background-color: transparent;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  position: sticky;
  top: 0;
  z-index: 1;
  backdrop-filter: blur(8px);
`;

const TradeDetail = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 ${({ theme }) => theme.spacing.xs};
`;

interface TradeListHeaderProps {
  visibleColumns: TradeColumn[];
}

/**
 * Trade List Header Component
 */
const TradeListHeader: React.FC<TradeListHeaderProps> = ({ visibleColumns }) => {
  return (
    <TradeHeader>
      {visibleColumns.map((column) => (
        <TradeDetail key={column.id}>{column.label}</TradeDetail>
      ))}
    </TradeHeader>
  );
};

export default TradeListHeader;
