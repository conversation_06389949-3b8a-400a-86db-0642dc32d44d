/**
 * Trade List Expanded Row Component
 *
 * Displays expanded details for a trade row
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

const ExpandedContent = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-top: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const ExpandedSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const DetailLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const DetailValue = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const ActionButton = styled(Link)`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  text-decoration: none;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

interface TradeListExpandedRowProps {
  trade: Trade;
}

/**
 * Trade List Expanded Row Component
 */
const TradeListExpandedRow: React.FC<TradeListExpandedRowProps> = ({ trade }) => {
  return (
    <ExpandedContent>
      <ExpandedSection>
        <SectionTitle>Trade Details</SectionTitle>
        <DetailGrid>
          <DetailItem>
            <DetailLabel>Symbol</DetailLabel>
            <DetailValue>{trade.symbol}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Date</DetailLabel>
            <DetailValue>{trade.date}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Direction</DetailLabel>
            <DetailValue>{trade.direction}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Entry Price</DetailLabel>
            <DetailValue>${trade.entry.toFixed(2)}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Exit Price</DetailLabel>
            <DetailValue>${trade.exit.toFixed(2)}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Quantity</DetailLabel>
            <DetailValue>{trade.size}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Profit/Loss</DetailLabel>
            <DetailValue
              style={{
                color: trade.profitLoss > 0 ? 'green' : trade.profitLoss < 0 ? 'red' : 'inherit',
              }}
            >
              ${trade.profitLoss.toFixed(2)}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>R-Multiple</DetailLabel>
            <DetailValue>{trade.rMultiple?.toFixed(2) || 'N/A'}</DetailValue>
          </DetailItem>
        </DetailGrid>
      </ExpandedSection>

      {trade.setup && (
        <ExpandedSection>
          <SectionTitle>Strategy</SectionTitle>
          <DetailGrid>
            <DetailItem>
              <DetailLabel>Setup</DetailLabel>
              <DetailValue>{trade.setup}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Model Type</DetailLabel>
              <DetailValue>{trade.modelType || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Primary Setup</DetailLabel>
              <DetailValue>{trade.primarySetupType || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Secondary Setup</DetailLabel>
              <DetailValue>{trade.secondarySetupType || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Liquidity Taken</DetailLabel>
              <DetailValue>{trade.liquidityTaken || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Pattern Quality</DetailLabel>
              <DetailValue>{trade.patternQuality || 'N/A'}</DetailValue>
            </DetailItem>
          </DetailGrid>
        </ExpandedSection>
      )}

      {trade.dolAnalysis && (
        <ExpandedSection>
          <SectionTitle>DOL Analysis</SectionTitle>
          <DetailGrid>
            <DetailItem>
              <DetailLabel>DOL Type</DetailLabel>
              <DetailValue>{trade.dolAnalysis.dolType || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>DOL Strength</DetailLabel>
              <DetailValue>{trade.dolAnalysis.dolStrength || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>DOL Reaction</DetailLabel>
              <DetailValue>{trade.dolAnalysis.dolReaction || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>DOL Effectiveness</DetailLabel>
              <DetailValue>{trade.dolAnalysis.effectiveness || 'N/A'}</DetailValue>
            </DetailItem>
          </DetailGrid>
        </ExpandedSection>
      )}

      {trade.notes && (
        <ExpandedSection>
          <SectionTitle>Notes</SectionTitle>
          <DetailItem>
            <DetailValue>{trade.notes}</DetailValue>
          </DetailItem>
        </ExpandedSection>
      )}

      <ActionButtons>
        <ActionButton to={`/trade/edit/${trade.id}`}>Edit Trade</ActionButton>
      </ActionButtons>
    </ExpandedContent>
  );
};

export default TradeListExpandedRow;
