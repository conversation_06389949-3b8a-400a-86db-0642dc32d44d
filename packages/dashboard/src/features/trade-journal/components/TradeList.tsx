/**
 * Trade List Component
 *
 * Displays a list of trades with expandable rows
 */

import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { CompleteTradeData } from '../types';
import { useTradeList } from '../hooks/useTradeList';
import {
  TradeListHeader,
  TradeListRow,
  TradeListExpandedRow,
  TradeListEmpty,
  TradeListLoading,
} from './list';

const TradeListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const TradeDetail = styled.div<{ profit?: boolean; loss?: boolean }>`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 ${({ theme }) => theme.spacing.xs};
  color: ${({ theme, profit, loss }) =>
    profit ? theme.colors.success : loss ? theme.colors.danger : theme.colors.textPrimary};
  font-weight: ${({ profit, loss }) => (profit || loss ? 600 : 'normal')};
`;

const Badge = styled.span<{ type?: 'long' | 'short' }>`
  display: inline-block;
  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: 600;
  text-transform: uppercase;
  background-color: ${({ theme, type }) =>
    type === 'long'
      ? theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'
      : type === 'short'
      ? theme.colors.dangerLight || 'rgba(244, 67, 54, 0.1)'
      : theme.colors.background};
  color: ${({ theme, type }) =>
    type === 'long'
      ? theme.colors.success
      : type === 'short'
      ? theme.colors.danger
      : theme.colors.textPrimary};
`;

export interface TradeColumn {
  id: string;
  label: string;
  accessor: (trade: CompleteTradeData) => React.ReactNode;
}

interface TradeListProps {
  trades: CompleteTradeData[];
  isLoading?: boolean;
  expandable?: boolean;
  onEditTrade?: (tradeId: number) => void;
}

/**
 * Trade List Component
 */
const TradeList: React.FC<TradeListProps> = ({
  trades,
  isLoading = false,
  expandable = false,
  onEditTrade,
}) => {
  const navigate = useNavigate();
  const { sortedTrades, toggleRowExpansion, isRowExpanded } = useTradeList(trades, expandable);

  // Handle edit trade
  const handleEditTrade = (tradeId: number) => {
    if (onEditTrade) {
      onEditTrade(tradeId);
    } else {
      // Default navigation to edit page
      navigate(`/trade/edit/${tradeId}`);
    }
  };

  // Define columns for the trade list
  const columns: TradeColumn[] = useMemo(
    () => [
      {
        id: 'date',
        label: 'Date',
        accessor: (trade) => <TradeDetail>{trade.trade.date}</TradeDetail>,
      },
      {
        id: 'symbol',
        label: 'Symbol',
        accessor: (trade) => <TradeDetail>{trade.trade.market || 'MNQ'}</TradeDetail>,
      },
      {
        id: 'direction',
        label: 'Direction',
        accessor: (trade) => (
          <TradeDetail>
            <Badge type={trade.trade.direction.toLowerCase() as 'long' | 'short'}>
              {trade.trade.direction}
            </Badge>
          </TradeDetail>
        ),
      },
      {
        id: 'setup',
        label: 'Setup',
        accessor: (trade) => <TradeDetail>{trade.setup?.primary_setup || 'N/A'}</TradeDetail>,
      },
      {
        id: 'entry',
        label: 'Entry',
        accessor: (trade) => (
          <TradeDetail>${(trade.trade.entry_price || 0).toFixed(2)}</TradeDetail>
        ),
      },
      {
        id: 'exit',
        label: 'Exit',
        accessor: (trade) => <TradeDetail>${(trade.trade.exit_price || 0).toFixed(2)}</TradeDetail>,
      },
      {
        id: 'size',
        label: 'Size',
        accessor: (trade) => <TradeDetail>{trade.trade.no_of_contracts || 0}</TradeDetail>,
      },
      {
        id: 'profitLoss',
        label: 'P/L',
        accessor: (trade) => (
          <TradeDetail
            profit={(trade.trade.achieved_pl || 0) > 0}
            loss={(trade.trade.achieved_pl || 0) < 0}
          >
            ${(trade.trade.achieved_pl || 0).toFixed(2)}
          </TradeDetail>
        ),
      },
      {
        id: 'actions',
        label: 'Actions',
        accessor: (trade) => (
          <TradeDetail>
            <button
              onClick={() => handleEditTrade(trade.trade.id!)}
              style={{
                padding: '4px 8px',
                fontSize: '12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                background: 'white',
                cursor: 'pointer',
              }}
            >
              Edit
            </button>
          </TradeDetail>
        ),
      },
    ],
    [handleEditTrade]
  );

  // Set grid template columns based on number of columns
  const gridTemplateColumns = `repeat(${columns.length}, 1fr)`;

  // If loading, show loading state
  if (isLoading) {
    return <TradeListLoading rowCount={5} />;
  }

  // If no trades, show empty state
  if (!sortedTrades || sortedTrades.length === 0) {
    return <TradeListEmpty filtered={trades && trades.length > 0} />;
  }

  return (
    <TradeListContainer style={{ '--grid-template-columns': gridTemplateColumns } as any}>
      <TradeListHeader visibleColumns={columns} />

      {sortedTrades.map((trade) => (
        <React.Fragment key={trade.trade.id}>
          <TradeListRow
            trade={trade}
            visibleColumns={columns}
            expanded={isRowExpanded(trade.trade.id!)}
            toggleRowExpansion={toggleRowExpansion}
          />
          {isRowExpanded(trade.trade.id!) && <TradeListExpandedRow trade={trade} />}
        </React.Fragment>
      ))}
    </TradeListContainer>
  );
};

export default TradeList;
