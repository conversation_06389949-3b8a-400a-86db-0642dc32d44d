/**
 * TimePicker Component
 *
 * A reusable time picker component for selecting hours and minutes.
 */

import React from "react";
import styled from "styled-components";

interface TimePickerProps {
  id: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  min?: string;
  max?: string;
}

const TimePickerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const TimeInput = styled.input`
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.surface};
  transition: border-color ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.chartGrid};
    cursor: not-allowed;
  }
`;

/**
 * TimePicker Component
 *
 * A reusable time picker component for selecting hours and minutes.
 */
const TimePicker: React.FC<TimePickerProps> = ({
  id,
  name,
  value,
  onChange,
  label,
  required = false,
  disabled = false,
  className,
  placeholder = "HH:MM",
  min,
  max,
}) => {
  return (
    <TimePickerContainer className={className}>
      {label && (
        <Label htmlFor={id}>
          {label}
          {required && <span style={{ color: "red" }}> *</span>}
        </Label>
      )}
      <TimeInput
        id={id}
        name={name}
        type="time"
        value={value}
        onChange={onChange}
        required={required}
        disabled={disabled}
        placeholder={placeholder}
        min={min}
        max={max}
      />
    </TimePickerContainer>
  );
};

export default TimePicker;
