/**
 * Trade Journal Filters Component
 *
 * Displays the filters for the trade journal
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { FilterState } from '../../types';

const FilterSection = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const FilterLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const FilterSelect = styled.select`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.textPrimary};
  min-width: 150px;
`;

const FilterInput = styled.input`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const FilterButton = styled.button`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  align-self: flex-end;

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

interface TradeJournalFiltersProps {
  filters: FilterState;
  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  resetFilters: () => void;
  uniqueSetups: string[];
  uniqueModelTypes: string[];
  uniquePrimarySetupTypes: string[];
  uniqueSecondarySetupTypes: string[];
  uniqueLiquidityTypes: string[];
  uniqueDOLTypes: string[];
}

/**
 * Trade Journal Filters Component
 */
const TradeJournalFilters: React.FC<TradeJournalFiltersProps> = ({
  filters,
  handleFilterChange,
  resetFilters,
  uniqueSetups,
  uniqueModelTypes,
  uniquePrimarySetupTypes,
  uniqueSecondarySetupTypes,
  uniqueLiquidityTypes,
  uniqueDOLTypes,
}) => {
  return (
    <FilterSection>
      <FilterGroup>
        <FilterLabel htmlFor="symbol">Symbol</FilterLabel>
        <FilterInput
          id="symbol"
          name="symbol"
          value={filters.symbol}
          onChange={handleFilterChange}
          placeholder="AAPL, MSFT, etc."
        />
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="direction">Direction</FilterLabel>
        <FilterSelect
          id="direction"
          name="direction"
          value={filters.direction}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          <option value="Long">Long</option>
          <option value="Short">Short</option>
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="setup">Setup</FilterLabel>
        <FilterSelect id="setup" name="setup" value={filters.setup} onChange={handleFilterChange}>
          <option value="">All</option>
          {uniqueSetups.map((setup) => (
            <option key={setup} value={setup}>
              {setup}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="modelType">Model Type</FilterLabel>
        <FilterSelect
          id="modelType"
          name="modelType"
          value={filters.modelType}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          {uniqueModelTypes.map((modelType) => (
            <option key={modelType} value={modelType}>
              {modelType}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="result">Result</FilterLabel>
        <FilterSelect
          id="result"
          name="result"
          value={filters.result}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          <option value="win">Wins</option>
          <option value="loss">Losses</option>
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="dateFrom">From Date</FilterLabel>
        <FilterInput
          id="dateFrom"
          name="dateFrom"
          type="date"
          value={filters.dateFrom}
          onChange={handleFilterChange}
        />
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="dateTo">To Date</FilterLabel>
        <FilterInput
          id="dateTo"
          name="dateTo"
          type="date"
          value={filters.dateTo}
          onChange={handleFilterChange}
        />
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="primarySetupType">Primary Setup</FilterLabel>
        <FilterSelect
          id="primarySetupType"
          name="primarySetupType"
          value={filters.primarySetupType}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          {uniquePrimarySetupTypes.map((setupType) => (
            <option key={setupType} value={setupType}>
              {setupType}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="secondarySetupType">Secondary Setup</FilterLabel>
        <FilterSelect
          id="secondarySetupType"
          name="secondarySetupType"
          value={filters.secondarySetupType}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          {uniqueSecondarySetupTypes.map((setupType) => (
            <option key={setupType} value={setupType}>
              {setupType}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="liquidityTaken">Liquidity Taken</FilterLabel>
        <FilterSelect
          id="liquidityTaken"
          name="liquidityTaken"
          value={filters.liquidityTaken}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          {uniqueLiquidityTypes.map((liquidityType) => (
            <option key={liquidityType} value={liquidityType}>
              {liquidityType}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="patternQualityMin">Pattern Quality Min</FilterLabel>
        <FilterSelect
          id="patternQualityMin"
          name="patternQualityMin"
          value={filters.patternQualityMin}
          onChange={handleFilterChange}
        >
          <option value="">Any</option>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
            <option key={`min-${rating}`} value={rating}>
              {rating}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="patternQualityMax">Pattern Quality Max</FilterLabel>
        <FilterSelect
          id="patternQualityMax"
          name="patternQualityMax"
          value={filters.patternQualityMax}
          onChange={handleFilterChange}
        >
          <option value="">Any</option>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
            <option key={`max-${rating}`} value={rating}>
              {rating}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="dolType">DOL Type</FilterLabel>
        <FilterSelect
          id="dolType"
          name="dolType"
          value={filters.dolType}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          {uniqueDOLTypes.map((dolType) => (
            <option key={dolType} value={dolType}>
              {dolType}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="dolEffectivenessMin">DOL Effectiveness Min</FilterLabel>
        <FilterSelect
          id="dolEffectivenessMin"
          name="dolEffectivenessMin"
          value={filters.dolEffectivenessMin}
          onChange={handleFilterChange}
        >
          <option value="">Any</option>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
            <option key={`dol-min-${rating}`} value={rating}>
              {rating}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterGroup>
        <FilterLabel htmlFor="dolEffectivenessMax">DOL Effectiveness Max</FilterLabel>
        <FilterSelect
          id="dolEffectivenessMax"
          name="dolEffectivenessMax"
          value={filters.dolEffectivenessMax}
          onChange={handleFilterChange}
        >
          <option value="">Any</option>
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
            <option key={`dol-max-${rating}`} value={rating}>
              {rating}
            </option>
          ))}
        </FilterSelect>
      </FilterGroup>

      <FilterButton onClick={resetFilters}>Reset Filters</FilterButton>
    </FilterSection>
  );
};

export default TradeJournalFilters;
