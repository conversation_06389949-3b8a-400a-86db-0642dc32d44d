/**
 * Trade Journal Content Component
 *
 * Displays the content section of the trade journal
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import TradeList from '../TradeList';
import TradeJournalFilters from './TradeJournalFilters';

const ContentSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.danger};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

interface TradeJournalContentProps {
  error: string | null;
  showFilters: boolean;
  filteredTrades: Trade[];
  isLoading: boolean;
  filters: any;
  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  resetFilters: () => void;
  uniqueSetups: string[];
  uniqueModelTypes: string[];
  uniquePrimarySetupTypes: string[];
  uniqueSecondarySetupTypes: string[];
  uniqueLiquidityTypes: string[];
  uniqueDOLTypes: string[];
}

/**
 * Trade Journal Content Component
 */
const TradeJournalContent: React.FC<TradeJournalContentProps> = ({
  error,
  showFilters,
  filteredTrades,
  isLoading,
  filters,
  handleFilterChange,
  resetFilters,
  uniqueSetups,
  uniqueModelTypes,
  uniquePrimarySetupTypes,
  uniqueSecondarySetupTypes,
  uniqueLiquidityTypes,
  uniqueDOLTypes,
}) => {
  return (
    <>
      {error && <ErrorMessage>{error}</ErrorMessage>}

      <ContentSection>
        <SectionTitle>Recent Trades</SectionTitle>

        {showFilters && (
          <TradeJournalFilters
            filters={filters}
            handleFilterChange={handleFilterChange}
            resetFilters={resetFilters}
            uniqueSetups={uniqueSetups}
            uniqueModelTypes={uniqueModelTypes}
            uniquePrimarySetupTypes={uniquePrimarySetupTypes}
            uniqueSecondarySetupTypes={uniqueSecondarySetupTypes}
            uniqueLiquidityTypes={uniqueLiquidityTypes}
            uniqueDOLTypes={uniqueDOLTypes}
          />
        )}

        <TradeList trades={filteredTrades} isLoading={isLoading} expandable={true} />
      </ContentSection>
    </>
  );
};

export default TradeJournalContent;
