/**
 * Trade Journal Header Component
 *
 * Displays the header for the trade journal with title and actions
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
`;

const ActionButton = styled.button`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const RefreshButton = styled(ActionButton)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const AddTradeButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 500;
  text-decoration: none;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

interface TradeJournalHeaderProps {
  refreshTrades?: () => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
}

/**
 * Trade Journal Header Component
 */
const TradeJournalHeader: React.FC<TradeJournalHeaderProps> = ({
  refreshTrades,
  showFilters,
  setShowFilters,
}) => {
  return (
    <PageHeader>
      <Title>Trade Journal</Title>
      <HeaderActions>
        <RefreshButton onClick={() => refreshTrades && refreshTrades()}>↻ Refresh</RefreshButton>
        <ActionButton onClick={() => setShowFilters(!showFilters)}>
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </ActionButton>
        <AddTradeButton to="/trade/new">+ Add Trade</AddTradeButton>
      </HeaderActions>
    </PageHeader>
  );
};

export default TradeJournalHeader;
