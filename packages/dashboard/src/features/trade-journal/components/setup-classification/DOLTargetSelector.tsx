/**
 * DOL Target Selector Component
 * 
 * Component for selecting the DOL target type and specific type
 */

import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { 
  DOL_TARGET_OPTIONS, 
  getDOLTargetOptions 
} from '../../constants/setupClassification';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const GuidanceNote = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
  padding-left: ${({ theme }) => theme.spacing.md};
  border-left: 3px solid ${({ theme }) => theme.colors.border};
`;

const RadioOption = styled.div`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const RadioInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing.sm};
`;

const RadioLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

interface DOLTargetSelectorProps {
  value: {
    targetType?: string;
    specificType?: string;
  };
  onChange: (field: string, value: string) => void;
}

const DOLTargetSelector: React.FC<DOLTargetSelectorProps> = ({ value, onChange }) => {
  const [specificOptions, setSpecificOptions] = useState<{ value: string; label: string }[]>([]);

  // Update specific options when target type changes
  useEffect(() => {
    if (value.targetType && value.targetType !== 'RD Target') {
      setSpecificOptions(getDOLTargetOptions(value.targetType));
    } else {
      setSpecificOptions([]);
    }
  }, [value.targetType]);

  // Handle target type change
  const handleTargetTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newTargetType = e.target.value;
    onChange('dolTargetType', newTargetType);
    
    // Reset specific type when target type changes
    onChange('specificDOLType', '');
  };

  // Handle specific type change
  const handleSpecificTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange('specificDOLType', e.target.value);
  };

  return (
    <SelectorContainer>
      <SectionTitle>DOL Target Type</SectionTitle>
      
      <GuidanceNote>
        Select the primary target type for your Draw on Liquidity (DOL) analysis.
      </GuidanceNote>
      
      <FormGroup>
        <Label htmlFor="dolTargetType">DOL Target Type:</Label>
        <Select
          id="dolTargetType"
          name="dolTargetType"
          value={value.targetType || ''}
          onChange={handleTargetTypeChange}
        >
          {DOL_TARGET_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </FormGroup>
      
      {value.targetType && value.targetType !== 'RD Target' && specificOptions.length > 0 && (
        <>
          <FormGroup>
            <Label>
              {value.targetType === 'FVG Target' ? 'FVG Target Type:' : 'Liquidity Target Type:'}
            </Label>
            <RadioGroup>
              {specificOptions.map((option) => (
                option.value && (
                  <RadioOption key={option.value}>
                    <RadioInput
                      type="radio"
                      id={`dolTarget_${option.value}`}
                      name="specificDOLType"
                      value={option.value}
                      checked={value.specificType === option.value}
                      onChange={handleSpecificTypeChange}
                    />
                    <RadioLabel htmlFor={`dolTarget_${option.value}`}>
                      {option.label}
                    </RadioLabel>
                  </RadioOption>
                )
              ))}
            </RadioGroup>
          </FormGroup>
        </>
      )}
    </SelectorContainer>
  );
};

export default DOLTargetSelector;
