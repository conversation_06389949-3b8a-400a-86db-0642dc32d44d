/**
 * Liquidity Selector Component
 * 
 * Component for selecting the liquidity taken/swept
 */

import React from 'react';
import styled from 'styled-components';
import { LIQUIDITY_OPTIONS } from '../../constants/setupClassification';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const GuidanceNote = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

interface LiquiditySelectorProps {
  value: string;
  onChange: (field: string, value: string) => void;
}

const LiquiditySelector: React.FC<LiquiditySelectorProps> = ({ value, onChange }) => {
  // Handle liquidity change
  const handleLiquidityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange('liquidityTaken', e.target.value);
  };

  return (
    <SelectorContainer>
      <SectionTitle>Liquidity Taken</SectionTitle>
      
      <GuidanceNote>
        If your trade took/swept a specific type of liquidity, select it below.
      </GuidanceNote>
      
      <FormGroup>
        <Label htmlFor="liquidityTaken">Liquidity Taken/Swept:</Label>
        <Select
          id="liquidityTaken"
          name="liquidityTaken"
          value={value || ''}
          onChange={handleLiquidityChange}
        >
          {LIQUIDITY_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </FormGroup>
    </SelectorContainer>
  );
};

export default LiquiditySelector;
