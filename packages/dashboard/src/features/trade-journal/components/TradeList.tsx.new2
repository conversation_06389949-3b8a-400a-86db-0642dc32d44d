  // State for columns and expanded rows
  const [columns, setColumns] = useState<TradeColumn[]>(
    defaultColumns || defaultTradeColumns
  );
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [showColumnToggle, setShowColumnToggle] = useState(false);

  // Toggle column visibility
  const toggleColumn = (columnId: string) => {
    setColumns(
      columns.map((col) =>
        col.id === columnId ? { ...col, visible: !col.visible } : col
      )
    );
  };

  // Toggle row expansion
  const toggleRowExpansion = (tradeId: string) => {
    if (!expandable) return;
    
    setExpandedRows((prev) => ({
      ...prev,
      [tradeId]: !prev[tradeId],
    }));
  };

  // Get visible columns
  const visibleColumns = columns.filter((col) => col.visible);

  // Set grid template columns CSS variable
  const gridTemplateColumns = visibleColumns
    .map((col) => col.width || "1fr")
    .join(" ");

  // Loading state
  if (isLoading) {
    return <Placeholder>Loading trades...</Placeholder>;
  }

  // Empty state
  if (!trades || trades.length === 0) {
    return (
      <Placeholder>
        No trades found. Add your first trade to get started.
      </Placeholder>
    );
  }

  return (
    <ListContainer>
      {/* Column toggle */}
      <Button onClick={() => setShowColumnToggle(!showColumnToggle)}>
        {showColumnToggle ? "Hide Columns" : "Configure Columns"}
      </Button>
      
      {showColumnToggle && (
        <ColumnToggle>
          {columns.map((column) => (
            <ColumnToggleButton
              key={column.id}
              active={column.visible}
              onClick={() => toggleColumn(column.id)}
            >
              {column.label || column.id}
            </ColumnToggleButton>
          ))}
        </ColumnToggle>
      )}

      {/* Table header */}
      <div style={{ "--grid-template-columns": gridTemplateColumns } as any}>
        <TradeHeader>
          {visibleColumns.map((column) => (
            <TradeDetail key={column.id}>{column.label}</TradeDetail>
          ))}
        </TradeHeader>

        {/* Table rows */}
        {trades.map((trade) => (
          <React.Fragment key={trade.id}>
            <TradeItem
              expanded={expandable ? expandedRows[trade.id] : undefined}
              onClick={() => toggleRowExpansion(trade.id)}
            >
              {visibleColumns.map((column) => (
                <React.Fragment key={column.id}>
                  {column.accessor(trade)}
                </React.Fragment>
              ))}
            </TradeItem>
            
            {/* Expanded content */}
            {expandable && expandedRows[trade.id] && (
              <ExpandedContent>
                <ExpandedSection>
                  <SectionTitle>Trade Details</SectionTitle>
                  <DetailItem>
                    <DetailLabel>Entry Price</DetailLabel>
                    <DetailValue>${trade.entry}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Exit Price</DetailLabel>
                    <DetailValue>${trade.exit}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Stop Loss</DetailLabel>
                    <DetailValue>${trade.stopLoss}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Take Profit</DetailLabel>
                    <DetailValue>${trade.takeProfit}</DetailValue>
                  </DetailItem>
                </ExpandedSection>
                
                <ExpandedSection>
                  <SectionTitle>Strategy</SectionTitle>
                  <DetailItem>
                    <DetailLabel>Model Type</DetailLabel>
                    <DetailValue>{trade.modelType || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Setup</DetailLabel>
                    <DetailValue>{trade.setup || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Entry Version</DetailLabel>
                    <DetailValue>{trade.entryVersion || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Pattern Quality</DetailLabel>
                    <DetailValue>{trade.patternQuality || "-"}</DetailValue>
                  </DetailItem>
                </ExpandedSection>
                
                <ExpandedSection>
                  <SectionTitle>Timing</SectionTitle>
                  <DetailItem>
                    <DetailLabel>Session</DetailLabel>
                    <DetailValue>{trade.session || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Entry Time</DetailLabel>
                    <DetailValue>{trade.entryTime || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Exit Time</DetailLabel>
                    <DetailValue>{trade.exitTime || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>RD Time</DetailLabel>
                    <DetailValue>{trade.rdTime || "-"}</DetailValue>
                  </DetailItem>
                </ExpandedSection>
                
                <ExpandedSection>
                  <SectionTitle>Risk Management</SectionTitle>
                  <DetailItem>
                    <DetailLabel>R-Multiple</DetailLabel>
                    <DetailValue>{trade.rMultiple || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Risk Points</DetailLabel>
                    <DetailValue>{trade.riskPoints || "-"}</DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Market</DetailLabel>
                    <DetailValue>{trade.market || "-"}</DetailValue>
                  </DetailItem>
                </ExpandedSection>
              </ExpandedContent>
            )}
          </React.Fragment>
        ))}
      </div>
    </ListContainer>
  );
};

export default TradeList;
