/**
 * FVG Selector Component
 * 
 * Component for selecting additional FVGs present in the trade
 */

import React from 'react';
import styled from 'styled-components';
import { 
  TIME_BASED_FVG_OPTIONS,
  CURRENT_SESSION_FVG_OPTIONS,
  PREV_DAY_FVG_OPTIONS,
  THREE_DAY_FVG_OPTIONS,
  SPECIAL_FVG_OPTIONS
} from '../../constants/setupClassification';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const GuidanceNote = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const CheckboxGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const CategoryGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const CategoryTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.sm} 0;
  padding-bottom: ${({ theme }) => theme.spacing.xs};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const CheckboxOption = styled.div`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const CheckboxInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing.sm};
`;

const CheckboxLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

interface FVGSelectorProps {
  value: string[];
  onChange: (field: string, value: string[]) => void;
}

const FVGSelector: React.FC<FVGSelectorProps> = ({ value, onChange }) => {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fvgType = e.target.value;
    const isChecked = e.target.checked;
    
    let newValue: string[];
    
    if (isChecked) {
      // Add to array if checked
      newValue = [...value, fvgType];
    } else {
      // Remove from array if unchecked
      newValue = value.filter(item => item !== fvgType);
    }
    
    onChange('additionalFVGs', newValue);
  };

  return (
    <SelectorContainer>
      <SectionTitle>Additional FVGs Present</SectionTitle>
      
      <GuidanceNote>
        Select all additional FVG types that were present in this trade setup.
      </GuidanceNote>
      
      <CheckboxGroup>
        {/* Time-based FVGs */}
        <CategoryGroup>
          <CategoryTitle>Time-frame FVGs:</CategoryTitle>
          {TIME_BASED_FVG_OPTIONS.map((option) => (
            <CheckboxOption key={option.value}>
              <CheckboxInput
                type="checkbox"
                id={`fvg_${option.value}`}
                name="additionalFVGs"
                value={option.value}
                checked={value.includes(option.value)}
                onChange={handleCheckboxChange}
              />
              <CheckboxLabel htmlFor={`fvg_${option.value}`}>
                {option.label}
              </CheckboxLabel>
            </CheckboxOption>
          ))}
        </CategoryGroup>
        
        {/* Current Session FPFVGs */}
        <CategoryGroup>
          <CategoryTitle>Current Session FPFVGs:</CategoryTitle>
          {CURRENT_SESSION_FVG_OPTIONS.map((option) => (
            <CheckboxOption key={option.value}>
              <CheckboxInput
                type="checkbox"
                id={`fvg_${option.value}`}
                name="additionalFVGs"
                value={option.value}
                checked={value.includes(option.value)}
                onChange={handleCheckboxChange}
              />
              <CheckboxLabel htmlFor={`fvg_${option.value}`}>
                {option.label}
              </CheckboxLabel>
            </CheckboxOption>
          ))}
        </CategoryGroup>
        
        {/* Previous Day FPFVGs */}
        <CategoryGroup>
          <CategoryTitle>Previous Day FPFVGs:</CategoryTitle>
          {PREV_DAY_FVG_OPTIONS.map((option) => (
            <CheckboxOption key={option.value}>
              <CheckboxInput
                type="checkbox"
                id={`fvg_${option.value}`}
                name="additionalFVGs"
                value={option.value}
                checked={value.includes(option.value)}
                onChange={handleCheckboxChange}
              />
              <CheckboxLabel htmlFor={`fvg_${option.value}`}>
                {option.label}
              </CheckboxLabel>
            </CheckboxOption>
          ))}
        </CategoryGroup>
        
        {/* 3Day FPFVGs */}
        <CategoryGroup>
          <CategoryTitle>3-Day FPFVGs:</CategoryTitle>
          {THREE_DAY_FVG_OPTIONS.map((option) => (
            <CheckboxOption key={option.value}>
              <CheckboxInput
                type="checkbox"
                id={`fvg_${option.value}`}
                name="additionalFVGs"
                value={option.value}
                checked={value.includes(option.value)}
                onChange={handleCheckboxChange}
              />
              <CheckboxLabel htmlFor={`fvg_${option.value}`}>
                {option.label}
              </CheckboxLabel>
            </CheckboxOption>
          ))}
        </CategoryGroup>
        
        {/* Special FVGs */}
        <CategoryGroup>
          <CategoryTitle>Special FVG Types:</CategoryTitle>
          {SPECIAL_FVG_OPTIONS.map((option) => (
            <CheckboxOption key={option.value}>
              <CheckboxInput
                type="checkbox"
                id={`fvg_${option.value}`}
                name="additionalFVGs"
                value={option.value}
                checked={value.includes(option.value)}
                onChange={handleCheckboxChange}
              />
              <CheckboxLabel htmlFor={`fvg_${option.value}`}>
                {option.label}
              </CheckboxLabel>
            </CheckboxOption>
          ))}
        </CategoryGroup>
      </CheckboxGroup>
    </SelectorContainer>
  );
};

export default FVGSelector;
