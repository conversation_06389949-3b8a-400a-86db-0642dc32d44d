/**
 * Primary Setup Selector Component
 * 
 * Component for selecting the primary setup category and type
 */

import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { 
  SETUP_CATEGORY_OPTIONS, 
  getSetupOptionsByCategory 
} from '../../constants/setupClassification';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const GuidanceNote = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
  padding-left: ${({ theme }) => theme.spacing.md};
  border-left: 3px solid ${({ theme }) => theme.colors.border};
`;

const RadioOption = styled.div`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const RadioInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing.sm};
`;

const RadioLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

interface PrimarySetupSelectorProps {
  value: {
    category?: string;
    type?: string;
  };
  onChange: (field: string, value: string) => void;
}

const PrimarySetupSelector: React.FC<PrimarySetupSelectorProps> = ({ value, onChange }) => {
  const [setupOptions, setSetupOptions] = useState<{ value: string; label: string }[]>([]);

  // Update setup options when category changes
  useEffect(() => {
    if (value.category) {
      setSetupOptions(getSetupOptionsByCategory(value.category));
    } else {
      setSetupOptions([]);
    }
  }, [value.category]);

  // Handle category change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newCategory = e.target.value;
    onChange('primarySetupCategory', newCategory);
    
    // Reset setup type when category changes
    onChange('primarySetupType', '');
  };

  // Handle setup type change
  const handleSetupTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange('primarySetupType', e.target.value);
  };

  return (
    <SelectorContainer>
      <SectionTitle>Primary Setup Classification</SectionTitle>
      
      <GuidanceNote>
        This is the main element that drove your entry decision. Think about
        the most critical factor that made you enter the trade.
      </GuidanceNote>
      
      <FormGroup>
        <Label htmlFor="primarySetupCategory">Select Primary Category:</Label>
        <Select
          id="primarySetupCategory"
          name="primarySetupCategory"
          value={value.category || ''}
          onChange={handleCategoryChange}
        >
          <option value="">-- Select Primary Category --</option>
          {SETUP_CATEGORY_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </FormGroup>
      
      {value.category && setupOptions.length > 0 && (
        <RadioGroup>
          {setupOptions.map((option) => (
            <RadioOption key={option.value}>
              <RadioInput
                type="radio"
                id={`primary_${option.value}`}
                name="primarySetupType"
                value={option.value}
                checked={value.type === option.value}
                onChange={handleSetupTypeChange}
              />
              <RadioLabel htmlFor={`primary_${option.value}`}>
                {option.label}
              </RadioLabel>
            </RadioOption>
          ))}
        </RadioGroup>
      )}
    </SelectorContainer>
  );
};

export default PrimarySetupSelector;
