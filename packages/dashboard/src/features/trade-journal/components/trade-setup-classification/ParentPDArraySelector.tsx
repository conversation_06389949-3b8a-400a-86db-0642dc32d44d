/**
 * Parent PD Array Selector Component
 * 
 * Component for selecting the parent PD array
 */

import React from 'react';
import styled from 'styled-components';
import { PARENT_PD_ARRAY_OPTIONS } from '../../constants/setupClassification';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const GuidanceNote = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-left: 4px solid ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

interface ParentPDArraySelectorProps {
  value: string;
  onChange: (field: string, value: string) => void;
}

const ParentPDArraySelector: React.FC<ParentPDArraySelectorProps> = ({ value, onChange }) => {
  // Handle parent PD array change
  const handleParentPDArrayChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange('parentPDArray', e.target.value);
  };

  return (
    <SelectorContainer>
      <SectionTitle>Parent PD Array</SectionTitle>
      
      <GuidanceNote>
        If your trade used a specific Parent PD Array, please select it below.
        This is often relevant for Structure-Based Setups.
      </GuidanceNote>
      
      <FormGroup>
        <Label htmlFor="parentPDArray">Parent PD Array:</Label>
        <Select
          id="parentPDArray"
          name="parentPDArray"
          value={value || ''}
          onChange={handleParentPDArrayChange}
        >
          {PARENT_PD_ARRAY_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </FormGroup>
    </SelectorContainer>
  );
};

export default ParentPDArraySelector;
