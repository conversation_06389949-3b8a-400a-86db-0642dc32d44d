/**
 * <PERSON><PERSON> Detailed Analysis Component
 * 
 * Component for entering detailed DOL analysis information
 */

import React from 'react';
import styled from 'styled-components';
import { 
  DOL_PRICE_ACTION_DESCRIPTIONS, 
  DOL_VOLUME_PROFILE_DESCRIPTIONS,
  DOL_TIME_OF_DAY_DESCRIPTION,
  DOL_MARKET_STRUCTURE_DESCRIPTION
} from '../../constants/dolAnalysis';

const AnalysisContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const Description = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 80px;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.background};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

interface DOLDetailedAnalysisProps {
  formValues: {
    dolType?: string;
    dolStrength?: string;
    dolPriceAction?: string;
    dolVolumeProfile?: string;
    dolTimeOfDay?: string;
    dolMarketStructure?: string;
  };
  onChange: (field: string, value: string) => void;
}

const DOLDetailedAnalysis: React.FC<DOLDetailedAnalysisProps> = ({ formValues, onChange }) => {
  // Get the appropriate price action description based on DOL type
  const getPriceActionDescription = () => {
    if (formValues.dolType && DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType as keyof typeof DOL_PRICE_ACTION_DESCRIPTIONS]) {
      return DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType as keyof typeof DOL_PRICE_ACTION_DESCRIPTIONS];
    }
    return 'Describe the price action during the liquidity interaction.';
  };

  // Get the appropriate volume profile description based on DOL strength
  const getVolumeProfileDescription = () => {
    if (formValues.dolStrength && DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength as keyof typeof DOL_VOLUME_PROFILE_DESCRIPTIONS]) {
      return DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength as keyof typeof DOL_VOLUME_PROFILE_DESCRIPTIONS];
    }
    return 'Describe the volume profile during the liquidity interaction.';
  };

  // Handle text area change
  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.name, e.target.value);
  };

  return (
    <AnalysisContainer>
      <SectionTitle>Detailed Analysis</SectionTitle>
      <Description>
        Provide detailed information about the liquidity interaction.
      </Description>
      
      <FormGroup>
        <Label htmlFor="dolPriceAction">Price Action</Label>
        <Description>{getPriceActionDescription()}</Description>
        <TextArea
          id="dolPriceAction"
          name="dolPriceAction"
          value={formValues.dolPriceAction || ''}
          onChange={handleTextAreaChange}
          placeholder="Describe the price action..."
        />
      </FormGroup>
      
      <FormGroup>
        <Label htmlFor="dolVolumeProfile">Volume Profile</Label>
        <Description>{getVolumeProfileDescription()}</Description>
        <TextArea
          id="dolVolumeProfile"
          name="dolVolumeProfile"
          value={formValues.dolVolumeProfile || ''}
          onChange={handleTextAreaChange}
          placeholder="Describe the volume profile..."
        />
      </FormGroup>
      
      <FormGroup>
        <Label htmlFor="dolTimeOfDay">Time of Day Significance</Label>
        <Description>{DOL_TIME_OF_DAY_DESCRIPTION}</Description>
        <TextArea
          id="dolTimeOfDay"
          name="dolTimeOfDay"
          value={formValues.dolTimeOfDay || ''}
          onChange={handleTextAreaChange}
          placeholder="Describe the time of day significance..."
        />
      </FormGroup>
      
      <FormGroup>
        <Label htmlFor="dolMarketStructure">Market Structure</Label>
        <Description>{DOL_MARKET_STRUCTURE_DESCRIPTION}</Description>
        <TextArea
          id="dolMarketStructure"
          name="dolMarketStructure"
          value={formValues.dolMarketStructure || ''}
          onChange={handleTextAreaChange}
          placeholder="Describe the market structure..."
        />
      </FormGroup>
    </AnalysisContainer>
  );
};

export default DOLDetailedAnalysis;
