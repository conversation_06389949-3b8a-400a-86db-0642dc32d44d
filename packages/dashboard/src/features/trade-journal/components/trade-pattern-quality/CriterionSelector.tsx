/**
 * Criterion Selector Component
 * 
 * Component for selecting a score for a specific pattern quality criterion
 */

import React from 'react';
import styled from 'styled-components';
import { ScoreRange } from '../../types';
import { SCORE_RANGE_OPTIONS, PATTERN_QUALITY_CRITERIA } from '../../constants/patternQuality';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const CriterionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const CriterionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;

const RadioOption = styled.div`
  display: flex;
  align-items: flex-start;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const RadioInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing.sm};
  margin-top: 3px;
`;

const RadioLabelContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const RadioLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const RadioDescription = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: 2px;
`;

interface CriterionSelectorProps {
  criterion: keyof typeof PATTERN_QUALITY_CRITERIA;
  value: ScoreRange | '';
  onChange: (field: string, value: string) => void;
  fieldName: string;
}

const CriterionSelector: React.FC<CriterionSelectorProps> = ({ 
  criterion, 
  value, 
  onChange,
  fieldName
}) => {
  const criterionData = PATTERN_QUALITY_CRITERIA[criterion];

  // Handle score change
  const handleScoreChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(fieldName, e.target.value);
  };

  return (
    <SelectorContainer>
      <CriterionTitle>{criterionData.title}</CriterionTitle>
      <CriterionDescription>{criterionData.description}</CriterionDescription>
      
      <RadioGroup>
        {SCORE_RANGE_OPTIONS.map((option) => (
          <RadioOption key={option.value}>
            <RadioInput
              type="radio"
              id={`${fieldName}_${option.value}`}
              name={fieldName}
              value={option.value}
              checked={value === option.value}
              onChange={handleScoreChange}
            />
            <RadioLabelContainer>
              <RadioLabel htmlFor={`${fieldName}_${option.value}`}>
                {option.label}
              </RadioLabel>
              <RadioDescription>
                {criterionData[option.value as ScoreRange]}
              </RadioDescription>
            </RadioLabelContainer>
          </RadioOption>
        ))}
      </RadioGroup>
    </SelectorContainer>
  );
};

export default CriterionSelector;
