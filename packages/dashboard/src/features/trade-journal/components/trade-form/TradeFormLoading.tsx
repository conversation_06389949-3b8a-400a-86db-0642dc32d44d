/**
 * Trade Form Loading Component
 *
 * Displays a loading overlay for the trade form
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`;

const LoadingSpinner = styled.div`
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid ${({ theme }) => theme.colors.primary};
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: ${({ theme }) => theme.spacing.md};

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const LoadingText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

interface TradeFormLoadingProps {
  isLoading: boolean;
}

/**
 * Trade Form Loading Component
 */
const TradeFormLoading: React.FC<TradeFormLoadingProps> = ({ isLoading }) => {
  if (!isLoading) return null;

  return (
    <LoadingOverlay>
      <LoadingSpinner />
      <LoadingText>Loading trade data...</LoadingText>
    </LoadingOverlay>
  );
};

export default TradeFormLoading;
