/**
 * TradeList Component
 *
 * Displays a list of trades in the trade journal with configurable columns.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { Trade } from '../types';

// Column definition for trade list
export interface TradeColumn {
  id: string;
  label: string;
  accessor: (trade: Trade) => React.ReactNode;
  visible: boolean;
  sortable?: boolean;
  width?: string;
}

interface TradeListProps {
  trades: Trade[];
  isLoading: boolean;
  defaultColumns?: TradeColumn[];
  expandable?: boolean;
}

const ListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const TradeItem = styled.div<{ expanded?: boolean }>`
  display: grid;
  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.cardBackground};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: all ${({ theme }) => theme.transitions.fast};
  cursor: ${({ expanded }) => (expanded !== undefined ? 'pointer' : 'default')};
  position: relative;

  &:hover {
    background-color: ${({ theme }) => theme.colors.chartGrid};
  }
`;

const TradeHeader = styled.div`
  display: grid;
  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));
  align-items: center;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textSecondary};
  background-color: transparent;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  position: sticky;
  top: 0;
  z-index: 1;
  backdrop-filter: blur(8px);
`;

const TradeDetail = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 ${({ theme }) => theme.spacing.xs};
`;

const TradeProfit = styled.div<{ isProfit: boolean }>`
  font-weight: 500;
  color: ${({ isProfit, theme }) => (isProfit ? theme.colors.success : theme.colors.danger)};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 ${({ theme }) => theme.spacing.xs};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  justify-content: flex-end;
`;

const Button = styled.button`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  font-size: ${({ theme }) => theme.fontSizes.sm};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary};
    border-color: ${({ theme }) => theme.colors.primary};
    color: white;
  }
`;

const ViewButton = styled(Button)``;

const ExpandButton = styled(Button)<{ expanded: boolean }>`
  background-color: ${({ expanded, theme }) => (expanded ? theme.colors.primary : 'transparent')};
  color: ${({ expanded }) => (expanded ? 'white' : 'inherit')};
  border-color: ${({ expanded, theme }) => (expanded ? theme.colors.primary : theme.colors.border)};
`;

const ExpandedContent = styled.div`
  grid-column: 1 / -1;
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const ExpandedSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const SectionTitle = styled.h4`
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-weight: 600;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const DetailLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const DetailValue = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const ColumnToggle = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

const ColumnToggleButton = styled.button<{ active: boolean }>`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  background-color: ${({ active, theme }) => (active ? theme.colors.primary : 'transparent')};
  color: ${({ active, theme }) => (active ? 'white' : theme.colors.textSecondary)};
  border: 1px solid ${({ active, theme }) => (active ? theme.colors.primary : theme.colors.border)};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ active, theme }) =>
      active ? theme.colors.primaryDark : theme.colors.background};
  }
`;

const Placeholder = styled.div`
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

/**
 * TradeList Component
 *
 * Displays a list of trades from the journal with configurable columns,
 * expandable rows, and ability to view trade details.
 */
const TradeList: React.FC<TradeListProps> = ({
  trades,
  isLoading,
  defaultColumns,
  expandable = true,
}) => {
  // Default columns if not provided
  const defaultTradeColumns: TradeColumn[] = [
    {
      id: "symbol",
      label: "Symbol",
      accessor: (trade) => <TradeDetail>{trade.symbol}</TradeDetail>,
      visible: true,
      sortable: true,
    },
    {
      id: "date",
      label: "Date",
      accessor: (trade) => (
        <TradeDetail>{new Date(trade.date).toLocaleDateString()}</TradeDetail>
      ),
      visible: true,
      sortable: true,
    },
    {
      id: "direction",
      label: "Direction",
      accessor: (trade) => <TradeDetail>{trade.direction}</TradeDetail>,
      visible: true,
      sortable: true,
    },
    {
      id: "size",
      label: "Size",
      accessor: (trade) => <TradeDetail>{trade.size}</TradeDetail>,
      visible: true,
      sortable: true,
    },
    {
      id: "profitLoss",
      label: "P/L",
      accessor: (trade) => (
        <TradeProfit isProfit={trade.profitLoss > 0}>
          ${trade.profitLoss.toFixed(2)}
        </TradeProfit>
      ),
      visible: true,
      sortable: true,
    },
    {
      id: "setup",
      label: "Setup",
      accessor: (trade) => <TradeDetail>{trade.setup || "-"}</TradeDetail>,
      visible: false,
      sortable: true,
    },
    {
      id: "modelType",
      label: "Model",
      accessor: (trade) => <TradeDetail>{trade.modelType || "-"}</TradeDetail>,
      visible: false,
      sortable: true,
    },
    {
      id: "rMultiple",
      label: "R-Multiple",
      accessor: (trade) => <TradeDetail>{trade.rMultiple || "-"}</TradeDetail>,
      visible: false,
      sortable: true,
    },
    {
      id: "actions",
      label: "",
      accessor: (trade) => (
        <ActionButtons>
          <ViewButton
            onClick={(e) => {
              e.stopPropagation();
              window.location.href = `/trade/${trade.id}`;
            }}
          >
            View
          </ViewButton>
        </ActionButtons>
      ),
      visible: true,
      sortable: false,
    },
  ];
