/**
 * DOL Analysis Component
 * 
 * Main component for the DOL analysis section that combines all the individual components
 */

import React from 'react';
import styled from 'styled-components';
import DOLTypeSelector from './DOLTypeSelector';
import DOLStrengthSelector from './DOLStrengthSelector';
import DOLReactionSelector from './DOLReactionSelector';
import DOLContextSelector from './DOLContextSelector';
import DOLDetailedAnalysis from './DOLDetailedAnalysis';
import DOLEffectivenessRating from './DOLEffectivenessRating';

const AnalysisContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const Introduction = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const IntroTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const IntroText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;

const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;

const ValidationError = styled.div`
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.errorLight};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

interface DOLAnalysisProps {
  formValues: {
    dolType?: string;
    dolStrength?: string;
    dolReaction?: string;
    dolContext?: string[];
    dolPriceAction?: string;
    dolVolumeProfile?: string;
    dolTimeOfDay?: string;
    dolMarketStructure?: string;
    dolEffectiveness?: string;
    dolNotes?: string;
  };
  onChange: (field: string, value: any) => void;
  validationErrors: {
    [key: string]: string;
  };
}

const DOLAnalysis: React.FC<DOLAnalysisProps> = ({
  formValues,
  onChange,
  validationErrors,
}) => {
  return (
    <AnalysisContainer>
      <Introduction>
        <IntroTitle>Draw on Liquidity (DOL) Analysis</IntroTitle>
        <IntroText>
          Analyze how price interacted with liquidity levels in this trade. This analysis
          will help you understand market behavior around key levels and improve your
          ability to anticipate price movements.
        </IntroText>
      </Introduction>

      {/* Display validation errors if any */}
      {validationErrors.dolAnalysis && (
        <ValidationError>{validationErrors.dolAnalysis}</ValidationError>
      )}

      <Divider />

      {/* DOL Type Selector */}
      <DOLTypeSelector
        value={formValues.dolType || ''}
        onChange={onChange}
      />

      <Divider />

      {/* DOL Strength Selector */}
      <DOLStrengthSelector
        value={formValues.dolStrength || ''}
        onChange={onChange}
      />

      <Divider />

      {/* DOL Reaction Selector */}
      <DOLReactionSelector
        value={formValues.dolReaction || ''}
        onChange={onChange}
      />

      <Divider />

      {/* DOL Context Selector */}
      <DOLContextSelector
        value={formValues.dolContext || []}
        onChange={onChange}
      />

      <Divider />

      {/* DOL Detailed Analysis */}
      <DOLDetailedAnalysis
        formValues={formValues}
        onChange={onChange}
      />

      <Divider />

      {/* DOL Effectiveness Rating */}
      <DOLEffectivenessRating
        value={formValues.dolEffectiveness || '5'}
        notes={formValues.dolNotes || ''}
        onChange={onChange}
      />
    </AnalysisContainer>
  );
};

export default DOLAnalysis;
