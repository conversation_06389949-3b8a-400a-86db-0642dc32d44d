/**
 * DOL Type Selector Component
 * 
 * Component for selecting the DOL type
 */

import React from 'react';
import styled from 'styled-components';
import { DOL_TYPE_OPTIONS } from '../../constants/dolAnalysis';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const Description = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;

const RadioOption = styled.div`
  display: flex;
  align-items: flex-start;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const RadioInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing.sm};
  margin-top: 3px;
`;

const RadioLabelContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const RadioLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const RadioDescription = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: 2px;
`;

interface DOLTypeSelectorProps {
  value: string;
  onChange: (field: string, value: string) => void;
}

const DOLTypeSelector: React.FC<DOLTypeSelectorProps> = ({ value, onChange }) => {
  // Handle DOL type change
  const handleDOLTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange('dolType', e.target.value);
  };

  return (
    <SelectorContainer>
      <SectionTitle>DOL Type</SectionTitle>
      <Description>
        Select the type of liquidity interaction that occurred in this trade.
      </Description>
      
      <RadioGroup>
        {DOL_TYPE_OPTIONS.map((option) => {
          const [label, description] = option.label.split(' - ');
          
          return (
            <RadioOption key={option.value}>
              <RadioInput
                type="radio"
                id={`dolType_${option.value}`}
                name="dolType"
                value={option.value}
                checked={value === option.value}
                onChange={handleDOLTypeChange}
              />
              <RadioLabelContainer>
                <RadioLabel htmlFor={`dolType_${option.value}`}>
                  {label}
                </RadioLabel>
                <RadioDescription>
                  {description}
                </RadioDescription>
              </RadioLabelContainer>
            </RadioOption>
          );
        })}
      </RadioGroup>
    </SelectorContainer>
  );
};

export default DOLTypeSelector;
