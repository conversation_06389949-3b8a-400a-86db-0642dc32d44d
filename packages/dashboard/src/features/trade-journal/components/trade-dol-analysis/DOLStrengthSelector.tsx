/**
 * DOL Strength Selector Component
 * 
 * Component for selecting the DOL strength
 */

import React from 'react';
import styled from 'styled-components';
import { DOL_STRENGTH_OPTIONS } from '../../constants/dolAnalysis';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const Description = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;

const RadioOption = styled.div`
  display: flex;
  align-items: flex-start;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const RadioInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing.sm};
  margin-top: 3px;
`;

const RadioLabelContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const RadioLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const RadioDescription = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: 2px;
`;

interface DOLStrengthSelectorProps {
  value: string;
  onChange: (field: string, value: string) => void;
}

const DOLStrengthSelector: React.FC<DOLStrengthSelectorProps> = ({ value, onChange }) => {
  // Handle DOL strength change
  const handleDOLStrengthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange('dolStrength', e.target.value);
  };

  return (
    <SelectorContainer>
      <SectionTitle>DOL Strength</SectionTitle>
      <Description>
        Evaluate the strength of the liquidity interaction.
      </Description>
      
      <RadioGroup>
        {DOL_STRENGTH_OPTIONS.map((option) => {
          const [label, description] = option.label.split(' - ');
          
          return (
            <RadioOption key={option.value}>
              <RadioInput
                type="radio"
                id={`dolStrength_${option.value}`}
                name="dolStrength"
                value={option.value}
                checked={value === option.value}
                onChange={handleDOLStrengthChange}
              />
              <RadioLabelContainer>
                <RadioLabel htmlFor={`dolStrength_${option.value}`}>
                  {label}
                </RadioLabel>
                <RadioDescription>
                  {description}
                </RadioDescription>
              </RadioLabelContainer>
            </RadioOption>
          );
        })}
      </RadioGroup>
    </SelectorContainer>
  );
};

export default DOLStrengthSelector;
