/**
 * DOL Context Selector Component
 * 
 * Component for selecting the DOL context
 */

import React from 'react';
import styled from 'styled-components';
import { DOL_CONTEXT_OPTIONS } from '../../constants/dolAnalysis';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const Description = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0;
`;

const CheckboxGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;

const CheckboxOption = styled.div`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const CheckboxInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing.sm};
`;

const CheckboxLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

interface DOLContextSelectorProps {
  value: string[];
  onChange: (field: string, value: string[]) => void;
}

const DOLContextSelector: React.FC<DOLContextSelectorProps> = ({ value, onChange }) => {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const contextValue = e.target.value;
    const isChecked = e.target.checked;
    
    let newValue: string[];
    
    if (isChecked) {
      // Add to array if checked
      newValue = [...value, contextValue];
    } else {
      // Remove from array if unchecked
      newValue = value.filter(item => item !== contextValue);
    }
    
    onChange('dolContext', newValue);
  };

  return (
    <SelectorContainer>
      <SectionTitle>DOL Context</SectionTitle>
      <Description>
        Select all contextual factors that apply to this liquidity interaction.
      </Description>
      
      <CheckboxGroup>
        {DOL_CONTEXT_OPTIONS.map((option) => (
          <CheckboxOption key={option.value}>
            <CheckboxInput
              type="checkbox"
              id={`dolContext_${option.value}`}
              name="dolContext"
              value={option.value}
              checked={value.includes(option.value)}
              onChange={handleCheckboxChange}
            />
            <CheckboxLabel htmlFor={`dolContext_${option.value}`}>
              {option.label}
            </CheckboxLabel>
          </CheckboxOption>
        ))}
      </CheckboxGroup>
    </SelectorContainer>
  );
};

export default DOLContextSelector;
