# Setup Classification Feature Documentation

This document provides an overview of the Setup Classification feature integrated into the Trade Journal.

## Overview

The Setup Classification feature allows traders to categorize their trades using a structured classification system. This helps in identifying patterns, analyzing performance, and improving trading strategies based on specific setup types.

## Data Model

The Setup Classification feature extends the Trade data model with the following fields:

| Field | Type | Description |
|-------|------|-------------|
| `primarySetupCategory` | `SetupCategoryType` | The primary category of the setup (structure, session, model) |
| `primarySetupType` | `string` | The specific type of the primary setup |
| `secondarySetupCategory` | `SetupCategoryType` | The secondary category of the setup |
| `secondarySetupType` | `string` | The specific type of the secondary setup |
| `liquidityTaken` | `LiquidityType` | The type of liquidity taken/swept in the trade |
| `additionalFVGs` | `FVGType[]` | Array of additional FVG types present in the trade |
| `dolTargetType` | `DOLTargetType` | The type of DOL (Draw on Liquidity) target |
| `specificDOLType` | `string` | The specific type of DOL target |
| `parentPDArray` | `ParentPDArrayType` | The parent PD array used in the trade |

## Setup Categories

### Structure-Based Setups
- High/Low Reversal Setup
- FVG Redelivery Setup
- Strong-FVG Reversal Setup
- NWOG Reaction Setup
- NDOG Reaction Setup
- Multi-Array Confluence Setup

### Session-Based Setups
- Opening Range Setup (09:30-10:10)
- Morning Session Setup (10:50-11:10)
- Lunch Macro Setup (11:45-13:15)
- Afternoon Session Setup (13:30-15:00)
- MOC Setup (15:30-16:15)

### Model-Specific Setups
- Simple FVG-RD
- Complex FVG-RD
- True-RD Continuation
- IMM-RD Continuation
- Dispersed-RD Continuation
- Wide-Gap-RD Continuation

## Components

### PrimarySetupSelector
- Allows selection of the primary setup category and type
- Displays radio buttons for setup types based on the selected category

### SecondarySetupSelector
- Allows selection of the secondary setup category and type
- Validates that the secondary setup is different from the primary setup

### LiquiditySelector
- Allows selection of the liquidity taken/swept in the trade

### FVGSelector
- Allows selection of multiple FVG types present in the trade
- Organizes FVG types into categories for easier selection

### DOLTargetSelector
- Allows selection of the DOL target type and specific type
- Dynamically updates specific type options based on the selected target type

### ParentPDArraySelector
- Allows selection of the parent PD array used in the trade

### SetupClassificationSection
- Main component that combines all the individual components
- Handles validation and error display

## Integration

The Setup Classification feature is integrated into the Trade Journal in the following ways:

1. **TradeForm**: The Strategy tab includes a Setup Classification section with all the components
2. **TradeList**: The expanded view includes a Setup Classification section with all the fields
3. **TradeJournal**: The filter section includes filters for primary setup, secondary setup, and liquidity taken

## Validation Rules

1. If a primary setup category is selected, a primary setup type must also be selected
2. If a secondary setup category is selected, a secondary setup type must also be selected
3. The primary and secondary setup types must be different
4. If a DOL target type is selected (except for RD Target), a specific DOL type must also be selected

## Data Flow

1. User selects a primary setup category
2. Available primary setup types are updated based on the selected category
3. User selects a primary setup type
4. User selects a secondary setup category
5. Available secondary setup types are updated based on the selected category
6. User selects a secondary setup type
7. User selects a liquidity type
8. User selects additional FVG types
9. User selects a DOL target type
10. Available specific DOL types are updated based on the selected target type
11. User selects a specific DOL type
12. User selects a parent PD array
13. Form validation is performed
14. Data is saved to the trade record

## Usage Guidelines

1. **Primary Setup**: Select the main element that drove your entry decision
2. **Secondary Setup**: Select the supporting element that added context or confirmation
3. **Liquidity Taken**: Select the type of liquidity that was taken/swept in the trade
4. **Additional FVGs**: Select all FVG types that were present in the trade setup
5. **DOL Target**: Select the primary target type for your Draw on Liquidity analysis
6. **Parent PD Array**: Select the parent PD array used in the trade

## API Changes

The following API changes were made to support the Setup Classification feature:

1. New fields added to the Trade interface
2. New fields added to the TradeFormValues interface
3. New type definitions for SetupCategoryType, StructureSetupType, SessionSetupType, ModelSetupType, LiquidityType, FVGType, DOLTargetType, and ParentPDArrayType
4. New constants for setup classification options
5. New components for setup classification
6. Updated validation logic in the useTradeForm hook
7. Updated TradeList component to display setup classification information
8. Updated TradeJournal component to include filters for setup classification fields
