/**
 * useTradeFilters Hook
 *
 * Custom hook for managing trade filters
 */

import { useState, useMemo } from 'react';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { FilterState, CompleteTradeData } from '../types';

/**
 * Hook for managing trade filters
 * @param trades The trades to filter
 */
export function useTradeFilters(trades: CompleteTradeData[]) {
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    symbol: '',
    direction: '',
    setup: '',
    modelType: '',
    result: '',
    dateFrom: '',
    dateTo: '',
    primarySetupType: '',
    secondarySetupType: '',
    liquidityTaken: '',
    patternQualityMin: '',
    patternQualityMax: '',
    dolType: '',
    dolEffectivenessMin: '',
    dolEffectivenessMax: '',
  });

  // Handle filter changes
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      symbol: '',
      direction: '',
      setup: '',
      modelType: '',
      result: '',
      dateFrom: '',
      dateTo: '',
      primarySetupType: '',
      secondarySetupType: '',
      liquidityTaken: '',
      patternQualityMin: '',
      patternQualityMax: '',
      dolType: '',
      dolEffectivenessMin: '',
      dolEffectivenessMax: '',
    });
  };

  // Apply filters to trades
  const filteredTrades = useMemo(() => {
    if (!trades) return [];

    return trades.filter((tradeData) => {
      const { trade, setup, analysis } = tradeData;

      // Symbol filter (using market as symbol)
      if (
        filters.symbol &&
        trade.market &&
        !trade.market.toLowerCase().includes(filters.symbol.toLowerCase())
      ) {
        return false;
      }

      // Direction filter
      if (filters.direction && trade.direction !== filters.direction) {
        return false;
      }

      // Setup filter
      if (filters.setup && setup?.primary_setup !== filters.setup) {
        return false;
      }

      // Model type filter
      if (filters.modelType && trade.model_type !== filters.modelType) {
        return false;
      }

      // Result filter (win/loss)
      if (filters.result) {
        const isWin = (trade.achieved_pl || 0) > 0;
        if ((filters.result === 'win' && !isWin) || (filters.result === 'loss' && isWin)) {
          return false;
        }
      }

      // Date range filter
      if (filters.dateFrom) {
        const tradeDate = new Date(trade.date);
        const fromDate = new Date(filters.dateFrom);
        if (tradeDate < fromDate) {
          return false;
        }
      }

      if (filters.dateTo) {
        const tradeDate = new Date(trade.date);
        const toDate = new Date(filters.dateTo);
        // Set time to end of day
        toDate.setHours(23, 59, 59, 999);
        if (tradeDate > toDate) {
          return false;
        }
      }

      // Primary Setup Type filter
      if (filters.primarySetupType && setup?.primary_setup !== filters.primarySetupType) {
        return false;
      }

      // Secondary Setup Type filter
      if (filters.secondarySetupType && setup?.secondary_setup !== filters.secondarySetupType) {
        return false;
      }

      // Liquidity Taken filter
      if (filters.liquidityTaken && setup?.liquidity_taken !== filters.liquidityTaken) {
        return false;
      }

      // Pattern Quality Min filter
      if (filters.patternQualityMin && trade.pattern_quality_rating) {
        const minQuality = parseInt(filters.patternQualityMin);
        if (!isNaN(minQuality) && trade.pattern_quality_rating < minQuality) {
          return false;
        }
      }

      // Pattern Quality Max filter
      if (filters.patternQualityMax && trade.pattern_quality_rating) {
        const maxQuality = parseInt(filters.patternQualityMax);
        if (!isNaN(maxQuality) && trade.pattern_quality_rating > maxQuality) {
          return false;
        }
      }

      // DOL Type filter
      if (filters.dolType && analysis?.dol_target_type !== filters.dolType) {
        return false;
      }

      // DOL Effectiveness Min filter (not available in new schema, skip)
      if (filters.dolEffectivenessMin) {
        // This field doesn't exist in the new schema, so we'll skip this filter
      }

      // DOL Effectiveness Max filter (not available in new schema, skip)
      if (filters.dolEffectivenessMax) {
        // This field doesn't exist in the new schema, so we'll skip this filter
      }

      return true;
    });
  }, [trades, filters]);

  // Get unique values for filter dropdowns
  const uniqueSetups = useMemo(() => {
    if (!trades) return [];
    const setups = trades
      .map((tradeData) => tradeData.setup?.primary_setup)
      .filter((setup): setup is string => !!setup);
    return Array.from(new Set(setups));
  }, [trades]);

  const uniqueModelTypes = useMemo(() => {
    if (!trades) return [];
    const modelTypes = trades
      .map((tradeData) => tradeData.trade.model_type)
      .filter((modelType): modelType is string => !!modelType);
    return Array.from(new Set(modelTypes));
  }, [trades]);

  const uniquePrimarySetupTypes = useMemo(() => {
    if (!trades) return [];
    const setupTypes = trades
      .map((tradeData) => tradeData.setup?.primary_setup)
      .filter((setupType): setupType is string => !!setupType);
    return Array.from(new Set(setupTypes));
  }, [trades]);

  const uniqueSecondarySetupTypes = useMemo(() => {
    if (!trades) return [];
    const setupTypes = trades
      .map((tradeData) => tradeData.setup?.secondary_setup)
      .filter((setupType): setupType is string => !!setupType);
    return Array.from(new Set(setupTypes));
  }, [trades]);

  const uniqueLiquidityTypes = useMemo(() => {
    if (!trades) return [];
    const liquidityTypes = trades
      .map((tradeData) => tradeData.setup?.liquidity_taken)
      .filter((liquidityType): liquidityType is string => !!liquidityType);
    return Array.from(new Set(liquidityTypes));
  }, [trades]);

  const uniqueDOLTypes = useMemo(() => {
    if (!trades) return [];
    const dolTypes = trades
      .filter((tradeData) => tradeData.analysis)
      .map((tradeData) => tradeData.analysis?.dol_target_type)
      .filter((dolType): dolType is string => !!dolType);
    return Array.from(new Set(dolTypes));
  }, [trades]);

  return {
    filters,
    setFilters,
    handleFilterChange,
    resetFilters,
    filteredTrades,
    uniqueSetups,
    uniqueModelTypes,
    uniquePrimarySetupTypes,
    uniqueSecondarySetupTypes,
    uniqueLiquidityTypes,
    uniqueDOLTypes,
  };
}

export type TradeFiltersHook = ReturnType<typeof useTradeFilters>;
