/**
 * Trade Journal Hooks
 *
 * This file exports all hooks used in the trade journal feature
 */

import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

// Export the main useTradeForm hook
export { useTradeForm } from './useTradeForm';

// Export dropdown options from useTradeForm
export {
  MODEL_TYPE_OPTIONS,
  SESSION_OPTIONS,
  SETUP_OPTIONS,
  MARKET_OPTIONS,
  ENTRY_VERSION_OPTIONS,
  PATTERN_QUALITY_OPTIONS,
} from './useTradeForm';

// Export individual hooks
export { useTradeFormData } from './useTradeFormData';
export { useTradeValidation } from './useTradeValidation';
export { useTradeCalculations } from './useTradeCalculations';
export { useTradeSubmission } from './useTradeSubmission';

// Export hook types
export type { TradeFormDataHook } from './useTradeFormData';
export type { TradeValidationHook } from './useTradeValidation';
export type { TradeCalculationsHook } from './useTradeCalculations';
export type { TradeSubmissionHook } from './useTradeSubmission';
