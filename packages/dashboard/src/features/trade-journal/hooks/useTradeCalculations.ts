/**
 * Trade Calculations Hook
 *
 * Custom hook for calculating trade metrics
 */

import { useEffect } from 'react';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { TradeFormValues } from '../types';

/**
 * Hook for calculating trade metrics
 * @param formValues The form values to calculate metrics for
 * @param setFormValues Function to update form values
 */
export function useTradeCalculations(
  formValues: TradeFormValues,
  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>
) {
  // Calculate R-Multiple when risk points and profit change
  useEffect(() => {
    if (formValues.riskPoints && formValues.profit) {
      const riskPoints = parseFloat(formValues.riskPoints);
      const profit = parseFloat(formValues.profit);

      if (riskPoints > 0) {
        const rMultiple = (profit / riskPoints).toFixed(2);
        setFormValues((prev) => ({
          ...prev,
          rMultiple,
        }));
      }
    }
  }, [formValues.riskPoints, formValues.profit, setFormValues]);

  // Validate time relationships
  useEffect(() => {
    // This effect doesn't update form values directly,
    // but it's related to calculations and validation of time relationships
    // The actual validation errors are handled in useTradeValidation
  }, [formValues.entryTime, formValues.exitTime, formValues.rdTime]);

  /**
   * Calculate profit/loss based on entry price, exit price, and quantity
   * This function can be called when those values change
   */
  const calculateProfitLoss = () => {
    if (formValues.entryPrice && formValues.exitPrice && formValues.quantity) {
      const entryPrice = parseFloat(formValues.entryPrice);
      const exitPrice = parseFloat(formValues.exitPrice);
      const quantity = parseFloat(formValues.quantity);

      if (!isNaN(entryPrice) && !isNaN(exitPrice) && !isNaN(quantity)) {
        let profitLoss: number;

        if (formValues.direction === 'long') {
          profitLoss = (exitPrice - entryPrice) * quantity;
        } else {
          profitLoss = (entryPrice - exitPrice) * quantity;
        }

        setFormValues((prev) => ({
          ...prev,
          profit: profitLoss.toFixed(2),
          result: profitLoss > 0 ? 'win' : profitLoss < 0 ? 'loss' : 'breakeven',
        }));
      }
    }
  };

  return {
    calculateProfitLoss,
  };
}

export type TradeCalculationsHook = ReturnType<typeof useTradeCalculations>;
