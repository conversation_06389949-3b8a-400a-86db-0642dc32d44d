/**
 * SettingItem Component
 *
 * Reusable component for individual settings
 */

import React, { ReactNode } from "react";
import styled from "styled-components";

interface SettingItemProps {
  label: string;
  description?: string;
  control: ReactNode;
}

const SettingContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  &:last-child {
    border-bottom: none;
  }
`;

const SettingRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SettingLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const SettingDescription = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: ${({ theme }) => theme.spacing.xs};
`;

/**
 * SettingItem Component
 *
 * A single setting item with label, description, and control
 */
const SettingItem: React.FC<SettingItemProps> = ({
  label,
  description,
  control,
}) => {
  return (
    <SettingContainer>
      <SettingRow>
        <div>
          <SettingLabel>{label}</SettingLabel>
          {description && (
            <SettingDescription>{description}</SettingDescription>
          )}
        </div>
        {control}
      </SettingRow>
    </SettingContainer>
  );
};

export default SettingItem;
