/**
 * Trade Analysis Context
 *
 * Context for managing trade analysis state
 */

import React, {
  createContext,
  useContext,
  useReducer,
  ReactNode,
  useCallback,
  useEffect,
} from 'react';
import { useLocalStorage } from '@adhd-trading-dashboard/shared';
import {
  TradeAnalysisState,
  TradeAnalysisAction,
  TradeAnalysisData,
  TradeFilters,
  UserPreferences,
} from '../types';
import { fetchTradeAnalysisData } from '../services/tradeAnalysisApi';

// Get default date range
const getDefaultDateRange = () => {
  const today = new Date();
  const startDate = new Date();
  startDate.setMonth(today.getMonth() - 1); // Default to last month

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: today.toISOString().split('T')[0],
  };
};

// Initial state
const initialState: TradeAnalysisState = {
  data: null,
  filters: {
    dateRange: getDefaultDateRange(),
  },
  preferences: {
    defaultDateRange: 'month',
    defaultView: 'summary',
    chartTypes: {
      performance: 'bar',
      distribution: 'pie',
      timeAnalysis: 'bar',
    },
    tableColumns: [
      'symbol',
      'direction',
      'entryTime',
      'exitTime',
      'profitLoss',
      'status',
      'strategy',
    ],
    favoriteStrategies: [],
    favoriteTags: [],
  },
  isLoading: false,
  error: null,
  selectedTradeId: null,
};

// Reducer function
const tradeAnalysisReducer = (
  state: TradeAnalysisState,
  action: TradeAnalysisAction
): TradeAnalysisState => {
  switch (action.type) {
    case 'FETCH_DATA_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'FETCH_DATA_SUCCESS':
      return {
        ...state,
        data: action.payload,
        isLoading: false,
        error: null,
      };
    case 'FETCH_DATA_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case 'UPDATE_FILTERS':
      return {
        ...state,
        filters: {
          ...state.filters,
          ...action.payload,
        },
      };
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload,
        },
      };
    case 'SELECT_TRADE':
      return {
        ...state,
        selectedTradeId: action.payload,
      };
    case 'RESET_FILTERS':
      return {
        ...state,
        filters: {
          dateRange: getDefaultDateRange(),
        },
      };
    default:
      return state;
  }
};

// Context
interface TradeAnalysisContextType extends TradeAnalysisState {
  fetchData: () => Promise<void>;
  updateFilters: (filters: Partial<TradeFilters>) => void;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  selectTrade: (tradeId: string | null) => void;
  resetFilters: () => void;
}

const TradeAnalysisContext = createContext<TradeAnalysisContextType | undefined>(undefined);

// Provider component
interface TradeAnalysisProviderProps {
  children: ReactNode;
}

export const TradeAnalysisProvider: React.FC<TradeAnalysisProviderProps> = ({ children }) => {
  // Load saved preferences from localStorage
  const [savedPreferences] = useLocalStorage<Partial<UserPreferences>>(
    'trade-analysis-preferences',
    {}
  );

  // Merge saved preferences with initial state
  const mergedInitialState = {
    ...initialState,
    preferences: {
      ...initialState.preferences,
      ...savedPreferences,
    },
  };

  const [state, dispatch] = useReducer(tradeAnalysisReducer, mergedInitialState);

  // Save preferences to localStorage when they change
  useEffect(() => {
    localStorage.setItem('trade-analysis-preferences', JSON.stringify(state.preferences));
  }, [state.preferences]);

  const fetchData = useCallback(async () => {
    dispatch({ type: 'FETCH_DATA_START' });
    try {
      const data = await fetchTradeAnalysisData(state.filters);
      dispatch({ type: 'FETCH_DATA_SUCCESS', payload: data });
    } catch (error) {
      dispatch({
        type: 'FETCH_DATA_ERROR',
        payload: error instanceof Error ? error.message : 'An unknown error occurred',
      });
    }
  }, [state.filters]);

  const updateFilters = useCallback((filters: Partial<TradeFilters>) => {
    dispatch({ type: 'UPDATE_FILTERS', payload: filters });
  }, []);

  const updatePreferences = useCallback((preferences: Partial<UserPreferences>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  }, []);

  const selectTrade = useCallback((tradeId: string | null) => {
    dispatch({ type: 'SELECT_TRADE', payload: tradeId });
  }, []);

  const resetFilters = useCallback(() => {
    dispatch({ type: 'RESET_FILTERS' });
  }, []);

  // Fetch data when filters change
  useEffect(() => {
    fetchData();
  }, [fetchData, state.filters]);

  const value = {
    ...state,
    fetchData,
    updateFilters,
    updatePreferences,
    selectTrade,
    resetFilters,
  };

  return <TradeAnalysisContext.Provider value={value}>{children}</TradeAnalysisContext.Provider>;
};

// Custom hook for using the context
export const useTradeAnalysis = (): TradeAnalysisContextType => {
  const context = useContext(TradeAnalysisContext);
  if (context === undefined) {
    throw new Error('useTradeAnalysis must be used within a TradeAnalysisProvider');
  }
  return context;
};
