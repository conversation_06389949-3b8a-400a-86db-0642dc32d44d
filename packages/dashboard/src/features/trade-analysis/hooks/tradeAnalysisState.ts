/**
 * Trade Analysis State
 *
 * This module exports the state management for the trade analysis feature.
 */
import {
  createStoreContext,
  createSelector,
  persistState,
  Trade,
  TradeFormData,
  CompleteTradeData,
} from '@adhd-trading-dashboard/shared';

// Action types
export enum TradeAnalysisActionTypes {
  SET_FILTER = 'tradeAnalysis/SET_FILTER',
  CLEAR_FILTERS = 'tradeAnalysis/CLEAR_FILTERS',
  SET_SORT = 'tradeAnalysis/SET_SORT',
  SET_PAGE = 'tradeAnalysis/SET_PAGE',
  SET_PAGE_SIZE = 'tradeAnalysis/SET_PAGE_SIZE',
  SET_TRADES = 'tradeAnalysis/SET_TRADES',
  SET_LOADING = 'tradeAnalysis/SET_LOADING',
  SET_ERROR = 'tradeAnalysis/SET_ERROR',
}

// Action interfaces
export interface SetFilterAction {
  type: TradeAnalysisActionTypes.SET_FILTER;
  payload: {
    key: keyof TradeFilter;
    value: any;
  };
}

export interface ClearFiltersAction {
  type: TradeAnalysisActionTypes.CLEAR_FILTERS;
}

export interface SetSortAction {
  type: TradeAnalysisActionTypes.SET_SORT;
  payload: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

export interface SetPageAction {
  type: TradeAnalysisActionTypes.SET_PAGE;
  payload: number;
}

export interface SetPageSizeAction {
  type: TradeAnalysisActionTypes.SET_PAGE_SIZE;
  payload: number;
}

export interface SetTradesAction {
  type: TradeAnalysisActionTypes.SET_TRADES;
  payload: CompleteTradeData[];
}

export interface SetLoadingAction {
  type: TradeAnalysisActionTypes.SET_LOADING;
  payload: boolean;
}

export interface SetErrorAction {
  type: TradeAnalysisActionTypes.SET_ERROR;
  payload: string | null;
}

// Union type for all actions
export type TradeAnalysisAction =
  | SetFilterAction
  | ClearFiltersAction
  | SetSortAction
  | SetPageAction
  | SetPageSizeAction
  | SetTradesAction
  | SetLoadingAction
  | SetErrorAction;

// Filter interface - updated for new schema
export interface TradeFilter {
  startDate?: string;
  endDate?: string;
  symbol?: string;
  direction?: 'Long' | 'Short';
  modelType?: string;
  session?: string;
  minProfit?: number;
  maxProfit?: number;
  minRMultiple?: number;
  maxRMultiple?: number;
  winLoss?: 'Win' | 'Loss';
}

// Sort interface
export interface TradeSort {
  field: string;
  direction: 'asc' | 'desc';
}

// State interface
export interface TradeAnalysisState {
  trades: CompleteTradeData[];
  filter: TradeFilter;
  sort: TradeSort;
  page: number;
  pageSize: number;
  isLoading: boolean;
  error: string | null;
}

// Initial state
export const initialTradeAnalysisState: TradeAnalysisState = {
  trades: [],
  filter: {},
  sort: {
    field: 'date',
    direction: 'desc',
  },
  page: 1,
  pageSize: 10,
  isLoading: false,
  error: null,
};

// Reducer
export const tradeAnalysisReducer = (
  state: TradeAnalysisState,
  action: TradeAnalysisAction
): TradeAnalysisState => {
  switch (action.type) {
    case TradeAnalysisActionTypes.SET_FILTER:
      return {
        ...state,
        filter: {
          ...state.filter,
          [action.payload.key]: action.payload.value,
        },
        // Reset to first page when filter changes
        page: 1,
      };
    case TradeAnalysisActionTypes.CLEAR_FILTERS:
      return {
        ...state,
        filter: {},
        page: 1,
      };
    case TradeAnalysisActionTypes.SET_SORT:
      return {
        ...state,
        sort: action.payload,
      };
    case TradeAnalysisActionTypes.SET_PAGE:
      return {
        ...state,
        page: action.payload,
      };
    case TradeAnalysisActionTypes.SET_PAGE_SIZE:
      return {
        ...state,
        pageSize: action.payload,
        // Reset to first page when page size changes
        page: 1,
      };
    case TradeAnalysisActionTypes.SET_TRADES:
      return {
        ...state,
        trades: action.payload,
      };
    case TradeAnalysisActionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
    case TradeAnalysisActionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      };
    default:
      return state;
  }
};

// Persist state
const { reducer: persistedReducer, initialState: persistedInitialState } = persistState(
  tradeAnalysisReducer,
  {
    key: 'tradeAnalysis',
    initialState: initialTradeAnalysisState,
    version: 1,
    filter: (state) => ({
      // Only persist user preferences, not data
      filter: state.filter,
      sort: state.sort,
      pageSize: state.pageSize,
    }),
  }
);

// Create store context
export const {
  Context: TradeAnalysisContext,
  Provider: TradeAnalysisProvider,
  useStore: useTradeAnalysisStore,
  useSelector: useTradeAnalysisSelector,
  useAction: useTradeAnalysisAction,
  useActions: useTradeAnalysisActions,
} = createStoreContext<TradeAnalysisState, TradeAnalysisAction>(
  persistedReducer,
  persistedInitialState,
  'TradeAnalysisContext'
);

// Action creators
export const tradeAnalysisActions = {
  setFilter: (key: keyof TradeFilter, value: any): SetFilterAction => ({
    type: TradeAnalysisActionTypes.SET_FILTER,
    payload: { key, value },
  }),
  clearFilters: (): ClearFiltersAction => ({
    type: TradeAnalysisActionTypes.CLEAR_FILTERS,
  }),
  setSort: (field: string, direction: 'asc' | 'desc'): SetSortAction => ({
    type: TradeAnalysisActionTypes.SET_SORT,
    payload: { field, direction },
  }),
  setPage: (page: number): SetPageAction => ({
    type: TradeAnalysisActionTypes.SET_PAGE,
    payload: page,
  }),
  setPageSize: (pageSize: number): SetPageSizeAction => ({
    type: TradeAnalysisActionTypes.SET_PAGE_SIZE,
    payload: pageSize,
  }),
  setTrades: (trades: CompleteTradeData[]): SetTradesAction => ({
    type: TradeAnalysisActionTypes.SET_TRADES,
    payload: trades,
  }),
  setLoading: (isLoading: boolean): SetLoadingAction => ({
    type: TradeAnalysisActionTypes.SET_LOADING,
    payload: isLoading,
  }),
  setError: (error: string | null): SetErrorAction => ({
    type: TradeAnalysisActionTypes.SET_ERROR,
    payload: error,
  }),
};

// Selectors
export const selectTrades = (state: TradeAnalysisState) => state.trades;
export const selectFilter = (state: TradeAnalysisState) => state.filter;
export const selectSort = (state: TradeAnalysisState) => state.sort;
export const selectPage = (state: TradeAnalysisState) => state.page;
export const selectPageSize = (state: TradeAnalysisState) => state.pageSize;
export const selectIsLoading = (state: TradeAnalysisState) => state.isLoading;
export const selectError = (state: TradeAnalysisState) => state.error;

// Memoized selectors
export const selectFilteredTrades = createSelector(selectTrades, selectFilter, (trades, filter) => {
  return trades.filter((tradeData) => {
    const { trade } = tradeData;

    // Apply date filter
    if (filter.startDate && new Date(trade.date) < new Date(filter.startDate)) {
      return false;
    }
    if (filter.endDate && new Date(trade.date) > new Date(filter.endDate)) {
      return false;
    }

    // Apply symbol filter (using market as symbol)
    if (filter.symbol && trade.market && !trade.market.includes(filter.symbol)) {
      return false;
    }

    // Apply direction filter
    if (filter.direction && trade.direction !== filter.direction) {
      return false;
    }

    // Apply model type filter
    if (filter.modelType && trade.model_type !== filter.modelType) {
      return false;
    }

    // Apply session filter
    if (filter.session && trade.session !== filter.session) {
      return false;
    }

    // Apply profit filter
    if (filter.minProfit !== undefined && (trade.achieved_pl || 0) < filter.minProfit) {
      return false;
    }
    if (filter.maxProfit !== undefined && (trade.achieved_pl || 0) > filter.maxProfit) {
      return false;
    }

    // Apply R-multiple filter
    if (filter.minRMultiple !== undefined && (trade.r_multiple || 0) < filter.minRMultiple) {
      return false;
    }
    if (filter.maxRMultiple !== undefined && (trade.r_multiple || 0) > filter.maxRMultiple) {
      return false;
    }

    // Apply win/loss filter
    if (filter.winLoss && trade.win_loss !== filter.winLoss) {
      return false;
    }

    return true;
  });
});

export const selectSortedTrades = createSelector(
  selectFilteredTrades,
  selectSort,
  (trades, sort) => {
    return [...trades].sort((a, b) => {
      // Get values from the trade record for sorting
      let aValue: any;
      let bValue: any;

      switch (sort.field) {
        case 'date':
          aValue = a.trade.date;
          bValue = b.trade.date;
          break;
        case 'symbol':
          aValue = a.trade.market;
          bValue = b.trade.market;
          break;
        case 'profit':
          aValue = a.trade.achieved_pl || 0;
          bValue = b.trade.achieved_pl || 0;
          break;
        case 'direction':
          aValue = a.trade.direction;
          bValue = b.trade.direction;
          break;
        default:
          aValue = a.trade[sort.field as keyof typeof a.trade];
          bValue = b.trade[sort.field as keyof typeof b.trade];
      }

      if (aValue < bValue) {
        return sort.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sort.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }
);

export const selectPaginatedTrades = createSelector(
  selectSortedTrades,
  selectPage,
  selectPageSize,
  (trades, page, pageSize) => {
    const startIndex = (page - 1) * pageSize;
    return trades.slice(startIndex, startIndex + pageSize);
  }
);

export const selectTotalPages = createSelector(
  selectFilteredTrades,
  selectPageSize,
  (trades, pageSize) => {
    return Math.ceil(trades.length / pageSize);
  }
);

export const selectTradeSummary = createSelector(selectFilteredTrades, (trades) => {
  const totalTrades = trades.length;
  const winningTrades = trades.filter((tradeData) => (tradeData.trade.achieved_pl || 0) > 0).length;
  const losingTrades = trades.filter((tradeData) => (tradeData.trade.achieved_pl || 0) < 0).length;
  const breakEvenTrades = trades.filter(
    (tradeData) => (tradeData.trade.achieved_pl || 0) === 0
  ).length;

  const totalProfit = trades.reduce(
    (sum, tradeData) => sum + (tradeData.trade.achieved_pl || 0),
    0
  );
  const totalFees = 0; // Fees not tracked in new schema
  const netProfit = totalProfit - totalFees;

  const winRate = totalTrades > 0 ? winningTrades / totalTrades : 0;

  const averageWin =
    winningTrades > 0
      ? trades
          .filter((tradeData) => (tradeData.trade.achieved_pl || 0) > 0)
          .reduce((sum, tradeData) => sum + (tradeData.trade.achieved_pl || 0), 0) / winningTrades
      : 0;

  const averageLoss =
    losingTrades > 0
      ? trades
          .filter((tradeData) => (tradeData.trade.achieved_pl || 0) < 0)
          .reduce((sum, tradeData) => sum + (tradeData.trade.achieved_pl || 0), 0) / losingTrades
      : 0;

  const profitFactor = Math.abs(averageLoss) > 0 ? averageWin / Math.abs(averageLoss) : 0;

  // Additional metrics from new schema
  const averageRMultiple =
    totalTrades > 0
      ? trades.reduce((sum, tradeData) => sum + (tradeData.trade.r_multiple || 0), 0) / totalTrades
      : 0;

  return {
    totalTrades,
    winningTrades,
    losingTrades,
    breakEvenTrades,
    totalProfit,
    totalFees,
    netProfit,
    winRate,
    averageWin,
    averageLoss,
    profitFactor,
    averageRMultiple,
  };
});
