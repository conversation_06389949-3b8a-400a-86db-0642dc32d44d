/**
 * Trade Analysis Tests
 *
 * Tests for the Trade Analysis feature
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import { TradeAnalysis } from '../index';
import { fetchTradeAnalysisData } from '../services/tradeAnalysisApi';

// Mock the API
vi.mock('../services/tradeAnalysisApi', () => ({
  fetchTradeAnalysisData: vi.fn(),
}));

// Mock the theme
const mockTheme = {
  colors: {
    textPrimary: '#000',
    textSecondary: '#666',
    background: '#fff',
    surface: '#f5f5f5',
    primary: '#ff0000',
    primaryDark: '#cc0000',
    primaryLight: '#ff3333',
    secondary: '#0000ff',
    secondaryDark: '#0000cc',
    secondaryLight: '#3333ff',
    profit: '#00cc00',
    loss: '#cc0000',
    neutral: '#cccccc',
    error: '#ff0000',
    warning: '#ffcc00',
    info: '#0099ff',
    accent: '#ff9900',
    border: '#e0e0e0',
    textInverse: '#ffffff',
  },
  spacing: {
    xxs: '4px',
    xs: '8px',
    sm: '12px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  fontSizes: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '24px',
    xxl: '32px',
  },
  borderRadius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    pill: '999px',
    circle: '50%',
  },
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
  },
  transitions: {
    fast: '0.2s ease',
    normal: '0.3s ease',
    slow: '0.5s ease',
  },
  fontWeights: {
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.8,
  },
  fontFamilies: {
    body: 'Arial, sans-serif',
    heading: 'Arial, sans-serif',
    mono: 'monospace',
  },
  zIndex: {
    base: 0,
    overlay: 10,
    modal: 20,
    popover: 30,
    tooltip: 40,
    fixed: 50,
  },
  name: 'default',
  breakpoints: {
    xs: '480px',
    sm: '768px',
    md: '992px',
    lg: '1200px',
    xl: '1600px',
  },
};

// Mock data
const mockData = {
  trades: [
    {
      id: '1',
      symbol: 'AAPL',
      direction: 'long',
      entryPrice: 150.25,
      exitPrice: 155.75,
      quantity: 100,
      entryTime: '2023-05-01T09:30:00Z',
      exitTime: '2023-05-01T11:45:00Z',
      status: 'win',
      profitLoss: 550,
      profitLossPercent: 3.66,
      timeframe: '15m',
      session: 'regular',
      strategy: 'Breakout',
      tags: ['High Volume', 'Earnings'],
    },
    {
      id: '2',
      symbol: 'MSFT',
      direction: 'short',
      entryPrice: 320.5,
      exitPrice: 315.25,
      quantity: 50,
      entryTime: '2023-05-02T10:15:00Z',
      exitTime: '2023-05-02T14:30:00Z',
      status: 'win',
      profitLoss: 262.5,
      profitLossPercent: 1.64,
      timeframe: '30m',
      session: 'regular',
      strategy: 'Reversal',
      tags: ['Technical'],
    },
  ],
  metrics: {
    totalTrades: 2,
    winningTrades: 2,
    losingTrades: 0,
    breakeven: 0,
    winRate: 100,
    averageWin: 406.25,
    averageLoss: 0,
    profitFactor: Infinity,
    totalProfitLoss: 812.5,
    largestWin: 550,
    largestLoss: 0,
    averageDuration: 195,
    expectancy: 406.25,
  },
  symbolPerformance: [
    {
      category: 'symbol',
      value: 'AAPL',
      trades: 1,
      winRate: 100,
      profitLoss: 550,
      averageProfitLoss: 550,
    },
    {
      category: 'symbol',
      value: 'MSFT',
      trades: 1,
      winRate: 100,
      profitLoss: 262.5,
      averageProfitLoss: 262.5,
    },
  ],
  strategyPerformance: [
    {
      category: 'strategy',
      value: 'Breakout',
      trades: 1,
      winRate: 100,
      profitLoss: 550,
      averageProfitLoss: 550,
    },
    {
      category: 'strategy',
      value: 'Reversal',
      trades: 1,
      winRate: 100,
      profitLoss: 262.5,
      averageProfitLoss: 262.5,
    },
  ],
  timeframePerformance: [
    {
      category: 'timeframe',
      value: '15m',
      trades: 1,
      winRate: 100,
      profitLoss: 550,
      averageProfitLoss: 550,
    },
    {
      category: 'timeframe',
      value: '30m',
      trades: 1,
      winRate: 100,
      profitLoss: 262.5,
      averageProfitLoss: 262.5,
    },
  ],
  sessionPerformance: [
    {
      category: 'session',
      value: 'regular',
      trades: 2,
      winRate: 100,
      profitLoss: 812.5,
      averageProfitLoss: 406.25,
    },
  ],
  timeOfDayPerformance: [
    {
      timeSlot: '9:30-10:30',
      trades: 1,
      winRate: 100,
      profitLoss: 550,
    },
    {
      timeSlot: '10:30-11:30',
      trades: 1,
      winRate: 100,
      profitLoss: 262.5,
    },
  ],
  dayOfWeekPerformance: [
    {
      timeSlot: 'Monday',
      trades: 1,
      winRate: 100,
      profitLoss: 550,
    },
    {
      timeSlot: 'Tuesday',
      trades: 1,
      winRate: 100,
      profitLoss: 262.5,
    },
  ],
};

describe('TradeAnalysis', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (fetchTradeAnalysisData as any).mockResolvedValue(mockData);
  });

  it('renders the trade analysis page with all sections', async () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <TradeAnalysis />
      </ThemeProvider>
    );

    // Check that the title is rendered
    expect(screen.getByText('Trade Analysis')).toBeInTheDocument();

    // Wait for data to load
    await waitFor(() => {
      // Check that the summary view is active by default
      expect(screen.getByText('Performance Summary')).toBeInTheDocument();
    });

    // Check that performance metrics are displayed
    expect(screen.getByText('Total P&L')).toBeInTheDocument();
    expect(screen.getByText('Win Rate')).toBeInTheDocument();

    // Switch to trades view
    fireEvent.click(screen.getByText('Trades'));

    // Check that trades table is displayed
    await waitFor(() => {
      expect(screen.getByText('Symbol')).toBeInTheDocument();
      expect(screen.getByText('Direction')).toBeInTheDocument();
      expect(screen.getByText('AAPL')).toBeInTheDocument();
      expect(screen.getByText('MSFT')).toBeInTheDocument();
    });

    // Switch to symbols view
    fireEvent.click(screen.getByText('Symbols'));

    // Check that symbol performance is displayed
    await waitFor(() => {
      expect(screen.getByText('Performance by Symbol')).toBeInTheDocument();
    });

    // Switch to strategies view
    fireEvent.click(screen.getByText('Strategies'));

    // Check that strategy performance is displayed
    await waitFor(() => {
      expect(screen.getByText('Performance by Strategy')).toBeInTheDocument();
    });
  });

  it('handles API errors gracefully', async () => {
    // Mock API error
    (fetchTradeAnalysisData as any).mockRejectedValue(new Error('API error'));

    render(
      <ThemeProvider theme={mockTheme}>
        <TradeAnalysis />
      </ThemeProvider>
    );

    // Wait for error to be displayed
    await waitFor(() => {
      expect(screen.getByText('API error')).toBeInTheDocument();
    });
  });
});
