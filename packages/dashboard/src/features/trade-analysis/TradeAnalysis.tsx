/**
 * Trade Analysis Page
 *
 * This page displays trade analysis and performance metrics.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { DataCard } from '@adhd-trading-dashboard/shared';
import { TradeAnalysisProvider, useTradeAnalysis } from './context/TradeAnalysisContext';
import { FilterPanel } from './components/FilterPanel';
import { PerformanceSummary } from './components/PerformanceSummary';
import { TradesTable } from './components/TradesTable';
import { CategoryPerformanceChart } from './components/CategoryPerformanceChart';
import { TimePerformanceChart } from './components/TimePerformanceChart';
import { TradeDetail } from './components/TradeDetail';

type ViewType = 'summary' | 'trades' | 'symbols' | 'strategies' | 'timeframes' | 'time';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const ViewTabs = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  padding-bottom: ${({ theme }) => theme.spacing.xs};
`;

const ViewTab = styled.button<{ active: boolean }>`
  background: none;
  border: none;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: ${({ theme, active }) =>
    active ? theme.fontWeights.semibold : theme.fontWeights.regular};
  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textSecondary)};
  cursor: pointer;
  border-bottom: 2px solid ${({ theme, active }) => (active ? theme.colors.primary : 'transparent')};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

const TradeAnalysisContent: React.FC = () => {
  const { data, isLoading, error, selectedTradeId, preferences, updatePreferences } =
    useTradeAnalysis();
  const [activeView, setActiveView] = useState<ViewType>(
    (preferences.defaultView as ViewType) || 'summary'
  );

  const handleViewChange = (view: ViewType) => {
    setActiveView(view);
    updatePreferences({ defaultView: view });
  };

  const renderContent = () => {
    switch (activeView) {
      case 'summary':
        return (
          <>
            <DataCard
              title="Performance Summary"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.metrics}
              emptyMessage="No performance data available for the selected filters."
            >
              <PerformanceSummary />
            </DataCard>

            <DataCard
              title="Performance by Time of Day"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}
              emptyMessage="No time of day performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="timeOfDay" title="Time of Day" />
            </DataCard>

            <DataCard
              title="Performance by Day of Week"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}
              emptyMessage="No day of week performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="dayOfWeek" title="Day of Week" />
            </DataCard>
          </>
        );

      case 'trades':
        return (
          <>
            <DataCard
              title="Trades"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.trades || data.trades.length === 0}
              emptyMessage="No trades available for the selected filters."
            >
              <TradesTable />
            </DataCard>

            {selectedTradeId && <TradeDetail />}
          </>
        );

      case 'symbols':
        return (
          <DataCard
            title="Performance by Symbol"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
            isEmpty={!data?.symbolPerformance || data.symbolPerformance.length === 0}
            emptyMessage="No symbol performance data available for the selected filters."
          >
            <CategoryPerformanceChart category="symbol" title="Symbol" />
          </DataCard>
        );

      case 'strategies':
        return (
          <DataCard
            title="Performance by Strategy"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
            isEmpty={!data?.strategyPerformance || data.strategyPerformance.length === 0}
            emptyMessage="No strategy performance data available for the selected filters."
          >
            <CategoryPerformanceChart category="strategy" title="Strategy" />
          </DataCard>
        );

      case 'timeframes':
        return (
          <>
            <DataCard
              title="Performance by Timeframe"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.timeframePerformance || data.timeframePerformance.length === 0}
              emptyMessage="No timeframe performance data available for the selected filters."
            >
              <CategoryPerformanceChart category="timeframe" title="Timeframe" />
            </DataCard>

            <DataCard
              title="Performance by Session"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.sessionPerformance || data.sessionPerformance.length === 0}
              emptyMessage="No session performance data available for the selected filters."
            >
              <CategoryPerformanceChart category="session" title="Session" />
            </DataCard>
          </>
        );

      case 'time':
        return (
          <>
            <DataCard
              title="Performance by Time of Day"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}
              emptyMessage="No time of day performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="timeOfDay" title="Time of Day" />
            </DataCard>

            <DataCard
              title="Performance by Day of Week"
              isLoading={isLoading}
              hasError={!!error}
              errorMessage={error || ''}
              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}
              emptyMessage="No day of week performance data available for the selected filters."
            >
              <TimePerformanceChart timeType="dayOfWeek" title="Day of Week" />
            </DataCard>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Title>Trade Analysis</Title>
      </PageHeader>

      <FilterPanel />

      <ViewTabs>
        <ViewTab active={activeView === 'summary'} onClick={() => handleViewChange('summary')}>
          Summary
        </ViewTab>
        <ViewTab active={activeView === 'trades'} onClick={() => handleViewChange('trades')}>
          Trades
        </ViewTab>
        <ViewTab active={activeView === 'symbols'} onClick={() => handleViewChange('symbols')}>
          Symbols
        </ViewTab>
        <ViewTab
          active={activeView === 'strategies'}
          onClick={() => handleViewChange('strategies')}
        >
          Strategies
        </ViewTab>
        <ViewTab
          active={activeView === 'timeframes'}
          onClick={() => handleViewChange('timeframes')}
        >
          Timeframes
        </ViewTab>
        <ViewTab active={activeView === 'time'} onClick={() => handleViewChange('time')}>
          Time Analysis
        </ViewTab>
      </ViewTabs>

      {renderContent()}
    </PageContainer>
  );
};

const TradeAnalysis: React.FC = () => {
  return (
    <TradeAnalysisProvider>
      <TradeAnalysisContent />
    </TradeAnalysisProvider>
  );
};

export default TradeAnalysis;
