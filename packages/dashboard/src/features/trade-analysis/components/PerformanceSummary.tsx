/**
 * Performance Summary Component
 * 
 * Displays a summary of trading performance metrics
 */

import React from 'react';
import styled from 'styled-components';
import { PerformanceMetrics } from '../types';
import { useTradeAnalysis } from '../context/TradeAnalysisContext';

interface PerformanceSummaryProps {
  className?: string;
}

const Container = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const MetricCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  flex-direction: column;
`;

const MetricLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const MetricValue = styled.div<{ positive?: boolean; negative?: boolean }>`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, positive, negative }) => {
    if (positive) return theme.colors.profit;
    if (negative) return theme.colors.loss;
    return theme.colors.textPrimary;
  }};
`;

const MetricChange = styled.div<{ positive?: boolean; negative?: boolean }>`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
  color: ${({ theme, positive, negative }) => {
    if (positive) return theme.colors.profit;
    if (negative) return theme.colors.loss;
    return theme.colors.textSecondary;
  }};
`;

export const PerformanceSummary: React.FC<PerformanceSummaryProps> = ({ className }) => {
  const { data } = useTradeAnalysis();
  
  if (!data) {
    return null;
  }
  
  const { metrics } = data;
  
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  
  const formatPercent = (value: number): string => {
    return `${value.toFixed(2)}%`;
  };
  
  return (
    <Container className={className}>
      <MetricCard>
        <MetricLabel>Total P&L</MetricLabel>
        <MetricValue positive={metrics.totalProfitLoss > 0} negative={metrics.totalProfitLoss < 0}>
          {formatCurrency(metrics.totalProfitLoss)}
        </MetricValue>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Win Rate</MetricLabel>
        <MetricValue positive={metrics.winRate > 50} negative={metrics.winRate < 50}>
          {formatPercent(metrics.winRate)}
        </MetricValue>
        <MetricChange>
          {metrics.winningTrades} / {metrics.totalTrades} trades
        </MetricChange>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Profit Factor</MetricLabel>
        <MetricValue positive={metrics.profitFactor > 1} negative={metrics.profitFactor < 1}>
          {metrics.profitFactor.toFixed(2)}
        </MetricValue>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Expectancy</MetricLabel>
        <MetricValue positive={metrics.expectancy > 0} negative={metrics.expectancy < 0}>
          {formatCurrency(metrics.expectancy)}
        </MetricValue>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Average Win</MetricLabel>
        <MetricValue positive={true}>
          {formatCurrency(metrics.averageWin)}
        </MetricValue>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Average Loss</MetricLabel>
        <MetricValue negative={true}>
          {formatCurrency(-Math.abs(metrics.averageLoss))}
        </MetricValue>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Largest Win</MetricLabel>
        <MetricValue positive={true}>
          {formatCurrency(metrics.largestWin)}
        </MetricValue>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Largest Loss</MetricLabel>
        <MetricValue negative={true}>
          {formatCurrency(metrics.largestLoss)}
        </MetricValue>
      </MetricCard>
      
      <MetricCard>
        <MetricLabel>Total Trades</MetricLabel>
        <MetricValue>
          {metrics.totalTrades}
        </MetricValue>
        <MetricChange>
          Avg Duration: {metrics.averageDuration.toFixed(0)} min
        </MetricChange>
      </MetricCard>
    </Container>
  );
};
