/**
 * Filter Panel Component
 * 
 * Provides filtering options for trade analysis
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  TradeFilters, 
  TradeDirection, 
  TradeStatus, 
  TradeTimeframe, 
  TradingSession 
} from '../types';
import { useTradeAnalysis } from '../context/TradeAnalysisContext';
import { Button, Card, Tag } from '@adhd-trading-dashboard/shared';

interface FilterPanelProps {
  className?: string;
}

const Container = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FilterSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const FilterLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const DateRangeContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const DateInput = styled.input`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  flex: 1;
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
`;

const FilterTag = styled(Tag)<{ selected: boolean }>`
  cursor: pointer;
  opacity: ${({ selected }) => (selected ? 1 : 0.6)};
  
  &:hover {
    opacity: 0.8;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-top: ${({ theme }) => theme.spacing.md};
`;

export const FilterPanel: React.FC<FilterPanelProps> = ({ className }) => {
  const { filters, updateFilters, resetFilters, data } = useTradeAnalysis();
  
  // Local state for filter values
  const [localFilters, setLocalFilters] = useState<TradeFilters>(filters);
  
  // Available options from data
  const availableSymbols = data?.trades 
    ? [...new Set(data.trades.map(trade => trade.symbol))]
    : [];
    
  const availableStrategies = data?.trades 
    ? [...new Set(data.trades.map(trade => trade.strategy))]
    : [];
    
  const availableTags = data?.trades 
    ? [...new Set(data.trades.flatMap(trade => trade.tags || []))]
    : [];
  
  // Direction options
  const directionOptions: TradeDirection[] = ['long', 'short'];
  
  // Status options
  const statusOptions: TradeStatus[] = ['win', 'loss', 'breakeven'];
  
  // Timeframe options
  const timeframeOptions: TradeTimeframe[] = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];
  
  // Session options
  const sessionOptions: TradingSession[] = ['pre-market', 'regular', 'after-hours'];
  
  // Handle date range change
  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: value,
      },
    }));
  };
  
  // Handle array filter toggle
  const handleToggleFilter = <T extends string>(
    field: keyof Pick<TradeFilters, 'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'>,
    value: T
  ) => {
    setLocalFilters(prev => {
      const currentValues = prev[field] as T[] || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      
      return {
        ...prev,
        [field]: newValues.length > 0 ? newValues : undefined,
      };
    });
  };
  
  // Apply filters
  const applyFilters = () => {
    updateFilters(localFilters);
  };
  
  // Reset filters
  const handleResetFilters = () => {
    resetFilters();
    setLocalFilters(filters);
  };
  
  // Check if a filter value is selected
  const isSelected = <T extends string>(
    field: keyof Pick<TradeFilters, 'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'>,
    value: T
  ): boolean => {
    const values = localFilters[field] as T[] | undefined;
    return values ? values.includes(value) : false;
  };
  
  return (
    <Container 
      className={className}
      title="Filters"
      variant="default"
      padding="medium"
    >
      <FilterGrid>
        <FilterSection>
          <FilterLabel>Date Range</FilterLabel>
          <DateRangeContainer>
            <DateInput
              type="date"
              value={localFilters.dateRange.startDate}
              onChange={(e) => handleDateChange('startDate', e.target.value)}
            />
            <DateInput
              type="date"
              value={localFilters.dateRange.endDate}
              onChange={(e) => handleDateChange('endDate', e.target.value)}
            />
          </DateRangeContainer>
        </FilterSection>
        
        <FilterSection>
          <FilterLabel>Direction</FilterLabel>
          <TagsContainer>
            {directionOptions.map((direction) => (
              <FilterTag
                key={direction}
                variant={direction === 'long' ? 'success' : 'error'}
                selected={isSelected('directions', direction)}
                onClick={() => handleToggleFilter('directions', direction)}
              >
                {direction}
              </FilterTag>
            ))}
          </TagsContainer>
        </FilterSection>
        
        <FilterSection>
          <FilterLabel>Status</FilterLabel>
          <TagsContainer>
            {statusOptions.map((status) => (
              <FilterTag
                key={status}
                variant={
                  status === 'win' ? 'success' : 
                  status === 'loss' ? 'error' : 
                  'info'
                }
                selected={isSelected('statuses', status)}
                onClick={() => handleToggleFilter('statuses', status)}
              >
                {status}
              </FilterTag>
            ))}
          </TagsContainer>
        </FilterSection>
        
        {availableSymbols.length > 0 && (
          <FilterSection>
            <FilterLabel>Symbols</FilterLabel>
            <TagsContainer>
              {availableSymbols.map((symbol) => (
                <FilterTag
                  key={symbol}
                  variant="primary"
                  selected={isSelected('symbols', symbol)}
                  onClick={() => handleToggleFilter('symbols', symbol)}
                >
                  {symbol}
                </FilterTag>
              ))}
            </TagsContainer>
          </FilterSection>
        )}
        
        {availableStrategies.length > 0 && (
          <FilterSection>
            <FilterLabel>Strategies</FilterLabel>
            <TagsContainer>
              {availableStrategies.map((strategy) => (
                <FilterTag
                  key={strategy}
                  variant="secondary"
                  selected={isSelected('strategies', strategy)}
                  onClick={() => handleToggleFilter('strategies', strategy)}
                >
                  {strategy}
                </FilterTag>
              ))}
            </TagsContainer>
          </FilterSection>
        )}
        
        <FilterSection>
          <FilterLabel>Timeframe</FilterLabel>
          <TagsContainer>
            {timeframeOptions.map((timeframe) => (
              <FilterTag
                key={timeframe}
                variant="default"
                selected={isSelected('timeframes', timeframe)}
                onClick={() => handleToggleFilter('timeframes', timeframe)}
              >
                {timeframe}
              </FilterTag>
            ))}
          </TagsContainer>
        </FilterSection>
        
        <FilterSection>
          <FilterLabel>Session</FilterLabel>
          <TagsContainer>
            {sessionOptions.map((session) => (
              <FilterTag
                key={session}
                variant="default"
                selected={isSelected('sessions', session)}
                onClick={() => handleToggleFilter('sessions', session)}
              >
                {session}
              </FilterTag>
            ))}
          </TagsContainer>
        </FilterSection>
        
        {availableTags.length > 0 && (
          <FilterSection>
            <FilterLabel>Tags</FilterLabel>
            <TagsContainer>
              {availableTags.map((tag) => (
                <FilterTag
                  key={tag}
                  variant="info"
                  selected={isSelected('tags', tag)}
                  onClick={() => handleToggleFilter('tags', tag)}
                >
                  {tag}
                </FilterTag>
              ))}
            </TagsContainer>
          </FilterSection>
        )}
      </FilterGrid>
      
      <ButtonContainer>
        <Button variant="outline" onClick={handleResetFilters}>
          Reset
        </Button>
        <Button onClick={applyFilters}>
          Apply Filters
        </Button>
      </ButtonContainer>
    </Container>
  );
};
