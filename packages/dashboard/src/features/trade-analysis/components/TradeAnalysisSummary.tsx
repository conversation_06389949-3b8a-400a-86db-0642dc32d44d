/**
 * Trade Analysis Summary Component
 *
 * A component for displaying trade analysis summary metrics.
 */
import React from 'react';
import { Card } from '@adhd-trading-dashboard/shared';
import { TradeSummary } from '../types';

export interface TradeAnalysisSummaryProps {
  /** The trade summary data */
  summary: TradeSummary;
  /** Whether the component is in a loading state */
  isLoading?: boolean;
}

/**
 * Trade Analysis Summary Component
 * 
 * A component for displaying trade analysis summary metrics.
 */
export const TradeAnalysisSummary: React.FC<TradeAnalysisSummaryProps> = ({
  summary,
  isLoading = false,
}) => {
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Format number
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Metric card style
  const metricCardStyle: React.CSSProperties = {
    padding: '16px',
    borderRadius: '8px',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  };

  // Metric value style
  const metricValueStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    marginBottom: '4px',
  };

  // Metric label style
  const metricLabelStyle: React.CSSProperties = {
    fontSize: '14px',
    color: '#666',
  };

  // Loading state
  if (isLoading) {
    return (
      <Card title="Performance Summary">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Loading summary data...
        </div>
      </Card>
    );
  }

  return (
    <Card title="Performance Summary">
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))', 
        gap: '16px',
        padding: '16px',
      }}>
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{summary.totalTrades}</div>
          <div style={metricLabelStyle}>Total Trades</div>
        </div>
        
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{formatPercentage(summary.winRate)}</div>
          <div style={metricLabelStyle}>Win Rate</div>
        </div>
        
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{formatNumber(summary.profitFactor)}</div>
          <div style={metricLabelStyle}>Profit Factor</div>
        </div>
        
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{formatCurrency(summary.netProfit)}</div>
          <div style={metricLabelStyle}>Net Profit</div>
        </div>
        
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{formatCurrency(summary.averageWin)}</div>
          <div style={metricLabelStyle}>Average Win</div>
        </div>
        
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{formatCurrency(summary.averageLoss)}</div>
          <div style={metricLabelStyle}>Average Loss</div>
        </div>
        
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{summary.winningTrades}</div>
          <div style={metricLabelStyle}>Winning Trades</div>
        </div>
        
        <div style={metricCardStyle}>
          <div style={metricValueStyle}>{summary.losingTrades}</div>
          <div style={metricLabelStyle}>Losing Trades</div>
        </div>
      </div>
    </Card>
  );
};
