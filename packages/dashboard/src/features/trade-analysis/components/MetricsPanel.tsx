/**
 * MetricsPanel Component
 *
 * Displays key trading performance metrics in a grid layout.
 */

import React from 'react';
import styled from 'styled-components';
import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared';

interface MetricsPanelProps {
  metrics: PerformanceMetrics | null;
  isLoading: boolean;
}

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const MetricCard = styled.div`
  background-color: ${({ theme }) => theme.colors.cardBackground};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacing.md};
`;

const MetricTitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const MetricValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const LoadingPlaceholder = styled.div`
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

/**
 * MetricsPanel Component
 *
 * Displays a grid of key trading performance metrics.
 */
const MetricsPanel: React.FC<MetricsPanelProps> = ({ metrics, isLoading }) => {
  if (isLoading) {
    return <LoadingPlaceholder>Loading metrics...</LoadingPlaceholder>;
  }

  if (!metrics) {
    return <LoadingPlaceholder>No metrics available</LoadingPlaceholder>;
  }

  const formatCurrency = (value: number): string => {
    return value >= 0
      ? `$${Math.abs(value).toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`
      : `-$${Math.abs(value).toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`;
  };

  return (
    <MetricsGrid>
      <MetricCard>
        <MetricTitle>Win Rate</MetricTitle>
        <MetricValue>{metrics.winRate}%</MetricValue>
      </MetricCard>

      <MetricCard>
        <MetricTitle>Profit Factor</MetricTitle>
        <MetricValue>{metrics.profitFactor}</MetricValue>
      </MetricCard>

      <MetricCard>
        <MetricTitle>Average Win</MetricTitle>
        <MetricValue>{formatCurrency(metrics.averageWin)}</MetricValue>
      </MetricCard>

      <MetricCard>
        <MetricTitle>Average Loss</MetricTitle>
        <MetricValue>{formatCurrency(metrics.averageLoss)}</MetricValue>
      </MetricCard>

      <MetricCard>
        <MetricTitle>Largest Win</MetricTitle>
        <MetricValue>{formatCurrency(metrics.largestWin)}</MetricValue>
      </MetricCard>

      <MetricCard>
        <MetricTitle>Largest Loss</MetricTitle>
        <MetricValue>{formatCurrency(metrics.largestLoss)}</MetricValue>
      </MetricCard>
    </MetricsGrid>
  );
};

export default MetricsPanel;
