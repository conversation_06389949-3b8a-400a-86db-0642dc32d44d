/**
 * Category Performance Chart Component
 *
 * Displays performance metrics by category (symbol, strategy, timeframe, session)
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { CategoryPerformance } from '../types';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';

interface CategoryPerformanceChartProps {
  className?: string;
  category: 'symbol' | 'strategy' | 'timeframe' | 'session';
  title: string;
}

type SortField = 'value' | 'trades' | 'winRate' | 'profitLoss' | 'averageProfitLoss';
type SortDirection = 'asc' | 'desc';

const Container = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const TableHead = styled.thead`
  background-color: ${({ theme }) => theme.colors.background};
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const TableHeaderCell = styled.th<{ sortable?: boolean; active?: boolean }>`
  padding: ${({ theme }) => theme.spacing.sm};
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textPrimary)};
  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};

  &:hover {
    ${({ sortable, theme }) =>
      sortable &&
      `
      color: ${theme.colors.primary};
    `}
  }
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing.sm};
`;

const SortIcon = styled.span<{ direction: SortDirection }>`
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing.xs};

  &::after {
    content: '${({ direction }) => (direction === 'asc' ? '↑' : '↓')}';
  }
`;

const BarContainer = styled.div`
  height: 8px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.pill};
  overflow: hidden;
  margin-top: ${({ theme }) => theme.spacing.xs};
`;

const Bar = styled.div<{ width: number; positive: boolean }>`
  height: 100%;
  width: ${({ width }) => `${width}%`};
  background-color: ${({ theme, positive }) =>
    positive ? theme.colors.profit : theme.colors.loss};
`;

const ProfitLoss = styled.span<{ value: number }>`
  color: ${({ theme, value }) =>
    value > 0 ? theme.colors.profit : value < 0 ? theme.colors.loss : theme.colors.textSecondary};
  font-weight: ${({ theme, value }) =>
    value !== 0 ? theme.fontWeights.medium : theme.fontWeights.regular};
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

export const CategoryPerformanceChart: React.FC<CategoryPerformanceChartProps> = ({
  className,
  category,
  title,
}) => {
  const { data } = useTradeAnalysis();
  const [sortField, setSortField] = useState<SortField>('profitLoss');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  if (!data) {
    return null;
  }

  let performanceData: CategoryPerformance[] = [];

  switch (category) {
    case 'symbol':
      performanceData = data.symbolPerformance;
      break;
    case 'strategy':
      performanceData = data.strategyPerformance;
      break;
    case 'timeframe':
      performanceData = data.timeframePerformance;
      break;
    case 'session':
      performanceData = data.sessionPerformance;
      break;
  }

  if (!performanceData || performanceData.length === 0) {
    return <EmptyState>No {category} performance data available.</EmptyState>;
  }

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default direction
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Sort data
  const sortedData = [...performanceData].sort((a, b) => {
    let comparison = 0;

    switch (sortField) {
      case 'value':
        comparison = a.value.localeCompare(b.value);
        break;
      case 'trades':
        comparison = a.trades - b.trades;
        break;
      case 'winRate':
        comparison = a.winRate - b.winRate;
        break;
      case 'profitLoss':
        comparison = a.profitLoss - b.profitLoss;
        break;
      case 'averageProfitLoss':
        comparison = a.averageProfitLoss - b.averageProfitLoss;
        break;
      default:
        comparison = 0;
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Find max profit/loss for bar scaling
  const maxProfitLoss = Math.max(...performanceData.map((item) => Math.abs(item.profitLoss)));

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatPercent = (value: number): string => {
    return `${value.toFixed(2)}%`;
  };

  return (
    <Container className={className}>
      <Table>
        <TableHead>
          <TableRow>
            <TableHeaderCell
              sortable
              active={sortField === 'value'}
              onClick={() => handleSort('value')}
            >
              {title}
              {sortField === 'value' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>

            <TableHeaderCell
              sortable
              active={sortField === 'trades'}
              onClick={() => handleSort('trades')}
            >
              Trades
              {sortField === 'trades' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>

            <TableHeaderCell
              sortable
              active={sortField === 'winRate'}
              onClick={() => handleSort('winRate')}
            >
              Win Rate
              {sortField === 'winRate' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>

            <TableHeaderCell
              sortable
              active={sortField === 'profitLoss'}
              onClick={() => handleSort('profitLoss')}
            >
              P&L
              {sortField === 'profitLoss' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>

            <TableHeaderCell
              sortable
              active={sortField === 'averageProfitLoss'}
              onClick={() => handleSort('averageProfitLoss')}
            >
              Avg P&L
              {sortField === 'averageProfitLoss' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>
          </TableRow>
        </TableHead>

        <TableBody>
          {sortedData.map((item, index) => (
            <TableRow key={index}>
              <TableCell>{item.value}</TableCell>
              <TableCell>{item.trades}</TableCell>
              <TableCell>{formatPercent(item.winRate)}</TableCell>
              <TableCell>
                <ProfitLoss value={item.profitLoss}>{formatCurrency(item.profitLoss)}</ProfitLoss>
                <BarContainer>
                  <Bar
                    width={Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100)}
                    positive={item.profitLoss >= 0}
                  />
                </BarContainer>
              </TableCell>
              <TableCell>
                <ProfitLoss value={item.averageProfitLoss}>
                  {formatCurrency(item.averageProfitLoss)}
                </ProfitLoss>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Container>
  );
};
