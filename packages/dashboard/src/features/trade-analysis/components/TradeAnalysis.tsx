/**
 * Trade Analysis Component
 *
 * The main component for the trade analysis feature.
 */
import React, { useEffect } from 'react';
import { Button } from '@adhd-trading-dashboard/shared';
import { TradeAnalysisProvider } from '../hooks/tradeAnalysisState';
import { useTradeAnalysis } from '../hooks/useTradeAnalysis';
import { TradeAnalysisFilter } from './TradeAnalysisFilter';
import { TradeAnalysisTable } from './TradeAnalysisTable';
import { TradeAnalysisSummary } from './TradeAnalysisSummary';
import { TradeAnalysisCharts } from './TradeAnalysisCharts';

export interface TradeAnalysisProps {
  /** The title of the component */
  title?: string;
}

/**
 * Trade Analysis Component (Connected)
 *
 * The connected component that uses the trade analysis hook.
 */
const TradeAnalysisContent: React.FC = () => {
  const {
    trades,
    filter,
    sort,
    page,
    pageSize,
    totalPages,
    tradeSummary,
    equityCurveData,
    distributionData,
    isLoading,
    error,
    setFilter,
    clearFilters,
    setSort,
    setPage,
    setPageSize,
    fetchTrades,
    exportTrades,
  } = useTradeAnalysis();

  // Fetch trades on mount
  useEffect(() => {
    fetchTrades();
  }, [fetchTrades]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Header */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <h1 style={{ margin: 0 }}>Trade Analysis</h1>

        <div style={{ display: 'flex', gap: '16px' }}>
          <Button
            variant="outline"
            onClick={fetchTrades}
            disabled={isLoading}
            startIcon={<span>🔄</span>}
          >
            Refresh
          </Button>

          <Button
            onClick={exportTrades}
            disabled={isLoading || trades.length === 0}
            startIcon={<span>📊</span>}
          >
            Export
          </Button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div
          style={{
            padding: '16px',
            backgroundColor: '#ffebee',
            color: '#c62828',
            borderRadius: '4px',
          }}
        >
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Filter */}
      <TradeAnalysisFilter
        filter={filter}
        onSetFilter={setFilter}
        onClearFilters={clearFilters}
        isLoading={isLoading}
      />

      {/* Summary */}
      {tradeSummary && <TradeAnalysisSummary summary={tradeSummary} isLoading={isLoading} />}

      {/* Charts */}
      <TradeAnalysisCharts
        equityCurveData={equityCurveData}
        distributionData={distributionData}
        isLoading={isLoading}
      />

      {/* Table */}
      <TradeAnalysisTable
        trades={trades}
        sort={sort}
        onSort={setSort}
        page={page}
        onPageChange={setPage}
        pageSize={pageSize}
        onPageSizeChange={setPageSize}
        totalPages={totalPages}
        isLoading={isLoading}
      />
    </div>
  );
};

/**
 * Trade Analysis Component
 *
 * The main component for the trade analysis feature.
 */
export const TradeAnalysis: React.FC<TradeAnalysisProps> = ({ title }) => {
  return (
    <TradeAnalysisProvider>
      <TradeAnalysisContent />
    </TradeAnalysisProvider>
  );
};
