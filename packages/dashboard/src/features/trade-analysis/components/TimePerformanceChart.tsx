/**
 * Time Performance Chart Component
 *
 * Displays performance metrics by time (time of day, day of week)
 */

import React from 'react';
import styled from 'styled-components';
import { TimePerformance } from '../types';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';

interface TimePerformanceChartProps {
  className?: string;
  timeType: 'timeOfDay' | 'dayOfWeek';
  title: string;
}

const Container = styled.div``;

const ChartContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const TimeSlot = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const TimeSlotHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const TimeSlotLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const TimeSlotMetrics = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const MetricValue = styled.span<{ positive?: boolean; negative?: boolean }>`
  color: ${({ theme, positive, negative }) => {
    if (positive) return theme.colors.profit;
    if (negative) return theme.colors.loss;
    return theme.colors.textSecondary;
  }};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const BarContainer = styled.div`
  height: 24px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  overflow: hidden;
  position: relative;
`;

const Bar = styled.div<{ width: number; positive: boolean }>`
  height: 100%;
  width: ${({ width }) => `${width}%`};
  background-color: ${({ theme, positive }) =>
    positive ? theme.colors.profit : theme.colors.loss};
  transition: width 0.3s ease;
`;

const BarLabel = styled.div`
  position: absolute;
  top: 0;
  left: ${({ theme }) => theme.spacing.sm};
  height: 100%;
  display: flex;
  align-items: center;
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textInverse};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

export const TimePerformanceChart: React.FC<TimePerformanceChartProps> = ({
  className,
  timeType,
  title,
}) => {
  const { data } = useTradeAnalysis();

  if (!data) {
    return null;
  }

  const performanceData =
    timeType === 'timeOfDay' ? data.timeOfDayPerformance : data.dayOfWeekPerformance;

  if (!performanceData || performanceData.length === 0) {
    return <EmptyState>No {timeType} performance data available.</EmptyState>;
  }

  // Find max profit/loss for bar scaling
  const maxProfitLoss = Math.max(...performanceData.map((item) => Math.abs(item.profitLoss)));

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatPercent = (value: number): string => {
    return `${value.toFixed(2)}%`;
  };

  return (
    <Container className={className}>
      <ChartContainer>
        {performanceData.map((item, index) => (
          <TimeSlot key={index}>
            <TimeSlotHeader>
              <TimeSlotLabel>{item.timeSlot}</TimeSlotLabel>
              <TimeSlotMetrics>
                <div>
                  Trades: <MetricValue>{item.trades}</MetricValue>
                </div>
                <div>
                  Win Rate:{' '}
                  <MetricValue positive={item.winRate > 50} negative={item.winRate < 50}>
                    {formatPercent(item.winRate)}
                  </MetricValue>
                </div>
                <div>
                  P&L:{' '}
                  <MetricValue positive={item.profitLoss > 0} negative={item.profitLoss < 0}>
                    {formatCurrency(item.profitLoss)}
                  </MetricValue>
                </div>
              </TimeSlotMetrics>
            </TimeSlotHeader>
            <BarContainer>
              <Bar
                width={Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100)}
                positive={item.profitLoss >= 0}
              >
                {item.profitLoss !== 0 && <BarLabel>{formatCurrency(item.profitLoss)}</BarLabel>}
              </Bar>
            </BarContainer>
          </TimeSlot>
        ))}
      </ChartContainer>
    </Container>
  );
};
