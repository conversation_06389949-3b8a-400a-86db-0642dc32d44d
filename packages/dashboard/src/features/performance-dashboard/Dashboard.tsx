/**
 * Dashboard Page Component
 *
 * This is the main dashboard page that displays trading metrics and charts.
 */

import React, { useEffect } from "react";
import styled from "styled-components";
import { MetricsPanel } from "./components/MetricsPanel";
import { PerformanceChart } from "./components/PerformanceChart";
import { RecentTradesPanel } from "./components/RecentTradesPanel";
import { useDashboardData } from "./hooks/useDashboardData";

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const ChartSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const ChartTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
`;

const ChartPlaceholder = styled.div`
  height: 300px;
  background-color: ${({ theme }) => theme.colors.chartGrid};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const RecentTradesSection = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const Dashboard: React.FC = () => {
  const { metrics, chartData, recentTrades, isLoading, fetchDashboardData } =
    useDashboardData();

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return (
    <PageContainer>
      <PageHeader>
        <Title>Trading Dashboard</Title>
      </PageHeader>

      <MetricsPanel metrics={metrics} isLoading={isLoading} />

      <ChartSection>
        <ChartTitle>Performance</ChartTitle>
        {isLoading ? (
          <ChartPlaceholder>Loading chart data...</ChartPlaceholder>
        ) : (
          <PerformanceChart data={chartData} />
        )}
      </ChartSection>

      <RecentTradesSection>
        <ChartTitle>Recent Trades</ChartTitle>
        <RecentTradesPanel trades={recentTrades} isLoading={isLoading} />
      </RecentTradesSection>
    </PageContainer>
  );
};

export default Dashboard;
