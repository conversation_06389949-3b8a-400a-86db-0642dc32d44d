/**
 * Metrics Panel Component
 *
 * Displays key trading metrics in a card format
 */

import React from "react";
import styled from "styled-components";

interface Metric {
  title: string;
  value: string;
}

interface MetricsPanelProps {
  metrics: Metric[];
  isLoading?: boolean;
}

const Container = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const MetricCard = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const MetricTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
`;

const MetricValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const LoadingIndicator = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

export const MetricsPanel: React.FC<MetricsPanelProps> = ({
  metrics,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <Container>
        {[1, 2, 3, 4].map((i) => (
          <MetricCard key={i}>
            <MetricTitle>Loading...</MetricTitle>
            <LoadingIndicator>Fetching data...</LoadingIndicator>
          </MetricCard>
        ))}
      </Container>
    );
  }

  return (
    <Container>
      {metrics.map((metric, index) => (
        <MetricCard key={index}>
          <MetricTitle>{metric.title}</MetricTitle>
          <MetricValue>{metric.value}</MetricValue>
        </MetricCard>
      ))}
    </Container>
  );
};
