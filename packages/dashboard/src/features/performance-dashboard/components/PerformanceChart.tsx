/**
 * Performance Chart Component
 *
 * Displays a chart showing trading performance over time
 */

import React from "react";
import styled from "styled-components";

interface ChartDataPoint {
  date: string;
  value: number;
}

interface PerformanceChartProps {
  data: ChartDataPoint[];
}

const ChartContainer = styled.div`
  height: 300px;
  position: relative;
  width: 100%;
`;

const PlaceholderText = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: ${({ theme }) => theme.colors.textSecondary};
`;

// This is a placeholder component - in a real app, you'd use a charting library
// like recharts, chart.js, or d3.js
export const PerformanceChart: React.FC<PerformanceChartProps> = ({ data }) => {
  // Simple implementation - would be replaced with an actual chart
  if (!data || data.length === 0) {
    return (
      <ChartContainer>
        <PlaceholderText>No chart data available</PlaceholderText>
      </ChartContainer>
    );
  }

  return (
    <ChartContainer>
      {/* In a real implementation, you would render your chart library here */}
      <PlaceholderText>
        Chart would render {data.length} data points from {data[0].date} to{" "}
        {data[data.length - 1].date}
      </PlaceholderText>
    </ChartContainer>
  );
};
