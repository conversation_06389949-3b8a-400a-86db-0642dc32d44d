# Feature Module Template

This document provides a standardized structure for feature modules in the ADHD Trading Dashboard.

## Feature Module Structure

```
feature-name/
├── api/                    # API integration
│   ├── featureNameApi.ts   # API functions
│   └── index.ts            # API exports
├── components/             # Feature-specific components
│   ├── ui/                 # Reusable UI components for this feature
│   │   ├── ComponentName.tsx
│   │   └── index.ts
│   ├── ComponentName.tsx   # Feature-specific components
│   └── index.ts            # Component exports
├── context/                # State management
│   ├── FeatureNameContext.tsx  # Context provider
│   └── index.ts            # Context exports
├── hooks/                  # Custom hooks
│   ├── useFeatureName.ts   # Feature-specific hooks
│   └── index.ts            # Hook exports
├── types/                  # Type definitions
│   ├── index.ts            # Type exports
│   └── types.ts            # Type definitions
├── utils/                  # Utility functions
│   ├── helpers.ts          # Helper functions
│   └── index.ts            # Utility exports
├── __tests__/              # Tests
│   ├── FeatureName.test.tsx
│   └── components/
│       └── ComponentName.test.tsx
├── FeatureName.tsx         # Main feature component
└── index.ts                # Feature exports
```

## State Management Pattern

Each feature should use React Context for state management:

```typescript
// context/FeatureNameContext.tsx
import React, { createContext, useContext, useReducer, ReactNode } from 'react';

// Define state and actions
interface FeatureNameState {
  // State properties
}

type FeatureNameAction = 
  | { type: 'ACTION_TYPE_1'; payload: any }
  | { type: 'ACTION_TYPE_2'; payload: any };

// Create initial state
const initialState: FeatureNameState = {
  // Initial state values
};

// Create reducer
const featureNameReducer = (state: FeatureNameState, action: FeatureNameAction): FeatureNameState => {
  switch (action.type) {
    case 'ACTION_TYPE_1':
      return {
        // Updated state
      };
    case 'ACTION_TYPE_2':
      return {
        // Updated state
      };
    default:
      return state;
  }
};

// Create context
interface FeatureNameContextType extends FeatureNameState {
  // Context methods
  doSomething: () => void;
}

const FeatureNameContext = createContext<FeatureNameContextType | undefined>(undefined);

// Create provider
interface FeatureNameProviderProps {
  children: ReactNode;
}

export const FeatureNameProvider: React.FC<FeatureNameProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(featureNameReducer, initialState);

  // Define context methods
  const doSomething = () => {
    dispatch({ type: 'ACTION_TYPE_1', payload: {} });
  };

  const value = {
    ...state,
    doSomething,
  };

  return (
    <FeatureNameContext.Provider value={value}>
      {children}
    </FeatureNameContext.Provider>
  );
};

// Create hook
export const useFeatureName = (): FeatureNameContextType => {
  const context = useContext(FeatureNameContext);
  if (context === undefined) {
    throw new Error('useFeatureName must be used within a FeatureNameProvider');
  }
  return context;
};
```

## API Integration Pattern

API functions should be separated from components:

```typescript
// api/featureNameApi.ts
import { FeatureNameData } from '../types';

export const fetchFeatureNameData = async (): Promise<FeatureNameData> => {
  try {
    // API call logic
    const response = await fetch('/api/feature-name');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching feature data:', error);
    throw error;
  }
};
```

## Component Pattern

Components should be focused and reusable:

```typescript
// components/ComponentName.tsx
import React from 'react';
import styled from 'styled-components';
import { useFeatureName } from '../context/FeatureNameContext';

interface ComponentNameProps {
  // Props
  className?: string;
}

const Container = styled.div`
  // Styles
`;

export const ComponentName: React.FC<ComponentNameProps> = ({ className }) => {
  const { /* context values */ } = useFeatureName();

  return (
    <Container className={className}>
      {/* Component content */}
    </Container>
  );
};
```

## Testing Pattern

Each component and feature should have tests:

```typescript
// __tests__/ComponentName.test.tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import { ComponentName } from '../components/ComponentName';
import { FeatureNameProvider } from '../context/FeatureNameContext';

describe('ComponentName', () => {
  it('renders correctly', () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <FeatureNameProvider>
          <ComponentName />
        </FeatureNameProvider>
      </ThemeProvider>
    );

    // Assertions
  });
});
```

## Export Pattern

Always use named exports and re-export from index.ts files:

```typescript
// index.ts
export { default as FeatureName } from './FeatureName';
export { FeatureNameProvider, useFeatureName } from './context/FeatureNameContext';
export * from './components';
export * from './types';
```

This structure ensures consistency across features and makes it easier to maintain and extend the application.
