/**
 * Trading Dashboard Component
 *
 * Main component for the Trading Dashboard feature
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { useTradingDashboard } from './hooks/useTradingDashboard';
import MetricsPanel from './components/MetricsPanel';
import PerformanceChart from './components/PerformanceChart';
import RecentTradesTable from './components/RecentTradesTable';
import SetupAnalysis from './components/SetupAnalysis';

// Tab types
type TabType = 'summary' | 'trades' | 'analysis';

const DashboardContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  margin: 0;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const StatusBadge = styled.span`
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.textInverse};
  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.sm}`};
  border-radius: ${({ theme }) => theme.borderRadius.pill};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: 500;
  margin-left: ${({ theme }) => theme.spacing.sm};
`;

const TabsContainer = styled.div`
  display: flex;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const Tab = styled.button<{ active: boolean }>`
  background: none;
  border: none;
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textSecondary)};
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 500;
  cursor: pointer;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: ${({ theme, active }) => (active ? theme.colors.primary : 'transparent')};
  }

  &:hover {
    color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textPrimary)};
  }
`;

const RefreshButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.textInverse};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.border};
    cursor: not-allowed;
  }
`;

const LoadingOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex.modal};
`;

const LoadingSpinner = styled.div`
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

/**
 * TradingDashboard Component
 *
 * Main component for the Trading Dashboard feature
 */
export const TradingDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('summary');
  const {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    isLoading,
    error,
    fetchDashboardData,
  } = useTradingDashboard();

  const handleRefresh = () => {
    fetchDashboardData();
  };

  return (
    <DashboardContainer>
      <Header>
        <div>
          <Title>Trading Dashboard</Title>
          <StatusBadge>SESSION 1</StatusBadge>
        </div>
        <RefreshButton onClick={handleRefresh} disabled={isLoading}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </RefreshButton>
      </Header>

      <TabsContainer>
        <Tab active={activeTab === 'summary'} onClick={() => setActiveTab('summary')}>
          Summary
        </Tab>
        <Tab active={activeTab === 'trades'} onClick={() => setActiveTab('trades')}>
          Trades
        </Tab>
        <Tab active={activeTab === 'analysis'} onClick={() => setActiveTab('analysis')}>
          Analysis
        </Tab>
      </TabsContainer>

      {error && <div style={{ color: 'red', marginBottom: '16px' }}>Error: {error}</div>}

      {activeTab === 'summary' && (
        <>
          <MetricsPanel metrics={performanceMetrics} isLoading={isLoading} />
          <PerformanceChart data={chartData} isLoading={isLoading} />
          <RecentTradesTable trades={trades.slice(0, 5)} isLoading={isLoading} />
        </>
      )}

      {activeTab === 'trades' && <RecentTradesTable trades={trades} isLoading={isLoading} />}

      {activeTab === 'analysis' && (
        <>
          <MetricsPanel metrics={performanceMetrics} isLoading={isLoading} />
          <SetupAnalysis
            setupPerformance={setupPerformance}
            sessionPerformance={sessionPerformance}
            isLoading={isLoading}
          />
        </>
      )}

      {isLoading && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}
    </DashboardContainer>
  );
};

export default TradingDashboard;
