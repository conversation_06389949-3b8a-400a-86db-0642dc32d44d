/**
 * Metrics Panel Component
 *
 * Displays key trading metrics in a card format
 */

import React from 'react';
import styled from 'styled-components';
import { PerformanceMetric } from '../types';

interface MetricsPanelProps {
  metrics: PerformanceMetric[];
  isLoading?: boolean;
}

const Container = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const MetricCard = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100px;
`;

const MetricTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const MetricValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const MetricChange = styled.div<{ isPositive?: boolean }>`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme, isPositive }) => 
    isPositive ? theme.colors.success : theme.colors.error};
  display: flex;
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing.xxs};
`;

const LoadingIndicator = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

/**
 * MetricsPanel Component
 * 
 * Displays key trading metrics in a card format
 */
export const MetricsPanel: React.FC<MetricsPanelProps> = ({
  metrics,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <Container>
        {[1, 2, 3, 4].map((i) => (
          <MetricCard key={i}>
            <MetricTitle>Loading...</MetricTitle>
            <LoadingIndicator>Fetching data...</LoadingIndicator>
          </MetricCard>
        ))}
      </Container>
    );
  }

  return (
    <Container>
      {metrics.map((metric, index) => (
        <MetricCard key={index}>
          <MetricTitle>{metric.title}</MetricTitle>
          <MetricValue>{metric.value}</MetricValue>
          {metric.change !== undefined && (
            <MetricChange isPositive={metric.isPositive}>
              {metric.isPositive ? '↑' : '↓'} {metric.change}%
            </MetricChange>
          )}
        </MetricCard>
      ))}
    </Container>
  );
};

export default MetricsPanel;
