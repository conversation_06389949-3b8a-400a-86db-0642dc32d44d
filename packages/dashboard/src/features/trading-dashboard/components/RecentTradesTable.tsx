/**
 * Recent Trades Table Component
 *
 * Displays a table of recent trades
 */

import React from 'react';
import styled from 'styled-components';
import { Trade } from '../types';

interface RecentTradesTableProps {
  trades: Trade[];
  isLoading?: boolean;
}

const TableContainer = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  overflow-x: auto;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const TableTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
`;

const TableHead = styled.thead`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const TableHeader = styled.th`
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  text-transform: uppercase;
`;

const TableRow = styled.tr`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const DirectionCell = styled(TableCell)<{ direction: 'Long' | 'Short' }>`
  color: ${({ theme, direction }) => 
    direction === 'Long' ? theme.colors.success : theme.colors.error};
`;

const ResultCell = styled(TableCell)<{ win: boolean }>`
  color: ${({ theme, win }) => 
    win ? theme.colors.success : theme.colors.error};
`;

const PnlCell = styled(TableCell)<{ value: number }>`
  color: ${({ theme, value }) => 
    value >= 0 ? theme.colors.success : theme.colors.error};
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const NoDataContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

/**
 * RecentTradesTable Component
 * 
 * Displays a table of recent trades
 */
export const RecentTradesTable: React.FC<RecentTradesTableProps> = ({
  trades,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <TableContainer>
        <TableTitle>Recent Trades</TableTitle>
        <LoadingContainer>Loading trades data...</LoadingContainer>
      </TableContainer>
    );
  }

  if (!trades || trades.length === 0) {
    return (
      <TableContainer>
        <TableTitle>Recent Trades</TableTitle>
        <NoDataContainer>No trades data available</NoDataContainer>
      </TableContainer>
    );
  }

  return (
    <TableContainer>
      <TableTitle>Recent Trades</TableTitle>
      <Table>
        <TableHead>
          <tr>
            <TableHeader>Date</TableHeader>
            <TableHeader>Setup</TableHeader>
            <TableHeader>Session</TableHeader>
            <TableHeader>Direction</TableHeader>
            <TableHeader>Market</TableHeader>
            <TableHeader>Entry</TableHeader>
            <TableHeader>Exit</TableHeader>
            <TableHeader>R-Multiple</TableHeader>
            <TableHeader>P&L</TableHeader>
          </tr>
        </TableHead>
        <tbody>
          {trades.map((trade) => (
            <TableRow key={trade.id}>
              <TableCell>{trade.date}</TableCell>
              <TableCell>{trade.setup}</TableCell>
              <TableCell>{trade.session}</TableCell>
              <DirectionCell direction={trade.direction}>
                {trade.direction}
              </DirectionCell>
              <TableCell>{trade.market}</TableCell>
              <TableCell>{trade.entry}</TableCell>
              <TableCell>{trade.exit}</TableCell>
              <ResultCell win={trade.win}>
                {trade.rMultiple.toFixed(2)}
              </ResultCell>
              <PnlCell value={trade.pnl}>
                ${trade.pnl.toFixed(2)}
              </PnlCell>
            </TableRow>
          ))}
        </tbody>
      </Table>
    </TableContainer>
  );
};

export default RecentTradesTable;
