/**
 * Priority Tag Component
 * 
 * A tag component for displaying priority levels
 */

import React from 'react';
import styled from 'styled-components';
import { TradingPlanPriority } from '../../types';

interface PriorityTagProps {
  /** The priority level to display */
  priority: TradingPlanPriority;
  /** Additional CSS class names */
  className?: string;
}

const Tag = styled.span<{ priority: TradingPlanPriority }>`
  display: inline-block;
  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};
  border-radius: ${({ theme }) => theme.borderRadius.xs};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: 500;
  text-transform: uppercase;
  
  ${({ priority, theme }) => {
    switch (priority) {
      case 'high':
        return `
          background-color: ${theme.colors.error}15;
          color: ${theme.colors.error};
        `;
      case 'medium':
        return `
          background-color: ${theme.colors.warning}15;
          color: ${theme.colors.warning};
        `;
      case 'low':
        return `
          background-color: ${theme.colors.info}15;
          color: ${theme.colors.info};
        `;
      default:
        return '';
    }
  }}
`;

export const PriorityTag: React.FC<PriorityTagProps> = ({ priority, className }) => {
  return (
    <Tag priority={priority} className={className}>
      {priority}
    </Tag>
  );
};
