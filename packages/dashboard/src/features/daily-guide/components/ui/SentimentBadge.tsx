/**
 * Sentiment Badge Component
 * 
 * A badge component for displaying market sentiment
 */

import React from 'react';
import styled from 'styled-components';
import { MarketSentiment } from '../../types';

interface SentimentBadgeProps {
  /** The market sentiment to display */
  sentiment: MarketSentiment;
  /** Additional CSS class names */
  className?: string;
}

const Badge = styled.div<{ sentiment: MarketSentiment }>`
  display: inline-block;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  
  ${({ sentiment, theme }) => {
    switch (sentiment) {
      case 'bullish':
        return `
          background-color: ${theme.colors.profit}20;
          color: ${theme.colors.profit};
        `;
      case 'bearish':
        return `
          background-color: ${theme.colors.loss}20;
          color: ${theme.colors.loss};
        `;
      case 'neutral':
        return `
          background-color: ${theme.colors.neutral}20;
          color: ${theme.colors.neutral};
        `;
      default:
        return '';
    }
  }}
`;

export const SentimentBadge: React.FC<SentimentBadgeProps> = ({ sentiment, className }) => {
  const formattedSentiment = sentiment.charAt(0).toUpperCase() + sentiment.slice(1);
  
  return (
    <Badge sentiment={sentiment} className={className}>
      {formattedSentiment}
    </Badge>
  );
};
