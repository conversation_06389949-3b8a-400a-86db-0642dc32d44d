/**
 * Market Indicators Component
 *
 * Displays market indices with their values, changes, and previous close data.
 */
import React from 'react';
import styled from 'styled-components';
import { MarketIndex } from '../types';

export interface MarketIndicatorsProps {
  /** Array of market indices */
  indices: MarketIndex[];
}

// Styled components
const IndexGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const IndexCard = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  flex-direction: column;
  border: 1px solid ${({ theme }) => theme.colors.border};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const IndexName = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
  font-weight: 500;
`;

const IndexValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const IndexChange = styled.div<{ value: number }>`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme, value }) => (value >= 0 ? theme.colors.profit : theme.colors.loss)};
  display: flex;
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing.xs};
  font-weight: 500;
`;

const PreviousClose = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-top: ${({ theme }) => theme.spacing.xs};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

/**
 * Market Indicators Component
 *
 * Displays market indices with their values, changes, and previous close data.
 */
export const MarketIndicators: React.FC<MarketIndicatorsProps> = ({ indices }) => {
  if (!indices || indices.length === 0) {
    return <EmptyState>No market indices data available</EmptyState>;
  }

  return (
    <IndexGrid>
      {indices.map((index) => (
        <IndexCard key={index.symbol}>
          <IndexName>{index.name}</IndexName>
          <IndexValue>{index.value.toFixed(2)}</IndexValue>
          <IndexChange value={index.change}>
            {index.change >= 0 ? '▲ ' : '▼ '}
            {Math.abs(index.change).toFixed(2)} ({(index.changePercent * 100).toFixed(2)}%)
          </IndexChange>
          {index.previousClose && (
            <PreviousClose>Previous: {index.previousClose.toFixed(2)}</PreviousClose>
          )}
        </IndexCard>
      ))}
    </IndexGrid>
  );
};
