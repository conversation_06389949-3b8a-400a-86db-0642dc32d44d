/**
 * Daily Guide Component Tests
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { DailyGuide } from '../DailyGuide';
import { ThemeProvider } from 'styled-components';
import { theme } from '@adhd-trading-dashboard/shared';

// Mock the hooks
jest.mock('../../hooks/useDailyGuide', () => ({
  useDailyGuide: () => ({
    selectedDate: '2023-01-01',
    marketOverview: {
      sentiment: 'neutral',
      summary: 'Market is showing mixed signals.',
      indices: [
        {
          symbol: 'SPY',
          name: 'S&P 500',
          value: 4500,
          change: 0.5,
          changePercent: 0.01,
        },
      ],
      economicEvents: [],
      news: [],
      lastUpdated: '2023-01-01T12:00:00Z',
    },
    tradingPlan: {
      items: [
        {
          id: '1',
          description: 'Wait for market open',
          priority: 'high',
          completed: false,
        },
      ],
      strategy: 'Focus on momentum plays',
      riskManagement: {
        maxRiskPerTrade: 1,
        maxDailyLoss: 3,
        maxTrades: 5,
        positionSizing: '2% of account',
      },
      notes: 'Be cautious today',
    },
    keyPriceLevels: [
      {
        symbol: 'SPY',
        support: ['450.00', '445.00'],
        resistance: ['455.00', '460.00'],
        pivotPoint: '452.50',
      },
    ],
    isLoading: false,
    error: null,
    currentDate: 'January 1, 2023',
    onDateChange: jest.fn(),
    onTradingPlanItemToggle: jest.fn(),
    onAddTradingPlanItem: jest.fn(),
    onRemoveTradingPlanItem: jest.fn(),
    onRefresh: jest.fn(),
  }),
}));

// Mock the Card component
jest.mock('@adhd-trading-dashboard/shared', () => {
  const original = jest.requireActual('@adhd-trading-dashboard/shared');
  return {
    ...original,
    Card: ({ title, children }: { title: string; children: React.ReactNode }) => (
      <div data-testid="card">
        <h2>{title}</h2>
        {children}
      </div>
    ),
    Button: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
      <button onClick={onClick}>{children}</button>
    ),
  };
});

// Mock the child components
jest.mock('../MarketOverview', () => ({
  MarketOverview: () => <div data-testid="market-overview">Market Overview</div>,
}));

jest.mock('../TradingPlan', () => ({
  TradingPlan: () => <div data-testid="trading-plan">Trading Plan</div>,
}));

jest.mock('../KeyLevels', () => ({
  KeyLevels: () => <div data-testid="key-levels">Key Levels</div>,
}));

describe('DailyGuide', () => {
  it('renders without crashing', () => {
    render(
      <ThemeProvider theme={theme}>
        <DailyGuide />
      </ThemeProvider>
    );
    
    expect(screen.getByText('Daily Guide')).toBeInTheDocument();
  });

  it('renders with a custom title', () => {
    render(
      <ThemeProvider theme={theme}>
        <DailyGuide title="Custom Title" />
      </ThemeProvider>
    );
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
  });

  it('renders the date selector', () => {
    render(
      <ThemeProvider theme={theme}>
        <DailyGuide />
      </ThemeProvider>
    );
    
    expect(screen.getByText('January 1, 2023')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('renders the refresh button', () => {
    render(
      <ThemeProvider theme={theme}>
        <DailyGuide />
      </ThemeProvider>
    );
    
    expect(screen.getByText('Refresh')).toBeInTheDocument();
  });

  it('renders all child components', () => {
    render(
      <ThemeProvider theme={theme}>
        <DailyGuide />
      </ThemeProvider>
    );
    
    expect(screen.getByTestId('market-overview')).toBeInTheDocument();
    expect(screen.getByTestId('trading-plan')).toBeInTheDocument();
    expect(screen.getByTestId('key-levels')).toBeInTheDocument();
  });
});
