/**
 * Market Summary Component
 *
 * Displays market sentiment, summary text, and last updated information.
 */
import React from 'react';
import styled from 'styled-components';
import { Badge } from '@adhd-trading-dashboard/shared';
import { MarketSentiment } from '../types';

export interface MarketSummaryProps {
  /** Market sentiment */
  sentiment: MarketSentiment;
  /** Market summary text */
  summary: string;
  /** Last updated timestamp */
  lastUpdated?: string;
}

// Styled components
const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const SentimentContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const SentimentLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const LastUpdated = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Summary = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.md};
  line-height: 1.5;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

/**
 * Get the badge variant for a market sentiment
 */
const getSentimentVariant = (sentiment: MarketSentiment) => {
  switch (sentiment) {
    case 'bullish':
      return 'success';
    case 'bearish':
      return 'error';
    case 'neutral':
    default:
      return 'neutral';
  }
};

/**
 * Format a date string
 */
const formatDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Market Summary Component
 *
 * Displays market sentiment, summary text, and last updated information.
 */
export const MarketSummary: React.FC<MarketSummaryProps> = ({
  sentiment,
  summary,
  lastUpdated,
}) => {
  return (
    <>
      <Header>
        <SentimentContainer>
          <SentimentLabel>Market Sentiment:</SentimentLabel>
          <Badge variant={getSentimentVariant(sentiment)} solid>
            {sentiment.toUpperCase()}
          </Badge>
        </SentimentContainer>
        <LastUpdated>Last updated: {formatDate(lastUpdated)}</LastUpdated>
      </Header>

      <Summary>{summary}</Summary>
    </>
  );
};
