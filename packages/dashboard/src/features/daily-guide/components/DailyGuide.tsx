/**
 * Daily Guide Component
 *
 * The main component for the daily guide feature.
 */
import React from 'react';
import { Card, Button } from '@adhd-trading-dashboard/shared';
import { DailyGuideProvider } from '../state';
import { useDailyGuide } from '../hooks';
import { MarketOverview } from './MarketOverview';
import { TradingPlan } from './TradingPlan';
import { KeyLevels } from './KeyLevels';
import styled from 'styled-components';

export interface DailyGuideProps {
  /** The title of the component */
  title?: string;
  /** Additional class name */
  className?: string;
}

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const DateSelector = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`;

const DateInput = styled.input`
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  
  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
`;

const Column = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

/**
 * Daily Guide Content Component
 * 
 * The connected component that uses the daily guide hook.
 */
const DailyGuideContent: React.FC<{ className?: string }> = ({ className }) => {
  const {
    selectedDate,
    marketOverview,
    tradingPlan,
    keyPriceLevels,
    isLoading,
    error,
    currentDate,
    onDateChange,
    onTradingPlanItemToggle,
    onAddTradingPlanItem,
    onRemoveTradingPlanItem,
    onRefresh,
  } = useDailyGuide();

  return (
    <Container className={className}>
      <Header>
        <Title>Daily Trading Guide - {currentDate}</Title>
        <DateSelector>
          <DateInput
            type="date"
            value={selectedDate}
            onChange={(e) => onDateChange(e.target.value)}
            max={new Date().toISOString().split('T')[0]}
          />
          <Button onClick={onRefresh} startIcon="🔄">
            Refresh
          </Button>
        </DateSelector>
      </Header>

      <Grid>
        <Column>
          <MarketOverview
            marketOverview={marketOverview}
            isLoading={isLoading}
            error={error}
            onRefresh={onRefresh}
          />
          <KeyLevels
            keyLevels={keyPriceLevels}
            isLoading={isLoading}
            error={error}
            onRefresh={onRefresh}
          />
        </Column>
        <Column>
          <TradingPlan
            tradingPlan={tradingPlan}
            isLoading={isLoading}
            error={error}
            onItemToggle={onTradingPlanItemToggle}
            onItemAdd={onAddTradingPlanItem}
            onItemRemove={onRemoveTradingPlanItem}
          />
        </Column>
      </Grid>
    </Container>
  );
};

/**
 * Daily Guide Component
 * 
 * The main component for the daily guide feature.
 */
export const DailyGuide: React.FC<DailyGuideProps> = ({
  title = 'Daily Guide',
  className,
}) => {
  return (
    <DailyGuideProvider>
      <Card title={title}>
        <DailyGuideContent className={className} />
      </Card>
    </DailyGuideProvider>
  );
};
