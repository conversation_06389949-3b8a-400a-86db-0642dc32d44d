/**
 * Market News Component
 *
 * Displays economic events and news in a structured table format.
 */
import React from 'react';
import styled from 'styled-components';
import { Badge } from '@adhd-trading-dashboard/shared';
import { EconomicEvent } from '../types';

export interface MarketNewsProps {
  /** Array of economic events */
  events: EconomicEvent[];
}

// Styled components
const EventsContainer = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

const EventsHeader = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes.md};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
  color: ${({ theme }) => theme.colors.textPrimary};
  font-weight: 600;
`;

const EventsGrid = styled.div`
  display: grid;
  grid-template-columns: 80px 1fr 100px 100px 100px;
  gap: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  overflow: hidden;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0;
  }
`;

const EventsGridHeader = styled.div`
  font-weight: 600;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.surface};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  @media (max-width: 768px) {
    display: none;
  }
`;

const EventsGridRow = styled.div`
  display: contents;

  & > div {
    padding: ${({ theme }) => theme.spacing.sm};
    border-bottom: 1px solid ${({ theme }) => theme.colors.border};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    display: flex;
    align-items: center;
  }

  &:last-child > div {
    border-bottom: none;
  }

  &:hover > div {
    background-color: ${({ theme }) => theme.colors.hover};
  }

  @media (max-width: 768px) {
    display: block;
    border-bottom: 1px solid ${({ theme }) => theme.colors.border};

    &:last-child {
      border-bottom: none;
    }

    & > div {
      border-bottom: none;
      padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};

      &:before {
        content: attr(data-label);
        font-weight: 600;
        color: ${({ theme }) => theme.colors.textSecondary};
        margin-right: ${({ theme }) => theme.spacing.sm};
        min-width: 80px;
        display: inline-block;
      }
    }
  }
`;

const EventTitle = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  flex-wrap: wrap;
`;

const EventTime = styled.div`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const EventValue = styled.div<{ isActual?: boolean }>`
  font-weight: ${({ isActual }) => (isActual ? '600' : '400')};
  color: ${({ theme, isActual }) =>
    isActual ? theme.colors.textPrimary : theme.colors.textSecondary};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

/**
 * Market News Component
 *
 * Displays economic events and news in a structured table format.
 */
export const MarketNews: React.FC<MarketNewsProps> = ({ events }) => {
  if (!events || events.length === 0) {
    return (
      <EventsContainer>
        <EventsHeader>Economic Events</EventsHeader>
        <EmptyState>No economic events scheduled for today</EmptyState>
      </EventsContainer>
    );
  }

  return (
    <EventsContainer>
      <EventsHeader>Economic Events</EventsHeader>
      <EventsGrid>
        <EventsGridHeader>Time</EventsGridHeader>
        <EventsGridHeader>Event</EventsGridHeader>
        <EventsGridHeader>Expected</EventsGridHeader>
        <EventsGridHeader>Previous</EventsGridHeader>
        <EventsGridHeader>Actual</EventsGridHeader>

        {events.map((event, index) => (
          <EventsGridRow key={index}>
            <EventTime data-label="Time:">{event.time}</EventTime>
            <EventTitle data-label="Event:">
              {event.title}
              {event.importance === 'high' && (
                <Badge variant="error" size="small">
                  High Impact
                </Badge>
              )}
              {event.importance === 'medium' && (
                <Badge variant="warning" size="small">
                  Medium Impact
                </Badge>
              )}
            </EventTitle>
            <EventValue data-label="Expected:">{event.expected || '-'}</EventValue>
            <EventValue data-label="Previous:">{event.previous || '-'}</EventValue>
            <EventValue data-label="Actual:" isActual>
              {event.actual || 'Pending'}
            </EventValue>
          </EventsGridRow>
        ))}
      </EventsGrid>
    </EventsContainer>
  );
};
