/**
 * Trading Plan Component
 *
 * A component for displaying and managing a trading plan.
 */
import React, { useState } from 'react';
import { Card, Badge, Button, Input, FormField } from '@adhd-trading-dashboard/shared';
import { TradingPlan as TradingPlanType, TradingPlanItem, TradingPlanPriority } from '../types';
import styled from 'styled-components';

export interface TradingPlanProps {
  /** The trading plan data */
  tradingPlan: TradingPlanType | null;
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when a trading plan item is toggled */
  onItemToggle?: (id: string, completed: boolean) => void;
  /** Function called when a trading plan item is added */
  onItemAdd?: (item: TradingPlanItem) => void;
  /** Function called when a trading plan item is removed */
  onItemRemove?: (id: string) => void;
  /** Additional class name */
  className?: string;
}

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const Section = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes.md};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const PlanList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const PlanItem = styled.div<{ completed?: boolean }>`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  opacity: ${({ completed }) => (completed ? 0.6 : 1)};
  transition: opacity 0.2s ease;
`;

const CheckboxContainer = styled.div`
  margin-right: ${({ theme }) => theme.spacing.sm};
`;

const Checkbox = styled.input`
  cursor: pointer;
  width: 18px;
  height: 18px;
`;

const ItemContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  flex: 1;
`;

const Description = styled.div<{ completed?: boolean }>`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  text-decoration: ${({ completed }) => (completed ? 'line-through' : 'none')};
`;

const ItemActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const RiskManagementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.sm};
`;

const RiskManagementItem = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  flex-direction: column;
`;

const RiskManagementLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const RiskManagementValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const Notes = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
  line-height: 1.5;
  color: ${({ theme }) => theme.colors.textPrimary};
  white-space: pre-wrap;
`;

const AddItemForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

/**
 * Get the badge variant for a priority
 */
const getPriorityVariant = (priority: TradingPlanPriority) => {
  switch (priority) {
    case 'high':
      return 'error';
    case 'medium':
      return 'warning';
    case 'low':
    default:
      return 'info';
  }
};

/**
 * Trading Plan Component
 *
 * A component for displaying and managing a trading plan.
 */
export const TradingPlan: React.FC<TradingPlanProps> = ({
  tradingPlan,
  isLoading = false,
  error = null,
  onItemToggle,
  onItemAdd,
  onItemRemove,
  className,
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newItem, setNewItem] = useState<Omit<TradingPlanItem, 'id'>>({
    description: '',
    priority: 'medium',
    completed: false,
  });

  const handleAddItem = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newItem.description.trim() || !onItemAdd) return;

    onItemAdd({
      ...newItem,
      id: Date.now().toString(), // Generate a unique ID
    });

    setNewItem({
      description: '',
      priority: 'medium',
      completed: false,
    });

    setShowAddForm(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <Card title="Trading Plan">
        <div style={{ padding: '24px', textAlign: 'center' }}>Loading trading plan...</div>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card title="Trading Plan">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>Error: {error}</div>
      </Card>
    );
  }

  // Empty state
  if (!tradingPlan) {
    return (
      <Card title="Trading Plan">
        <EmptyState>
          No trading plan available.
          {onItemAdd && (
            <div style={{ marginTop: '16px' }}>
              <Button onClick={() => setShowAddForm(true)}>Create Trading Plan</Button>
            </div>
          )}
        </EmptyState>
      </Card>
    );
  }

  return (
    <Card
      title="Trading Plan"
      actions={
        onItemAdd
          ? [{ label: 'Add Item', onClick: () => setShowAddForm(true), icon: '➕' }]
          : undefined
      }
    >
      <Container className={className}>
        {/* Strategy */}
        {tradingPlan.strategy && (
          <Section>
            <SectionTitle>Strategy</SectionTitle>
            <Notes>{tradingPlan.strategy}</Notes>
          </Section>
        )}

        {/* Trading Plan Items */}
        <Section>
          <SectionTitle>Action Items</SectionTitle>
          <PlanList>
            {tradingPlan.items.map((item) => (
              <PlanItem key={item.id} completed={item.completed}>
                <CheckboxContainer>
                  <Checkbox
                    type="checkbox"
                    checked={!!item.completed}
                    onChange={(e) => onItemToggle?.(item.id, e.target.checked)}
                    disabled={!onItemToggle}
                  />
                </CheckboxContainer>
                <ItemContent>
                  <Description completed={item.completed}>{item.description}</Description>
                  <Badge variant={getPriorityVariant(item.priority)}>{item.priority}</Badge>
                </ItemContent>
                {onItemRemove && (
                  <ItemActions>
                    <Button
                      variant="icon"
                      onClick={() => onItemRemove(item.id)}
                      aria-label="Remove item"
                    >
                      🗑️
                    </Button>
                  </ItemActions>
                )}
              </PlanItem>
            ))}
          </PlanList>
        </Section>

        {/* Risk Management */}
        {tradingPlan.riskManagement && (
          <Section>
            <SectionTitle>Risk Management</SectionTitle>
            <RiskManagementGrid>
              <RiskManagementItem>
                <RiskManagementLabel>Max Risk Per Trade</RiskManagementLabel>
                <RiskManagementValue>
                  {tradingPlan.riskManagement.maxRiskPerTrade}%
                </RiskManagementValue>
              </RiskManagementItem>
              <RiskManagementItem>
                <RiskManagementLabel>Max Daily Loss</RiskManagementLabel>
                <RiskManagementValue>
                  {tradingPlan.riskManagement.maxDailyLoss}%
                </RiskManagementValue>
              </RiskManagementItem>
              <RiskManagementItem>
                <RiskManagementLabel>Max Trades</RiskManagementLabel>
                <RiskManagementValue>{tradingPlan.riskManagement.maxTrades}</RiskManagementValue>
              </RiskManagementItem>
              <RiskManagementItem>
                <RiskManagementLabel>Position Sizing</RiskManagementLabel>
                <RiskManagementValue>
                  {tradingPlan.riskManagement.positionSizing}
                </RiskManagementValue>
              </RiskManagementItem>
            </RiskManagementGrid>
          </Section>
        )}

        {/* Notes */}
        {tradingPlan.notes && (
          <Section>
            <SectionTitle>Notes</SectionTitle>
            <Notes>{tradingPlan.notes}</Notes>
          </Section>
        )}

        {/* Add Item Form */}
        {showAddForm && (
          <AddItemForm onSubmit={handleAddItem}>
            <FormField label="Description">
              <Input
                value={newItem.description}
                onChange={(value) => setNewItem({ ...newItem, description: value })}
                placeholder="Enter task description"
                required
                fullWidth
              />
            </FormField>
            <FormField label="Priority">
              <select
                value={newItem.priority}
                onChange={(e) =>
                  setNewItem({ ...newItem, priority: e.target.value as TradingPlanPriority })
                }
                style={{
                  padding: '8px',
                  borderRadius: '4px',
                  border: '1px solid #ccc',
                  width: '100%',
                }}
              >
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </FormField>
            <FormActions>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                Cancel
              </Button>
              <Button type="submit">Add Item</Button>
            </FormActions>
          </AddItemForm>
        )}
      </Container>
    </Card>
  );
};
