/**
 * Daily Guide Types
 *
 * Type definitions for the daily guide feature.
 */

/**
 * Market Sentiment
 */
export type MarketSentiment = 'bullish' | 'bearish' | 'neutral';

/**
 * Trading Plan Priority
 */
export type TradingPlanPriority = 'high' | 'medium' | 'low';

/**
 * Market Index
 */
export interface MarketIndex {
  /** The index symbol */
  symbol: string;
  /** The index name */
  name: string;
  /** The current value */
  value: number;
  /** The change from previous close */
  change: number;
  /** The percentage change from previous close */
  changePercent: number;
  /** The previous close value */
  previousClose?: number;
}

/**
 * Economic Event
 */
export interface EconomicEvent {
  /** The event title */
  title: string;
  /** The event time */
  time: string;
  /** The event importance */
  importance: 'high' | 'medium' | 'low';
  /** The expected value */
  expected?: string;
  /** The previous value */
  previous?: string;
  /** The actual value (if released) */
  actual?: string;
}

/**
 * Market News Item
 */
export interface MarketNewsItem {
  /** The news item ID */
  id: string;
  /** The news item title */
  title: string;
  /** The news item source */
  source: string;
  /** The news item timestamp */
  timestamp: string;
  /** The news item URL */
  url: string;
  /** The news item impact */
  impact: 'high' | 'medium' | 'low';
  /** The news item sentiment */
  sentiment?: 'positive' | 'negative' | 'neutral';
}

/**
 * Market Overview
 */
export interface MarketOverview {
  /** The overall market sentiment */
  sentiment: MarketSentiment;
  /** The market summary */
  summary: string;
  /** Major indices data */
  indices: MarketIndex[];
  /** Economic events for the day */
  economicEvents: EconomicEvent[];
  /** Market news items */
  news: MarketNewsItem[];
  /** Last updated timestamp */
  lastUpdated: string;
}

/**
 * Trading Plan Item
 */
export interface TradingPlanItem {
  /** The item ID */
  id: string;
  /** The item description */
  description: string;
  /** The item priority */
  priority: TradingPlanPriority;
  /** Whether the item is completed */
  completed?: boolean;
}

/**
 * Risk Management
 */
export interface RiskManagement {
  /** The maximum risk per trade */
  maxRiskPerTrade: number;
  /** The maximum daily loss */
  maxDailyLoss: number;
  /** The maximum number of trades */
  maxTrades: number;
  /** The position sizing strategy */
  positionSizing: string;
}

/**
 * Trading Plan
 */
export interface TradingPlan {
  /** The trading plan items */
  items: TradingPlanItem[];
  /** The overall strategy for the day */
  strategy: string;
  /** The risk management plan */
  riskManagement: RiskManagement;
  /** Notes for the day */
  notes: string;
}

/**
 * Watchlist Item
 */
export interface WatchlistItem {
  /** The symbol */
  symbol: string;
  /** The company name */
  name: string;
  /** The current price */
  price: number;
  /** The reason for watching */
  reason: string;
  /** The potential trade setup */
  setup?: string;
  /** The entry price */
  entryPrice?: number;
  /** The stop loss price */
  stopLoss?: number;
  /** The take profit price */
  takeProfit?: number;
}

/**
 * Key Price Level
 */
export interface KeyPriceLevel {
  /** The symbol */
  symbol: string;
  /** Support levels */
  support: string[];
  /** Resistance levels */
  resistance: string[];
  /** Pivot point */
  pivotPoint?: string;
  /** The level description */
  description?: string;
}

/**
 * Daily Guide Data
 */
export interface DailyGuideData {
  /** The market overview */
  marketOverview: MarketOverview | null;
  /** The trading plan */
  tradingPlan: TradingPlan | null;
  /** The key price levels */
  keyPriceLevels: KeyPriceLevel[];
  /** The watchlist */
  watchlist: WatchlistItem[];
  /** Market news items */
  marketNews: MarketNewsItem[];
}

/**
 * Daily Guide State
 */
export interface DailyGuideState {
  /** The daily guide data */
  data: DailyGuideData;
  /** Whether the data is loading */
  isLoading: boolean;
  /** Any error that occurred */
  error: string | null;
  /** The selected date */
  selectedDate: string;
}

/**
 * Daily Guide User Preferences
 */
export interface DailyGuidePreferences {
  /** Default watchlist symbols */
  defaultWatchlist: string[];
  /** Whether to show economic events */
  showEconomicEvents: boolean;
  /** Whether to show market news */
  showMarketNews: boolean;
  /** The default risk management settings */
  defaultRiskManagement: RiskManagement;
}
