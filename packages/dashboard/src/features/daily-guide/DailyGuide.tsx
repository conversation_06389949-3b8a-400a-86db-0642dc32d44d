/**
 * Daily Guide Page
 *
 * This page displays daily trading guidance and market insights.
 */

import React from 'react';
import styled from 'styled-components';
import { Button } from '@adhd-trading-dashboard/shared';
import { MarketOverview } from './components/MarketOverview';
import { TradingPlan } from './components/TradingPlan';
import { KeyLevels } from './components/KeyLevels';
import { MarketNews } from './components/MarketNews';
import { SectionCard } from './components/ui';
import { DailyGuideProvider, useDailyGuide } from './context/DailyGuideContext';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes.xxl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const DateDisplay = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const RefreshButton = styled(Button)`
  margin-left: ${({ theme }) => theme.spacing.md};
`;

const DailyGuideContent: React.FC = () => {
  const { isLoading, error, currentDate, refreshData } = useDailyGuide();

  const handleRefresh = () => {
    refreshData();
  };

  return (
    <PageContainer>
      <PageHeader>
        <Title>Daily Trading Guide</Title>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DateDisplay>{currentDate}</DateDisplay>
          <RefreshButton
            variant="outline"
            size="small"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            Refresh
          </RefreshButton>
        </div>
      </PageHeader>

      <SectionCard
        title="Market Overview"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <MarketOverview />
      </SectionCard>

      <SectionCard
        title="Trading Plan"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <TradingPlan />
      </SectionCard>

      <SectionCard
        title="Key Levels"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <KeyLevels />
      </SectionCard>

      <SectionCard
        title="Market News"
        isLoading={isLoading}
        hasError={!!error}
        errorMessage={error || ''}
      >
        <MarketNews />
      </SectionCard>
    </PageContainer>
  );
};

const DailyGuide: React.FC = () => {
  return (
    <DailyGuideProvider>
      <DailyGuideContent />
    </DailyGuideProvider>
  );
};

export default DailyGuide;
