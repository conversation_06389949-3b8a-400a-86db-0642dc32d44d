/**
 * Daily Guide State
 *
 * State management for the daily guide feature.
 *
 * This module provides:
 * - State interface and initial state
 * - Action types and action creators
 * - Reducer function
 * - Context provider and hooks
 *
 * @example
 * ```tsx
 * // Wrap your component with the provider
 * <DailyGuideProvider>
 *   <YourComponent />
 * </DailyGuideProvider>
 *
 * // Use the state in your component
 * const { state, dispatch } = useDailyGuideStore();
 * const value = useDailyGuideSelector(selectSomething);
 * const action = useDailyGuideAction(dailyGuideActions.doSomething);
 * ```
 */
import { createStoreContext, persistState } from '@adhd-trading-dashboard/shared';
import { 
  DailyGuideState, 
  DailyGuideData, 
  TradingPlanItem, 
  DailyGuidePreferences 
} from '../types';

// Action types
export enum DailyGuideActionTypes {
  FETCH_DATA_START = 'dailyGuide/FETCH_DATA_START',
  FETCH_DATA_SUCCESS = 'dailyGuide/FETCH_DATA_SUCCESS',
  FETCH_DATA_ERROR = 'dailyGuide/FETCH_DATA_ERROR',
  UPDATE_TRADING_PLAN_ITEM = 'dailyGuide/UPDATE_TRADING_PLAN_ITEM',
  ADD_TRADING_PLAN_ITEM = 'dailyGuide/ADD_TRADING_PLAN_ITEM',
  REMOVE_TRADING_PLAN_ITEM = 'dailyGuide/REMOVE_TRADING_PLAN_ITEM',
  UPDATE_SELECTED_DATE = 'dailyGuide/UPDATE_SELECTED_DATE',
  UPDATE_MARKET_OVERVIEW = 'dailyGuide/UPDATE_MARKET_OVERVIEW',
  UPDATE_KEY_PRICE_LEVELS = 'dailyGuide/UPDATE_KEY_PRICE_LEVELS',
  UPDATE_PREFERENCES = 'dailyGuide/UPDATE_PREFERENCES',
  RESET_STATE = 'dailyGuide/RESET_STATE',
}

// Action interfaces
export interface FetchDataStartAction {
  type: DailyGuideActionTypes.FETCH_DATA_START;
}

export interface FetchDataSuccessAction {
  type: DailyGuideActionTypes.FETCH_DATA_SUCCESS;
  payload: DailyGuideData;
}

export interface FetchDataErrorAction {
  type: DailyGuideActionTypes.FETCH_DATA_ERROR;
  payload: string;
}

export interface UpdateTradingPlanItemAction {
  type: DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM;
  payload: {
    id: string;
    completed: boolean;
  };
}

export interface AddTradingPlanItemAction {
  type: DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM;
  payload: TradingPlanItem;
}

export interface RemoveTradingPlanItemAction {
  type: DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM;
  payload: string; // Item ID
}

export interface UpdateSelectedDateAction {
  type: DailyGuideActionTypes.UPDATE_SELECTED_DATE;
  payload: string; // Date string
}

export interface UpdateMarketOverviewAction {
  type: DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW;
  payload: DailyGuideData['marketOverview'];
}

export interface UpdateKeyPriceLevelsAction {
  type: DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS;
  payload: DailyGuideData['keyPriceLevels'];
}

export interface UpdatePreferencesAction {
  type: DailyGuideActionTypes.UPDATE_PREFERENCES;
  payload: Partial<DailyGuidePreferences>;
}

export interface ResetStateAction {
  type: DailyGuideActionTypes.RESET_STATE;
}

// Union type for all actions
export type DailyGuideAction =
  | FetchDataStartAction
  | FetchDataSuccessAction
  | FetchDataErrorAction
  | UpdateTradingPlanItemAction
  | AddTradingPlanItemAction
  | RemoveTradingPlanItemAction
  | UpdateSelectedDateAction
  | UpdateMarketOverviewAction
  | UpdateKeyPriceLevelsAction
  | UpdatePreferencesAction
  | ResetStateAction;

// Initial state
export const initialDailyGuideState: DailyGuideState = {
  data: {
    marketOverview: null,
    tradingPlan: null,
    keyPriceLevels: [],
    watchlist: [],
    marketNews: [],
  },
  isLoading: false,
  error: null,
  selectedDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
};

// Reducer
export const dailyGuideReducer = (
  state: DailyGuideState,
  action: DailyGuideAction
): DailyGuideState => {
  switch (action.type) {
    case DailyGuideActionTypes.FETCH_DATA_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case DailyGuideActionTypes.FETCH_DATA_SUCCESS:
      return {
        ...state,
        data: action.payload,
        isLoading: false,
        error: null,
      };
    case DailyGuideActionTypes.FETCH_DATA_ERROR:
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM:
      if (!state.data.tradingPlan) {
        return state;
      }
      return {
        ...state,
        data: {
          ...state.data,
          tradingPlan: {
            ...state.data.tradingPlan,
            items: state.data.tradingPlan.items.map(item =>
              item.id === action.payload.id
                ? { ...item, completed: action.payload.completed }
                : item
            ),
          },
        },
      };
    case DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM:
      if (!state.data.tradingPlan) {
        return {
          ...state,
          data: {
            ...state.data,
            tradingPlan: {
              items: [action.payload],
              strategy: '',
              riskManagement: {
                maxRiskPerTrade: 0,
                maxDailyLoss: 0,
                maxTrades: 0,
                positionSizing: '',
              },
              notes: '',
            },
          },
        };
      }
      return {
        ...state,
        data: {
          ...state.data,
          tradingPlan: {
            ...state.data.tradingPlan,
            items: [...state.data.tradingPlan.items, action.payload],
          },
        },
      };
    case DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM:
      if (!state.data.tradingPlan) {
        return state;
      }
      return {
        ...state,
        data: {
          ...state.data,
          tradingPlan: {
            ...state.data.tradingPlan,
            items: state.data.tradingPlan.items.filter(
              item => item.id !== action.payload
            ),
          },
        },
      };
    case DailyGuideActionTypes.UPDATE_SELECTED_DATE:
      return {
        ...state,
        selectedDate: action.payload,
      };
    case DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW:
      return {
        ...state,
        data: {
          ...state.data,
          marketOverview: action.payload,
        },
      };
    case DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS:
      return {
        ...state,
        data: {
          ...state.data,
          keyPriceLevels: action.payload,
        },
      };
    case DailyGuideActionTypes.RESET_STATE:
      return {
        ...initialDailyGuideState,
        selectedDate: state.selectedDate, // Preserve selected date
      };
    default:
      return state;
  }
};

// Persist state
const { reducer: persistedReducer, initialState: persistedInitialState } = persistState(
  dailyGuideReducer,
  {
    key: 'dailyGuide',
    initialState: initialDailyGuideState,
    version: 1,
    filter: (state) => ({
      // Only persist user preferences, not transient data
      selectedDate: state.selectedDate,
    }),
  }
);

// Create store context
export const {
  Context: DailyGuideContext,
  Provider: DailyGuideProvider,
  useStore: useDailyGuideStore,
  useSelector: useDailyGuideSelector,
  useAction: useDailyGuideAction,
  useActions: useDailyGuideActions,
} = createStoreContext<DailyGuideState, DailyGuideAction>(
  persistedReducer,
  persistedInitialState,
  'DailyGuideContext'
);

// Action creators
export const dailyGuideActions = {
  fetchDataStart: (): FetchDataStartAction => ({
    type: DailyGuideActionTypes.FETCH_DATA_START,
  }),
  fetchDataSuccess: (data: DailyGuideData): FetchDataSuccessAction => ({
    type: DailyGuideActionTypes.FETCH_DATA_SUCCESS,
    payload: data,
  }),
  fetchDataError: (error: string): FetchDataErrorAction => ({
    type: DailyGuideActionTypes.FETCH_DATA_ERROR,
    payload: error,
  }),
  updateTradingPlanItem: (id: string, completed: boolean): UpdateTradingPlanItemAction => ({
    type: DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM,
    payload: { id, completed },
  }),
  addTradingPlanItem: (item: TradingPlanItem): AddTradingPlanItemAction => ({
    type: DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM,
    payload: item,
  }),
  removeTradingPlanItem: (id: string): RemoveTradingPlanItemAction => ({
    type: DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM,
    payload: id,
  }),
  updateSelectedDate: (date: string): UpdateSelectedDateAction => ({
    type: DailyGuideActionTypes.UPDATE_SELECTED_DATE,
    payload: date,
  }),
  updateMarketOverview: (overview: DailyGuideData['marketOverview']): UpdateMarketOverviewAction => ({
    type: DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW,
    payload: overview,
  }),
  updateKeyPriceLevels: (levels: DailyGuideData['keyPriceLevels']): UpdateKeyPriceLevelsAction => ({
    type: DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS,
    payload: levels,
  }),
  resetState: (): ResetStateAction => ({
    type: DailyGuideActionTypes.RESET_STATE,
  }),
};
