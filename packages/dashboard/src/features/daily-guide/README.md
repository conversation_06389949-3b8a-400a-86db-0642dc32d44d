# Daily Guide Feature

The Daily Guide feature provides traders with a comprehensive daily overview of market conditions, a customizable trading plan, and key price levels for securities of interest.

## Components

- `DailyGuide`: Main component that combines all other components
- `MarketOverview`: Displays current market conditions and sentiment
- `TradingPlan`: Displays and manages the trading plan for the day
- `KeyLevels`: Displays key support and resistance levels for securities

## State Management

This feature uses the standard state management pattern with:

- State interface and initial state
- Action types and action creators
- Reducer function
- Context provider and hooks
- Selectors for derived state

### State Structure

```typescript
interface DailyGuideState {
  data: {
    marketOverview: MarketOverview | null;
    tradingPlan: TradingPlan | null;
    keyPriceLevels: KeyPriceLevel[];
    watchlist: WatchlistItem[];
    marketNews: MarketNewsItem[];
  };
  isLoading: boolean;
  error: string | null;
  selectedDate: string;
}
```

### Actions

- `FETCH_DATA_START`: Start loading data
- `FETCH_DATA_SUCCESS`: Successfully loaded data
- `FETCH_DATA_ERROR`: Error loading data
- `UPDATE_TRADING_PLAN_ITEM`: Update a trading plan item
- `ADD_TRADING_PLAN_ITEM`: Add a trading plan item
- `REMOVE_TRADING_PLAN_ITEM`: Remove a trading plan item
- `UPDATE_SELECTED_DATE`: Update the selected date
- `UPDATE_MARKET_OVERVIEW`: Update the market overview
- `UPDATE_KEY_PRICE_LEVELS`: Update the key price levels
- `RESET_STATE`: Reset the state

### Selectors

- `selectMarketOverview`: Select the market overview
- `selectTradingPlan`: Select the trading plan
- `selectKeyPriceLevels`: Select the key price levels
- `selectTradingPlanItems`: Select the trading plan items
- `selectCompletedTradingPlanItems`: Select the completed trading plan items
- `selectIncompleteTradingPlanItems`: Select the incomplete trading plan items
- `selectTradingPlanCompletion`: Select the trading plan completion percentage
- `selectMarketIndices`: Select the market indices
- `selectMarketSentiment`: Select the market sentiment
- `selectEconomicEvents`: Select the economic events
- `selectHighImpactEconomicEvents`: Select the high impact economic events

## API Integration

This feature integrates with the following API endpoints:

- `GET /api/daily-guide?date={date}`: Get the daily guide data for a specific date

## Usage

```tsx
import { DailyGuide } from '@adhd-trading-dashboard/dashboard';

const App = () => {
  return <DailyGuide />;
};
```

## Customization

The `DailyGuide` component accepts the following props:

- `title`: The title of the component (default: 'Daily Guide')
- `className`: Additional class name for styling

## Examples

### Basic Usage

```tsx
import { DailyGuide } from '@adhd-trading-dashboard/dashboard';

const App = () => {
  return <DailyGuide />;
};
```

### Custom Title

```tsx
import { DailyGuide } from '@adhd-trading-dashboard/dashboard';

const App = () => {
  return <DailyGuide title="My Daily Trading Guide" />;
};
```

### Custom Styling

```tsx
import { DailyGuide } from '@adhd-trading-dashboard/dashboard';
import styled from 'styled-components';

const StyledDailyGuide = styled(DailyGuide)`
  background-color: #f5f5f5;
  padding: 16px;
`;

const App = () => {
  return <StyledDailyGuide />;
};
```

## Using Individual Components

You can also use the individual components separately:

```tsx
import { 
  MarketOverview, 
  TradingPlan, 
  KeyLevels 
} from '@adhd-trading-dashboard/dashboard';
import { useDailyGuide } from '@adhd-trading-dashboard/dashboard';

const MyComponent = () => {
  const {
    marketOverview,
    tradingPlan,
    keyPriceLevels,
    isLoading,
    error,
    onTradingPlanItemToggle,
    onRefresh,
  } = useDailyGuide();

  return (
    <div>
      <MarketOverview
        marketOverview={marketOverview}
        isLoading={isLoading}
        error={error}
        onRefresh={onRefresh}
      />
      <TradingPlan
        tradingPlan={tradingPlan}
        isLoading={isLoading}
        error={error}
        onItemToggle={onTradingPlanItemToggle}
      />
      <KeyLevels
        keyLevels={keyPriceLevels}
        isLoading={isLoading}
        error={error}
        onRefresh={onRefresh}
      />
    </div>
  );
};
```
