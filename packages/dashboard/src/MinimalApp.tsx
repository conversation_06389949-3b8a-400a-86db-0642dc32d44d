import React from 'react';

/**
 * Minimal App Component
 * 
 * A simple component to test React rendering
 */
const MinimalApp: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      background: 'green', 
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <h1>Minimal App Working!</h1>
      <p>This is a minimal test component to verify React rendering</p>
      <button 
        style={{
          padding: '10px 20px',
          background: 'white',
          color: 'green',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginTop: '20px'
        }}
        onClick={() => alert('Button clicked!')}
      >
        Click Me
      </button>
    </div>
  );
};

export default MinimalApp;
