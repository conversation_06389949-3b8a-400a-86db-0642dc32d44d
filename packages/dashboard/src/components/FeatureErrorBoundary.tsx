/**
 * Feature Error Boundary
 *
 * An error boundary for feature modules.
 * This is a simplified version that uses the unified error boundary approach.
 */
import React from 'react';
import { FeatureErrorBoundary as UnifiedFeatureErrorBoundary } from '@adhd-trading-dashboard/shared';

/**
 * Feature Error Boundary Props
 */
export interface FeatureErrorBoundaryProps {
  children: React.ReactNode;
  featureName: string;
  onError?: (error: Error) => void;
  onSkip?: () => void;
}

/**
 * Feature Error Boundary
 *
 * An error boundary for feature modules.
 */
export const FeatureErrorBoundary: React.FC<FeatureErrorBoundaryProps> = ({
  children,
  featureName,
  onError,
  onSkip,
}) => {
  const handleError = (error: Error) => {
    console.error(`Error in feature "${featureName}":`, error);

    if (onError) {
      onError(error);
    }

    // Here you could also log to an error tracking service like Sentry
    // if (typeof window !== 'undefined' && window.Sentry) {
    //   window.Sentry.withScope((scope) => {
    //     scope.setTag('feature', featureName);
    //     scope.setTag('boundary', 'feature');
    //     window.Sentry.captureException(error);
    //   });
    // }
  };

  return (
    <UnifiedFeatureErrorBoundary featureName={featureName} onError={handleError} onSkip={onSkip}>
      {children}
    </UnifiedFeatureErrorBoundary>
  );
};

export default FeatureErrorBoundary;
