/**
 * FeatureErrorBoundary Tests
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest';
import FeatureErrorBoundary from '../FeatureErrorBoundary';

// Mock the ErrorBoundary component from shared package
vi.mock('@adhd-trading-dashboard/shared', async () => {
  const actual = await vi.importActual('@adhd-trading-dashboard/shared');
  return {
    ...(actual as any),
    ErrorBoundary: ({ children, fallback, onError }: any) => {
      // For testing, we'll render the fallback directly if the test case includes an error
      const error = new Error('Test error');
      const resetError = vi.fn();

      if (window.__TEST_WITH_ERROR__) {
        // Call onError if provided
        if (onError) {
          onError(error);
        }

        // Render fallback
        if (typeof fallback === 'function') {
          return fallback({ error, resetError });
        }
        return fallback || <div>Error fallback</div>;
      }

      // Otherwise render children
      return children;
    },
    ThemeProvider: ({ children }: any) => <>{children}</>,
  };
});

// Mock console.error to avoid test output noise
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = vi.fn();
  // @ts-ignore - Add a global flag to simulate error state
  window.__TEST_WITH_ERROR__ = false;
});
afterAll(() => {
  console.error = originalConsoleError;
  // @ts-ignore - Clean up
  delete window.__TEST_WITH_ERROR__;
});

describe('FeatureErrorBoundary', () => {
  it('renders children when there is no error', () => {
    // @ts-ignore - Set the global flag to false for this test
    window.__TEST_WITH_ERROR__ = false;

    render(
      <FeatureErrorBoundary featureName="Test Feature">
        <div data-testid="child">Child content</div>
      </FeatureErrorBoundary>
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
    expect(screen.getByText('Child content')).toBeInTheDocument();
  });

  it('renders the error UI when an error occurs', () => {
    // @ts-ignore - Set the global flag to true for this test
    window.__TEST_WITH_ERROR__ = true;

    render(
      <FeatureErrorBoundary featureName="Test Feature">
        <div>This content should not be visible</div>
      </FeatureErrorBoundary>
    );

    // The FeatureErrorFallback component should be rendered
    expect(screen.getByRole('heading', { name: /Error in Test Feature/i })).toBeInTheDocument();
    expect(
      screen.getByText(/We encountered a problem while loading this feature/i)
    ).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Try Again/i })).toBeInTheDocument();
  });

  it('calls onError when an error occurs', () => {
    // @ts-ignore - Set the global flag to true for this test
    window.__TEST_WITH_ERROR__ = true;

    const onError = vi.fn();
    render(
      <FeatureErrorBoundary featureName="Test Feature" onError={onError}>
        <div>This content should not be visible</div>
      </FeatureErrorBoundary>
    );

    expect(onError).toHaveBeenCalledTimes(1);
    expect(onError).toHaveBeenCalledWith(expect.objectContaining({ message: 'Test error' }));
  });

  it('calls onSkip when skip button is clicked', () => {
    // @ts-ignore - Set the global flag to true for this test
    window.__TEST_WITH_ERROR__ = true;

    const onSkip = vi.fn();
    render(
      <FeatureErrorBoundary featureName="Test Feature" onSkip={onSkip}>
        <div>This content should not be visible</div>
      </FeatureErrorBoundary>
    );

    const skipButton = screen.getByRole('button', { name: /Skip This Feature/i });
    fireEvent.click(skipButton);

    expect(onSkip).toHaveBeenCalledTimes(1);
  });
});
