/**
 * App Error Boundary
 *
 * A top-level error boundary for the entire application.
 * This is a simplified version that uses the unified error boundary approach.
 */
import React from 'react';
import { AppErrorBoundary as UnifiedAppErrorBoundary } from '@adhd-trading-dashboard/shared';

/**
 * App Error Boundary Props
 */
export interface AppErrorBoundaryProps {
  children: React.ReactNode;
}

/**
 * App Error Boundary
 *
 * A top-level error boundary for the entire application.
 */
export const AppErrorBoundary: React.FC<AppErrorBoundaryProps> = ({ children }) => {
  const handleError = (error: Error) => {
    // Log the error to the console
    console.error('Application Error:', error);

    // Here you could also log to an error tracking service like Sentry
    // if (typeof window !== 'undefined' && window.Sentry) {
    //   window.Sentry.withScope((scope) => {
    //     scope.setTag('boundary', 'app');
    //     window.Sentry.captureException(error);
    //   });
    // }
  };

  return (
    <UnifiedAppErrorBoundary onError={handleError} name="Application">
      {children}
    </UnifiedAppErrorBoundary>
  );
};

export default AppErrorBoundary;
