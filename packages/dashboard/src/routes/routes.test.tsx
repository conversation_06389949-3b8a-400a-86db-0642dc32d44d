import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { f1Theme } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from '../routes';

// Mock the lazy-loaded components
vi.mock('../pages/Dashboard', () => ({
  default: () => <div data-testid="dashboard-page">Dashboard Page</div>,
}));

vi.mock('../pages/TradeJournal', () => ({
  default: () => <div data-testid="trade-journal-page">Trade Journal Page</div>,
}));

vi.mock('../pages/TradeForm', () => ({
  default: () => <div data-testid="trade-form-page">Trade Form Page</div>,
}));

vi.mock('../pages/NotFound', () => ({
  default: () => <div data-testid="not-found-page">Not Found Page</div>,
}));

// Mock other components
vi.mock('./layouts/MainLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="main-layout">{children}</div>
  ),
}));

vi.mock('./components/molecules/LoadingScreen', () => ({
  default: () => <div data-testid="loading-screen">Loading...</div>,
}));

describe('AppRoutes Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  it('renders the dashboard page for the root route', async () => {
    render(
      <ThemeProvider theme={f1Theme}>
        <MemoryRouter initialEntries={['/']}>
          <AppRoutes />
        </MemoryRouter>
      </ThemeProvider>
    );

    // Wait for the lazy-loaded component to render
    await screen.findByTestId('dashboard-page');
    
    expect(screen.getByTestId('dashboard-page')).toBeInTheDocument();
  });

  it('renders the trade journal page for the /journal route', async () => {
    render(
      <ThemeProvider theme={f1Theme}>
        <MemoryRouter initialEntries={['/journal']}>
          <AppRoutes />
        </MemoryRouter>
      </ThemeProvider>
    );

    // Wait for the lazy-loaded component to render
    await screen.findByTestId('trade-journal-page');
    
    expect(screen.getByTestId('trade-journal-page')).toBeInTheDocument();
  });

  it('renders the trade form page for the /trade/new route', async () => {
    render(
      <ThemeProvider theme={f1Theme}>
        <MemoryRouter initialEntries={['/trade/new']}>
          <AppRoutes />
        </MemoryRouter>
      </ThemeProvider>
    );

    // Wait for the lazy-loaded component to render
    await screen.findByTestId('trade-form-page');
    
    expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
  });

  it('renders the trade form page for the /trade/edit/:id route', async () => {
    render(
      <ThemeProvider theme={f1Theme}>
        <MemoryRouter initialEntries={['/trade/edit/123']}>
          <AppRoutes />
        </MemoryRouter>
      </ThemeProvider>
    );

    // Wait for the lazy-loaded component to render
    await screen.findByTestId('trade-form-page');
    
    expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
  });

  it('renders the trade form page for the /trade/:id route', async () => {
    render(
      <ThemeProvider theme={f1Theme}>
        <MemoryRouter initialEntries={['/trade/123']}>
          <AppRoutes />
        </MemoryRouter>
      </ThemeProvider>
    );

    // Wait for the lazy-loaded component to render
    await screen.findByTestId('trade-form-page');
    
    expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
  });

  it('renders the not found page for an unknown route', async () => {
    render(
      <ThemeProvider theme={f1Theme}>
        <MemoryRouter initialEntries={['/unknown-route']}>
          <AppRoutes />
        </MemoryRouter>
      </ThemeProvider>
    );

    // Wait for the lazy-loaded component to render
    await screen.findByTestId('not-found-page');
    
    expect(screen.getByTestId('not-found-page')).toBeInTheDocument();
  });

  it('correctly prioritizes /trade/edit/:id over /trade/:id', async () => {
    // This test ensures that /trade/edit/123 is not matched by /trade/:id
    render(
      <ThemeProvider theme={f1Theme}>
        <MemoryRouter initialEntries={['/trade/edit/123']}>
          <AppRoutes />
        </MemoryRouter>
      </ThemeProvider>
    );

    // Wait for the lazy-loaded component to render
    await screen.findByTestId('trade-form-page');
    
    expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
    
    // We can't directly test that the route was matched correctly,
    // but we can check that the component rendered
  });
});
