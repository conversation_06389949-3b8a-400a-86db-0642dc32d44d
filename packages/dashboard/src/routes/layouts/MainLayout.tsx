/**
 * Main Layout Component
 *
 * Provides the main layout structure for the application
 */

import React from 'react';
import { Outlet, NavLink as RouterNavLink } from 'react-router-dom';
import styled from 'styled-components';

const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: ${({ theme }) => theme.colors.background};
`;

const Header = styled.header`
  background-color: ${({ theme }) => theme.colors.headerBackground};
  color: ${({ theme }) => theme.colors.textPrimary};
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const Logo = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: 700;
  color: ${({ theme }) => theme.colors.primary};
`;

const Navigation = styled.nav`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
`;

const NavLink = styled(RouterNavLink)`
  color: ${({ theme }) => theme.colors.textSecondary};
  text-decoration: none;
  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
  border-radius: ${({ theme }) => theme.borderRadius.sm};

  &:hover {
    color: ${({ theme }) => theme.colors.textPrimary};
    background-color: rgba(255, 255, 255, 0.05);
  }

  &.active {
    color: ${({ theme }) => theme.colors.primary};
    font-weight: 500;
  }
`;

const Main = styled.main`
  flex: 1;
  padding: ${({ theme }) => theme.spacing.md};
`;

const Footer = styled.footer`
  background-color: ${({ theme }) => theme.colors.headerBackground};
  color: ${({ theme }) => theme.colors.textSecondary};
  padding: ${({ theme }) => theme.spacing.md};
  text-align: center;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;

/**
 * MainLayout Component
 *
 * Provides the main layout structure for the application
 */
const MainLayout: React.FC = () => {
  return (
    <LayoutContainer>
      <Header>
        <Logo>ADHD Trading</Logo>
        <Navigation>
          <NavLink to="/" end>
            Dashboard
          </NavLink>
          <NavLink to="/daily-guide">Daily Guide</NavLink>
          <NavLink to="/journal">Journal</NavLink>
          <NavLink to="/trade-analysis">Trade Analysis</NavLink>
          <NavLink to="/settings">Settings</NavLink>
        </Navigation>
      </Header>

      <Main>
        <Outlet />
      </Main>

      <Footer>&copy; {new Date().getFullYear()} ADHD Trading Dashboard</Footer>
    </LayoutContainer>
  );
};

export default MainLayout;
