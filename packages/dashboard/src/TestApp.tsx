/**
 * Test App Component
 *
 * A minimal test component to verify React rendering
 */
import * as React from 'react';

/**
 * TestApp Component
 *
 * A minimal component for testing React rendering
 */
const TestApp: React.FC = () => {
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    console.log('TestApp mounted');
    return () => {
      console.log('TestApp unmounted');
    };
  }, []);

  const handleClick = () => {
    setCount((prev) => prev + 1);
  };

  const styles = {
    container: {
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      backgroundColor: '#1a1f2c',
      color: 'white',
      fontFamily: 'Inter, sans-serif',
    },
    heading: {
      fontSize: '2rem',
      marginBottom: '1rem',
      color: '#e10600',
    },
    button: {
      padding: '0.5rem 1rem',
      backgroundColor: '#e10600',
      color: 'white',
      border: 'none',
      borderRadius: '0.25rem',
      cursor: 'pointer',
      fontSize: '1rem',
      marginTop: '1rem',
    },
    counter: {
      fontSize: '1.5rem',
      marginTop: '1rem',
    },
  };

  return (
    <div style={styles.container}>
      <h1 style={styles.heading}>ADHD Trading Dashboard Test</h1>
      <p>This is a minimal test component to verify React rendering</p>
      <div style={styles.counter}>Count: {count}</div>
      <button style={styles.button} onClick={handleClick}>
        Increment
      </button>
    </div>
  );
};

export default TestApp;
