if(!self.define){let s,e={};const i=(i,r)=>(i=new URL(i+".js",r).href,e[i]||new Promise((e=>{if("document"in self){const s=document.createElement("script");s.src=i,s.onload=e,document.head.appendChild(s)}else s=i,importScripts(i),e()})).then((()=>{let s=e[i];if(!s)throw new Error(`Module ${i} didn’t register its module`);return s})));self.define=(r,l)=>{const n=s||("document"in self?document.currentScript.src:"")||location.href;if(e[n])return;let a={};const o=s=>i(s,n),u={module:{uri:n},exports:a,require:o};e[n]=Promise.all(r.map((s=>u[s]||o(s)))).then((s=>(l(...s),a)))}}define(["./workbox-a660b9f6"],(function(s){"use strict";self.skipWaiting(),s.clientsClaim(),s.precacheAndRoute([{url:"asset-test.html",revision:"5ac8a6cccf7228246a5e2466f13d070c"},{url:"assets/Card-a75b9d5e.js",revision:null},{url:"assets/client-4c27269c.js",revision:null},{url:"assets/create-images.js",revision:null},{url:"assets/DailyGuide-6bd79e86.js",revision:null},{url:"assets/Dashboard-c9f9e5d6.js",revision:null},{url:"assets/generate-placeholder-assets.js",revision:null},{url:"assets/index.css",revision:null},{url:"assets/main-2920b47d.js",revision:null},{url:"assets/NotFound-fd69771b.js",revision:null},{url:"assets/react-60374de9.js",revision:null},{url:"assets/recharts-7aea75de.js",revision:null},{url:"assets/router-e715efa2.js",revision:null},{url:"assets/Settings-57ab97e1.js",revision:null},{url:"assets/simple-dc6a3ba7.js",revision:null},{url:"assets/styled-components-3ebafa9a.js",revision:null},{url:"assets/TradeAnalysis-72da3a81.js",revision:null},{url:"assets/TradeForm-7631201a.js",revision:null},{url:"assets/TradeJournal-af586680.js",revision:null},{url:"assets/tradeStorage-0955231a.js",revision:null},{url:"assets/web-vitals-60d3425a.js",revision:null},{url:"favicon.ico",revision:"51f07ed252377bd14b092c5c34cfd3c9"},{url:"favicon.svg",revision:"a6752bb0b3b15b2de9cbb1059b4411e6"},{url:"index.html",revision:"6226275c070ad22cc86ef8723453b049"},{url:"logo192.png",revision:"85101af205c43ac3c1c69ffd4e0a1b75"},{url:"logo192.svg",revision:"042afe13204cb9bac32028a076568df3"},{url:"logo512.png",revision:"7c0514800de302ba3d2e0046e6aff763"},{url:"logo512.svg",revision:"15680eb9223d4853899c3529d56b4b4a"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"simple.html",revision:"6e87a343c3f6d6d2e73deb43fe74f19a"},{url:"favicon.ico",revision:"51f07ed252377bd14b092c5c34cfd3c9"},{url:"logo192.png",revision:"85101af205c43ac3c1c69ffd4e0a1b75"},{url:"logo512.png",revision:"7c0514800de302ba3d2e0046e6aff763"},{url:"manifest.webmanifest",revision:"96cb2fb514b8bdcb81e81f34652af3f5"}],{}),s.cleanupOutdatedCaches(),s.registerRoute(new s.NavigationRoute(s.createHandlerBoundToURL("index.html")))}));
//# sourceMappingURL=sw.js.map
