const l="adhd-trading-dashboard";const n="trades",u=()=>new Promise((i,e)=>{const r=indexedDB.open(l,1);r.onupgradeneeded=t=>{const a=t.target.result;if(!a.objectStoreNames.contains(n)){const c=a.createObjectStore(n,{keyPath:"id"});c.createIndex("date","date",{unique:!1}),c.createIndex("symbol","symbol",{unique:!1}),c.createIndex("direction","direction",{unique:!1})}},r.onsuccess=t=>{i(t.target.result)},r.onerror=t=>{console.error("IndexedDB error:",t.target.error),e(t.target.error)}});class g{constructor(){this.dbPromise=u()}async saveTrade(e){const r=await this.dbPromise;return new Promise((t,a)=>{const s=r.transaction(n,"readwrite").objectStore(n),o={...e,id:e.id||crypto.randomUUID()},d=s.put(o);d.onsuccess=()=>{t(o)},d.onerror=()=>{console.error("Error saving trade:",d.error),a(d.error)}})}async getAllTrades(){const e=await this.dbPromise;return new Promise((r,t)=>{const s=e.transaction(n,"readonly").objectStore(n).getAll();s.onsuccess=()=>{r(s.result)},s.onerror=()=>{console.error("Error getting trades:",s.error),t(s.error)}})}async getTradeById(e){if(console.log(`tradeStorage.getTradeById called with ID: "${e}"`),!e){console.error("getTradeById called with empty ID");return}try{const r=await this.dbPromise;return console.log("IndexedDB connection established for getTradeById"),new Promise((t,a)=>{const s=r.transaction(n,"readonly").objectStore(n);console.log(`Attempting to get trade with ID: "${e}" from IndexedDB`);const o=s.get(e);o.onsuccess=()=>{console.log(`IndexedDB get request succeeded for ID: "${e}"`),console.log("Result:",o.result),t(o.result)},o.onerror=()=>{console.error(`Error getting trade with ID: "${e}"`,o.error),a(o.error)}})}catch(r){throw console.error(`Exception in getTradeById for ID: "${e}"`,r),r}}async deleteTrade(e){const r=await this.dbPromise;return new Promise((t,a)=>{const o=r.transaction(n,"readwrite").objectStore(n).delete(e);o.onsuccess=()=>{t()},o.onerror=()=>{console.error("Error deleting trade:",o.error),a(o.error)}})}}const I=new g;export{I as t};
//# sourceMappingURL=tradeStorage-0955231a.js.map
