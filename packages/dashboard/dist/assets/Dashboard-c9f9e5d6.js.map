{"version": 3, "file": "Dashboard-c9f9e5d6.js", "sources": ["../../src/features/performance-dashboard/components/MetricsPanel.tsx", "../../src/features/performance-dashboard/components/PerformanceChart.tsx", "../../src/features/performance-dashboard/components/RecentTradesPanel.tsx", "../../src/features/performance-dashboard/hooks/useDashboardData.ts", "../../src/features/performance-dashboard/Dashboard.tsx"], "sourcesContent": ["/**\n * Metrics Panel Component\n *\n * Displays key trading metrics in a card format\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\ninterface Metric {\n  title: string;\n  value: string;\n}\n\ninterface MetricsPanelProps {\n  metrics: Metric[];\n  isLoading?: boolean;\n}\n\nconst Container = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst MetricCard = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst MetricTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst MetricValue = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst LoadingIndicator = styled.div`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const MetricsPanel: React.FC<MetricsPanelProps> = ({\n  metrics,\n  isLoading = false,\n}) => {\n  if (isLoading) {\n    return (\n      <Container>\n        {[1, 2, 3, 4].map((i) => (\n          <MetricCard key={i}>\n            <MetricTitle>Loading...</MetricTitle>\n            <LoadingIndicator>Fetching data...</LoadingIndicator>\n          </MetricCard>\n        ))}\n      </Container>\n    );\n  }\n\n  return (\n    <Container>\n      {metrics.map((metric, index) => (\n        <MetricCard key={index}>\n          <MetricTitle>{metric.title}</MetricTitle>\n          <MetricValue>{metric.value}</MetricValue>\n        </MetricCard>\n      ))}\n    </Container>\n  );\n};\n", "/**\n * Performance Chart Component\n *\n * Displays a chart showing trading performance over time\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\ninterface ChartDataPoint {\n  date: string;\n  value: number;\n}\n\ninterface PerformanceChartProps {\n  data: ChartDataPoint[];\n}\n\nconst ChartContainer = styled.div`\n  height: 300px;\n  position: relative;\n  width: 100%;\n`;\n\nconst PlaceholderText = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\n// This is a placeholder component - in a real app, you'd use a charting library\n// like recharts, chart.js, or d3.js\nexport const PerformanceChart: React.FC<PerformanceChartProps> = ({ data }) => {\n  // Simple implementation - would be replaced with an actual chart\n  if (!data || data.length === 0) {\n    return (\n      <ChartContainer>\n        <PlaceholderText>No chart data available</PlaceholderText>\n      </ChartContainer>\n    );\n  }\n\n  return (\n    <ChartContainer>\n      {/* In a real implementation, you would render your chart library here */}\n      <PlaceholderText>\n        Chart would render {data.length} data points from {data[0].date} to{\" \"}\n        {data[data.length - 1].date}\n      </PlaceholderText>\n    </ChartContainer>\n  );\n};\n", "/**\n * Recent Trades Panel Component\n *\n * Displays a list of recent trades\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\ninterface Trade {\n  id: string;\n  date: string;\n  symbol: string;\n  direction: \"long\" | \"short\";\n  result: \"win\" | \"loss\" | \"breakeven\";\n  profit: number;\n}\n\ninterface RecentTradesPanelProps {\n  trades: Trade[];\n  isLoading?: boolean;\n}\n\nconst Container = styled.div`\n  width: 100%;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHead = styled.thead`\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst TableRow = styled.tr`\n  &:nth-child(even) {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.hover};\n  }\n`;\n\nconst TableHeader = styled.th`\n  text-align: left;\n  padding: ${({ theme }) => theme.spacing.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst TableCell = styled.td`\n  padding: ${({ theme }) => theme.spacing.sm};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n`;\n\nconst DirectionCell = styled(TableCell)<{ direction: \"long\" | \"short\" }>`\n  color: ${({ theme, direction }) =>\n    direction === \"long\" ? theme.colors.success : theme.colors.danger};\n`;\n\nconst ResultCell = styled(TableCell)<{ result: \"win\" | \"loss\" | \"breakeven\" }>`\n  color: ${({ theme, result }) => {\n    switch (result) {\n      case \"win\":\n        return theme.colors.success;\n      case \"loss\":\n        return theme.colors.danger;\n      default:\n        return theme.colors.textSecondary;\n    }\n  }};\n`;\n\nconst ProfitCell = styled(TableCell)<{ value: number }>`\n  color: ${({ theme, value }) =>\n    value > 0\n      ? theme.colors.success\n      : value < 0\n      ? theme.colors.danger\n      : theme.colors.textSecondary};\n`;\n\nconst LoadingIndicator = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const RecentTradesPanel: React.FC<RecentTradesPanelProps> = ({\n  trades,\n  isLoading = false,\n}) => {\n  if (isLoading) {\n    return <LoadingIndicator>Loading recent trades...</LoadingIndicator>;\n  }\n\n  if (!trades || trades.length === 0) {\n    return <LoadingIndicator>No recent trades found</LoadingIndicator>;\n  }\n\n  return (\n    <Container>\n      <Table>\n        <TableHead>\n          <TableRow>\n            <TableHeader>Date</TableHeader>\n            <TableHeader>Symbol</TableHeader>\n            <TableHeader>Direction</TableHeader>\n            <TableHeader>Result</TableHeader>\n            <TableHeader>Profit/Loss</TableHeader>\n          </TableRow>\n        </TableHead>\n        <tbody>\n          {trades.map((trade) => (\n            <TableRow key={trade.id}>\n              <TableCell>{trade.date}</TableCell>\n              <TableCell>{trade.symbol}</TableCell>\n              <DirectionCell direction={trade.direction}>\n                {trade.direction === \"long\" ? \"▲ Long\" : \"▼ Short\"}\n              </DirectionCell>\n              <ResultCell result={trade.result}>\n                {trade.result.charAt(0).toUpperCase() + trade.result.slice(1)}\n              </ResultCell>\n              <ProfitCell value={trade.profit}>\n                ${trade.profit.toFixed(2)}\n              </ProfitCell>\n            </TableRow>\n          ))}\n        </tbody>\n      </Table>\n    </Container>\n  );\n};\n", "/**\n * Dashboard Data Hook\n *\n * Custom hook for fetching and managing dashboard data\n */\n\nimport { useState, useCallback } from \"react\";\n\ninterface Metric {\n  title: string;\n  value: string;\n}\n\ninterface ChartDataPoint {\n  date: string;\n  value: number;\n}\n\ninterface Trade {\n  id: string;\n  date: string;\n  symbol: string;\n  direction: \"long\" | \"short\";\n  result: \"win\" | \"loss\" | \"breakeven\";\n  profit: number;\n}\n\nexport const useDashboardData = () => {\n  const [metrics, setMetrics] = useState<Metric[]>([\n    { title: \"Win Rate\", value: \"65%\" },\n    { title: \"Profit Factor\", value: \"2.3\" },\n    { title: \"Net Profit\", value: \"$12,500\" },\n    { title: \"Total Trades\", value: \"120\" },\n  ]);\n\n  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);\n  const [recentTrades, setRecentTrades] = useState<Trade[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Mock data generation for demo purposes\n  const generateChartData = () => {\n    const data: ChartDataPoint[] = [];\n    const today = new Date();\n\n    for (let i = 30; i >= 0; i--) {\n      const date = new Date(today);\n      date.setDate(date.getDate() - i);\n\n      data.push({\n        date: date.toISOString().split(\"T\")[0],\n        value: 10000 + Math.random() * 5000,\n      });\n    }\n\n    return data;\n  };\n\n  // Mock trade data\n  const generateTradeData = () => {\n    const trades: Trade[] = [];\n    const today = new Date();\n    const symbols = [\"AAPL\", \"MSFT\", \"GOOGL\", \"TSLA\", \"AMZN\"];\n\n    for (let i = 0; i < 5; i++) {\n      const date = new Date(today);\n      date.setDate(date.getDate() - i);\n\n      trades.push({\n        id: `trade-${i}`,\n        date: date.toISOString().split(\"T\")[0],\n        symbol: symbols[Math.floor(Math.random() * symbols.length)],\n        direction: Math.random() > 0.5 ? \"long\" : \"short\",\n        result:\n          Math.random() > 0.35\n            ? \"win\"\n            : Math.random() > 0.5\n            ? \"loss\"\n            : \"breakeven\",\n        profit:\n          Math.random() > 0.35 ? Math.random() * 1000 : -Math.random() * 500,\n      });\n    }\n\n    return trades;\n  };\n\n  const fetchDashboardData = useCallback(async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // In a real app, you would fetch data from an API\n      // For now, we'll use mock data with a timeout to simulate API call\n      await new Promise((resolve) => setTimeout(resolve, 1000));\n\n      setChartData(generateChartData());\n      setRecentTrades(generateTradeData());\n\n      // Update metrics if needed\n      // setMetrics([...])\n    } catch (err) {\n      console.error(\"Error fetching dashboard data:\", err);\n      setError(\"Failed to load dashboard data\");\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  return {\n    metrics,\n    chartData,\n    recentTrades,\n    isLoading,\n    error,\n    fetchDashboardData,\n  };\n};\n", "/**\n * Dashboard Page Component\n *\n * This is the main dashboard page that displays trading metrics and charts.\n */\n\nimport React, { useEffect } from \"react\";\nimport styled from \"styled-components\";\nimport { MetricsPanel } from \"./components/MetricsPanel\";\nimport { PerformanceChart } from \"./components/PerformanceChart\";\nimport { RecentTradesPanel } from \"./components/RecentTradesPanel\";\nimport { useDashboardData } from \"./hooks/useDashboardData\";\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xxl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst ChartSection = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst ChartTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;\n`;\n\nconst ChartPlaceholder = styled.div`\n  height: 300px;\n  background-color: ${({ theme }) => theme.colors.chartGrid};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst RecentTradesSection = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst Dashboard: React.FC = () => {\n  const { metrics, chartData, recentTrades, isLoading, fetchDashboardData } =\n    useDashboardData();\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n\n  return (\n    <PageContainer>\n      <PageHeader>\n        <Title>Trading Dashboard</Title>\n      </PageHeader>\n\n      <MetricsPanel metrics={metrics} isLoading={isLoading} />\n\n      <ChartSection>\n        <ChartTitle>Performance</ChartTitle>\n        {isLoading ? (\n          <ChartPlaceholder>Loading chart data...</ChartPlaceholder>\n        ) : (\n          <PerformanceChart data={chartData} />\n        )}\n      </ChartSection>\n\n      <RecentTradesSection>\n        <ChartTitle>Recent Trades</ChartTitle>\n        <RecentTradesPanel trades={recentTrades} isLoading={isLoading} />\n      </RecentTradesSection>\n    </PageContainer>\n  );\n};\n\nexport default Dashboard;\n"], "names": ["Container", "div", "withConfig", "displayName", "componentId", "theme", "spacing", "md", "MetricCard", "colors", "surface", "borderRadius", "lg", "shadows", "sm", "MetricTitle", "h3", "fontSizes", "textSecondary", "xs", "MetricValue", "xl", "textPrimary", "LoadingIndicator", "MetricsPanel", "metrics", "isLoading", "jsx", "map", "i", "jsxs", "metric", "index", "title", "value", "ChartContainer", "PlaceholderText", "Performance<PERSON>hart", "data", "length", "date", "Table", "table", "TableHead", "thead", "border", "TableRow", "tr", "background", "hover", "TableHeader", "th", "TableCell", "td", "DirectionCell", "styled", "direction", "success", "danger", "ResultCell", "result", "ProfitCell", "RecentTradesPanel", "trades", "trade", "symbol", "char<PERSON>t", "toUpperCase", "slice", "profit", "toFixed", "id", "useDashboardData", "setMetrics", "useState", "chartData", "setChartData", "recentTrades", "setRecentTrades", "setIsLoading", "error", "setError", "generateChartData", "today", "Date", "setDate", "getDate", "push", "toISOString", "split", "Math", "random", "generateTradeData", "symbols", "floor", "fetchDashboardData", "useCallback", "Promise", "resolve", "setTimeout", "err", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Title", "h1", "xxl", "ChartSection", "ChartTitle", "h2", "ChartPlaceholder", "chartGrid", "RecentTradesSection", "Dashboard", "useEffect"], "mappings": "qIAmBA,MAAMA,EAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,6EAAA,GAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAGlCC,EAAoBP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EACP,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOC,QAC/B,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMM,aAAaJ,GACxC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,GAC1B,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMQ,QAAQC,EAAE,EAGzCC,EAAqBC,EAAAA,GAAEd,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,KAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUH,GACnC,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMI,OAAOS,cACvB,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,EAAE,EAGzCC,EAAqBnB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUI,GAEnC,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,EAAMI,OAAOa,WAAW,EAG5CC,EAA0BtB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,SAAA,qBAAA,EACxB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOS,aAAa,EAIvCM,EAA4CA,CAAC,CACxDC,QAAAA,EACAC,UAAAA,EAAY,EACd,IACMA,EAEAC,EAAAA,IAAC3B,EACE,CAAA,SAAA,CAAC,EAAG,EAAG,EAAG,CAAC,EAAE4B,IACZC,GAAAC,EAAAA,KAACtB,EACC,CAAA,SAAA,CAAAmB,EAAAA,IAACZ,GAAY,SAAU,YAAA,CAAA,EACvBY,EAAAA,IAACJ,GAAiB,SAAgB,kBAAA,CAAA,CAAA,GAFnBM,CAGjB,CACD,CACH,CAAA,EAKFF,MAAC3B,GACEyB,SAAQG,EAAAA,IAAI,CAACG,EAAQC,WACnBxB,EACC,CAAA,SAAA,CAACmB,EAAAA,IAAAZ,EAAA,CAAagB,WAAOE,KAAM,CAAA,EAC3BN,EAAAA,IAACP,EAAaW,CAAAA,SAAAA,EAAOG,KAAM,CAAA,CAAA,GAFZF,CAGjB,CACD,CACH,CAAA,ECxDEG,EAAwBlC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAIhC,EAAA,CAAA,4CAAA,CAAA,EAEKgC,EAAyBnC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2EAAA,GAAA,EAKvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOS,aAAa,EAKvCmB,EAAoDA,CAAC,CAAEC,KAAAA,CAAK,IAEnE,CAACA,GAAQA,EAAKC,SAAW,EAExBZ,MAAAQ,EAAA,CACC,SAACR,EAAA,IAAAS,EAAA,CAAgB,mCAAuB,CAC1C,CAAA,EAKFT,EAAAA,IAACQ,EAEC,CAAA,SAAAL,EAAAA,KAACM,EAAe,CAAA,SAAA,CAAA,sBACME,EAAKC,OAAO,qBAAmBD,EAAK,CAAC,EAAEE,KAAK,MAAI,IACnEF,EAAKA,EAAKC,OAAS,CAAC,EAAEC,IAAAA,CACzB,CAAA,CACF,CAAA,EC5BExC,EAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAE3B,EAAA,CAAA,aAAA,CAAA,EAEKqC,EAAeC,EAAAA,MAAKxC,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAGzB,EAAA,CAAA,sCAAA,CAAA,EAEKuC,EAAmBC,EAAAA,MAAK1C,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,GAAA,EACD,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOoC,MAAM,EAGzDC,EAAkBC,EAAAA,GAAE7C,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,sCAAA,8BAAA,IAAA,EAEF,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOuC,WAI5B,CAAC,CAAE3C,MAAAA,CAAM,IAAMA,EAAMI,OAAOwC,KAAK,EAInDC,EAAqBC,EAAAA,GAAEjD,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,cAAA,0BAAA,GAAA,EAEhB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,GAC3B,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMY,UAAUH,GAEnC,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMI,OAAOS,aAAa,EAG9CkC,EAAmBC,EAAAA,GAAEnD,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,cAAA,GAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,GAC3B,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMY,UAAUV,EAAE,EAG1C+C,EAAgBC,EAAOH,CAAS,EAAClD,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EAC5B,CAAC,CAAEC,MAAAA,EAAOmD,UAAAA,CAAU,IAC3BA,IAAc,OAASnD,EAAMI,OAAOgD,QAAUpD,EAAMI,OAAOiD,MAAM,EAG/DC,EAAaJ,EAAOH,CAAS,EAAClD,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EACzB,CAAC,CAAEC,MAAAA,EAAOuD,OAAAA,CAAO,IAAM,CAC9B,OAAQA,EAAM,CACZ,IAAK,MACH,OAAOvD,EAAMI,OAAOgD,QACtB,IAAK,OACH,OAAOpD,EAAMI,OAAOiD,OACtB,QACE,OAAOrD,EAAMI,OAAOS,aACxB,CACF,CAAC,EAGG2C,EAAaN,EAAOH,CAAS,EAAClD,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EACzB,CAAC,CAAEC,MAAAA,EAAO6B,MAAAA,CAAM,IACvBA,EAAQ,EACJ7B,EAAMI,OAAOgD,QACbvB,EAAQ,EACR7B,EAAMI,OAAOiD,OACbrD,EAAMI,OAAOS,aAAa,EAG5BK,EAA0BtB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EACtB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,GAE/B,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMI,OAAOS,aAAa,EAIvC4C,EAAsDA,CAAC,CAClEC,OAAAA,EACArC,UAAAA,EAAY,EACd,IACMA,EACKC,EAAA,IAACJ,GAAiB,SAAwB,0BAAA,CAAA,EAG/C,CAACwC,GAAUA,EAAOxB,SAAW,EACxBZ,EAAA,IAACJ,GAAiB,SAAsB,wBAAA,CAAA,EAI/CI,EAAAA,IAAC3B,EACC,CAAA,SAAA8B,EAAAA,KAACW,EACC,CAAA,SAAA,CAACd,EAAA,IAAAgB,EAAA,CACC,gBAACG,EACC,CAAA,SAAA,CAAAnB,EAAAA,IAACuB,GAAY,SAAI,MAAA,CAAA,EACjBvB,EAAAA,IAACuB,GAAY,SAAM,QAAA,CAAA,EACnBvB,EAAAA,IAACuB,GAAY,SAAS,WAAA,CAAA,EACtBvB,EAAAA,IAACuB,GAAY,SAAM,QAAA,CAAA,EACnBvB,EAAAA,IAACuB,GAAY,SAAW,aAAA,CAAA,CAAA,CAAA,CAC1B,CACF,CAAA,QACC,QACEa,CAAAA,SAAAA,EAAOnC,IAAKoC,UACVlB,EACC,CAAA,SAAA,CAACnB,EAAAA,IAAAyB,EAAA,CAAWY,WAAMxB,IAAK,CAAA,EACvBb,EAAAA,IAACyB,EAAWY,CAAAA,SAAAA,EAAMC,MAAO,CAAA,EACzBtC,EAAAA,IAAC2B,GAAc,UAAWU,EAAMR,UAC7BQ,SAAMR,EAAAA,YAAc,OAAS,SAAW,SAC3C,CAAA,QACCG,EAAW,CAAA,OAAQK,EAAMJ,OACvBI,WAAMJ,OAAOM,OAAO,CAAC,EAAEC,cAAgBH,EAAMJ,OAAOQ,MAAM,CAAC,EAC9D,EACCtC,EAAA,KAAA+B,EAAA,CAAW,MAAOG,EAAMK,OAAO,SAAA,CAAA,IAC5BL,EAAMK,OAAOC,QAAQ,CAAC,CAAA,EAC1B,CAXaN,CAAAA,EAAAA,EAAMO,EAYrB,CACD,EACH,CAAA,CACF,CAAA,CACF,CAAA,EC5GSC,EAAmBA,IAAM,CACpC,KAAM,CAAC/C,EAASgD,CAAU,EAAIC,WAAmB,CAC/C,CAAEzC,MAAO,WAAYC,MAAO,KAAA,EAC5B,CAAED,MAAO,gBAAiBC,MAAO,KAAA,EACjC,CAAED,MAAO,aAAcC,MAAO,SAAA,EAC9B,CAAED,MAAO,eAAgBC,MAAO,KAAO,CAAA,CACxC,EAEK,CAACyC,EAAWC,CAAY,EAAIF,EAAAA,SAA2B,CAAE,CAAA,EACzD,CAACG,EAAcC,CAAe,EAAIJ,EAAAA,SAAkB,CAAE,CAAA,EACtD,CAAChD,EAAWqD,CAAY,EAAIL,WAAS,EAAI,EACzC,CAACM,EAAOC,CAAQ,EAAIP,WAAwB,IAAI,EAGhDQ,EAAoBA,IAAM,CAC9B,MAAM5C,EAAyB,CAAA,EACzB6C,MAAYC,KAElB,QAASvD,EAAI,GAAIA,GAAK,EAAGA,IAAK,CACtBW,MAAAA,EAAO,IAAI4C,KAAKD,CAAK,EAC3B3C,EAAK6C,QAAQ7C,EAAK8C,QAAQ,EAAIzD,CAAC,EAE/BS,EAAKiD,KAAK,CACR/C,KAAMA,EAAKgD,YAAAA,EAAcC,MAAM,GAAG,EAAE,CAAC,EACrCvD,MAAO,IAAQwD,KAAKC,OAAW,EAAA,GAAA,CAChC,EAGIrD,OAAAA,CAAAA,EAIHsD,EAAoBA,IAAM,CAC9B,MAAM7B,EAAkB,CAAA,EAClBoB,MAAYC,KACZS,EAAU,CAAC,OAAQ,OAAQ,QAAS,OAAQ,MAAM,EAExD,QAAShE,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACpBW,MAAAA,EAAO,IAAI4C,KAAKD,CAAK,EAC3B3C,EAAK6C,QAAQ7C,EAAK8C,QAAQ,EAAIzD,CAAC,EAE/BkC,EAAOwB,KAAK,CACVhB,GAAI,SAAS1C,IACbW,KAAMA,EAAKgD,YAAAA,EAAcC,MAAM,GAAG,EAAE,CAAC,EACrCxB,OAAQ4B,EAAQH,KAAKI,MAAMJ,KAAKC,OAAO,EAAIE,EAAQtD,MAAM,CAAC,EAC1DiB,UAAWkC,KAAKC,OAAO,EAAI,GAAM,OAAS,QAC1C/B,OACE8B,KAAKC,OAAW,EAAA,IACZ,MACAD,KAAKC,OAAW,EAAA,GAChB,OACA,YACNtB,OACEqB,KAAKC,OAAO,EAAI,IAAOD,KAAKC,OAAO,EAAI,IAAO,CAACD,KAAKC,OAAW,EAAA,GAAA,CAClE,EAGI5B,OAAAA,CAAAA,EAGHgC,EAAqBC,EAAAA,YAAY,SAAY,CACjDjB,EAAa,EAAI,EACjBE,EAAS,IAAI,EAET,GAAA,CAGF,MAAM,IAAIgB,QAASC,GAAYC,WAAWD,EAAS,GAAI,CAAC,EAExDtB,EAAaM,GAAmB,EAChCJ,EAAgBc,GAAmB,QAI5BQ,GACCpB,QAAAA,MAAM,iCAAkCoB,CAAG,EACnDnB,EAAS,+BAA+B,CAAA,QAChC,CACRF,EAAa,EAAK,CACpB,CACF,EAAG,CAAE,CAAA,EAEE,MAAA,CACLtD,QAAAA,EACAkD,UAAAA,EACAE,aAAAA,EACAnD,UAAAA,EACAsD,MAAAA,EACAe,mBAAAA,CAAAA,CAEJ,ECxGMM,EAAuBpG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,EAAE,EAGlC0F,EAAoBrG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIV,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,EAAE,EAG5C2F,EAAeC,EAAAA,GAAEtG,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACR,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUwF,IAEnC,CAAC,CAAEpG,MAAAA,CAAM,IAAMA,EAAMI,OAAOa,WAAW,EAI5CoF,EAAsBzG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EACT,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOC,QAC/B,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMM,aAAaJ,GACxC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,GAC1B,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMQ,QAAQC,EAAE,EAGzC6F,EAAoBC,EAAAA,GAAE1G,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUL,GAEnC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMI,OAAOa,YACvB,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,EAAE,EAGzCiG,EAA0B5G,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iCAAA,kBAAA,iEAAA,GAAA,EAEb,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOqG,UAC/B,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMM,aAAaG,GAI1C,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMI,OAAOS,aAAa,EAG9C6F,EAA6B9G,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOC,QAC/B,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMM,aAAaJ,GACxC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,GAC1B,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMQ,QAAQC,EAAE,EAGzCkG,EAAsBA,IAAM,CAC1B,KAAA,CAAEvF,QAAAA,EAASkD,UAAAA,EAAWE,aAAAA,EAAcnD,UAAAA,EAAWqE,mBAAAA,GACnDvB,EAAiB,EAEnByC,OAAAA,EAAAA,UAAU,IAAM,CACKlB,GAAA,EAClB,CAACA,CAAkB,CAAC,SAGpBM,EACC,CAAA,SAAA,CAAA1E,MAAC2E,EACC,CAAA,SAAA3E,EAAA,IAAC4E,EAAM,CAAA,SAAA,mBAAiB,CAAA,EAC1B,EAEA5E,EAAAA,IAACH,EAAa,CAAA,QAAAC,EAAkB,UAAAC,CAAqB,CAAA,SAEpDgF,EACC,CAAA,SAAA,CAAA/E,EAAAA,IAACgF,GAAW,SAAW,aAAA,CAAA,EACtBjF,QACEmF,EAAiB,CAAA,SAAA,uBAAA,CAAqB,EAEtClF,EAAAA,IAAAU,EAAA,CAAiB,KAAMsC,CACzB,CAAA,CAAA,EACH,SAECoC,EACC,CAAA,SAAA,CAAApF,EAAAA,IAACgF,GAAW,SAAa,eAAA,CAAA,EACxBhF,EAAAA,IAAAmC,EAAA,CAAkB,OAAQe,EAAc,UAAAnD,CAAqB,CAAA,CAAA,EAChE,CACF,CAAA,CAAA,CAEJ"}