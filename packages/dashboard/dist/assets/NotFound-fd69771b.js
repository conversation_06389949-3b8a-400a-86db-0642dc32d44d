import{j as r}from"./client-4c27269c.js";import{s as n}from"./styled-components-3ebafa9a.js";import{L as e}from"./router-e715efa2.js";import"./react-60374de9.js";const i=n.div.withConfig({displayName:"Container",componentId:"sc-d2o4e1-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;"],({theme:o})=>o.spacing.xl),t=n.div.withConfig({displayName:"ErrorCode",componentId:"sc-d2o4e1-1"})(["font-size:6rem;font-weight:700;color:",";margin-bottom:",";"],({theme:o})=>o.colors.primary,({theme:o})=>o.spacing.md),s=n.h1.withConfig({displayName:"ErrorMessage",componentId:"sc-d2o4e1-2"})(["font-size:",";color:",";margin-bottom:",";"],({theme:o})=>o.fontSizes.xl,({theme:o})=>o.colors.textPrimary,({theme:o})=>o.spacing.lg),a=n.p.withConfig({displayName:"ErrorDescription",componentId:"sc-d2o4e1-3"})(["font-size:",";color:",";max-width:500px;margin:0 auto ",";"],({theme:o})=>o.fontSizes.md,({theme:o})=>o.colors.textSecondary,({theme:o})=>o.spacing.lg),c=n(e).withConfig({displayName:"HomeLink",componentId:"sc-d2o4e1-4"})(["padding:",";background-color:",";color:white;border-radius:",";text-decoration:none;font-weight:500;transition:background-color 0.2s;&:hover{background-color:",";}"],({theme:o})=>`${o.spacing.sm} ${o.spacing.lg}`,({theme:o})=>o.colors.primary,({theme:o})=>o.borderRadius.md,({theme:o})=>o.colors.primaryDark),g=()=>r.jsxs(i,{children:[r.jsx(t,{children:"404"}),r.jsx(s,{children:"Page Not Found"}),r.jsx(a,{children:"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}),r.jsx(c,{to:"/",children:"Back to Dashboard"})]});export{g as default};
//# sourceMappingURL=NotFound-fd69771b.js.map
