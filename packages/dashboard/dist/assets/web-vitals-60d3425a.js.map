{"version": 3, "file": "web-vitals-60d3425a.js", "sources": ["../../../../node_modules/web-vitals/dist/web-vitals.js"], "sourcesContent": ["var e,t,n,i,r=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:\"v2-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12)}},a=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if(\"first-input\"===e&&!(\"PerformanceEventTiming\"in self))return;var n=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return n.observe({type:e,buffered:!0}),n}}catch(e){}},o=function(e,t){var n=function n(i){\"pagehide\"!==i.type&&\"hidden\"!==document.visibilityState||(e(i),t&&(removeEventListener(\"visibilitychange\",n,!0),removeEventListener(\"pagehide\",n,!0)))};addEventListener(\"visibilitychange\",n,!0),addEventListener(\"pagehide\",n,!0)},u=function(e){addEventListener(\"pageshow\",(function(t){t.persisted&&e(t)}),!0)},c=function(e,t,n){var i;return function(r){t.value>=0&&(r||n)&&(t.delta=t.value-(i||0),(t.delta||void 0===i)&&(i=t.value,e(t)))}},f=-1,s=function(){return\"hidden\"===document.visibilityState?0:1/0},m=function(){o((function(e){var t=e.timeStamp;f=t}),!0)},v=function(){return f<0&&(f=s(),m(),u((function(){setTimeout((function(){f=s(),m()}),0)}))),{get firstHiddenTime(){return f}}},d=function(e,t){var n,i=v(),o=r(\"FCP\"),f=function(e){\"first-contentful-paint\"===e.name&&(m&&m.disconnect(),e.startTime<i.firstHiddenTime&&(o.value=e.startTime,o.entries.push(e),n(!0)))},s=window.performance&&performance.getEntriesByName&&performance.getEntriesByName(\"first-contentful-paint\")[0],m=s?null:a(\"paint\",f);(s||m)&&(n=c(e,o,t),s&&f(s),u((function(i){o=r(\"FCP\"),n=c(e,o,t),requestAnimationFrame((function(){requestAnimationFrame((function(){o.value=performance.now()-i.timeStamp,n(!0)}))}))})))},p=!1,l=-1,h=function(e,t){p||(d((function(e){l=e.value})),p=!0);var n,i=function(t){l>-1&&e(t)},f=r(\"CLS\",0),s=0,m=[],v=function(e){if(!e.hadRecentInput){var t=m[0],i=m[m.length-1];s&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(s+=e.value,m.push(e)):(s=e.value,m=[e]),s>f.value&&(f.value=s,f.entries=m,n())}},h=a(\"layout-shift\",v);h&&(n=c(i,f,t),o((function(){h.takeRecords().map(v),n(!0)})),u((function(){s=0,l=-1,f=r(\"CLS\",0),n=c(i,f,t)})))},T={passive:!0,capture:!0},y=new Date,g=function(i,r){e||(e=r,t=i,n=new Date,w(removeEventListener),E())},E=function(){if(t>=0&&t<n-y){var r={entryType:\"first-input\",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+t};i.forEach((function(e){e(r)})),i=[]}},S=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,t){var n=function(){g(e,t),r()},i=function(){r()},r=function(){removeEventListener(\"pointerup\",n,T),removeEventListener(\"pointercancel\",i,T)};addEventListener(\"pointerup\",n,T),addEventListener(\"pointercancel\",i,T)}(t,e):g(t,e)}},w=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(t){return e(t,S,T)}))},L=function(n,f){var s,m=v(),d=r(\"FID\"),p=function(e){e.startTime<m.firstHiddenTime&&(d.value=e.processingStart-e.startTime,d.entries.push(e),s(!0))},l=a(\"first-input\",p);s=c(n,d,f),l&&o((function(){l.takeRecords().map(p),l.disconnect()}),!0),l&&u((function(){var a;d=r(\"FID\"),s=c(n,d,f),i=[],t=-1,e=null,w(addEventListener),a=p,i.push(a),E()}))},b={},F=function(e,t){var n,i=v(),f=r(\"LCP\"),s=function(e){var t=e.startTime;t<i.firstHiddenTime&&(f.value=t,f.entries.push(e),n())},m=a(\"largest-contentful-paint\",s);if(m){n=c(e,f,t);var d=function(){b[f.id]||(m.takeRecords().map(s),m.disconnect(),b[f.id]=!0,n(!0))};[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,d,{once:!0,capture:!0})})),o(d,!0),u((function(i){f=r(\"LCP\"),n=c(e,f,t),requestAnimationFrame((function(){requestAnimationFrame((function(){f.value=performance.now()-i.timeStamp,b[f.id]=!0,n(!0)}))}))}))}},P=function(e){var t,n=r(\"TTFB\");t=function(){try{var t=performance.getEntriesByType(\"navigation\")[0]||function(){var e=performance.timing,t={entryType:\"navigation\",startTime:0};for(var n in e)\"navigationStart\"!==n&&\"toJSON\"!==n&&(t[n]=Math.max(e[n]-e.navigationStart,0));return t}();if(n.value=n.delta=t.responseStart,n.value<0||n.value>performance.now())return;n.entries=[t],e(n)}catch(e){}},\"complete\"===document.readyState?setTimeout(t,0):addEventListener(\"load\",(function(){return setTimeout(t,0)}))};export{h as getCLS,d as getFCP,L as getFID,F as getLCP,P as getTTFB};\n"], "names": ["e", "t", "n", "i", "r", "a", "o", "u", "c", "f", "s", "m", "v", "d", "p", "l", "h", "T", "y", "g", "w", "E", "S", "L", "b", "F", "P"], "mappings": "AAAG,IAACA,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,SAASJ,EAAEC,EAAE,CAAC,MAAM,CAAC,KAAKD,EAAE,MAAeC,IAAT,OAAW,GAAGA,EAAE,MAAM,EAAE,QAAQ,CAAE,EAAC,GAAG,MAAM,OAAO,KAAK,IAAK,EAAC,GAAG,EAAE,OAAO,KAAK,MAAM,cAAc,KAAK,OAAQ,CAAA,EAAE,IAAI,CAAC,CAAC,EAAEI,EAAE,SAASL,EAAEC,EAAE,CAAC,GAAG,CAAC,GAAG,oBAAoB,oBAAoB,SAASD,CAAC,EAAE,CAAC,GAAmBA,IAAhB,eAAmB,EAAE,2BAA2B,MAAM,OAAO,IAAIE,EAAE,IAAI,oBAAqB,SAASF,EAAE,CAAC,OAAOA,EAAE,WAAY,EAAC,IAAIC,CAAC,CAAC,CAAG,EAAC,OAAOC,EAAE,QAAQ,CAAC,KAAKF,EAAE,SAAS,EAAE,CAAC,EAAEE,EAAE,MAAC,CAAU,CAAA,EAAEI,EAAE,SAASN,EAAEC,EAAE,CAAC,IAAIC,EAAE,SAASA,EAAEC,EAAE,CAAcA,EAAE,OAAf,YAAgC,SAAS,kBAApB,WAAsCH,EAAEG,CAAC,EAAEF,IAAI,oBAAoB,mBAAmBC,EAAE,EAAE,EAAE,oBAAoB,WAAWA,EAAE,EAAE,GAAG,EAAE,iBAAiB,mBAAmBA,EAAE,EAAE,EAAE,iBAAiB,WAAWA,EAAE,EAAE,CAAC,EAAEK,EAAE,SAASP,EAAE,CAAC,iBAAiB,WAAY,SAASC,EAAE,CAACA,EAAE,WAAWD,EAAEC,CAAC,CAAC,EAAG,EAAE,CAAC,EAAEO,EAAE,SAASR,EAAEC,EAAEC,EAAE,CAAC,IAAIC,EAAE,OAAO,SAASC,EAAE,CAACH,EAAE,OAAO,IAAIG,GAAGF,KAAKD,EAAE,MAAMA,EAAE,OAAOE,GAAG,IAAIF,EAAE,OAAgBE,IAAT,UAAcA,EAAEF,EAAE,MAAMD,EAAEC,CAAC,GAAG,CAAC,EAAEQ,EAAE,GAAGC,EAAE,UAAU,CAAC,OAAiB,SAAS,kBAApB,SAAoC,EAAE,EAAE,CAAC,EAAEC,EAAE,UAAU,CAACL,EAAG,SAASN,EAAE,CAAC,IAAIC,EAAED,EAAE,UAAUS,EAAER,CAAC,EAAG,EAAE,CAAC,EAAEW,EAAE,UAAU,CAAC,OAAOH,EAAE,IAAIA,EAAEC,EAAG,EAACC,EAAC,EAAGJ,EAAG,UAAU,CAAC,WAAY,UAAU,CAACE,EAAEC,EAAG,EAACC,EAAG,CAAA,EAAG,CAAC,CAAC,IAAK,CAAC,IAAI,iBAAiB,CAAC,OAAOF,CAAC,CAAC,CAAC,EAAEI,EAAE,SAASb,EAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAES,EAAG,EAACN,EAAEF,EAAE,KAAK,EAAEK,EAAE,SAAST,EAAE,CAA4BA,EAAE,OAA7B,2BAAoCW,GAAGA,EAAE,WAAU,EAAGX,EAAE,UAAUG,EAAE,kBAAkBG,EAAE,MAAMN,EAAE,UAAUM,EAAE,QAAQ,KAAKN,CAAC,EAAEE,EAAE,EAAE,GAAG,EAAEQ,EAAE,OAAO,aAAa,YAAY,kBAAkB,YAAY,iBAAiB,wBAAwB,EAAE,CAAC,EAAEC,EAAED,EAAE,KAAKL,EAAE,QAAQI,CAAC,GAAGC,GAAGC,KAAKT,EAAEM,EAAER,EAAEM,EAAEL,CAAC,EAAES,GAAGD,EAAEC,CAAC,EAAEH,EAAG,SAASJ,EAAE,CAACG,EAAEF,EAAE,KAAK,EAAEF,EAAEM,EAAER,EAAEM,EAAEL,CAAC,EAAE,sBAAuB,UAAU,CAAC,sBAAuB,UAAU,CAACK,EAAE,MAAM,YAAY,IAAG,EAAGH,EAAE,UAAUD,EAAE,EAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAG,EAAC,EAAEY,EAAE,GAAGC,EAAE,GAAGC,EAAE,SAAShB,EAAEC,EAAE,CAACa,IAAID,EAAG,SAASb,EAAE,CAACe,EAAEf,EAAE,KAAK,CAAC,EAAGc,EAAE,IAAI,IAAIZ,EAAEC,EAAE,SAASF,EAAE,CAACc,EAAE,IAAIf,EAAEC,CAAC,CAAC,EAAEQ,EAAEL,EAAE,MAAM,CAAC,EAAEM,EAAE,EAAEC,EAAE,CAAA,EAAGC,EAAE,SAASZ,EAAE,CAAC,GAAG,CAACA,EAAE,eAAe,CAAC,IAAIC,EAAEU,EAAE,CAAC,EAAER,EAAEQ,EAAEA,EAAE,OAAO,CAAC,EAAED,GAAGV,EAAE,UAAUG,EAAE,UAAU,KAAKH,EAAE,UAAUC,EAAE,UAAU,KAAKS,GAAGV,EAAE,MAAMW,EAAE,KAAKX,CAAC,IAAIU,EAAEV,EAAE,MAAMW,EAAE,CAACX,CAAC,GAAGU,EAAED,EAAE,QAAQA,EAAE,MAAMC,EAAED,EAAE,QAAQE,EAAET,EAAG,GAAE,EAAEc,EAAEX,EAAE,eAAeO,CAAC,EAAEI,IAAId,EAAEM,EAAEL,EAAEM,EAAER,CAAC,EAAEK,EAAG,UAAU,CAACU,EAAE,cAAc,IAAIJ,CAAC,EAAEV,EAAE,EAAE,CAAC,CAAC,EAAGK,EAAG,UAAU,CAACG,EAAE,EAAEK,EAAE,GAAGN,EAAEL,EAAE,MAAM,CAAC,EAAEF,EAAEM,EAAEL,EAAEM,EAAER,CAAC,CAAC,CAAC,EAAG,EAAEgB,EAAE,CAAC,QAAQ,GAAG,QAAQ,EAAE,EAAEC,EAAE,IAAI,KAAKC,EAAE,SAAShB,EAAEC,EAAE,CAACJ,IAAIA,EAAEI,EAAEH,EAAEE,EAAED,EAAE,IAAI,KAAKkB,EAAE,mBAAmB,EAAEC,EAAG,EAAC,EAAEA,EAAE,UAAU,CAAC,GAAGpB,GAAG,GAAGA,EAAEC,EAAEgB,EAAE,CAAC,IAAId,EAAE,CAAC,UAAU,cAAc,KAAKJ,EAAE,KAAK,OAAOA,EAAE,OAAO,WAAWA,EAAE,WAAW,UAAUA,EAAE,UAAU,gBAAgBA,EAAE,UAAUC,CAAC,EAAEE,EAAE,QAAS,SAAS,EAAE,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAGD,EAAE,CAAA,EAAG,EAAEmB,EAAE,SAAStB,EAAE,CAAC,GAAGA,EAAE,WAAW,CAAC,IAAIC,GAAGD,EAAE,UAAU,KAAK,IAAI,KAAK,YAAY,IAAK,GAAEA,EAAE,UAAyBA,EAAE,MAAjB,cAAsB,SAASA,EAAEC,EAAE,CAAC,IAAI,EAAE,UAAU,CAACkB,EAAEnB,EAAEC,CAAC,EAAE,EAAC,CAAE,EAAEE,EAAE,UAAU,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,oBAAoB,YAAY,EAAEc,CAAC,EAAE,oBAAoB,gBAAgBd,EAAEc,CAAC,CAAC,EAAE,iBAAiB,YAAY,EAAEA,CAAC,EAAE,iBAAiB,gBAAgBd,EAAEc,CAAC,CAAC,EAAEhB,EAAED,CAAC,EAAEmB,EAAElB,EAAED,CAAC,EAAE,EAAEoB,EAAE,SAASpB,EAAE,CAAC,CAAC,YAAY,UAAU,aAAa,aAAa,EAAE,QAAS,SAASC,EAAE,CAAC,OAAOD,EAAEC,EAAEqB,EAAEL,CAAC,CAAC,CAAG,CAAA,EAAEM,EAAE,SAASrB,EAAEO,EAAE,CAAC,IAAIC,EAAEC,EAAEC,EAAC,EAAGC,EAAET,EAAE,KAAK,EAAEU,EAAE,SAASd,EAAE,CAACA,EAAE,UAAUW,EAAE,kBAAkBE,EAAE,MAAMb,EAAE,gBAAgBA,EAAE,UAAUa,EAAE,QAAQ,KAAKb,CAAC,EAAEU,EAAE,EAAE,EAAE,EAAEK,EAAEV,EAAE,cAAcS,CAAC,EAAEJ,EAAEF,EAAEN,EAAEW,EAAEJ,CAAC,EAAEM,GAAGT,EAAG,UAAU,CAACS,EAAE,YAAW,EAAG,IAAID,CAAC,EAAEC,EAAE,WAAY,CAAA,EAAG,EAAE,EAAEA,GAAGR,EAAG,UAAU,CAAC,IAAIF,EAAEQ,EAAET,EAAE,KAAK,EAAEM,EAAEF,EAAEN,EAAEW,EAAEJ,CAAC,EAAEN,EAAE,CAAA,EAAGF,EAAE,GAAGD,EAAE,KAAKoB,EAAE,gBAAgB,EAAEf,EAAES,EAAEX,EAAE,KAAKE,CAAC,EAAEgB,GAAG,CAAC,CAAE,EAAEG,EAAE,GAAGC,EAAE,SAASzB,EAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAES,EAAC,EAAGH,EAAEL,EAAE,KAAK,EAAEM,EAAE,SAASV,EAAE,CAAC,IAAIC,EAAED,EAAE,UAAUC,EAAEE,EAAE,kBAAkBM,EAAE,MAAMR,EAAEQ,EAAE,QAAQ,KAAKT,CAAC,EAAEE,EAAC,EAAG,EAAES,EAAEN,EAAE,2BAA2BK,CAAC,EAAE,GAAGC,EAAE,CAACT,EAAEM,EAAER,EAAES,EAAER,CAAC,EAAE,IAAIY,EAAE,UAAU,CAACW,EAAEf,EAAE,EAAE,IAAIE,EAAE,YAAW,EAAG,IAAID,CAAC,EAAEC,EAAE,WAAU,EAAGa,EAAEf,EAAE,EAAE,EAAE,GAAGP,EAAE,EAAE,EAAE,EAAE,CAAC,UAAU,OAAO,EAAE,QAAS,SAASF,EAAE,CAAC,iBAAiBA,EAAEa,EAAE,CAAC,KAAK,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAGP,EAAEO,EAAE,EAAE,EAAEN,EAAG,SAASJ,EAAE,CAACM,EAAEL,EAAE,KAAK,EAAEF,EAAEM,EAAER,EAAES,EAAER,CAAC,EAAE,sBAAuB,UAAU,CAAC,sBAAuB,UAAU,CAACQ,EAAE,MAAM,YAAY,IAAK,EAACN,EAAE,UAAUqB,EAAEf,EAAE,EAAE,EAAE,GAAGP,EAAE,EAAE,CAAC,CAAC,CAAE,CAAG,CAAA,CAAG,EAAC,EAAEwB,EAAE,SAAS1B,EAAE,CAAC,IAAIC,EAAEC,EAAEE,EAAE,MAAM,EAAEH,EAAE,UAAU,CAAC,GAAG,CAAC,IAAIA,EAAE,YAAY,iBAAiB,YAAY,EAAE,CAAC,GAAG,UAAU,CAAC,IAAID,EAAE,YAAY,OAAOC,EAAE,CAAC,UAAU,aAAa,UAAU,CAAC,EAAE,QAAQC,KAAKF,EAAsBE,IAApB,mBAAkCA,IAAX,WAAeD,EAAEC,CAAC,EAAE,KAAK,IAAIF,EAAEE,CAAC,EAAEF,EAAE,gBAAgB,CAAC,GAAG,OAAOC,CAAC,EAAG,EAAC,GAAGC,EAAE,MAAMA,EAAE,MAAMD,EAAE,cAAcC,EAAE,MAAM,GAAGA,EAAE,MAAM,YAAY,IAAK,EAAC,OAAOA,EAAE,QAAQ,CAACD,CAAC,EAAED,EAAEE,CAAC,CAAC,MAAC,CAAU,CAAA,EAAe,SAAS,aAAtB,WAAiC,WAAWD,EAAE,CAAC,EAAE,iBAAiB,OAAQ,UAAU,CAAC,OAAO,WAAWA,EAAE,CAAC,CAAC,CAAG,CAAA", "x_google_ignoreList": [0]}