{"version": 3, "file": "Settings-57ab97e1.js", "sources": ["../../src/features/settings/hooks/useSettings.ts", "../../src/features/settings/Settings.tsx"], "sourcesContent": ["/**\n * useSettings Hook\n *\n * Custom hook for managing application settings\n */\n\nimport { useState } from \"react\";\nimport { useTheme } from \"@adhd-trading-dashboard/shared\";\n\ninterface Settings {\n  theme: string;\n  refreshInterval: number;\n  showNotifications: boolean;\n  enableAdvancedMetrics: boolean;\n  autoSaveJournal: boolean;\n}\n\n/**\n * useSettings Hook\n *\n * Provides state management and handlers for user settings\n */\nexport const useSettings = () => {\n  const { theme, setTheme } = useTheme();\n\n  const [settings, setSettings] = useState<Settings>({\n    theme: theme.name,\n    refreshInterval: 5,\n    showNotifications: true,\n    enableAdvancedMetrics: false,\n    autoSaveJournal: true,\n  });\n\n  /**\n   * Handle setting changes\n   */\n  const handleChange = (name: string, value: any) => {\n    setSettings((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n\n    // Apply theme change immediately\n    if (name === \"theme\") {\n      setTheme(value);\n    }\n  };\n\n  /**\n   * Save settings\n   */\n  const handleSave = () => {\n    console.log(\"Settings saved:\", settings);\n    // In a real app, you would call an API to save the settings here\n\n    // Could add notification logic here\n  };\n\n  return {\n    settings,\n    handleChange,\n    handleSave,\n  };\n};\n", "/**\n * Settings Component\n *\n * This component displays application settings and user preferences.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\nimport { useSettings } from \"./hooks/useSettings\";\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xxl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst ContentSection = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst SettingGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  padding: ${({ theme }) => theme.spacing.md} 0;\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n\n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst SettingRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst SettingLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst SettingDescription = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst Input = styled.input`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst Toggle = styled.label`\n  position: relative;\n  display: inline-block;\n  width: 50px;\n  height: 24px;\n`;\n\nconst ToggleInput = styled.input`\n  opacity: 0;\n  width: 0;\n  height: 0;\n\n  &:checked + span {\n    background-color: ${({ theme }) => theme.colors.primary};\n  }\n\n  &:checked + span:before {\n    transform: translateX(26px);\n  }\n`;\n\nconst ToggleSlider = styled.span`\n  position: absolute;\n  cursor: pointer;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: ${({ theme }) => theme.colors.border};\n  transition: ${({ theme }) => theme.transitions.normal};\n  border-radius: 34px;\n\n  &:before {\n    position: absolute;\n    content: \"\";\n    height: 18px;\n    width: 18px;\n    left: 3px;\n    bottom: 3px;\n    background-color: white;\n    transition: ${({ theme }) => theme.transitions.normal};\n    border-radius: 50%;\n  }\n`;\n\nconst SaveButton = styled.button`\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};\n  background-color: ${({ theme }) => theme.colors.primary};\n  border: none;\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  color: white;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n  align-self: flex-end;\n  margin-top: ${({ theme }) => theme.spacing.lg};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\n/**\n * Settings Component\n *\n * Main component for application settings and preferences\n */\nconst Settings: React.FC = () => {\n  const { settings, handleChange, handleSave } = useSettings();\n\n  return (\n    <PageContainer>\n      <PageHeader>\n        <Title>Settings</Title>\n      </PageHeader>\n\n      <ContentSection>\n        <SectionTitle>Appearance</SectionTitle>\n\n        <SettingGroup>\n          <SettingRow>\n            <div>\n              <SettingLabel>Theme</SettingLabel>\n              <SettingDescription>\n                Choose your preferred theme\n              </SettingDescription>\n            </div>\n            <Select\n              value={settings.theme}\n              onChange={(e) => handleChange(\"theme\", e.target.value)}\n            >\n              <option value=\"f1\">Formula 1</option>\n              <option value=\"light\">Light</option>\n            </Select>\n          </SettingRow>\n        </SettingGroup>\n      </ContentSection>\n\n      <ContentSection>\n        <SectionTitle>General Settings</SectionTitle>\n\n        <SettingGroup>\n          <SettingRow>\n            <div>\n              <SettingLabel>Data Refresh Interval</SettingLabel>\n              <SettingDescription>\n                How often to refresh dashboard data (minutes)\n              </SettingDescription>\n            </div>\n            <Input\n              type=\"number\"\n              min=\"1\"\n              max=\"60\"\n              value={settings.refreshInterval}\n              onChange={(e) =>\n                handleChange(\"refreshInterval\", parseInt(e.target.value))\n              }\n              style={{ width: \"80px\" }}\n            />\n          </SettingRow>\n        </SettingGroup>\n\n        <SettingGroup>\n          <SettingRow>\n            <div>\n              <SettingLabel>Notifications</SettingLabel>\n              <SettingDescription>\n                Enable desktop notifications\n              </SettingDescription>\n            </div>\n            <Toggle>\n              <ToggleInput\n                type=\"checkbox\"\n                checked={settings.showNotifications}\n                onChange={(e) =>\n                  handleChange(\"showNotifications\", e.target.checked)\n                }\n              />\n              <ToggleSlider />\n            </Toggle>\n          </SettingRow>\n        </SettingGroup>\n\n        <SettingGroup>\n          <SettingRow>\n            <div>\n              <SettingLabel>Advanced Metrics</SettingLabel>\n              <SettingDescription>\n                Show additional performance metrics\n              </SettingDescription>\n            </div>\n            <Toggle>\n              <ToggleInput\n                type=\"checkbox\"\n                checked={settings.enableAdvancedMetrics}\n                onChange={(e) =>\n                  handleChange(\"enableAdvancedMetrics\", e.target.checked)\n                }\n              />\n              <ToggleSlider />\n            </Toggle>\n          </SettingRow>\n        </SettingGroup>\n\n        <SettingGroup>\n          <SettingRow>\n            <div>\n              <SettingLabel>Auto-Save Journal</SettingLabel>\n              <SettingDescription>\n                Automatically save trade entries as you type\n              </SettingDescription>\n            </div>\n            <Toggle>\n              <ToggleInput\n                type=\"checkbox\"\n                checked={settings.autoSaveJournal}\n                onChange={(e) =>\n                  handleChange(\"autoSaveJournal\", e.target.checked)\n                }\n              />\n              <ToggleSlider />\n            </Toggle>\n          </SettingRow>\n        </SettingGroup>\n\n        <SaveButton onClick={handleSave}>Save Settings</SaveButton>\n      </ContentSection>\n    </PageContainer>\n  );\n};\n\nexport default Settings;\n"], "names": ["useSettings", "theme", "setTheme", "useTheme", "settings", "setSettings", "useState", "name", "refreshInterval", "showNotifications", "enableAdvancedMetrics", "autoSaveJournal", "handleChange", "value", "prev", "handleSave", "log", "<PERSON><PERSON><PERSON><PERSON>", "div", "withConfig", "displayName", "componentId", "spacing", "lg", "<PERSON><PERSON><PERSON><PERSON>", "Title", "h1", "fontSizes", "xxl", "colors", "textPrimary", "ContentSection", "surface", "borderRadius", "md", "shadows", "sm", "SectionTitle", "h2", "SettingGroup", "border", "SettingRow", "SettingLabel", "SettingDescription", "textSecondary", "xs", "Select", "select", "background", "primary", "Input", "input", "Toggle", "label", "ToggleInput", "ToggleSlider", "span", "transitions", "normal", "SaveButton", "button", "fast", "primaryDark", "Settings", "jsx", "jsxs", "e", "target", "parseInt", "width", "checked"], "mappings": "yMAsBO,MAAMA,EAAcA,IAAM,CACzB,KAAA,CAAEC,MAAAA,EAAOC,SAAAA,GAAaC,EAAS,EAE/B,CAACC,EAAUC,CAAW,EAAIC,WAAmB,CACjDL,MAAOA,EAAMM,KACbC,gBAAiB,EACjBC,kBAAmB,GACnBC,sBAAuB,GACvBC,gBAAiB,EAAA,CAClB,EA2BM,MAAA,CACLP,SAAAA,EACAQ,aAxBmBA,CAACL,EAAcM,IAAe,CACjDR,EAAuBS,IAAA,CACrB,GAAGA,EACH,CAACP,CAAI,EAAGM,CACR,EAAA,EAGEN,IAAS,SACXL,EAASW,CAAK,CAChB,EAgBAE,WAViBA,IAAM,CACfC,QAAAA,IAAI,kBAAmBZ,CAAQ,CAAA,CASvCW,CAEJ,ECrDME,EAAuBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGvB,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMqB,QAAQC,EAAE,EAGlCC,EAAoBN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIV,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMqB,QAAQC,EAAE,EAG5CE,EAAeC,EAAAA,GAAEP,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACR,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAM0B,UAAUC,IAEnC,CAAC,CAAE3B,MAAAA,CAAM,IAAMA,EAAM4B,OAAOC,WAAW,EAI5CC,EAAwBb,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EACX,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAM4B,OAAOG,QAC/B,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,EAAMgC,aAAaC,GACxC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMqB,QAAQC,GAC1B,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMkC,QAAQC,EAAE,EAGzCC,EAAsBC,EAAAA,GAAEnB,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACf,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAM0B,UAAUJ,GAEnC,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAM4B,OAAOC,YACvB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMqB,QAAQY,EAAE,EAGzCK,EAAsBrB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,YAAA,8BAAA,oCAAA,EAGtB,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMqB,QAAQY,GACzB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMqB,QAAQY,GACb,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAM4B,OAAOW,MAAM,EAOzDC,EAAoBvB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAI5B,EAAA,CAAA,gEAAA,CAAA,EAEKqB,EAAsBxB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EAChB,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAM0B,UAAUO,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAM4B,OAAOC,WAAW,EAG5Ca,EAA4BzB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,GAAA,EACtB,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAM0B,UAAUS,GACnC,CAAC,CAAEnC,MAAAA,CAAM,IAAMA,EAAM4B,OAAOe,cACvB,CAAC,CAAE3C,MAAAA,CAAM,IAAMA,EAAMqB,QAAQuB,EAAE,EAGzCC,EAAgBC,EAAAA,OAAM5B,WAAA,CAAAC,YAAA,SAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,qBAAA,kBAAA,UAAA,yBAAA,iBAAA,EACf,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMqB,QAAQc,GACpB,CAAC,CAAEnC,MAAAA,CAAM,IAAMA,EAAM4B,OAAOmB,WAC5B,CAAC,CAAE/C,MAAAA,CAAM,IAAMA,EAAM4B,OAAOW,OAC/B,CAAC,CAAEvC,MAAAA,CAAM,IAAMA,EAAMgC,aAAaG,GAC1C,CAAC,CAAEnC,MAAAA,CAAM,IAAMA,EAAM4B,OAAOC,YAGnB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAM4B,OAAOoB,OAAO,EAKjDC,EAAeC,EAAAA,MAAKhC,WAAA,CAAAC,YAAA,QAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,qBAAA,kBAAA,UAAA,yBAAA,iBAAA,EACb,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMqB,QAAQc,GACpB,CAAC,CAAEnC,MAAAA,CAAM,IAAMA,EAAM4B,OAAOmB,WAC5B,CAAC,CAAE/C,MAAAA,CAAM,IAAMA,EAAM4B,OAAOW,OAC/B,CAAC,CAAEvC,MAAAA,CAAM,IAAMA,EAAMgC,aAAaG,GAC1C,CAAC,CAAEnC,MAAAA,CAAM,IAAMA,EAAM4B,OAAOC,YAGnB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAM4B,OAAOoB,OAAO,EAKjDG,EAAgBC,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,SAAAC,YAAA,eAAA,CAK1B,EAAA,CAAA,gEAAA,CAAA,EAEKiC,EAAqBH,EAAAA,MAAKhC,WAAA,CAAAC,YAAA,cAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,gEAAA,wDAAA,EAMR,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAM4B,OAAOoB,OAAO,EAQrDM,EAAsBC,EAAAA,KAAIrC,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,mFAAA,eAAA,0IAAA,sBAAA,EAOV,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAM4B,OAAOW,OAClC,CAAC,CAAEvC,MAAAA,CAAM,IAAMA,EAAMwD,YAAYC,OAW/B,CAAC,CAAEzD,MAAAA,CAAM,IAAMA,EAAMwD,YAAYC,MAAM,EAKnDC,EAAoBC,EAAAA,OAAMzC,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,WAAA,IAAA,qBAAA,8BAAA,2EAAA,mCAAA,6BAAA,IAAA,EACnB,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMqB,QAAQc,GAAM,CAAC,CAAEnC,MAAAA,CAAM,IAAMA,EAAMqB,QAAQC,GACvD,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAM4B,OAAOoB,QAE/B,CAAC,CAAEhD,MAAAA,CAAM,IAAMA,EAAMgC,aAAaC,GAIpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMwD,YAAYI,KAElD,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,EAAMqB,QAAQC,GAGrB,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAM4B,OAAOiC,WAAW,EASzDC,EAAqBA,IAAM,CACzB,KAAA,CAAE3D,SAAAA,EAAUQ,aAAAA,EAAcG,WAAAA,GAAef,EAAY,EAE3D,cACGiB,EACC,CAAA,SAAA,CAAA+C,MAACxC,EACC,CAAA,SAAAwC,EAAA,IAACvC,EAAM,CAAA,SAAA,UAAQ,CAAA,EACjB,SAECM,EACC,CAAA,SAAA,CAAAiC,EAAAA,IAAC3B,GAAa,SAAU,YAAA,CAAA,EAExB2B,EAAA,IAACzB,EACC,CAAA,SAAA0B,EAAAA,KAACxB,EACC,CAAA,SAAA,CAAAwB,OAAC,MACC,CAAA,SAAA,CAAAD,EAAAA,IAACtB,GAAa,SAAK,OAAA,CAAA,EACnBsB,EAAAA,IAACrB,GAAkB,SAEnB,6BAAA,CAAA,CAAA,EACF,EACCsB,EAAA,KAAAnB,EAAA,CACC,MAAO1C,EAASH,MAChB,SAAiBW,GAAAA,EAAa,QAASsD,EAAEC,OAAOtD,KAAK,EAErD,SAAA,CAACmD,EAAA,IAAA,SAAA,CAAO,MAAM,KAAK,SAAS,YAAA,EAC3BA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,CAAA,EAC7B,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,SAECjC,EACC,CAAA,SAAA,CAAAiC,EAAAA,IAAC3B,GAAa,SAAgB,kBAAA,CAAA,EAE9B2B,EAAA,IAACzB,EACC,CAAA,SAAA0B,EAAAA,KAACxB,EACC,CAAA,SAAA,CAAAwB,OAAC,MACC,CAAA,SAAA,CAAAD,EAAAA,IAACtB,GAAa,SAAqB,uBAAA,CAAA,EACnCsB,EAAAA,IAACrB,GAAkB,SAEnB,+CAAA,CAAA,CAAA,EACF,EACAqB,MAACd,GACC,KAAK,SACL,IAAI,IACJ,IAAI,KACJ,MAAO9C,EAASI,gBAChB,SAAW0D,GACTtD,EAAa,kBAAmBwD,SAASF,EAAEC,OAAOtD,KAAK,CAAC,EAE1D,MAAO,CAAEwD,MAAO,MAAA,EAAS,CAAA,CAAA,CAE7B,CACF,CAAA,EAEAL,EAAA,IAACzB,EACC,CAAA,SAAA0B,EAAAA,KAACxB,EACC,CAAA,SAAA,CAAAwB,OAAC,MACC,CAAA,SAAA,CAAAD,EAAAA,IAACtB,GAAa,SAAa,eAAA,CAAA,EAC3BsB,EAAAA,IAACrB,GAAkB,SAEnB,8BAAA,CAAA,CAAA,EACF,SACCS,EACC,CAAA,SAAA,CAAAY,EAAA,IAACV,EACC,CAAA,KAAK,WACL,QAASlD,EAASK,kBAClB,SAAWyD,GACTtD,EAAa,oBAAqBsD,EAAEC,OAAOG,OAAO,EACnD,QAEFf,EAAY,EAAA,CAAA,EACf,CAAA,CAAA,CACF,CACF,CAAA,EAEAS,EAAA,IAACzB,EACC,CAAA,SAAA0B,EAAAA,KAACxB,EACC,CAAA,SAAA,CAAAwB,OAAC,MACC,CAAA,SAAA,CAAAD,EAAAA,IAACtB,GAAa,SAAgB,kBAAA,CAAA,EAC9BsB,EAAAA,IAACrB,GAAkB,SAEnB,qCAAA,CAAA,CAAA,EACF,SACCS,EACC,CAAA,SAAA,CAAAY,EAAA,IAACV,EACC,CAAA,KAAK,WACL,QAASlD,EAASM,sBAClB,SAAWwD,GACTtD,EAAa,wBAAyBsD,EAAEC,OAAOG,OAAO,EACvD,QAEFf,EAAY,EAAA,CAAA,EACf,CAAA,CAAA,CACF,CACF,CAAA,EAEAS,EAAA,IAACzB,EACC,CAAA,SAAA0B,EAAAA,KAACxB,EACC,CAAA,SAAA,CAAAwB,OAAC,MACC,CAAA,SAAA,CAAAD,EAAAA,IAACtB,GAAa,SAAiB,mBAAA,CAAA,EAC/BsB,EAAAA,IAACrB,GAAkB,SAEnB,8CAAA,CAAA,CAAA,EACF,SACCS,EACC,CAAA,SAAA,CAAAY,EAAA,IAACV,EACC,CAAA,KAAK,WACL,QAASlD,EAASO,gBAClB,SAAWuD,GACTtD,EAAa,kBAAmBsD,EAAEC,OAAOG,OAAO,EACjD,QAEFf,EAAY,EAAA,CAAA,EACf,CAAA,CAAA,CACF,CACF,CAAA,EAECS,EAAA,IAAAL,EAAA,CAAW,QAAS5C,EAAY,SAAa,gBAAA,CAAA,EAChD,CACF,CAAA,CAAA,CAEJ"}