import{j as t}from"./client-4c27269c.js";import{s as o}from"./styled-components-3ebafa9a.js";import{r as g}from"./react-60374de9.js";import{t as ne}from"./tradeStorage-0955231a.js";import{_ as Ae}from"./main-2920b47d.js";import{c as re,d as _e}from"./router-e715efa2.js";function qe(e,n,a){const[i,s]=g.useState(n),[c,r]=g.useState(null),[p,l]=g.useState(null),[u,m]=g.useState(null),[y,v]=g.useState({date:new Date().toISOString().split("T")[0],symbol:"",direction:"long",quantity:"",entryPrice:"",exitPrice:"",stopLoss:"",takeProfit:"",profit:"",modelType:"",session:"",setup:"",entryTime:"",exitTime:"",rdTime:"",market:"Stocks",rMultiple:"",entryVersion:"First Entry",riskPoints:"",patternQuality:"5",primarySetupCategory:"",primarySetupType:"",secondarySetupCategory:"",secondarySetupType:"",liquidityTaken:"",additionalFVGs:[],dolTargetType:"",specificDOLType:"",parentPDArray:"",patternQualityClarity:"",patternQualityConfluence:"",patternQualityContext:"",patternQualityRisk:"",patternQualityReward:"",patternQualityTimeframe:"",patternQualityVolume:"",patternQualityNotes:"",dolType:"",dolStrength:"",dolReaction:"",dolContext:[],dolPriceAction:"",dolVolumeProfile:"",dolTimeOfDay:"",dolMarketStructure:"",dolEffectiveness:"5",dolNotes:"",notes:"",tags:[],result:"win"});g.useEffect(()=>{(async()=>{var h,f,b,k,x,T,F,N,R,ee,C,ae,se,le,ce,de,pe,me;if(console.log(`loadTradeData called - isEditMode: ${n}, tradeId: "${e}"`),!e){console.error("loadTradeData: tradeId is undefined or null");return}if(e==="new"){console.log("Creating new trade, skipping data loading");return}if(n)try{console.log(`Attempting to load trade data for ID: ${e}`),s(!0);const O=String(e).trim();if(!O)throw new Error("Invalid trade ID: empty or whitespace");console.log(`Calling tradeStorage.getTradeById with ID: ${O}`);const d=await ne.getTradeById(O);console.log("Trade data retrieved:",d),d?(console.log("Trade found, setting trade data"),m(d),console.log("Converting trade data to form values"),v({date:d.date,symbol:d.symbol,direction:d.direction==="Long"?"long":"short",quantity:String(d.size),entryPrice:String(d.entry),exitPrice:String(d.exit),stopLoss:d.stopLoss?String(d.stopLoss):"",takeProfit:d.takeProfit?String(d.takeProfit):"",profit:String(d.profitLoss),modelType:d.modelType||"",session:d.session||"",setup:d.setup||"",entryTime:d.entryTime||"",exitTime:d.exitTime||"",rdTime:d.rdTime||"",market:d.market||"Stocks",rMultiple:d.rMultiple?String(d.rMultiple):"",entryVersion:d.entryVersion||"First Entry",riskPoints:d.riskPoints?String(d.riskPoints):"",patternQuality:d.patternQuality?String(d.patternQuality):"5",primarySetupCategory:d.primarySetupCategory||"",primarySetupType:d.primarySetupType||"",secondarySetupCategory:d.secondarySetupCategory||"",secondarySetupType:d.secondarySetupType||"",liquidityTaken:d.liquidityTaken||"",additionalFVGs:d.additionalFVGs||[],dolTargetType:d.dolTargetType||"",specificDOLType:d.specificDOLType||"",parentPDArray:d.parentPDArray||"",patternQualityClarity:((h=d.patternQualityScore)==null?void 0:h.criteria.clarity)||"",patternQualityConfluence:((f=d.patternQualityScore)==null?void 0:f.criteria.confluence)||"",patternQualityContext:((b=d.patternQualityScore)==null?void 0:b.criteria.context)||"",patternQualityRisk:((k=d.patternQualityScore)==null?void 0:k.criteria.risk)||"",patternQualityReward:((x=d.patternQualityScore)==null?void 0:x.criteria.reward)||"",patternQualityTimeframe:((T=d.patternQualityScore)==null?void 0:T.criteria.timeframe)||"",patternQualityVolume:((F=d.patternQualityScore)==null?void 0:F.criteria.volume)||"",patternQualityNotes:((N=d.patternQualityScore)==null?void 0:N.notes)||"",dolType:((R=d.dolAnalysis)==null?void 0:R.dolType)||"",dolStrength:((ee=d.dolAnalysis)==null?void 0:ee.dolStrength)||"",dolReaction:((C=d.dolAnalysis)==null?void 0:C.dolReaction)||"",dolContext:((ae=d.dolAnalysis)==null?void 0:ae.dolContext)||[],dolPriceAction:((se=d.dolAnalysis)==null?void 0:se.priceAction)||"",dolVolumeProfile:((le=d.dolAnalysis)==null?void 0:le.volumeProfile)||"",dolTimeOfDay:((ce=d.dolAnalysis)==null?void 0:ce.timeOfDay)||"",dolMarketStructure:((de=d.dolAnalysis)==null?void 0:de.marketStructure)||"",dolEffectiveness:(pe=d.dolAnalysis)!=null&&pe.effectiveness?String(d.dolAnalysis.effectiveness):"5",dolNotes:((me=d.dolAnalysis)==null?void 0:me.notes)||"",notes:d.notes||"",tags:d.tags||[],result:d.profitLoss>0?"win":d.profitLoss<0?"loss":"breakeven"})):(console.error(`Trade with ID ${e} not found in IndexedDB`),r(`Trade with ID ${e} not found.`))}catch(O){console.error("Error loading trade:",O),r("Failed to load trade data. Please try again.")}finally{console.log("Setting isLoading to false"),s(!1)}})()},[n,e]);const P=g.useCallback(S=>{const{name:h,value:f}=S.target;v(b=>({...b,[h]:f}))},[]);return{formValues:y,setFormValues:v,handleChange:P,isLoading:i,setIsLoading:s,error:c,setError:r,success:p,setSuccess:l,tradeData:u}}function Ee(){const[e,n]=g.useState({}),a=g.useCallback((r,p)=>{const l={};switch(p){case"basic":r.date||(l.date="Date is required"),r.symbol||(l.symbol="Symbol is required"),r.entryPrice||(l.entryPrice="Entry price is required"),r.exitPrice||(l.exitPrice="Exit price is required"),r.quantity||(l.quantity="Quantity is required");break;case"timing":r.entryTime&&r.exitTime&&r.exitTime<r.entryTime&&(l.exitTime="Exit time must be after entry time"),r.rdTime&&r.entryTime&&r.entryTime<r.rdTime&&(l.entryTime="Entry time must be after risk/decision time");break;case"strategy":r.primarySetupCategory&&!r.primarySetupType&&(l.primarySetupType="Please select a primary setup type"),r.secondarySetupCategory&&!r.secondarySetupType&&(l.secondarySetupType="Please select a secondary setup type"),r.primarySetupType&&r.secondarySetupType&&r.primarySetupType===r.secondarySetupType&&(l.secondarySetupType="Primary and secondary setup types must be different");break;case"dol-analysis":r.dolType&&(r.dolStrength||(l.dolStrength="Please select a DOL strength"),r.dolReaction||(l.dolReaction="Please select a DOL reaction"),(!r.dolContext||r.dolContext.length===0)&&(l.dolContext="Please select at least one DOL context")),r.dolTargetType&&r.dolTargetType!=="RD Target"&&!r.specificDOLType&&(l.specificDOLType="Please select a specific DOL type");break}return n(u=>{const m={...u};return Object.keys(l).forEach(y=>{m[y]=l[y]}),m}),Object.keys(l).length===0},[]),i=g.useCallback(r=>{const p={};return r.date||(p.date="Date is required"),r.symbol||(p.symbol="Symbol is required"),r.entryPrice||(p.entryPrice="Entry price is required"),r.exitPrice||(p.exitPrice="Exit price is required"),r.quantity||(p.quantity="Quantity is required"),n(l=>({...l,...p})),Object.keys(p).length===0},[]),s=g.useCallback(r=>{n(p=>{const l={...p};return delete l[r],l})},[]),c=g.useCallback(()=>{n({})},[]);return{validationErrors:e,validateCurrentTab:a,validateBasicInfoTab:i,clearFieldError:s,clearAllErrors:c,setValidationErrors:n}}function Me(e,n){return g.useEffect(()=>{if(e.riskPoints&&e.profit){const i=parseFloat(e.riskPoints),s=parseFloat(e.profit);if(i>0){const c=(s/i).toFixed(2);n(r=>({...r,rMultiple:c}))}}},[e.riskPoints,e.profit,n]),g.useEffect(()=>{},[e.entryTime,e.exitTime,e.rdTime]),{calculateProfitLoss:()=>{if(e.entryPrice&&e.exitPrice&&e.quantity){const i=parseFloat(e.entryPrice),s=parseFloat(e.exitPrice),c=parseFloat(e.quantity);if(!isNaN(i)&&!isNaN(s)&&!isNaN(c)){let r;e.direction==="long"?r=(s-i)*c:r=(i-s)*c,n(p=>({...p,profit:r.toFixed(2),result:r>0?"win":r<0?"loss":"breakeven"}))}}}}}function Qe(e,n,a,i,s,c,r,p,l,u){const m=re(),[y,v]=g.useState(!1);return{handleSubmit:g.useCallback(async S=>{if(S.preventDefault(),!s()){l("Please complete the required fields in the Basic Info tab."),p("basic");return}if(r!=="basic"&&!c()){l("Please fix the validation errors in the current tab before submitting.");return}v(!0),l(null),u(null);try{const h={clarity:e.patternQualityClarity||"",confluence:e.patternQualityConfluence||"",context:e.patternQualityContext||"",risk:e.patternQualityRisk||"",reward:e.patternQualityReward||"",timeframe:e.patternQualityTimeframe||"",volume:e.patternQualityVolume||""},f=Object.values(h).every(T=>T!==""),b={...e};if(f){const{calculateTotalScore:T,convertScoreToRating:F}=await Ae(()=>Promise.resolve().then(()=>ot),void 0),N=T(h),R=F(N);b.patternQualityScore={total:N,rating:R,criteria:h,notes:e.patternQualityNotes||""}}e.dolType&&(b.dolAnalysis={dolType:e.dolType,dolStrength:e.dolStrength||"",dolReaction:e.dolReaction||"",dolContext:e.dolContext||[],priceAction:e.dolPriceAction||"",volumeProfile:e.dolVolumeProfile||"",timeOfDay:e.dolTimeOfDay||"",marketStructure:e.dolMarketStructure||"",effectiveness:e.dolEffectiveness?parseInt(e.dolEffectiveness):5,notes:e.dolNotes||""}),console.log("Form submitted:",b);const k={symbol:e.symbol,date:e.date,direction:e.direction==="long"?"Long":"Short",size:parseInt(e.quantity)||0,entry:parseFloat(e.entryPrice)||0,exit:parseFloat(e.exitPrice)||0,stopLoss:parseFloat(e.stopLoss||"0")||0,takeProfit:parseFloat(e.takeProfit||"0")||0,profitLoss:parseFloat(e.profit)||0,modelType:e.modelType,session:e.session,setup:e.setup,entryTime:e.entryTime,exitTime:e.exitTime,rdTime:e.rdTime,market:e.market,rMultiple:e.rMultiple?parseFloat(e.rMultiple):void 0,entryVersion:e.entryVersion,riskPoints:e.riskPoints?parseFloat(e.riskPoints):void 0,patternQuality:e.patternQuality?parseInt(e.patternQuality):void 0,patternQualityScore:b.patternQualityScore,dolAnalysis:b.dolAnalysis,primarySetupCategory:e.primarySetupCategory,primarySetupType:e.primarySetupType,secondarySetupCategory:e.secondarySetupCategory,secondarySetupType:e.secondarySetupType,liquidityTaken:e.liquidityTaken,additionalFVGs:e.additionalFVGs,dolTargetType:e.dolTargetType,specificDOLType:e.specificDOLType,parentPDArray:e.parentPDArray,strategy:e.setup||"Unknown",notes:e.notes,tags:e.tags||[]};let x;n&&i?(x=await ne.saveTrade({...k,id:i.id}),console.log("Trade updated in IndexedDB successfully",x)):(x=await ne.saveTrade(k),console.log("New trade saved to IndexedDB successfully",x)),u(n?`Trade for ${x.symbol} on ${x.date} updated successfully!`:`New trade for ${x.symbol} on ${x.date} created successfully!`),setTimeout(()=>{console.log("Navigating back to journal page after successful submission"),m("/journal")},1500)}catch(h){l("Failed to save trade. Please try again."),console.error("Error submitting form:",h)}finally{v(!1)}},[e,s,c,r,p,l,u,n,i,m]),isSubmitting:y}}const Be=[{value:"Price Action",label:"Price Action"},{value:"Momentum",label:"Momentum"},{value:"Trend Following",label:"Trend Following"},{value:"Mean Reversion",label:"Mean Reversion"},{value:"Breakout",label:"Breakout"},{value:"Support/Resistance",label:"Support/Resistance"},{value:"Other",label:"Other"}],He=[{value:"Pre-Market",label:"Pre-Market"},{value:"Regular Hours",label:"Regular Hours"},{value:"Power Hour",label:"Power Hour"},{value:"After Hours",label:"After Hours"},{value:"Overnight",label:"Overnight"}],We=[{value:"Breakout",label:"Breakout"},{value:"Pullback",label:"Pullback"},{value:"Reversal",label:"Reversal"},{value:"Trend Continuation",label:"Trend Continuation"},{value:"Gap Fill",label:"Gap Fill"},{value:"VWAP Bounce",label:"VWAP Bounce"},{value:"Support/Resistance",label:"Support/Resistance"},{value:"Other",label:"Other"}],Ue=[{value:"Stocks",label:"Stocks"},{value:"Options",label:"Options"},{value:"Futures",label:"Futures"},{value:"Forex",label:"Forex"},{value:"Crypto",label:"Crypto"},{value:"Other",label:"Other"}],Ve=[{value:"First Entry",label:"First Entry"},{value:"Re-Entry",label:"Re-Entry"},{value:"Scale In",label:"Scale In"},{value:"Averaging Down",label:"Averaging Down"},{value:"Averaging Up",label:"Averaging Up"}],Ye=Array.from({length:10},(e,n)=>({value:String(n+1),label:String(n+1)})),Ke=e=>{re();const[n,a]=g.useState("basic");console.log(`useTradeForm hook initialized with tradeId: "${e}"`);const i=window.location.hash.substring(1);console.log(`Current hash path in useTradeForm: ${i}`);let s=e;if(!s&&i.includes("/trade/edit/")){const C=i.match(/\/trade\/edit\/([^\/]+)/);C&&C[1]&&(s=C[1],console.log(`Extracted trade ID from URL: ${s}`))}const c=s==="new"||i.includes("/trade/new"),r=s&&s!=="new"||i.includes("/trade/edit/")&&!i.includes("/trade/edit/new");console.log(`useTradeForm - isNewTrade: ${c}, isEditMode: ${r}`);const{formValues:p,setFormValues:l,handleChange:u,isLoading:m,error:y,setError:v,success:P,setSuccess:S,tradeData:h}=qe(s,r),{validationErrors:f,validateCurrentTab:b,validateBasicInfoTab:k}=Ee(),{calculateProfitLoss:x}=Me(p,l),T=()=>b(p,n),F=()=>k(p),{handleSubmit:N,isSubmitting:R}=Qe(p,r,c,h,F,T,n,a,v,S);return{formValues:p,setFormValues:l,handleChange:u,handleSubmit:N,isSubmitting:R,isLoading:m,error:y,success:P,validationErrors:f,isNewTrade:c,activeTab:n,handleTabChange:C=>{console.log(`Tab change requested from ${n} to ${C}`),P&&(console.log("Clearing success message during tab change"),S(null)),y&&(console.log("Clearing error message during tab change"),v(null)),n&&(console.log(`Validating current tab (${n}) before switching`),T()),console.log(`Setting active tab to: ${C}`),a(C)},calculateProfitLoss:x}},Xe=o.div.withConfig({displayName:"TabContainer",componentId:"sc-jxcsz3-0"})(["display:flex;flex-direction:column;width:100%;"]),Je=o.div.withConfig({displayName:"TabList",componentId:"sc-jxcsz3-1"})(["display:flex;border-bottom:1px solid ",";margin-bottom:",";"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),Ze=o.button.withConfig({displayName:"TabButton",componentId:"sc-jxcsz3-2"})(["padding:"," ",";background:none;border:none;border-bottom:2px solid ",";color:",";font-weight:",";cursor:pointer;transition:all ",";&:hover{color:",";}&:focus{outline:none;color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({active:e,theme:n})=>e?n.colors.primary:"transparent",({active:e,theme:n})=>e?n.colors.primary:n.colors.textSecondary,({active:e,theme:n})=>e?n.fontWeights.semibold:n.fontWeights.regular,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),et=o.div.withConfig({displayName:"TabContent",componentId:"sc-jxcsz3-3"})(["padding:"," 0;"],({theme:e})=>e.spacing.sm),tt=({tabs:e,defaultTab:n,className:a,activeTab:i,onTabClick:s})=>{var u;const[c,r]=g.useState(n||e[0].id),p=i!==void 0?i:c,l=(m,y)=>{m.preventDefault(),m.stopPropagation(),console.log(`Tab clicked: ${y}`),s?(s(y),console.log(`Called onTabClick with: ${y}`)):(r(y),console.log(`Updated internal tab state to: ${y}`))};return t.jsxs(Xe,{className:a,children:[t.jsx(Je,{children:e.map(m=>t.jsx(Ze,{active:p===m.id,onClick:y=>l(y,m.id),type:"button",form:"",tabIndex:0,"data-tab-id":m.id,children:m.label},m.id))}),t.jsx(et,{children:(u=e.find(m=>m.id===p))==null?void 0:u.content})]})},Te={excellent:5,good:4,average:3,poor:2,unacceptable:1},we=[{value:"excellent",label:"Excellent (5)"},{value:"good",label:"Good (4)"},{value:"average",label:"Average (3)"},{value:"poor",label:"Poor (2)"},{value:"unacceptable",label:"Unacceptable (1)"}],Z={clarity:{title:"Pattern Clarity",description:"How clear and well-defined is the pattern?",excellent:"Pattern is extremely clear with perfect formation",good:"Pattern is clear with minor imperfections",average:"Pattern is recognizable but has some ambiguity",poor:"Pattern is difficult to recognize with significant ambiguity",unacceptable:"Pattern is barely recognizable or completely ambiguous"},confluence:{title:"Confluence Factors",description:"How many supporting factors align with this pattern?",excellent:"Multiple strong confluence factors (5+ factors)",good:"Several good confluence factors (3-4 factors)",average:"Some confluence factors (2-3 factors)",poor:"Minimal confluence (1-2 weak factors)",unacceptable:"No confluence factors"},context:{title:"Market Context",description:"How well does the pattern fit within the broader market context?",excellent:"Perfect alignment with market structure and conditions",good:"Good alignment with market structure and conditions",average:"Reasonable alignment with some contradicting factors",poor:"Poor alignment with several contradicting factors",unacceptable:"Pattern contradicts the broader market context"},risk:{title:"Risk Profile",description:"How well-defined and manageable is the risk?",excellent:"Extremely clear stop location with minimal risk",good:"Clear stop location with reasonable risk",average:"Identifiable stop location but with moderate risk",poor:"Unclear stop location or high risk",unacceptable:"No clear stop location or extremely high risk"},reward:{title:"Reward Potential",description:"What is the potential reward relative to risk?",excellent:"Exceptional reward potential (5R+)",good:"Strong reward potential (3-5R)",average:"Reasonable reward potential (2-3R)",poor:"Limited reward potential (1-2R)",unacceptable:"Poor reward potential (<1R)"},timeframe:{title:"Timeframe Alignment",description:"How well does the pattern align across multiple timeframes?",excellent:"Strong alignment across all relevant timeframes",good:"Good alignment across most timeframes",average:"Alignment on primary timeframe with some higher/lower support",poor:"Limited alignment across timeframes",unacceptable:"Pattern only appears on a single timeframe with contradictions on others"},volume:{title:"Volume Profile",description:"How does volume support the pattern?",excellent:"Volume perfectly confirms the pattern",good:"Volume generally supports the pattern",average:"Volume is neutral or mixed",poor:"Volume somewhat contradicts the pattern",unacceptable:"Volume strongly contradicts the pattern"}},je=e=>Object.values(e).reduce((n,a)=>n+(Te[a]||0),0),Pe=e=>{const n=Object.keys(Z).length*5,a=Math.round(e/n*10);return Math.max(1,Math.min(10,a))},ke=e=>e>=9?"Exceptional":e>=8?"Excellent":e>=7?"Very Good":e>=6?"Good":e>=5?"Average":e>=4?"Below Average":e>=3?"Poor":e>=2?"Very Poor":"Unacceptable",Ne=e=>e>=8?"#4CAF50":e>=6?"#8BC34A":e>=5?"#FFC107":e>=3?"#FF9800":"#F44336",ot=Object.freeze(Object.defineProperty({__proto__:null,PATTERN_QUALITY_CRITERIA:Z,SCORE_RANGE_OPTIONS:we,SCORE_VALUES:Te,calculateTotalScore:je,convertScoreToRating:Pe,getRatingColor:Ne,getRatingDescription:ke},Symbol.toStringTag,{value:"Module"})),it=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-l14zhl-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),nt=o.h3.withConfig({displayName:"CriterionTitle",componentId:"sc-l14zhl-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),rt=o.p.withConfig({displayName:"CriterionDescription",componentId:"sc-l14zhl-2"})(["font-size:",";color:",";margin:"," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),at=o.div.withConfig({displayName:"RadioGroup",componentId:"sc-l14zhl-3"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm),st=o.div.withConfig({displayName:"RadioOption",componentId:"sc-l14zhl-4"})(["display:flex;align-items:flex-start;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),lt=o.input.withConfig({displayName:"RadioInput",componentId:"sc-l14zhl-5"})(["margin-right:",";margin-top:3px;"],({theme:e})=>e.spacing.sm),ct=o.div.withConfig({displayName:"RadioLabelContainer",componentId:"sc-l14zhl-6"})(["display:flex;flex-direction:column;"]),dt=o.label.withConfig({displayName:"RadioLabel",componentId:"sc-l14zhl-7"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),pt=o.span.withConfig({displayName:"RadioDescription",componentId:"sc-l14zhl-8"})(["font-size:",";color:",";margin-top:2px;"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textSecondary),I=({criterion:e,value:n,onChange:a,fieldName:i})=>{const s=Z[e],c=r=>{a(i,r.target.value)};return t.jsxs(it,{children:[t.jsx(nt,{children:s.title}),t.jsx(rt,{children:s.description}),t.jsx(at,{children:we.map(r=>t.jsxs(st,{children:[t.jsx(lt,{type:"radio",id:`${i}_${r.value}`,name:i,value:r.value,checked:n===r.value,onChange:c}),t.jsxs(ct,{children:[t.jsx(dt,{htmlFor:`${i}_${r.value}`,children:r.label}),t.jsx(pt,{children:s[r.value]})]})]},r.value))})]})},mt=o.div.withConfig({displayName:"AssessmentContainer",componentId:"sc-103dgz-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.lg),ut=o.div.withConfig({displayName:"Introduction",componentId:"sc-103dgz-1"})(["margin-bottom:",";"],({theme:e})=>e.spacing.lg),gt=o.h3.withConfig({displayName:"IntroTitle",componentId:"sc-103dgz-2"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm),yt=o.p.withConfig({displayName:"IntroText",componentId:"sc-103dgz-3"})(["font-size:",";color:",";margin:0;line-height:1.5;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textSecondary),ht=o.hr.withConfig({displayName:"Divider",componentId:"sc-103dgz-4"})(["border:none;border-top:1px solid ",";margin:"," 0;"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),xt=o.div.withConfig({displayName:"ScoreSection",componentId:"sc-103dgz-5"})(["display:flex;flex-direction:column;gap:",";background-color:",";padding:",";border-radius:",";margin-top:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background,({theme:e})=>e.spacing.lg,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg),ft=o.h3.withConfig({displayName:"ScoreTitle",componentId:"sc-103dgz-6"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary),bt=o.div.withConfig({displayName:"ScoreDetails",componentId:"sc-103dgz-7"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>e.spacing.xl),vt=o.div.withConfig({displayName:"ScoreValue",componentId:"sc-103dgz-8"})(["font-size:3rem;font-weight:700;color:",";display:flex;align-items:center;justify-content:center;width:100px;height:100px;border-radius:50%;border:4px solid ",";"],({color:e})=>e,({color:e})=>e),St=o.div.withConfig({displayName:"ScoreInfo",componentId:"sc-103dgz-9"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Ct=o.div.withConfig({displayName:"ScoreDescription",componentId:"sc-103dgz-10"})(["font-size:",";font-weight:600;color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary),Tt=o.div.withConfig({displayName:"ScoreBreakdown",componentId:"sc-103dgz-11"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),wt=o.div.withConfig({displayName:"NotesSection",componentId:"sc-103dgz-12"})(["margin-top:",";"],({theme:e})=>e.spacing.lg),jt=o.label.withConfig({displayName:"NotesLabel",componentId:"sc-103dgz-13"})(["font-size:",";font-weight:600;color:",";display:block;margin-bottom:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm),Pt=o.textarea.withConfig({displayName:"NotesTextarea",componentId:"sc-103dgz-14"})(["width:100%;min-height:100px;padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";resize:vertical;&:focus{outline:none;border-color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),kt=({formValues:e,onChange:n})=>{const[a,i]=g.useState(0),[s,c]=g.useState(0);g.useEffect(()=>{const p={clarity:e.patternQualityClarity||"",confluence:e.patternQualityConfluence||"",context:e.patternQualityContext||"",risk:e.patternQualityRisk||"",reward:e.patternQualityReward||"",timeframe:e.patternQualityTimeframe||"",volume:e.patternQualityVolume||""};if(Object.values(p).every(u=>u!=="")){const u=je(p),m=Pe(u);i(u),c(m),n("patternQuality",m.toString())}},[e.patternQualityClarity,e.patternQualityConfluence,e.patternQualityContext,e.patternQualityRisk,e.patternQualityReward,e.patternQualityTimeframe,e.patternQualityVolume,n]);const r=p=>{n("patternQualityNotes",p.target.value)};return t.jsxs(mt,{children:[t.jsxs(ut,{children:[t.jsx(gt,{children:"Pattern Quality Assessment"}),t.jsx(yt,{children:"Evaluate the quality of your trade setup by rating each criterion below. This assessment will help you objectively analyze your trade patterns and improve your decision-making process."})]}),t.jsx(ht,{}),t.jsx(I,{criterion:"clarity",value:e.patternQualityClarity||"",onChange:n,fieldName:"patternQualityClarity"}),t.jsx(I,{criterion:"confluence",value:e.patternQualityConfluence||"",onChange:n,fieldName:"patternQualityConfluence"}),t.jsx(I,{criterion:"context",value:e.patternQualityContext||"",onChange:n,fieldName:"patternQualityContext"}),t.jsx(I,{criterion:"risk",value:e.patternQualityRisk||"",onChange:n,fieldName:"patternQualityRisk"}),t.jsx(I,{criterion:"reward",value:e.patternQualityReward||"",onChange:n,fieldName:"patternQualityReward"}),t.jsx(I,{criterion:"timeframe",value:e.patternQualityTimeframe||"",onChange:n,fieldName:"patternQualityTimeframe"}),t.jsx(I,{criterion:"volume",value:e.patternQualityVolume||"",onChange:n,fieldName:"patternQualityVolume"}),s>0&&t.jsxs(xt,{children:[t.jsx(ft,{children:"Pattern Quality Score"}),t.jsxs(bt,{children:[t.jsx(vt,{color:Ne(s),children:s}),t.jsxs(St,{children:[t.jsx(Ct,{children:ke(s)}),t.jsxs(Tt,{children:["Total Score: ",a," out of ",Object.keys(Z).length*5]})]})]})]}),t.jsxs(wt,{children:[t.jsx(jt,{htmlFor:"patternQualityNotes",children:"Additional Notes"}),t.jsx(Pt,{id:"patternQualityNotes",name:"patternQualityNotes",value:e.patternQualityNotes||"",onChange:r,placeholder:"Add any additional notes about the pattern quality..."})]})]})},Nt=[{value:"Sweep",label:"Sweep - Price moves through the liquidity level"},{value:"Tap",label:"Tap - Price touches the liquidity level exactly"},{value:"Approach",label:"Approach - Price approaches but doesn't quite reach the level"},{value:"Rejection",label:"Rejection - Price rejects from the liquidity level"}],It=[{value:"Strong",label:"Strong - Significant price movement with high volume"},{value:"Moderate",label:"Moderate - Noticeable price movement with average volume"},{value:"Weak",label:"Weak - Minimal price movement with low volume"}],Ft=[{value:"Immediate Reversal",label:"Immediate Reversal - Price reverses direction immediately"},{value:"Delayed Reversal",label:"Delayed Reversal - Price reverses after some consolidation"},{value:"Consolidation",label:"Consolidation - Price consolidates at the level"},{value:"Continuation",label:"Continuation - Price continues in the same direction"}],Rt=[{value:"High Volume Node",label:"High Volume Node"},{value:"Low Volume Node",label:"Low Volume Node"},{value:"VPOC",label:"Volume Point of Control (VPOC)"},{value:"VAH/VAL",label:"Value Area High/Low"},{value:"Previous Day High/Low",label:"Previous Day High/Low"},{value:"Previous Week High/Low",label:"Previous Week High/Low"},{value:"Previous Month High/Low",label:"Previous Month High/Low"},{value:"Round Number",label:"Round Number (00, 50, etc.)"},{value:"Technical Level",label:"Technical Level (Support/Resistance)"},{value:"News Event",label:"News Event"},{value:"Other",label:"Other"}];Array.from({length:10},(e,n)=>({value:String(n+1),label:String(n+1)}));const ue={Sweep:"Describe how price moved through the liquidity level. Was it a clean sweep or did it struggle?",Tap:"Describe how price interacted with the liquidity level. Was it a precise tap or did it linger?",Approach:"Describe how price approached the liquidity level. How close did it get and why did it stop?",Rejection:"Describe how price rejected from the liquidity level. Was it a sharp rejection or gradual?"},ge={Strong:"Describe the volume profile during the liquidity interaction. Was there a volume spike?",Moderate:"Describe the volume profile during the liquidity interaction. Was volume consistent?",Weak:"Describe the volume profile during the liquidity interaction. Why was volume low?"},zt="Describe the significance of the time of day for this liquidity interaction. Was it during a key market session or near a session transition?",Dt="Describe the market structure context for this liquidity interaction. Was price in an uptrend, downtrend, or range? Were there any key levels nearby?",Lt=e=>e>=8?"#4CAF50":e>=6?"#8BC34A":e>=5?"#FFC107":e>=3?"#FF9800":"#F44336",Ot=e=>e>=9?"Exceptional":e>=8?"Excellent":e>=7?"Very Good":e>=6?"Good":e>=5?"Average":e>=4?"Below Average":e>=3?"Poor":e>=2?"Very Poor":"Ineffective",Gt=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-6hiz0k-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),$t=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-6hiz0k-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),At=o.p.withConfig({displayName:"Description",componentId:"sc-6hiz0k-2"})(["font-size:",";color:",";margin:"," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),_t=o.div.withConfig({displayName:"RadioGroup",componentId:"sc-6hiz0k-3"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm),qt=o.div.withConfig({displayName:"RadioOption",componentId:"sc-6hiz0k-4"})(["display:flex;align-items:flex-start;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),Et=o.input.withConfig({displayName:"RadioInput",componentId:"sc-6hiz0k-5"})(["margin-right:",";margin-top:3px;"],({theme:e})=>e.spacing.sm),Mt=o.div.withConfig({displayName:"RadioLabelContainer",componentId:"sc-6hiz0k-6"})(["display:flex;flex-direction:column;"]),Qt=o.label.withConfig({displayName:"RadioLabel",componentId:"sc-6hiz0k-7"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),Bt=o.span.withConfig({displayName:"RadioDescription",componentId:"sc-6hiz0k-8"})(["font-size:",";color:",";margin-top:2px;"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textSecondary),Ht=({value:e,onChange:n})=>{const a=i=>{n("dolType",i.target.value)};return t.jsxs(Gt,{children:[t.jsx($t,{children:"DOL Type"}),t.jsx(At,{children:"Select the type of liquidity interaction that occurred in this trade."}),t.jsx(_t,{children:Nt.map(i=>{const[s,c]=i.label.split(" - ");return t.jsxs(qt,{children:[t.jsx(Et,{type:"radio",id:`dolType_${i.value}`,name:"dolType",value:i.value,checked:e===i.value,onChange:a}),t.jsxs(Mt,{children:[t.jsx(Qt,{htmlFor:`dolType_${i.value}`,children:s}),t.jsx(Bt,{children:c})]})]},i.value)})})]})},Wt=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-8x6wtq-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),Ut=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-8x6wtq-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),Vt=o.p.withConfig({displayName:"Description",componentId:"sc-8x6wtq-2"})(["font-size:",";color:",";margin:"," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),Yt=o.div.withConfig({displayName:"RadioGroup",componentId:"sc-8x6wtq-3"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm),Kt=o.div.withConfig({displayName:"RadioOption",componentId:"sc-8x6wtq-4"})(["display:flex;align-items:flex-start;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),Xt=o.input.withConfig({displayName:"RadioInput",componentId:"sc-8x6wtq-5"})(["margin-right:",";margin-top:3px;"],({theme:e})=>e.spacing.sm),Jt=o.div.withConfig({displayName:"RadioLabelContainer",componentId:"sc-8x6wtq-6"})(["display:flex;flex-direction:column;"]),Zt=o.label.withConfig({displayName:"RadioLabel",componentId:"sc-8x6wtq-7"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),eo=o.span.withConfig({displayName:"RadioDescription",componentId:"sc-8x6wtq-8"})(["font-size:",";color:",";margin-top:2px;"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textSecondary),to=({value:e,onChange:n})=>{const a=i=>{n("dolStrength",i.target.value)};return t.jsxs(Wt,{children:[t.jsx(Ut,{children:"DOL Strength"}),t.jsx(Vt,{children:"Evaluate the strength of the liquidity interaction."}),t.jsx(Yt,{children:It.map(i=>{const[s,c]=i.label.split(" - ");return t.jsxs(Kt,{children:[t.jsx(Xt,{type:"radio",id:`dolStrength_${i.value}`,name:"dolStrength",value:i.value,checked:e===i.value,onChange:a}),t.jsxs(Jt,{children:[t.jsx(Zt,{htmlFor:`dolStrength_${i.value}`,children:s}),t.jsx(eo,{children:c})]})]},i.value)})})]})},oo=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-7l72m2-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),io=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-7l72m2-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),no=o.p.withConfig({displayName:"Description",componentId:"sc-7l72m2-2"})(["font-size:",";color:",";margin:"," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),ro=o.div.withConfig({displayName:"RadioGroup",componentId:"sc-7l72m2-3"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm),ao=o.div.withConfig({displayName:"RadioOption",componentId:"sc-7l72m2-4"})(["display:flex;align-items:flex-start;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),so=o.input.withConfig({displayName:"RadioInput",componentId:"sc-7l72m2-5"})(["margin-right:",";margin-top:3px;"],({theme:e})=>e.spacing.sm),lo=o.div.withConfig({displayName:"RadioLabelContainer",componentId:"sc-7l72m2-6"})(["display:flex;flex-direction:column;"]),co=o.label.withConfig({displayName:"RadioLabel",componentId:"sc-7l72m2-7"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),po=o.span.withConfig({displayName:"RadioDescription",componentId:"sc-7l72m2-8"})(["font-size:",";color:",";margin-top:2px;"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textSecondary),mo=({value:e,onChange:n})=>{const a=i=>{n("dolReaction",i.target.value)};return t.jsxs(oo,{children:[t.jsx(io,{children:"DOL Reaction"}),t.jsx(no,{children:"Describe how price reacted after the liquidity interaction."}),t.jsx(ro,{children:Ft.map(i=>{const[s,c]=i.label.split(" - ");return t.jsxs(ao,{children:[t.jsx(so,{type:"radio",id:`dolReaction_${i.value}`,name:"dolReaction",value:i.value,checked:e===i.value,onChange:a}),t.jsxs(lo,{children:[t.jsx(co,{htmlFor:`dolReaction_${i.value}`,children:s}),t.jsx(po,{children:c})]})]},i.value)})})]})},uo=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-7eeejv-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),go=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-7eeejv-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),yo=o.p.withConfig({displayName:"Description",componentId:"sc-7eeejv-2"})(["font-size:",";color:",";margin:"," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),ho=o.div.withConfig({displayName:"CheckboxGroup",componentId:"sc-7eeejv-3"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm),xo=o.div.withConfig({displayName:"CheckboxOption",componentId:"sc-7eeejv-4"})(["display:flex;align-items:center;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),fo=o.input.withConfig({displayName:"CheckboxInput",componentId:"sc-7eeejv-5"})(["margin-right:",";"],({theme:e})=>e.spacing.sm),bo=o.label.withConfig({displayName:"CheckboxLabel",componentId:"sc-7eeejv-6"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),vo=({value:e,onChange:n})=>{const a=i=>{const s=i.target.value,c=i.target.checked;let r;c?r=[...e,s]:r=e.filter(p=>p!==s),n("dolContext",r)};return t.jsxs(uo,{children:[t.jsx(go,{children:"DOL Context"}),t.jsx(yo,{children:"Select all contextual factors that apply to this liquidity interaction."}),t.jsx(ho,{children:Rt.map(i=>t.jsxs(xo,{children:[t.jsx(fo,{type:"checkbox",id:`dolContext_${i.value}`,name:"dolContext",value:i.value,checked:e.includes(i.value),onChange:a}),t.jsx(bo,{htmlFor:`dolContext_${i.value}`,children:i.label})]},i.value))})]})},So=o.div.withConfig({displayName:"AnalysisContainer",componentId:"sc-1vd7l2j-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),Co=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1vd7l2j-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),G=o.p.withConfig({displayName:"Description",componentId:"sc-1vd7l2j-2"})(["font-size:",";color:",";margin:"," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),W=o.div.withConfig({displayName:"FormGroup",componentId:"sc-1vd7l2j-3"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.md),U=o.label.withConfig({displayName:"Label",componentId:"sc-1vd7l2j-4"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),V=o.textarea.withConfig({displayName:"TextArea",componentId:"sc-1vd7l2j-5"})(["width:100%;min-height:80px;padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";resize:vertical;&:focus{outline:none;border-color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),To=({formValues:e,onChange:n})=>{const a=()=>e.dolType&&ue[e.dolType]?ue[e.dolType]:"Describe the price action during the liquidity interaction.",i=()=>e.dolStrength&&ge[e.dolStrength]?ge[e.dolStrength]:"Describe the volume profile during the liquidity interaction.",s=c=>{n(c.target.name,c.target.value)};return t.jsxs(So,{children:[t.jsx(Co,{children:"Detailed Analysis"}),t.jsx(G,{children:"Provide detailed information about the liquidity interaction."}),t.jsxs(W,{children:[t.jsx(U,{htmlFor:"dolPriceAction",children:"Price Action"}),t.jsx(G,{children:a()}),t.jsx(V,{id:"dolPriceAction",name:"dolPriceAction",value:e.dolPriceAction||"",onChange:s,placeholder:"Describe the price action..."})]}),t.jsxs(W,{children:[t.jsx(U,{htmlFor:"dolVolumeProfile",children:"Volume Profile"}),t.jsx(G,{children:i()}),t.jsx(V,{id:"dolVolumeProfile",name:"dolVolumeProfile",value:e.dolVolumeProfile||"",onChange:s,placeholder:"Describe the volume profile..."})]}),t.jsxs(W,{children:[t.jsx(U,{htmlFor:"dolTimeOfDay",children:"Time of Day Significance"}),t.jsx(G,{children:zt}),t.jsx(V,{id:"dolTimeOfDay",name:"dolTimeOfDay",value:e.dolTimeOfDay||"",onChange:s,placeholder:"Describe the time of day significance..."})]}),t.jsxs(W,{children:[t.jsx(U,{htmlFor:"dolMarketStructure",children:"Market Structure"}),t.jsx(G,{children:Dt}),t.jsx(V,{id:"dolMarketStructure",name:"dolMarketStructure",value:e.dolMarketStructure||"",onChange:s,placeholder:"Describe the market structure..."})]})]})},wo=o.div.withConfig({displayName:"RatingContainer",componentId:"sc-11k2gk1-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),jo=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-11k2gk1-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),Po=o.p.withConfig({displayName:"Description",componentId:"sc-11k2gk1-2"})(["font-size:",";color:",";margin:"," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),ko=o.div.withConfig({displayName:"RatingSliderContainer",componentId:"sc-11k2gk1-3"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md),No=o.div.withConfig({displayName:"SliderContainer",componentId:"sc-11k2gk1-4"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>e.spacing.md),Io=o.input.withConfig({displayName:"Slider",componentId:"sc-11k2gk1-5"})(["flex:1;height:8px;-webkit-appearance:none;appearance:none;background:",";outline:none;border-radius:4px;&::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;width:20px;height:20px;border-radius:50%;background:",";cursor:pointer;}&::-moz-range-thumb{width:20px;height:20px;border-radius:50%;background:",";cursor:pointer;}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),Fo=o.div.withConfig({displayName:"RatingValue",componentId:"sc-11k2gk1-6"})(["font-size:2rem;font-weight:700;color:",";display:flex;align-items:center;justify-content:center;width:60px;height:60px;border-radius:50%;border:3px solid ",";"],({color:e})=>e,({color:e})=>e),Ro=o.div.withConfig({displayName:"RatingDescription",componentId:"sc-11k2gk1-7"})(["font-size:",";font-weight:600;color:",";margin-top:",";text-align:center;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm),zo=o.div.withConfig({displayName:"NotesContainer",componentId:"sc-11k2gk1-8"})(["margin-top:",";"],({theme:e})=>e.spacing.lg),Do=o.label.withConfig({displayName:"NotesLabel",componentId:"sc-11k2gk1-9"})(["font-size:",";font-weight:500;color:",";display:block;margin-bottom:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),Lo=o.textarea.withConfig({displayName:"NotesTextarea",componentId:"sc-11k2gk1-10"})(["width:100%;min-height:100px;padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";resize:vertical;&:focus{outline:none;border-color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),Oo=({value:e,notes:n,onChange:a})=>{const i=l=>{a("dolEffectiveness",l.target.value)},s=l=>{a("dolNotes",l.target.value)},c=e?parseInt(e):5,r=Lt(c),p=Ot(c);return t.jsxs(wo,{children:[t.jsx(jo,{children:"DOL Effectiveness Rating"}),t.jsx(Po,{children:"Rate how effectively price interacted with the liquidity level and how well this interaction aligned with your trade thesis."}),t.jsxs(ko,{children:[t.jsxs(No,{children:[t.jsx(Io,{type:"range",min:"1",max:"10",value:e||"5",onChange:i}),t.jsx(Fo,{color:r,children:c})]}),t.jsx(Ro,{children:p})]}),t.jsxs(zo,{children:[t.jsx(Do,{htmlFor:"dolNotes",children:"Additional Notes"}),t.jsx(Lo,{id:"dolNotes",name:"dolNotes",value:n,onChange:s,placeholder:"Add any additional notes about the DOL analysis..."})]})]})},Go=o.div.withConfig({displayName:"AnalysisContainer",componentId:"sc-tlgq5g-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.lg),$o=o.div.withConfig({displayName:"Introduction",componentId:"sc-tlgq5g-1"})(["margin-bottom:",";"],({theme:e})=>e.spacing.lg),Ao=o.h3.withConfig({displayName:"IntroTitle",componentId:"sc-tlgq5g-2"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm),_o=o.p.withConfig({displayName:"IntroText",componentId:"sc-tlgq5g-3"})(["font-size:",";color:",";margin:0;line-height:1.5;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textSecondary),z=o.hr.withConfig({displayName:"Divider",componentId:"sc-tlgq5g-4"})(["border:none;border-top:1px solid ",";margin:"," 0;"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),qo=o.div.withConfig({displayName:"ValidationError",componentId:"sc-tlgq5g-5"})(["color:",";font-size:",";padding:",";background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.error,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.errorLight,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.md),Eo=({formValues:e,onChange:n,validationErrors:a})=>t.jsxs(Go,{children:[t.jsxs($o,{children:[t.jsx(Ao,{children:"Draw on Liquidity (DOL) Analysis"}),t.jsx(_o,{children:"Analyze how price interacted with liquidity levels in this trade. This analysis will help you understand market behavior around key levels and improve your ability to anticipate price movements."})]}),a.dolAnalysis&&t.jsx(qo,{children:a.dolAnalysis}),t.jsx(z,{}),t.jsx(Ht,{value:e.dolType||"",onChange:n}),t.jsx(z,{}),t.jsx(to,{value:e.dolStrength||"",onChange:n}),t.jsx(z,{}),t.jsx(mo,{value:e.dolReaction||"",onChange:n}),t.jsx(z,{}),t.jsx(vo,{value:e.dolContext||[],onChange:n}),t.jsx(z,{}),t.jsx(To,{formValues:e,onChange:n}),t.jsx(z,{}),t.jsx(Oo,{value:e.dolEffectiveness||"5",notes:e.dolNotes||"",onChange:n})]}),Mo=o.div.withConfig({displayName:"PageHeader",componentId:"sc-1xbf9rq-0"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";"],({theme:e})=>e.spacing.lg),Qo=o.h1.withConfig({displayName:"Title",componentId:"sc-1xbf9rq-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.xxl,({theme:e})=>e.colors.textPrimary),Bo=({isNewTrade:e,formValues:n})=>t.jsx(Mo,{children:t.jsx(Qo,{children:e?"Add New Trade":`Edit Trade: ${n.symbol} (${n.date})`})}),Y=o.div.withConfig({displayName:"FormRow",componentId:"sc-1lc065c-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),w=o.div.withConfig({displayName:"FormGroup",componentId:"sc-1lc065c-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),j=o.label.withConfig({displayName:"Label",componentId:"sc-1lc065c-2"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),D=o.input.withConfig({displayName:"Input",componentId:"sc-1lc065c-3"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),ye=o.select.withConfig({displayName:"Select",componentId:"sc-1lc065c-4"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),$=o.span.withConfig({displayName:"ValidationError",componentId:"sc-1lc065c-5"})(["color:",";font-size:",";margin-top:2px;"],({theme:e})=>e.colors.error,({theme:e})=>e.fontSizes.sm),Ho=({formValues:e,handleChange:n,validationErrors:a,calculateProfitLoss:i})=>{const s=c=>{n(c),i&&setTimeout(i,0)};return t.jsxs(t.Fragment,{children:[t.jsxs(Y,{children:[t.jsxs(w,{children:[t.jsx(j,{htmlFor:"date",children:"Date"}),t.jsx(D,{id:"date",name:"date",type:"date",value:e.date,onChange:n,required:!0}),a.date&&t.jsx($,{children:a.date})]}),t.jsxs(w,{children:[t.jsx(j,{htmlFor:"symbol",children:"Symbol"}),t.jsx(D,{id:"symbol",name:"symbol",type:"text",value:e.symbol,onChange:n,required:!0}),a.symbol&&t.jsx($,{children:a.symbol})]})]}),t.jsxs(Y,{children:[t.jsxs(w,{children:[t.jsx(j,{htmlFor:"direction",children:"Direction"}),t.jsxs(ye,{id:"direction",name:"direction",value:e.direction,onChange:n,required:!0,children:[t.jsx("option",{value:"long",children:"Long"}),t.jsx("option",{value:"short",children:"Short"})]})]}),t.jsxs(w,{children:[t.jsx(j,{htmlFor:"result",children:"Result"}),t.jsxs(ye,{id:"result",name:"result",value:e.result,onChange:n,required:!0,children:[t.jsx("option",{value:"win",children:"Win"}),t.jsx("option",{value:"loss",children:"Loss"}),t.jsx("option",{value:"breakeven",children:"Breakeven"})]})]})]}),t.jsxs(Y,{children:[t.jsxs(w,{children:[t.jsx(j,{htmlFor:"entryPrice",children:"Entry Price"}),t.jsx(D,{id:"entryPrice",name:"entryPrice",type:"number",step:"0.01",value:e.entryPrice,onChange:s,required:!0}),a.entryPrice&&t.jsx($,{children:a.entryPrice})]}),t.jsxs(w,{children:[t.jsx(j,{htmlFor:"exitPrice",children:"Exit Price"}),t.jsx(D,{id:"exitPrice",name:"exitPrice",type:"number",step:"0.01",value:e.exitPrice,onChange:s,required:!0}),a.exitPrice&&t.jsx($,{children:a.exitPrice})]})]}),t.jsxs(Y,{children:[t.jsxs(w,{children:[t.jsx(j,{htmlFor:"quantity",children:"Quantity"}),t.jsx(D,{id:"quantity",name:"quantity",type:"number",value:e.quantity,onChange:s,required:!0}),a.quantity&&t.jsx($,{children:a.quantity})]}),t.jsxs(w,{children:[t.jsx(j,{htmlFor:"profit",children:"Profit/Loss ($)"}),t.jsx(D,{id:"profit",name:"profit",type:"number",step:"0.01",value:e.profit,onChange:n,required:!0})]})]})]})},Wo=o.div.withConfig({displayName:"TimePickerContainer",componentId:"sc-l3zp6j-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Uo=o.label.withConfig({displayName:"Label",componentId:"sc-l3zp6j-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Vo=o.input.withConfig({displayName:"TimeInput",componentId:"sc-l3zp6j-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),te=({id:e,name:n,value:a,onChange:i,label:s,required:c=!1,disabled:r=!1,className:p,placeholder:l="HH:MM",min:u,max:m})=>t.jsxs(Wo,{className:p,children:[s&&t.jsxs(Uo,{htmlFor:e,children:[s,c&&t.jsx("span",{style:{color:"red"},children:" *"})]}),t.jsx(Vo,{id:e,name:n,type:"time",value:a,onChange:i,required:c,disabled:r,placeholder:l,min:u,max:m})]}),Yo=o.div.withConfig({displayName:"SelectContainer",componentId:"sc-ytpv9p-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Ko=o.label.withConfig({displayName:"Label",componentId:"sc-ytpv9p-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Xo=o.select.withConfig({displayName:"Select",componentId:"sc-ytpv9p-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),L=({id:e,name:n,value:a,onChange:i,options:s,label:c,required:r=!1,disabled:p=!1,className:l,placeholder:u})=>t.jsxs(Yo,{className:l,children:[c&&t.jsxs(Ko,{htmlFor:e,children:[c,r&&t.jsx("span",{style:{color:"red"},children:" *"})]}),t.jsxs(Xo,{id:e,name:n,value:a,onChange:i,required:r,disabled:p,children:[u&&t.jsx("option",{value:"",disabled:!0,children:u}),s.map(m=>t.jsx("option",{value:m.value,children:m.label},m.value))]})]}),he=o.div.withConfig({displayName:"FormRow",componentId:"sc-2wteiz-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),A=o.div.withConfig({displayName:"FormGroup",componentId:"sc-2wteiz-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),oe=o.span.withConfig({displayName:"ValidationError",componentId:"sc-2wteiz-2"})(["color:",";font-size:",";margin-top:2px;"],({theme:e})=>e.colors.error,({theme:e})=>e.fontSizes.sm),Jo=({formValues:e,handleChange:n,validationErrors:a})=>t.jsxs(t.Fragment,{children:[t.jsxs(he,{children:[t.jsxs(A,{children:[t.jsx(te,{id:"rdTime",name:"rdTime",label:"Risk/Decision Time",value:e.rdTime||"",onChange:n}),a.rdTime&&t.jsx(oe,{children:a.rdTime})]}),t.jsxs(A,{children:[t.jsx(te,{id:"entryTime",name:"entryTime",label:"Entry Time",value:e.entryTime||"",onChange:n}),a.entryTime&&t.jsx(oe,{children:a.entryTime})]}),t.jsxs(A,{children:[t.jsx(te,{id:"exitTime",name:"exitTime",label:"Exit Time",value:e.exitTime||"",onChange:n}),a.exitTime&&t.jsx(oe,{children:a.exitTime})]})]}),t.jsxs(he,{children:[t.jsx(A,{children:t.jsx(L,{id:"session",name:"session",label:"Session (Time Block)",value:e.session||"",onChange:n,options:He,placeholder:"Select Session"})}),t.jsx(A,{children:t.jsx(L,{id:"market",name:"market",label:"Market",value:e.market||"Stocks",onChange:n,options:Ue})})]})]}),xe=o.div.withConfig({displayName:"FormRow",componentId:"sc-v0a7t0-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),K=o.div.withConfig({displayName:"FormGroup",componentId:"sc-v0a7t0-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),X=o.label.withConfig({displayName:"Label",componentId:"sc-v0a7t0-2"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),J=o.input.withConfig({displayName:"Input",componentId:"sc-v0a7t0-3"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),Zo=o.span.withConfig({displayName:"HelpText",componentId:"sc-v0a7t0-4"})(["font-size:0.8rem;color:",";"],({theme:e})=>e.colors.textSecondary),ei=({formValues:e,handleChange:n,validationErrors:a})=>t.jsxs(t.Fragment,{children:[t.jsxs(xe,{children:[t.jsxs(K,{children:[t.jsx(X,{htmlFor:"stopLoss",children:"Stop Loss"}),t.jsx(J,{id:"stopLoss",name:"stopLoss",type:"number",step:"0.01",value:e.stopLoss||"",onChange:n})]}),t.jsxs(K,{children:[t.jsx(X,{htmlFor:"takeProfit",children:"Take Profit"}),t.jsx(J,{id:"takeProfit",name:"takeProfit",type:"number",step:"0.01",value:e.takeProfit||"",onChange:n})]})]}),t.jsxs(xe,{children:[t.jsxs(K,{children:[t.jsx(X,{htmlFor:"riskPoints",children:"Risk (Points)"}),t.jsx(J,{id:"riskPoints",name:"riskPoints",type:"number",step:"0.01",value:e.riskPoints||"",onChange:n})]}),t.jsxs(K,{children:[t.jsx(X,{htmlFor:"rMultiple",children:"R-Multiple"}),t.jsx(J,{id:"rMultiple",name:"rMultiple",type:"number",step:"0.01",value:e.rMultiple||"",onChange:n,disabled:!0}),t.jsx(Zo,{children:"Auto-calculated from Risk Points and P/L"})]})]})]}),Ie=[{value:"structure",label:"Structure-Based Setup"},{value:"session",label:"Session-Based Setup"},{value:"model",label:"Model-Specific Setup"}],ti=[{value:"High/Low Reversal Setup",label:"High/Low Reversal Setup"},{value:"FVG Redelivery Setup",label:"FVG Redelivery Setup"},{value:"Strong-FVG Reversal Setup",label:"Strong-FVG Reversal Setup"},{value:"NWOG Reaction Setup",label:"NWOG Reaction Setup"},{value:"NDOG Reaction Setup",label:"NDOG Reaction Setup"},{value:"Multi-Array Confluence Setup",label:"Multi-Array Confluence Setup"}],oi=[{value:"Opening Range Setup (09:30-10:10)",label:"Opening Range Setup (09:30-10:10)"},{value:"Morning Session Setup (10:50-11:10)",label:"Morning Session Setup (10:50-11:10)"},{value:"Lunch Macro Setup (11:45-13:15)",label:"Lunch Macro Setup (11:45-13:15)"},{value:"Afternoon Session Setup (13:30-15:00)",label:"Afternoon Session Setup (13:30-15:00)"},{value:"MOC Setup (15:30-16:15)",label:"MOC Setup (15:30-16:15)"}],ii=[{value:"Simple FVG-RD",label:"Simple FVG-RD"},{value:"Complex FVG-RD",label:"Complex FVG-RD"},{value:"True-RD Continuation",label:"True-RD Continuation"},{value:"IMM-RD Continuation",label:"IMM-RD Continuation"},{value:"Dispersed-RD Continuation",label:"Dispersed-RD Continuation"},{value:"Wide-Gap-RD Continuation",label:"Wide-Gap-RD Continuation"}],Fe=[{value:"",label:"None/Not Applicable"},{value:"London-H/L",label:"London-H/L"},{value:"Premarket-H/L",label:"Premarket-H/L"},{value:"09:30-Opening-Range-H/L",label:"09:30-Opening-Range-H/L"},{value:"Post-10:00am-Lunch-Macro-H/L",label:"Post-10:00am-Lunch-Macro-H/L"},{value:"Lunch-H/L",label:"Lunch-H/L"},{value:"Monthly-H/L",label:"Monthly-H/L"},{value:"Prev-Week-H/L",label:"Prev-Week-H/L"},{value:"Prev-Day-H/L",label:"Prev-Day-H/L"},{value:"Macro-H/L",label:"Macro-H/L"}],Re=[{value:"Monthly-FVG",label:"Monthly-FVG"},{value:"Weekly-FVG",label:"Weekly-FVG"},{value:"Daily-FVG",label:"Daily-FVG"},{value:"Daily-Top/Bottom-FVG",label:"Daily-Top/Bottom-FVG"},{value:"1h-Top/Bottom-FVG",label:"1h-Top/Bottom-FVG"},{value:"15min-Top/Bottom-FVG",label:"15min-Top/Bottom-FVG"}],ze=[{value:"MNOR-FVG",label:"MNOR-FVG"},{value:"Asia-FPFVG",label:"Asia-FPFVG"},{value:"Premarket-FPFVG",label:"Premarket-FPFVG"},{value:"AM-FPFVG",label:"AM-FPFVG"},{value:"PM-FPFVG",label:"PM-FPFVG"}],De=[{value:"Prev-Day-MNOR-FVG",label:"Prev-Day-MNOR-FVG"},{value:"Prev-Day-Asia-FPFVG",label:"Prev-Day-Asia-FPFVG"},{value:"Prev-Day-Premarket-FPFVG",label:"Prev-Day-Premarket-FPFVG"},{value:"Prev-Day-AM-FPFVG",label:"Prev-Day-AM-FPFVG"},{value:"Prev-Day-PM-FPFVG",label:"Prev-Day-PM-FPFVG"}],Le=[{value:"3Day-MNOR-FVG",label:"3Day-MNOR-FVG"},{value:"3Day-Asia-FPFVG",label:"3Day-Asia-FPFVG"},{value:"3Day-Premarket-FPFVG",label:"3Day-Premarket-FPFVG"},{value:"3Day-AM-FPFVG",label:"3Day-AM-FPFVG"},{value:"3Day-PM-FPFVG",label:"3Day-PM-FPFVG"}],Oe=[{value:"Top/Bottom-FVG",label:"Top/Bottom-FVG"},{value:"Macro-FVG",label:"Macro-FVG"},{value:"News-FVG",label:"News-FVG"},{value:"10min-Prior-To-News-FVG",label:"10min-Prior-To-News-FVG"},{value:"Strong-FVG",label:"Strong-FVG"},{value:"RDRB-FVG",label:"RDRB-FVG"}],ni=[...Re,...ze,...De,...Le,...Oe],ri=[{value:"",label:"None/Not Applicable"},{value:"FVG Target",label:"FVG Target"},{value:"Liquidity Target",label:"Liquidity Target"},{value:"RD Target",label:"RD Target"}],ai=[{value:"",label:"None/Not Applicable"},{value:"NWOG",label:"NWOG"},{value:"Old-NWOG",label:"Old-NWOG"},{value:"NDOG",label:"NDOG"},{value:"Old-NDOG",label:"Old-NDOG"},{value:"Monthly-FVG",label:"Monthly-FVG"},{value:"Weekly-FVG",label:"Weekly-FVG"},{value:"Daily-FVG",label:"Daily-FVG"},{value:"Daily-Top/Bottom-FVG",label:"Daily-Top/Bottom-FVG"},{value:"1h-Top/Bottom-FVG",label:"1h-Top/Bottom-FVG"},{value:"15min-Top/Bottom-FVG",label:"15min-Top/Bottom-FVG"}],Ge=e=>{switch(e){case"structure":return ti;case"session":return oi;case"model":return ii;default:return[]}},si=e=>{switch(e){case"FVG Target":return ni;case"Liquidity Target":return Fe;default:return[]}},li=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-j62xb2-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),ci=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-j62xb2-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),di=o.div.withConfig({displayName:"GuidanceNote",componentId:"sc-j62xb2-2"})(["background-color:",";border-left:4px solid ",";padding:",";margin-bottom:",";font-style:italic;font-size:",";color:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),pi=o.div.withConfig({displayName:"FormGroup",componentId:"sc-j62xb2-3"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),mi=o.label.withConfig({displayName:"Label",componentId:"sc-j62xb2-4"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),ui=o.select.withConfig({displayName:"Select",componentId:"sc-j62xb2-5"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),gi=o.div.withConfig({displayName:"RadioGroup",componentId:"sc-j62xb2-6"})(["display:flex;flex-direction:column;gap:",";margin-top:",";padding-left:",";border-left:3px solid ",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),yi=o.div.withConfig({displayName:"RadioOption",componentId:"sc-j62xb2-7"})(["display:flex;align-items:center;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),hi=o.input.withConfig({displayName:"RadioInput",componentId:"sc-j62xb2-8"})(["margin-right:",";"],({theme:e})=>e.spacing.sm),xi=o.label.withConfig({displayName:"RadioLabel",componentId:"sc-j62xb2-9"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),fi=({value:e,onChange:n})=>{const[a,i]=g.useState([]);g.useEffect(()=>{e.category?i(Ge(e.category)):i([])},[e.category]);const s=r=>{const p=r.target.value;n("primarySetupCategory",p),n("primarySetupType","")},c=r=>{n("primarySetupType",r.target.value)};return t.jsxs(li,{children:[t.jsx(ci,{children:"Primary Setup Classification"}),t.jsx(di,{children:"This is the main element that drove your entry decision. Think about the most critical factor that made you enter the trade."}),t.jsxs(pi,{children:[t.jsx(mi,{htmlFor:"primarySetupCategory",children:"Select Primary Category:"}),t.jsxs(ui,{id:"primarySetupCategory",name:"primarySetupCategory",value:e.category||"",onChange:s,children:[t.jsx("option",{value:"",children:"-- Select Primary Category --"}),Ie.map(r=>t.jsx("option",{value:r.value,children:r.label},r.value))]})]}),e.category&&a.length>0&&t.jsx(gi,{children:a.map(r=>t.jsxs(yi,{children:[t.jsx(hi,{type:"radio",id:`primary_${r.value}`,name:"primarySetupType",value:r.value,checked:e.type===r.value,onChange:c}),t.jsx(xi,{htmlFor:`primary_${r.value}`,children:r.label})]},r.value))})]})},bi=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1p1gtzj-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),vi=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1p1gtzj-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),Si=o.div.withConfig({displayName:"GuidanceNote",componentId:"sc-1p1gtzj-2"})(["background-color:",";border-left:4px solid ",";padding:",";margin-bottom:",";font-style:italic;font-size:",";color:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Ci=o.div.withConfig({displayName:"FormGroup",componentId:"sc-1p1gtzj-3"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Ti=o.label.withConfig({displayName:"Label",componentId:"sc-1p1gtzj-4"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),wi=o.select.withConfig({displayName:"Select",componentId:"sc-1p1gtzj-5"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),ji=o.div.withConfig({displayName:"RadioGroup",componentId:"sc-1p1gtzj-6"})(["display:flex;flex-direction:column;gap:",";margin-top:",";padding-left:",";border-left:3px solid ",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),Pi=o.div.withConfig({displayName:"RadioOption",componentId:"sc-1p1gtzj-7"})(["display:flex;align-items:center;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),ki=o.input.withConfig({displayName:"RadioInput",componentId:"sc-1p1gtzj-8"})(["margin-right:",";"],({theme:e})=>e.spacing.sm),Ni=o.label.withConfig({displayName:"RadioLabel",componentId:"sc-1p1gtzj-9"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),Ii=o.span.withConfig({displayName:"ValidationError",componentId:"sc-1p1gtzj-10"})(["color:",";font-size:",";margin-top:2px;"],({theme:e})=>e.colors.error,({theme:e})=>e.fontSizes.sm),Fi=({value:e,primarySetup:n,onChange:a,error:i})=>{const[s,c]=g.useState([]);g.useEffect(()=>{e.category?c(Ge(e.category)):c([])},[e.category]);const r=l=>{const u=l.target.value;a("secondarySetupCategory",u),a("secondarySetupType","")},p=l=>{a("secondarySetupType",l.target.value)};return t.jsxs(bi,{children:[t.jsx(vi,{children:"Secondary Setup Classification"}),t.jsx(Si,{children:"This is the supporting element that added context or confirmation to your entry decision. It should be different from your Primary Setup Type."}),t.jsxs(Ci,{children:[t.jsx(Ti,{htmlFor:"secondarySetupCategory",children:"Select Secondary Category:"}),t.jsxs(wi,{id:"secondarySetupCategory",name:"secondarySetupCategory",value:e.category||"",onChange:r,children:[t.jsx("option",{value:"",children:"-- Select Secondary Category --"}),Ie.map(l=>t.jsx("option",{value:l.value,children:l.label},l.value))]})]}),e.category&&s.length>0&&t.jsx(ji,{children:s.map(l=>t.jsxs(Pi,{children:[t.jsx(ki,{type:"radio",id:`secondary_${l.value}`,name:"secondarySetupType",value:l.value,checked:e.type===l.value,onChange:p}),t.jsx(Ni,{htmlFor:`secondary_${l.value}`,children:l.label})]},l.value))}),i&&t.jsx(Ii,{children:i})]})},Ri=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1rq1jv5-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),zi=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1rq1jv5-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),Di=o.div.withConfig({displayName:"GuidanceNote",componentId:"sc-1rq1jv5-2"})(["background-color:",";border-left:4px solid ",";padding:",";margin-bottom:",";font-style:italic;font-size:",";color:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Li=o.div.withConfig({displayName:"FormGroup",componentId:"sc-1rq1jv5-3"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Oi=o.label.withConfig({displayName:"Label",componentId:"sc-1rq1jv5-4"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Gi=o.select.withConfig({displayName:"Select",componentId:"sc-1rq1jv5-5"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),$i=({value:e,onChange:n})=>{const a=i=>{n("liquidityTaken",i.target.value)};return t.jsxs(Ri,{children:[t.jsx(zi,{children:"Liquidity Taken"}),t.jsx(Di,{children:"If your trade took/swept a specific type of liquidity, select it below."}),t.jsxs(Li,{children:[t.jsx(Oi,{htmlFor:"liquidityTaken",children:"Liquidity Taken/Swept:"}),t.jsx(Gi,{id:"liquidityTaken",name:"liquidityTaken",value:e||"",onChange:a,children:Fe.map(i=>t.jsx("option",{value:i.value,children:i.label},i.value))})]})]})},Ai=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1l8xnc9-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),_i=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1l8xnc9-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),qi=o.div.withConfig({displayName:"GuidanceNote",componentId:"sc-1l8xnc9-2"})(["background-color:",";border-left:4px solid ",";padding:",";margin-bottom:",";font-style:italic;font-size:",";color:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Ei=o.div.withConfig({displayName:"CheckboxGroup",componentId:"sc-1l8xnc9-3"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.sm),_=o.div.withConfig({displayName:"CategoryGroup",componentId:"sc-1l8xnc9-4"})(["margin-bottom:",";"],({theme:e})=>e.spacing.md),q=o.h4.withConfig({displayName:"CategoryTitle",componentId:"sc-1l8xnc9-5"})(["font-size:",";font-weight:600;color:",";margin:"," 0;padding-bottom:",";border-bottom:1px solid ",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.border),E=o.div.withConfig({displayName:"CheckboxOption",componentId:"sc-1l8xnc9-6"})(["display:flex;align-items:center;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),M=o.input.withConfig({displayName:"CheckboxInput",componentId:"sc-1l8xnc9-7"})(["margin-right:",";"],({theme:e})=>e.spacing.sm),Q=o.label.withConfig({displayName:"CheckboxLabel",componentId:"sc-1l8xnc9-8"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),Mi=({value:e,onChange:n})=>{const a=i=>{const s=i.target.value,c=i.target.checked;let r;c?r=[...e,s]:r=e.filter(p=>p!==s),n("additionalFVGs",r)};return t.jsxs(Ai,{children:[t.jsx(_i,{children:"Additional FVGs Present"}),t.jsx(qi,{children:"Select all additional FVG types that were present in this trade setup."}),t.jsxs(Ei,{children:[t.jsxs(_,{children:[t.jsx(q,{children:"Time-frame FVGs:"}),Re.map(i=>t.jsxs(E,{children:[t.jsx(M,{type:"checkbox",id:`fvg_${i.value}`,name:"additionalFVGs",value:i.value,checked:e.includes(i.value),onChange:a}),t.jsx(Q,{htmlFor:`fvg_${i.value}`,children:i.label})]},i.value))]}),t.jsxs(_,{children:[t.jsx(q,{children:"Current Session FPFVGs:"}),ze.map(i=>t.jsxs(E,{children:[t.jsx(M,{type:"checkbox",id:`fvg_${i.value}`,name:"additionalFVGs",value:i.value,checked:e.includes(i.value),onChange:a}),t.jsx(Q,{htmlFor:`fvg_${i.value}`,children:i.label})]},i.value))]}),t.jsxs(_,{children:[t.jsx(q,{children:"Previous Day FPFVGs:"}),De.map(i=>t.jsxs(E,{children:[t.jsx(M,{type:"checkbox",id:`fvg_${i.value}`,name:"additionalFVGs",value:i.value,checked:e.includes(i.value),onChange:a}),t.jsx(Q,{htmlFor:`fvg_${i.value}`,children:i.label})]},i.value))]}),t.jsxs(_,{children:[t.jsx(q,{children:"3-Day FPFVGs:"}),Le.map(i=>t.jsxs(E,{children:[t.jsx(M,{type:"checkbox",id:`fvg_${i.value}`,name:"additionalFVGs",value:i.value,checked:e.includes(i.value),onChange:a}),t.jsx(Q,{htmlFor:`fvg_${i.value}`,children:i.label})]},i.value))]}),t.jsxs(_,{children:[t.jsx(q,{children:"Special FVG Types:"}),Oe.map(i=>t.jsxs(E,{children:[t.jsx(M,{type:"checkbox",id:`fvg_${i.value}`,name:"additionalFVGs",value:i.value,checked:e.includes(i.value),onChange:a}),t.jsx(Q,{htmlFor:`fvg_${i.value}`,children:i.label})]},i.value))]})]})]})},Qi=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-14zzywx-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),Bi=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-14zzywx-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),Hi=o.div.withConfig({displayName:"GuidanceNote",componentId:"sc-14zzywx-2"})(["background-color:",";border-left:4px solid ",";padding:",";margin-bottom:",";font-style:italic;font-size:",";color:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),fe=o.div.withConfig({displayName:"FormGroup",componentId:"sc-14zzywx-3"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),be=o.label.withConfig({displayName:"Label",componentId:"sc-14zzywx-4"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Wi=o.select.withConfig({displayName:"Select",componentId:"sc-14zzywx-5"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),Ui=o.div.withConfig({displayName:"RadioGroup",componentId:"sc-14zzywx-6"})(["display:flex;flex-direction:column;gap:",";margin-top:",";padding-left:",";border-left:3px solid ",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),Vi=o.div.withConfig({displayName:"RadioOption",componentId:"sc-14zzywx-7"})(["display:flex;align-items:center;padding:",";border-radius:",";transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),Yi=o.input.withConfig({displayName:"RadioInput",componentId:"sc-14zzywx-8"})(["margin-right:",";"],({theme:e})=>e.spacing.sm),Ki=o.label.withConfig({displayName:"RadioLabel",componentId:"sc-14zzywx-9"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textPrimary),Xi=({value:e,onChange:n})=>{const[a,i]=g.useState([]);g.useEffect(()=>{e.targetType&&e.targetType!=="RD Target"?i(si(e.targetType)):i([])},[e.targetType]);const s=r=>{const p=r.target.value;n("dolTargetType",p),n("specificDOLType","")},c=r=>{n("specificDOLType",r.target.value)};return t.jsxs(Qi,{children:[t.jsx(Bi,{children:"DOL Target Type"}),t.jsx(Hi,{children:"Select the primary target type for your Draw on Liquidity (DOL) analysis."}),t.jsxs(fe,{children:[t.jsx(be,{htmlFor:"dolTargetType",children:"DOL Target Type:"}),t.jsx(Wi,{id:"dolTargetType",name:"dolTargetType",value:e.targetType||"",onChange:s,children:ri.map(r=>t.jsx("option",{value:r.value,children:r.label},r.value))})]}),e.targetType&&e.targetType!=="RD Target"&&a.length>0&&t.jsx(t.Fragment,{children:t.jsxs(fe,{children:[t.jsx(be,{children:e.targetType==="FVG Target"?"FVG Target Type:":"Liquidity Target Type:"}),t.jsx(Ui,{children:a.map(r=>r.value&&t.jsxs(Vi,{children:[t.jsx(Yi,{type:"radio",id:`dolTarget_${r.value}`,name:"specificDOLType",value:r.value,checked:e.specificType===r.value,onChange:c}),t.jsx(Ki,{htmlFor:`dolTarget_${r.value}`,children:r.label})]},r.value))})]})})]})},Ji=o.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1xas9x5-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),Zi=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1xas9x5-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),en=o.div.withConfig({displayName:"GuidanceNote",componentId:"sc-1xas9x5-2"})(["background-color:",";border-left:4px solid ",";padding:",";margin-bottom:",";font-style:italic;font-size:",";color:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),tn=o.div.withConfig({displayName:"FormGroup",componentId:"sc-1xas9x5-3"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),on=o.label.withConfig({displayName:"Label",componentId:"sc-1xas9x5-4"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),nn=o.select.withConfig({displayName:"Select",componentId:"sc-1xas9x5-5"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),rn=({value:e,onChange:n})=>{const a=i=>{n("parentPDArray",i.target.value)};return t.jsxs(Ji,{children:[t.jsx(Zi,{children:"Parent PD Array"}),t.jsx(en,{children:"If your trade used a specific Parent PD Array, please select it below. This is often relevant for Structure-Based Setups."}),t.jsxs(tn,{children:[t.jsx(on,{htmlFor:"parentPDArray",children:"Parent PD Array:"}),t.jsx(nn,{id:"parentPDArray",name:"parentPDArray",value:e||"",onChange:a,children:ai.map(i=>t.jsx("option",{value:i.value,children:i.label},i.value))})]})]})},an=o.div.withConfig({displayName:"SectionContainer",componentId:"sc-1mt6p44-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.lg),B=o.hr.withConfig({displayName:"Divider",componentId:"sc-1mt6p44-1"})(["border:none;border-top:1px solid ",";margin:"," 0;"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),sn=o.div.withConfig({displayName:"ValidationError",componentId:"sc-1mt6p44-2"})(["color:",";font-size:",";padding:",";background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.error,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.errorLight,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.md),ln=({formValues:e,onChange:n,validationErrors:a})=>{const i=(s,c)=>{n(s,c)};return t.jsxs(an,{children:[a.setupClassification&&t.jsx(sn,{children:a.setupClassification}),t.jsx(fi,{value:{category:e.primarySetupCategory,type:e.primarySetupType},onChange:i}),t.jsx(B,{}),t.jsx(Fi,{value:{category:e.secondarySetupCategory,type:e.secondarySetupType},primarySetup:{category:e.primarySetupCategory,type:e.primarySetupType},onChange:i,error:a.secondarySetupType}),t.jsx(B,{}),t.jsx($i,{value:e.liquidityTaken||"",onChange:i}),t.jsx(B,{}),t.jsx(Mi,{value:e.additionalFVGs||[],onChange:i}),t.jsx(B,{}),t.jsx(Xi,{value:{targetType:e.dolTargetType,specificType:e.specificDOLType},onChange:i}),t.jsx(B,{}),t.jsx(rn,{value:e.parentPDArray||"",onChange:i})]})},ve=o.div.withConfig({displayName:"FormRow",componentId:"sc-1pczdz5-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),H=o.div.withConfig({displayName:"FormGroup",componentId:"sc-1pczdz5-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),cn=o.label.withConfig({displayName:"Label",componentId:"sc-1pczdz5-2"})(["font-size:",";font-weight:500;color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),dn=o.textarea.withConfig({displayName:"TextArea",componentId:"sc-1pczdz5-3"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";min-height:100px;&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),ie=o.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1pczdz5-4"})(["font-size:",";font-weight:600;color:",";margin:"," 0 "," 0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.sm),Se=o.hr.withConfig({displayName:"Divider",componentId:"sc-1pczdz5-5"})(["border:none;border-top:1px solid ",";margin:"," 0;"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),pn=({formValues:e,handleChange:n,validationErrors:a,setFormValues:i})=>t.jsxs(t.Fragment,{children:[t.jsx(ie,{children:"Basic Strategy"}),t.jsxs(ve,{children:[t.jsx(H,{children:t.jsx(L,{id:"modelType",name:"modelType",label:"Model Type",value:e.modelType||"",onChange:n,options:Be,placeholder:"Select Model Type"})}),t.jsx(H,{children:t.jsx(L,{id:"setup",name:"setup",label:"Setup",value:e.setup||"",onChange:n,options:We,placeholder:"Select Setup"})})]}),t.jsxs(ve,{children:[t.jsx(H,{children:t.jsx(L,{id:"entryVersion",name:"entryVersion",label:"Entry Version",value:e.entryVersion||"First Entry",onChange:n,options:Ve})}),t.jsx(H,{children:t.jsx(L,{id:"patternQuality",name:"patternQuality",label:"Pattern Quality (1-10)",value:e.patternQuality||"5",onChange:n,options:Ye})})]}),t.jsx(Se,{}),t.jsx(ie,{children:"Setup Classification"}),t.jsx(ln,{formValues:e,onChange:(s,c)=>{i(r=>({...r,[s]:c}))},validationErrors:a}),t.jsx(Se,{}),t.jsx(ie,{children:"Notes"}),t.jsxs(H,{children:[t.jsx(cn,{htmlFor:"notes",children:"Trade Notes"}),t.jsx(dn,{id:"notes",name:"notes",value:e.notes,onChange:n})]})]}),mn=o.div.withConfig({displayName:"ButtonGroup",componentId:"sc-jg13k5-0"})(["display:flex;justify-content:flex-end;gap:",";margin-top:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),$e=o.button.withConfig({displayName:"Button",componentId:"sc-jg13k5-1"})(["padding:"," ",";border-radius:",";font-weight:500;cursor:pointer;transition:background-color ",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.lg,({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast),un=o($e).withConfig({displayName:"CancelButton",componentId:"sc-jg13k5-2"})(["background-color:transparent;border:1px solid ",";color:",";&:hover{background-color:",";}"],({theme:e})=>e.colors.border,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.colors.background),gn=o($e).withConfig({displayName:"SubmitButton",componentId:"sc-jg13k5-3"})(["background-color:",";border:none;color:white;&:hover{background-color:",";}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primaryDark),yn=({isSubmitting:e,isLoading:n,isNewTrade:a})=>{const i=re();return t.jsxs(mn,{children:[t.jsx(un,{type:"button",onClick:()=>{console.log("Cancel button clicked, navigating to journal"),i("/journal")},children:"Cancel"}),t.jsx(gn,{type:"submit",disabled:e||n,"data-testid":a?"add-trade-button":"update-trade-button",children:e?"Saving...":a?"Add Trade":"Update Trade"})]})},hn=o.div.withConfig({displayName:"ErrorMessage",componentId:"sc-1wu72ll-0"})(["color:",";font-size:",";margin-top:",";padding:",";background-color:",";border-radius:",";"],({theme:e})=>e.colors.error,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.errorLight||"rgba(244, 67, 54, 0.1)",({theme:e})=>e.borderRadius.sm),xn=o.div.withConfig({displayName:"SuccessMessage",componentId:"sc-1wu72ll-1"})(["color:",";font-size:",";margin-top:",";padding:",";background-color:",";border-radius:",";"],({theme:e})=>e.colors.success,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.successLight||"rgba(76, 175, 80, 0.1)",({theme:e})=>e.borderRadius.sm),fn=o.div.withConfig({displayName:"TabInfo",componentId:"sc-1wu72ll-2"})(["color:",";font-size:",";margin-top:",";margin-bottom:",";font-style:italic;"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.md),Ce=({error:e,success:n,showTabInfo:a=!0})=>t.jsxs(t.Fragment,{children:[e&&t.jsx(hn,{children:e}),n&&t.jsx(xn,{children:n}),a&&t.jsx(fn,{children:"* Required tab for saving trade"})]}),bn=o.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-1stztlo-0"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(255,255,255,0.7);display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:10;border-radius:",";"],({theme:e})=>e.borderRadius.md),vn=o.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1stztlo-1"})(["border:4px solid rgba(0,0,0,0.1);border-radius:50%;border-top:4px solid ",";width:40px;height:40px;animation:spin 1s linear infinite;margin-bottom:",";@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.md),Sn=o.div.withConfig({displayName:"LoadingText",componentId:"sc-1stztlo-2"})(["font-size:",";color:",";font-weight:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.medium),Cn=({isLoading:e})=>e?t.jsxs(bn,{children:[t.jsx(vn,{}),t.jsx(Sn,{children:"Loading trade data..."})]}):null,Tn=o.div.withConfig({displayName:"PageContainer",componentId:"sc-fe0cln-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.lg),wn=o.div.withConfig({displayName:"ContentSection",componentId:"sc-fe0cln-1"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";position:relative;"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.shadows.sm),jn=o.form.withConfig({displayName:"Form",componentId:"sc-fe0cln-2"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),zn=()=>{const{id:e}=_e();console.log(`TradeForm component mounted with ID parameter: "${e}"`),console.log(`Current URL: ${window.location.href}`),console.log(`Is edit mode: ${e&&e!=="new"}`),console.log(`Is new trade: ${e==="new"}`);const{formValues:n,setFormValues:a,handleChange:i,handleSubmit:s,isSubmitting:c,isLoading:r,error:p,success:l,validationErrors:u,isNewTrade:m,activeTab:y,handleTabChange:v,calculateProfitLoss:P}=Ke(e);return t.jsxs(Tn,{children:[t.jsx(Bo,{isNewTrade:m,formValues:n}),t.jsxs(wn,{children:[t.jsx(Cn,{isLoading:r}),t.jsxs(jn,{onSubmit:s,children:[t.jsx(Ce,{error:p,success:l}),t.jsx(tt,{tabs:[{id:"basic",label:"Basic Info*",content:t.jsx(Ho,{formValues:n,handleChange:i,validationErrors:u,calculateProfitLoss:P})},{id:"timing",label:"Timing",content:t.jsx(Jo,{formValues:n,handleChange:i,validationErrors:u})},{id:"risk",label:"Risk Management",content:t.jsx(ei,{formValues:n,handleChange:i,validationErrors:u})},{id:"strategy",label:"Strategy",content:t.jsx(pn,{formValues:n,handleChange:i,validationErrors:u,setFormValues:a})},{id:"pattern-quality",label:"Pattern Quality",content:t.jsx(kt,{formValues:n,onChange:(S,h)=>{a(f=>({...f,[S]:h}))}})},{id:"dol-analysis",label:"DOL Analysis",content:t.jsx(Eo,{formValues:n,onChange:(S,h)=>{a(f=>({...f,[S]:h}))},validationErrors:u})}],defaultTab:"basic",activeTab:y,onTabClick:v}),t.jsx(Ce,{error:null,success:null,showTabInfo:!0}),t.jsx(yn,{isSubmitting:c,isLoading:r,isNewTrade:m})]})]})]})};export{zn as default};
//# sourceMappingURL=TradeForm-7631201a.js.map
