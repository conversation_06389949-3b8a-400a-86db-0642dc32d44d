const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Create the public directory if it doesn't exist
const publicDir = path.join(__dirname, '..');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Function to create a simple F1-themed logo
function createLogo(size) {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Background
  ctx.fillStyle = '#1a1f2c';
  ctx.fillRect(0, 0, size, size);

  // F1 red accent
  ctx.fillStyle = '#e10600';
  ctx.fillRect(0, size * 0.7, size, size * 0.3);

  // Text
  const fontSize = size * 0.4;
  ctx.font = `bold ${fontSize}px Arial`;
  ctx.fillStyle = 'white';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('ADHD', size / 2, size * 0.35);
  
  const smallerFontSize = size * 0.2;
  ctx.font = `bold ${smallerFontSize}px Arial`;
  ctx.fillText('TRADING', size / 2, size * 0.55);

  return canvas.toBuffer('image/png');
}

// Create favicon (16x16)
const faviconCanvas = createCanvas(16, 16);
const faviconCtx = faviconCanvas.getContext('2d');

// Background
faviconCtx.fillStyle = '#1a1f2c';
faviconCtx.fillRect(0, 0, 16, 16);

// F1 red accent
faviconCtx.fillStyle = '#e10600';
faviconCtx.fillRect(0, 10, 16, 6);

// Text
faviconCtx.font = 'bold 10px Arial';
faviconCtx.fillStyle = 'white';
faviconCtx.textAlign = 'center';
faviconCtx.textBaseline = 'middle';
faviconCtx.fillText('A', 8, 6);

// Save favicon as .ico file
const faviconBuffer = faviconCanvas.toBuffer('image/png');
fs.writeFileSync(path.join(publicDir, 'favicon.ico'), faviconBuffer);
console.log('Created favicon.ico');

// Create logo192.png
const logo192Buffer = createLogo(192);
fs.writeFileSync(path.join(publicDir, 'logo192.png'), logo192Buffer);
console.log('Created logo192.png');

// Create logo512.png
const logo512Buffer = createLogo(512);
fs.writeFileSync(path.join(publicDir, 'logo512.png'), logo512Buffer);
console.log('Created logo512.png');

console.log('All assets created successfully!');
