{"version": 3, "mappings": "oPAwCA,MAAMA,EAAwBC,MAAGC,mEAEpBC,4FAAWA,KAAMC,WAAa,IAAM,YAEfD,EAAMC,WAAa,UAAY,UAE5DD,GACDA,EAAMC,YACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASD,EAGGC,EAAmBJ,MAAGC,8DAM3B,mHAEKI,EAAoBC,KAAEL,gIAEZC,GAAWA,EAAMC,WAAa,SAAW,UAExCD,GAAWA,EAAMC,WAAa,SAAW,MAAO,EAG3DI,EAAsBC,IAACP,iEAEZC,yCAAWA,KAAMC,WAAa,SAAW,MAAO,EAG3DM,EAAsBC,UAAOT,iEASlC,sGAEKU,EAAoBC,MAAGX,+DAO5B,gIAEKY,EAAyBb,MAAGC,oEAIjC,2DAEKa,EAAqBC,SAAMd,gEAahC,uMAEKe,GAAoBD,SAAMd,+DAc/B,4OAEKgB,GAAeC,EAAOJ,CAAW,EAACb,iEAGvC,mCAKKkB,GAAkBA,CAAC,CACvBC,QACAC,aACAlB,aACAmB,OACAC,QAOF,IAAM,CACJ,MAAMC,EAAeA,IAAM,CACzBC,OAAOC,SAASC,QAAO,EAGzB,OAAIxB,EAECyB,SAAe,WAAU,GACxB,gBAACxB,EACC,WAACwB,SAAW,WAAU,GAAC,SAAoB,yBAC1CA,SAAa,WAAU,YAExB,oGACCnB,EACC,iBAAC,WAAQ,SAAiB,sBAC1BmB,MAACrB,EAAca,YAAMS,OAAQ,GAC5BT,EAAMU,OAAUF,SAAYR,WAAMU,MAAM,GAC3C,EACCF,UAAa,QAASJ,EAAc,SAAkB,uBACzD,EACF,UAKDzB,EACC,iBAACM,EAAYiB,YAAO,YAAYA,IAAS,uBAAuB,EAChEM,MAACrB,GACEe,SACGA,4CAA0CA,uBACxCC,EAAS,wBAA0B,MAErC,iDACN,UACCd,EACC,iBAAC,WAAQ,SAAiB,sBAC1BmB,MAACrB,EAAca,YAAMS,OAAQ,GAC5BT,EAAMU,OAAUF,SAAYR,WAAMU,MAAM,GAC3C,SACCjB,EACC,WAACe,SAAY,QAASP,EAAY,SAAS,cAC1CE,GAAUK,MAACZ,GAAW,SAASO,EAAQ,SAAiB,uBAC3D,CACF,GAEJ,EAUO,MAAMQ,WAAsBC,WAAkD,CACnFC,YAAY/B,EAA2B,CACrC,MAAMA,CAAK,EAsDbmB,gBAAaA,IAAY,CACvB,KAAKa,SAAS,CACZC,SAAU,GACVf,MAAO,KACR,GAzDD,KAAKgB,MAAQ,CACXD,SAAU,GACVf,MAAO,KAEX,CAEA,OAAOiB,yBAAyBjB,EAAkC,CAEzD,OACLe,SAAU,GACVf,QAEJ,CAEAkB,kBAAkBlB,EAAcmB,EAA4B,CAEpD,MAAEjB,QAAS,KAAKpB,MAChBsC,EAAelB,EAAO,iBAAiBA,KAAU,gBACvDmB,QAAQrB,MAAM,mBAAmBoB,KAAiBpB,EAAOmB,CAAS,EAG9D,KAAKrC,MAAMwC,SACRxC,WAAMwC,QAAQtB,EAAOmB,CAAS,CAWvC,CAEAI,mBAAmBC,EAAqC,CAGpD,KAAKR,MAAMD,UACX,KAAKjC,MAAM2C,oBACXD,EAAUE,WAAa,KAAK5C,MAAM4C,UAElC,KAAKzB,WAAW,CAEpB,CAEA0B,sBAA6B,CAEvB,KAAKX,MAAMD,UAAY,KAAKjC,MAAM8C,gBACpC,KAAK3B,WAAW,CAEpB,CASA4B,QAAoB,CACZ,MAAEd,WAAUf,SAAU,KAAKgB,MAC3B,CAAEU,WAAUI,WAAU5B,OAAM6B,oBAAmB5B,UAAW,KAAKrB,MAErE,OAAIiC,GAAYf,EAEV,OAAO8B,GAAa,WACfA,EAAS,CAAE9B,QAAOC,WAAY,KAAKA,WAAY,EAC7C6B,GAMTtB,MAACT,GACC,SACA,WAAY,KAAKE,WACjB,WAAY,CAAC8B,EACb,OACA,QACA,GAKCL,CACT,CACF,CCxRO,MAAMM,GAA4DA,CAAC,CACxEjD,aACAgD,oBACA,GAAGjD,CACL,IAAM,CAEJ,MAAMmD,EAAelD,EAAa,MAAQgD,EAAoB,UAAY,YAGpEG,EAA4C,CAChDT,mBAAoBQ,IAAiB,MACrCL,eAAgBK,IAAiB,MACjCF,kBAAmBE,IAAiB,WAGtC,OAAQzB,UAAkB0B,KAAc,GAAIpD,CAAS,EACvD,EAOaqD,GAERrD,GACK0B,UAAqB,WAAU,GAAK1B,IAAS,GCoC1CsD,EAAyB,CAEpCC,MAAO,UACPC,UAAW,UACXC,WAAY,UACZC,OAAQ,UACRC,WAAY,UACZC,YAAa,UAGbC,MAAO,UACPC,MAAO,UACPC,OAAQ,UACRC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UAGTC,MAAO,UACPC,UAAW,UACXC,WAAY,UACZC,OAAQ,UACRC,WAAY,UACZC,YAAa,UACbC,IAAK,UACLC,QAAS,UACTC,SAAU,UACVC,KAAM,UACNC,SAAU,UACVC,UAAW,UACXC,OAAQ,UACRC,WAAY,UACZC,YAAa,UAGbC,mBAAoB,2BACpBC,mBAAoB,oBACtB,EAGaC,EAA4B,CACvCC,WAAY,UACZC,QAAS,UACTC,eAAgB,UAChBC,OAAQ,UACRC,QAAS,2BACTC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,YAAa,UACbC,QAAS9C,EAAWmB,MACpB4B,QAAS/C,EAAWsB,OACpB1D,MAAOoC,EAAWyB,IAClBuB,KAAMhD,EAAW4B,KAGjBqB,UAAW,2BACXC,UAAWlD,EAAWC,MAGtBkD,OAAQnD,EAAWmB,MACnBiC,KAAMpD,EAAWyB,IACjB4B,QAASrD,EAAWa,QAGpByC,kBAAmB,wBACnBC,gBAAiB,uBACnB,EAGaC,EAA6B,CACxCnB,WAAY,UACZC,QAAS,UACTC,eAAgB,UAChBC,OAAQ,UACRC,QAAS,qBACTC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,YAAa,UACbC,QAAS9C,EAAWmB,MACpB4B,QAAS/C,EAAWsB,OACpB1D,MAAOoC,EAAWyB,IAClBuB,KAAMhD,EAAW4B,KAGjBqB,UAAW,qBACXC,UAAWlD,EAAWC,MAGtBkD,OAAQnD,EAAWmB,MACnBiC,KAAMpD,EAAWyB,IACjB4B,QAASrD,EAAWa,QAGpByC,kBAAmB,2BACnBC,gBAAiB,0BACnB,EAGaE,EAAU,CACrBC,IAAK,MACLC,GAAI,MACJC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,IAAK,MACP,EAGaC,EAAY,CACvBN,GAAI,UACJC,GAAI,WACJC,GAAI,OACJC,GAAI,WACJC,GAAI,UACJC,IAAK,SACLE,GAAI,SACJC,GAAI,OACJrH,GAAI,UACJsH,GAAI,SACJC,GAAI,UACJC,GAAI,MACN,EAGaC,EAAc,CACzBC,MAAO,IACPC,QAAS,IACTC,OAAQ,IACRC,SAAU,IACVC,KAAM,GACR,EAGaC,EAAc,CACzBC,MAAO,KACPC,OAAQ,IACRC,QAAS,IACX,EAGaC,EAAe,CAC1BC,KAAM,uIACNC,QACE,uIACFC,KAAM,sFACR,EAGaC,EAAc,CACzB1B,GAAI,QACJC,GAAI,QACJC,GAAI,QACJC,GAAI,SACJC,GAAI,QACN,EAGauB,EAAe,CAC1B3B,GAAI,MACJC,GAAI,MACJC,GAAI,MACJC,GAAI,MACJC,GAAI,OACJwB,KAAM,SACNC,OAAQ,KACV,EAGaC,EAAU,CACrB7B,GAAI,+BACJC,GAAI,+BACJC,GAAI,gCACN,EAGa4B,EAAc,CACzBC,KAAM,OACNZ,OAAQ,OACRa,KAAM,MACR,EAGaC,EAAS,CACpBC,KAAM,EACNC,QAAS,GACTC,MAAO,GACPC,QAAS,GACTC,QAAS,GACTC,MAAO,GACT,EC/PaC,EAAiB,CAC5BtI,KAAM,KACNuI,OAAQ,CAENC,QAAStG,EAAWC,MACpBsG,YAAavG,EAAWE,UACxBsG,aAAcxG,EAAWG,WAGzBsG,UAAWzG,EAAWI,OACtBsG,cAAe1G,EAAWK,WAC1BsG,eAAgB3G,EAAWM,YAG3BsG,OAAQ5G,EAAW+B,OACnB8E,WAAY7G,EAAWgC,WACvB8E,YAAa9G,EAAWiC,YAGxBa,QAASV,EAAeU,QACxBC,QAASX,EAAeW,QACxBnF,MAAOwE,EAAexE,MACtBoF,KAAMZ,EAAeY,KAGrBX,WAAYD,EAAeC,WAC3BC,QAASF,EAAeE,QACxBC,eAAgBH,EAAeE,QAC/BE,OAAQJ,EAAeI,OACvBC,QAAS,2BAGTC,YAAaN,EAAeM,YAC5BC,cAAeP,EAAeO,cAC9BC,aAAcR,EAAeQ,aAC7BC,YAAaT,EAAeS,YAG5BI,UAAWb,EAAea,UAC1BC,UAAWd,EAAec,UAC1B6D,UAAW/G,EAAWa,QACtBmG,aAAc5E,EAAekB,kBAG7BH,OAAQf,EAAee,OACvBC,KAAMhB,EAAegB,KACrBC,QAASjB,EAAeiB,QAGxB4D,UAAWjH,EAAWC,MACtBiH,YAAalH,EAAWe,QAGxBuC,kBAAmBlB,EAAekB,kBAClCC,gBAAiBnB,EAAemB,gBAChC4D,kBAAmBnH,EAAWiB,QAC9BmG,iBAAkB,oBACpB,EACA3D,UACA4B,cACApB,YACAM,cACAM,cACAI,eACAK,eACAG,UACAC,cACAG,QACF,ECpEawB,GAAoB,CAC/BvJ,KAAM,QACNuI,OAAQ,CAENC,QAAStG,EAAWC,MACpBsG,YAAavG,EAAWE,UACxBsG,aAAcxG,EAAWG,WAGzBsG,UAAWzG,EAAWI,OACtBsG,cAAe1G,EAAWK,WAC1BsG,eAAgB3G,EAAWM,YAG3BsG,OAAQ5G,EAAW+B,OACnB8E,WAAY7G,EAAWgC,WACvB8E,YAAa9G,EAAWiC,YAGxBa,QAASU,EAAgBV,QACzBC,QAASS,EAAgBT,QACzBnF,MAAO4F,EAAgB5F,MACvBoF,KAAMQ,EAAgBR,KAGtBX,WAAYmB,EAAgBnB,WAC5BC,QAASkB,EAAgBlB,QACzBC,eAAgBiB,EAAgBlB,QAChCE,OAAQgB,EAAgBhB,OACxBC,QAASzC,EAAWmC,mBAGpBO,YAAac,EAAgBd,YAC7BC,cAAea,EAAgBb,cAC/BC,aAAcY,EAAgBZ,aAC9BC,YAAaW,EAAgBX,YAG7BI,UAAWO,EAAgBP,UAC3BC,UAAWM,EAAgBN,UAC3B6D,UAAW/G,EAAWe,QACtBiG,aAAcxD,EAAgBF,kBAG9BH,OAAQK,EAAgBL,OACxBC,KAAMI,EAAgBJ,KACtBC,QAASG,EAAgBH,QAGzB4D,UAAWjH,EAAWC,MACtBiH,YAAalH,EAAWa,QAGxByC,kBAAmBE,EAAgBF,kBACnCC,gBAAiBC,EAAgBD,gBACjC4D,kBAAmBnH,EAAWO,MAC9B6G,iBAAkB,qBACpB,EACA3D,UACA4B,cACApB,YACAM,cACAM,cACAI,eACAK,eACAG,UACAC,cACAG,QACF,ECpEayB,GAAmB,CAC9BxJ,KAAM,OACNuI,OAAQ,CAENC,QAAStG,EAAWI,OACpBmG,YAAavG,EAAWK,WACxBmG,aAAcxG,EAAWM,YAGzBmG,UAAWzG,EAAWI,OACtBsG,cAAe1G,EAAWK,WAC1BsG,eAAgB3G,EAAWM,YAG3BsG,OAAQ5G,EAAW+B,OACnB8E,WAAY7G,EAAWgC,WACvB8E,YAAa9G,EAAWiC,YAGxBa,QAASV,EAAeU,QACxBC,QAASX,EAAeW,QACxBnF,MAAOwE,EAAexE,MACtBoF,KAAMZ,EAAeY,KAGrBX,WAAYrC,EAAWkB,QACvBoB,QAAStC,EAAWiB,QACpBsB,eAAgBvC,EAAWiB,QAC3BuB,OAAQxC,EAAWgB,QACnByB,QAAS,2BAGTC,YAAa1C,EAAWO,MACxBoC,cAAe3C,EAAWY,QAC1BgC,aAAc5C,EAAWc,QACzB+B,YAAa7C,EAAWkB,QAGxB+B,UAAWb,EAAea,UAC1BC,UAAWlD,EAAWI,OACtB2G,UAAW/G,EAAWa,QACtBmG,aAAc5E,EAAekB,kBAG7BH,OAAQf,EAAee,OACvBC,KAAMhB,EAAegB,KACrBC,QAASjB,EAAeiB,QAGxB4D,UAAWjH,EAAWI,OACtB8G,YAAalH,EAAWe,QAGxBuC,kBAAmB,wBACnBC,gBAAiB,wBACjB4D,kBAAmBnH,EAAWkB,QAC9BkG,iBAAkB,oBACpB,EACA3D,UACA4B,cACApB,YACAM,cACAM,cACAI,eACAK,eACAG,UACAC,cACAG,QACF,EClFa0B,GAAeC,+2CAsCT,CAAC,CAAEC,OAAM,IAAMA,EAAMxC,aAAaC,KAC7B,CAAC,CAAEuC,OAAM,IAAMA,EAAMpB,OAAOhE,WACvC,CAAC,CAAEoF,OAAM,IAAMA,EAAMpB,OAAO3D,YAyB5B,CAAC,CAAE+E,OAAM,IAAMA,EAAMpB,OAAOC,QAqBvB,CAAC,CAAEmB,OAAM,IAAMA,EAAMpB,OAAOhE,WAI5B,CAAC,CAAEoF,OAAM,IAAMA,EAAMpB,OAAO7D,OAK5B,CAAC,CAAEiF,OAAM,IAAMA,EAAMpB,OAAOC,QAKrB,CAAC,CAAEmB,OAAM,IAAMA,EAAMpB,OAAOC,QAM7B,CAAC,CAAEmB,OAAM,IAAMA,EAAMpB,OAAOC,QACvC,CAAC,CAAEmB,OAAM,IAAMA,EAAMpB,OAAOxD,WAAW,EAIpD6E,GAAeH,GC7GTI,GAAgC,CACpCC,GAAIxB,EACJ5B,MAAO6C,GACPQ,KAAMP,EACR,EAGMQ,EAAe1B,EAGf2B,EAAYC,GACTL,GAAOK,CAAS,GAAKF,EAIjBG,EAAeC,gBAGzB,CACDT,MAAOK,EACPK,SAAUA,IAAM,CAAC,CACnB,CAAC,EAGYC,GAAWA,IAAMC,aAAWJ,CAAY,EAkBxCK,GAAgBA,CAAC,CAC5BC,eAAeT,EACfU,eAAe,GACfC,aAAa,uBACbnJ,UACkB,IAAM,CAChBoJ,YAAI,6CAA8CH,CAAY,EAGtE,KAAM,CAACd,EAAOkB,CAAa,EAAIC,WAAgB,IAAM,CAI/CJ,GAHJvJ,QAAQyJ,IAAI,wCAAwC,EAGhDF,GAAgB,OAAOvK,OAAW,IAAa,CACjDgB,QAAQyJ,IAAI,0DAA0D,EACtE,MAAMG,EAAc5K,OAAO6K,aAAaC,QAAQN,CAAU,EAG1D,GAFQC,YAAI,6BAA8BG,CAAW,EAEjDA,EACE,IAEMH,YAAI,6CAA8CG,CAAW,EAC/DG,QAAcjB,EAASc,CAAW,EACxC,GAAIG,EACMN,mBAAI,qCAAsCM,EAAYlL,IAAI,EAC3DkL,EAIT/J,QAAQyJ,IAAI,6CAA6C,EACnDO,QAAcC,KAAKC,MAAMN,CAAW,EAClCH,mBAAI,8BAA+BO,CAAW,EAC/CA,QACArL,GACCA,cAAM,gCAAiCA,CAAK,CACtD,EAKJqB,QAAQyJ,IAAI,6CAA6C,EACzD,MAAMU,EAAgB,OAAOb,GAAiB,SAAWR,EAASQ,CAAY,EAAIA,EAC1EG,mBAAI,gCAAiCU,CAAa,EACnDA,EACR,EAGKjB,EAAYkB,GAA6B,CAC7C,MAAMC,EAAc,OAAOD,GAAa,SAAWtB,EAASsB,CAAQ,EAAIA,EACxEV,EAAcW,CAAW,EAGrBd,GAAgB,OAAOvK,OAAW,KAC7B6K,oBAAaS,QAAQd,EAAYa,EAAYxL,MAAQoL,KAAKM,UAAUF,CAAW,CAAC,CACzF,EAIIG,EAAwDA,CAAC,CAAEnK,cAAe,CACtEoJ,YAAI,qCAAsCjB,EAAM3J,IAAI,EAG5D,MAAM4L,EAAa,CACjBC,SAAU,QACVC,IAAK,OACLC,KAAM,EACNC,QAAS,OACTzH,WAAYoF,EAAMpB,OAAOC,QACzByD,MAAO,QACPlE,OAAQ,KACRmE,SAAU,OACVC,WAAY,aAIZ,cAACC,GAAoB,QACnB,gBAAC3C,GAAY,IACb4C,OAAC,MAAI,OAAOT,EAAY,oBAAQjC,EAAM3J,MAAK,EAC1CwB,CACH,KAKIoJ,mBAAI,8CAA+CjB,EAAM3J,IAAI,EAElEM,QAAa,SAAb,CAAsB,MAAO,CAAEqJ,QAAOU,YACrC,eAACsB,EAAcnK,YAAS,CAC1B,EAEJ,stBCxIM8K,GAA0BC,QAAK5N,gNAEf,CAAC,CAAEgL,OAAM,IAAMA,EAAMpB,OAAO/D,QACtB,CAAC,CAAEmF,OAAM,IAAMA,EAAMpB,OAAO7D,OAIlC,CAAC,CAAEiF,OAAM,IAAMA,EAAM/B,YAAYX,MAAM,EAIvDuF,GAAuB9N,MAAGC,mLAIX,CAAC,CAAE8N,QAAO,IAAOA,EAAS,aAAe,SAC/C,CAAC,CAAE9C,QAAO8C,QAAO,IAAOA,EAAS9C,EAAMhE,QAAQI,GAAK,IACtC,CAAC,CAAE4D,OAAM,IAAMA,EAAMpB,OAAO7D,MAAM,EAIzDgI,GAAchO,MAAGC,+KACR,CAAC,CAAEgL,OAAM,IAAMA,EAAMxD,UAAUH,GAEnC,CAAC,CAAE2D,OAAM,IAAMA,EAAMpB,OAAOC,QAG1B,CAAC,CAAEiE,QAAO,IAAOA,EAAS,EAAI,EACnB,CAAC,CAAE9C,OAAM,IAAMA,EAAM/B,YAAYX,MAAM,EAIzD0F,GAAsBC,MAAGjO,6GAGlB,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQI,EAAE,EAItC8G,GAAUjN,EAAOkN,CAAO,EAACnO,iWAGlB,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQG,GACpC,CAAC,CAAE6D,QAAOoD,SAAQ,IAAOA,EAAUpD,EAAMhE,QAAQI,GAAK,IACvC,CAAC,CAAEgH,SAAQ,IAAOA,EAAU,aAAe,SACrD,CAAC,CAAEpD,OAAM,IAAMA,EAAMpB,OAAO1D,cAEN,CAAC,CAAE8E,OAAM,IAAMA,EAAM/B,YAAYC,KACtD,CAAC,CAAE8B,OAAM,IAAMA,EAAM/B,YAAYC,KAIhC,CAAC,CAAE8B,OAAM,IAAMA,EAAMpB,OAAO3D,YAI5B,CAAC,CAAE+E,OAAM,IAAMA,EAAMpB,OAAOC,QAEZ,CAAC,CAAEmB,OAAM,IAAMA,EAAMpB,OAAOC,OAAO,EAK1DwE,GAActO,MAAGC,+JAML,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQG,EAAE,EAI3CmH,GAAeC,OAAIvO,qJAEZ,CAAC,CAAE8N,QAAO,IAAOA,EAAS,EAAI,EACnB,CAAC,CAAE9C,OAAM,IAAMA,EAAM/B,YAAYX,OAE1C,CAAC,CAAEwF,QAAO,IAAOA,EAAS,QAAU,GAAI,EAIjDU,GAAgBzO,MAAGC,2MACZ,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQG,GAAM,CAAC,CAAE6D,OAAM,IAAMA,EAAMhE,QAAQI,GACnD,CAAC,CAAE4D,OAAM,IAAMA,EAAMpB,OAAO7D,OACvC,CAAC,CAAEiF,OAAM,IAAMA,EAAMxD,UAAUL,GACnC,CAAC,CAAE6D,OAAM,IAAMA,EAAMpB,OAAO1D,cAE1B,CAAC,CAAE4H,QAAO,IAAOA,EAAS,EAAI,EACnB,CAAC,CAAE9C,OAAM,IAAMA,EAAM/B,YAAYX,MAAM,EAWzDmG,GAAkCA,CAAC,CAAEX,QAAO,IAAM,CACtD,MAAMrM,EAAWiN,IAEXC,EAAW,CACf,CAAEC,KAAM,IAAKC,MAAO,YAAaC,KAAM,MACvC,CAAEF,KAAM,eAAgBC,MAAO,cAAeC,KAAM,MACpD,CAAEF,KAAM,WAAYC,MAAO,gBAAiBC,KAAM,MAClD,CAAEF,KAAM,YAAaC,MAAO,WAAYC,KAAM,MAC9C,CAAEF,KAAM,YAAaC,MAAO,WAAYC,KAAM,KAAM,EAIpD,cAACnB,IAAiB,SAChB,gBAACE,IAAc,SACb,eAACE,GAAK,UAAgB,gBAAI,CAC5B,SAECC,GACEW,YAASI,IACRC,UAACd,IAEC,GAAIc,EAAKJ,KACT,QAASd,EACT,UAAWrM,EAASwN,WAAaD,EAAKJ,KAAO,SAAW,GAExD,UAACjN,UAAMqN,WAAKF,IAAK,GAChBnN,UAAM,SAAiBqN,WAAKH,MAAM,CAN9BG,KAAKJ,IAOZ,CACD,EACH,EAEAjN,MAAC6M,GAAO,UAAgB,SAAM,UAChC,GAEJ,ECvIMU,GAAyBC,SAAMnP,8NAKtB,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQI,GACtB,CAAC,CAAE4D,OAAM,IAAMA,EAAMpB,OAAO/D,QACrB,CAAC,CAAEmF,OAAM,IAAMA,EAAMpB,OAAO7D,OAC9C,CAAC,CAAEiF,OAAM,IAAMA,EAAMpB,OAAO3D,WAAW,EAI5CmJ,GAAqBrP,MAAGC,gEAG7B,wCAGKqP,GAAoBvO,SAAMd,iPAQrB,CAAC,CAAEgL,OAAM,IAAMA,EAAMpB,OAAO3D,YAEjB,CAAC,CAAE+E,OAAM,IAAMA,EAAM/B,YAAYC,KAG1C,CAAC,CAAE8B,OAAM,IAAMA,EAAMpB,OAAOC,OAAO,EAK1CkE,GAAchO,MAAGC,+IAGN,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQG,GAC/B,CAAC,CAAE6D,OAAM,IAAMA,EAAMxD,UAAUH,EAAE,EAK1CiI,GAAsBvP,MAAGC,gHAGtB,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQI,EAAE,EAIlCmI,GAAkBxP,MAAGC,qOAId,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQE,GAAM,CAAC,CAAE8D,OAAM,IAAMA,EAAMhE,QAAQG,GAC1D,CAAC,CAAE6D,OAAM,IAAMA,EAAMnC,aAAa1B,GACpB,CAAC,CAAE6D,OAAM,IAAMA,EAAM/B,YAAYC,IAAI,EAQhEsG,GAAgBzP,MAAGC,oNAIH,CAAC,CAAEgL,OAAM,IAAMA,EAAMpB,OAAOC,OAAO,EAgBnD4F,GAAgCA,CAAC,CAAEC,gBAAeC,aAAY,WAE/DT,GACC,kBAACE,GACC,iBAACC,GAAW,SAASK,EAAe,aAAW,iBAC5CC,WAAehO,cAAK,SAAC,MAAWA,cAAK,YAAC,GACzC,EACAA,MAACoM,IAAK,SAAsB,4BAC9B,EAEApM,MAAC2N,IACC,SAAC3N,UACC,eAAC6N,GAAO,eAAE,EACZ,CACF,EACF,IClGEI,GAAyB7P,MAAGC,sJAKZ,CAAC,CAAEgL,OAAM,IAAMA,EAAMpB,OAAOhE,UAAU,EAItD+H,GAA0B5N,MAAGC,kNACxB,CAAC,CAAE8N,QAAO,IAAOA,EAAS,QAAU,OAEzB,CAAC,CAAE9C,OAAM,IAAMA,EAAM/B,YAAYX,OAGhC,CAAC,CAAE0C,OAAM,IAAMA,EAAMpC,YAAYxB,GAEzC,CAAC,CAAE4D,OAAM,IAAMA,EAAM5B,OAAOM,MAC9B,CAAC,CAAEoE,QAAO,IAAOA,EAAS,QAAU,IAC/B,CAAC,CAAEA,SAAQ9C,OAAM,IAAO8C,EAAS9C,EAAMhC,QAAQ3B,GAAK,MAAO,EAKvEwI,GAA0B9P,MAAGC,mNAKP,CAAC,CAAEgL,OAAM,IAAMA,EAAM/B,YAAYX,OAEtC,CAAC,CAAE0C,OAAM,IAAMA,EAAMpC,YAAYxB,EAAE,EAOpD8H,GAAyBnP,MAAGC,qEAGjC,kCAGK8P,GAAqBC,OAAI/P,6IAGlB,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQK,GAEnB,CAAC,CAAE2D,OAAM,IAAMA,EAAMpC,YAAYzB,GACzC,CAAC,CAAE6D,OAAM,IAAMA,EAAMhE,QAAQI,EAAE,EAKxC4I,GAAiBjQ,MAAGC,oQAOb,CAAC,CAAEgL,OAAM,IAAMA,EAAM5B,OAAOG,MAAQ,EACpC,CAAC,CAAE0G,WAAU,IAAOA,EAAY,EAAI,EACjC,CAAC,CAAEA,WAAU,IAAOA,EAAY,UAAY,SACpC,CAAC,CAAEjF,OAAM,IAAMA,EAAM/B,YAAYX,OACxC,CAAC,CAAE0C,OAAM,IAAMA,EAAM/B,YAAYX,OAE3B,CAAC,CAAE0C,OAAM,IAAMA,EAAMpC,YAAYxB,EAAE,EAQpD8I,GAAuBA,IAAM,CACjC,KAAM,CAACP,EAAaQ,CAAc,EAAIhE,WAAS,EAAI,EAG7CuD,EAAgBA,IAAM,CAC1BS,EAAe,CAACR,CAAW,GAIvBS,EAAeA,IAAM,CACzBD,EAAe,EAAK,GAGtB,cACGP,GAEC,iBAACjC,IAAiB,OAAQgC,EACxB,eAAClB,GAAQ,QAAQkB,EAAY,CAC/B,GAGChO,UAAQ,UAAWgO,EAAa,QAASS,EAAa,EAGvD1C,OAACmC,IAAiB,cAChB,gBAACX,GACC,gBAACO,GAAO,iBAA8B,aAAyB,GACjE,EAEC9N,UACC,SAACA,SAAM,GACT,GACF,CACF,GAEJ,ECnHM0O,GAA0BtQ,MAAGC,kLAMtB,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQK,EAAE,EAGtCiJ,GAAiBvQ,MAAGC,yPAKJ,CAAC,CAAEgL,OAAM,IAAMA,EAAMpB,OAAOC,OAAO,EAUnD0G,GAAqBxQ,MAAGC,+GACd,CAAC,CAAEgL,OAAM,IAAMA,EAAMhE,QAAQI,GAClC,CAAC,CAAE4D,OAAM,IAAMA,EAAMpB,OAAO1D,cACxB,CAAC,CAAE8E,OAAM,IAAMA,EAAMxD,UAAUJ,EAAE,EAM1CoJ,GAA0BA,WAE3BH,GACC,iBAACC,GAAO,IACR3O,MAAC4O,IAAY,SAAU,cACzB,ICnCEE,GAAYC,OAChB,UAAM,OAAO,yBAA6C,iIAC5D,EACMC,GAAaD,OAAK,IAAME,aAAO,0BAAoC,4JAAC,EACpEC,GAAeH,OACnB,IAAME,aAAO,4BAAwC,kMACvD,EACME,GAAgBJ,OACpB,IAAME,aAAO,6BAA0C,+JACzD,EACMG,EAAYL,OAAK,IAAME,aAAO,yBAAqC,+LAAC,EACpEI,GAAWN,OAAK,IAAME,aAAO,wBAA+B,4JAAC,EAC7DK,GAAWP,OAAK,IAAME,aAAO,wBAAwB,EAAC,4JAOtDM,GAAsBA,UAEvBC,WAAS,gBAAWX,GAAa,IAChC,gBAACY,EAEC,kBAACC,GAAM,KAAK,IAAI,QAAS1P,MAACuO,OACxB,gBAACmB,GAAM,MAAK,GAAC,QAAS1P,MAAC8O,IAAS,GAAI,QACnCY,EAAM,MAAK,cAAc,QAAS1P,MAACgP,OAAc,QACjDU,EAAM,MAAK,UAAU,QAAS1P,MAACkP,OAAgB,QAC/CQ,EAAM,MAAK,WAAW,QAAS1P,MAACmP,OAAiB,QACjDO,EAAM,MAAK,YAAY,QAAS1P,MAACoP,MAAa,QAC9CM,EAAM,MAAK,YAAY,QAAS1P,MAACoP,MAAa,QAC9CM,EAAM,MAAK,WAAW,QAAS1P,MAACqP,OAAY,QAC5CK,EAAM,MAAK,IAAI,QAAS1P,MAACsP,OAAY,GACxC,EAGAtP,MAAC0P,EAAM,MAAK,aAAa,QAAU1P,SAAS,GAAG,IAAI,QAAO,IAAI,GAChE,EACF,GC7BS2B,GAAoDA,CAAC,CAAET,UAAS,IAAM,CAC3EyO,QAAenQ,GAAiB,CAE5BA,cAAM,qBAAsBA,CAAK,GAW3C,aACGoQ,GAAwB,SAASD,EAAa,KAAK,cACjDzO,UACH,EAEJ,EC9BA,SAAS2O,IAAM,CACb,KAAM,CAACC,EAAaC,CAAc,EAAIvF,WAAS,CAAC,EAGhDwF,YAAU,KACRnP,QAAQyJ,IAAI,uBAAuB,EAGnCzJ,QAAQyJ,IAAI,gBAAiB2F,SAASC,eAAe,MAAM,CAAC,EAC5DrP,QAAQyJ,IAAI,uBAAwB2F,SAASnJ,KAAK5F,SAASiP,MAAM,EAGjDC,KAASA,EAAO,CAAC,EAE1B,IAAM,CACXvP,QAAQyJ,IAAI,yBAAyB,IAEtC,CAAE,GAGGA,YAAI,yBAAyBwF,IAAc,EAGnD,MAAMxE,EAAa,CACjBC,SAAU,QACVC,IAAK,EACLC,KAAM,EACNC,QAAS,OACTzH,WAAY,MACZ0H,MAAO,QACPlE,OAAQ,KACRmE,SAAU,OACVC,WAAY,aAGd,aACGlK,GACC,gBAACuI,IAAc,aAAa,KAC1B,gBAACmG,EACC,WAACtE,cAAI,MAAOT,EAAY,2BAAewE,GAAY,QAClDP,GAAS,KACZ,EACF,CACF,EAEJ,CC/CA,MAAMe,GAAmBC,GAAgC,CACnDA,GAAeA,aAAuBC,UACjCvB,uCAAY,MAAEwB,KAAK,CAAC,CAAEC,SAAQC,SAAQC,SAAQC,SAAQC,aAAc,CACzEJ,EAAOH,CAAW,EAClBI,EAAOJ,CAAW,EAClBK,EAAOL,CAAW,EAClBM,EAAON,CAAW,EAClBO,EAAQP,CAAW,EACpB,CAEL,ECHA,MAAMnC,GAAOA,IAAM,CAOjB,GANAvN,QAAQyJ,IAAI,wCAAwC,EAMhD,CAHgB2F,SAASC,eAAe,MAAM,EAGhC,CAChBrP,QAAQrB,MAAM,qDAAqD,EAC7DuR,QAAed,SAASe,cAAc,KAAK,EACjDD,EAAaE,GAAK,OACTnK,cAAKoK,YAAYH,CAAY,EAI3BI,EAASC,WAAWnB,SAASC,eAAe,MAAM,CAAgB,EAG1E7O,aACFgQ,EAAM,WAAN,CACC,SAACrR,WAAG,EACN,CACF,EAGgBsQ,IAClB,EAGAzQ,OAAOyR,iBAAiB,QAAoBC,IAC1C1Q,QAAQrB,MAAM,SAAU+R,EAAM/R,OAAS+R,EAAMtR,OAAO,CACtD,CAAC,EAGDmO,GAAK", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "withConfig", "props", "isAppLevel", "ErrorCard", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h3", "ErrorMessage", "p", "ErrorDetails", "details", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pre", "ButtonContainer", "RetryButton", "button", "Ski<PERSON><PERSON><PERSON>on", "ReloadButton", "styled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "resetError", "name", "onSkip", "handleReload", "window", "location", "reload", "jsx", "message", "stack", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "setState", "<PERSON><PERSON><PERSON><PERSON>", "state", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "boundaryName", "console", "onError", "componentDidUpdate", "prevProps", "resetOnPropsChange", "children", "componentWillUnmount", "resetOnUnmount", "render", "fallback", "isFeatureBoundary", "UnifiedErrorBoundary", "boundaryType", "defaultProps", "AppError<PERSON>ou<PERSON>ry", "baseColors", "f1Red", "f1RedDark", "f1RedLight", "f1Blue", "f1BlueDark", "f1BlueLight", "white", "black", "gray50", "gray100", "gray200", "gray300", "gray400", "gray500", "gray600", "gray700", "gray800", "gray900", "green", "greenDark", "greenLight", "yellow", "yellowDark", "yellowLight", "red", "redDark", "redLight", "blue", "blueDark", "blueLight", "purple", "purpleDark", "purpleLight", "whiteTransparent10", "blackTransparent10", "darkModeColors", "background", "surface", "cardBackground", "border", "divider", "textPrimary", "textSecondary", "textDisabled", "textInverse", "success", "warning", "info", "chartGrid", "chartLine", "profit", "loss", "neutral", "tooltipBackground", "modalBackground", "lightModeColors", "spacing", "xxs", "xs", "sm", "md", "lg", "xl", "xxl", "fontSizes", "h1", "h2", "h4", "h5", "h6", "fontWeights", "light", "regular", "medium", "semibold", "bold", "lineHeights", "tight", "normal", "relaxed", "fontFamilies", "body", "heading", "mono", "breakpoints", "borderRadius", "pill", "circle", "shadows", "transitions", "fast", "slow", "zIndex", "base", "overlay", "modal", "popover", "tooltip", "fixed", "f1Theme", "colors", "primary", "primaryDark", "primaryLight", "secondary", "secondaryDark", "secondaryLight", "accent", "accentDark", "accentLight", "chartAxis", "chartTooltip", "tabActive", "tabInactive", "sidebarBackground", "headerBackground", "lightTheme", "darkTheme", "GlobalStyles", "createGlobalStyle", "theme", "GlobalStyles$1", "themes", "f1", "dark", "defaultTheme", "getTheme", "themeName", "ThemeContext", "createContext", "setTheme", "useTheme", "useContext", "ThemeProvider", "initialTheme", "persistTheme", "storageKey", "log", "setThemeState", "useState", "storedTheme", "localStorage", "getItem", "themeByName", "parsedTheme", "JSON", "parse", "resolvedTheme", "newTheme", "themeObject", "setItem", "stringify", "ThemeWrapper", "debugStyle", "position", "top", "left", "padding", "color", "fontSize", "fontFamily", "StyledThemeProvider", "jsxs", "SidebarContainer", "aside", "LogoContainer", "isOpen", "Logo", "NavContainer", "nav", "NavItem", "NavLink", "$isOpen", "Icon", "Label", "span", "Footer", "Sidebar", "useLocation", "navItems", "path", "label", "icon", "map", "item", "pathname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "LeftSection", "MenuButton", "RightSection", "UserMenu", "Avatar", "Header", "toggleSidebar", "sidebarOpen", "LayoutContainer", "ContentContainer", "MainContent", "main", "Overlay", "isVisible", "MainLayout", "setSidebarOpen", "closeSidebar", "LoadingContainer", "Spinner", "LoadingText", "LoadingScreen", "Dashboard", "lazy", "DailyGuide", "__vitePreload", "TradeJournal", "TradeAnalysis", "TradeForm", "Settings", "NotFound", "AppRoutes", "Suspense", "Routes", "Route", "handleError", "UnifiedAppErrorBoundary", "App", "renderCount", "setRenderCount", "useEffect", "document", "getElementById", "length", "prev", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportWebVitals", "onPerfEntry", "Function", "then", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB", "fallbackRoot", "createElement", "id", "append<PERSON><PERSON><PERSON>", "ReactDOM", "createRoot", "React", "addEventListener", "event"], "sources": ["../../../shared/src/components/molecules/ErrorBoundary.tsx", "../../../shared/src/components/molecules/UnifiedErrorBoundary.tsx", "../../../shared/src/theme/tokens.ts", "../../../shared/src/theme/f1Theme.ts", "../../../shared/src/theme/lightTheme.ts", "../../../shared/src/theme/darkTheme.ts", "../../../shared/src/theme/GlobalStyles.tsx", "../../../shared/src/theme/ThemeProvider.tsx", "../../src/layouts/Sidebar.tsx", "../../src/layouts/Header.tsx", "../../src/layouts/MainLayout.tsx", "../../src/components/molecules/LoadingScreen.tsx", "../../src/routes/routes.tsx", "../../src/components/AppErrorBoundary.tsx", "../../src/App.tsx", "../../src/reportWebVitals.ts", "../../src/index.tsx"], "sourcesContent": ["/**\n * Error Boundary Component\n *\n * A React error boundary component that catches errors in its child component tree\n * and displays a fallback UI instead of crashing the entire application.\n *\n * This is a unified error boundary that can be used at any level of the application.\n */\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\nimport styled from 'styled-components';\n\n// Error boundary props\nexport interface ErrorBoundaryProps {\n  /** The children to render */\n  children: ReactNode;\n  /** Custom fallback component to render when an error occurs */\n  fallback?: ReactNode | ((props: { error: Error; resetError: () => void }) => ReactNode);\n  /** Function to call when an error occurs */\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n  /** Whether to reset the error boundary when the children prop changes */\n  resetOnPropsChange?: boolean;\n  /** Whether to reset the error boundary when the component unmounts */\n  resetOnUnmount?: boolean;\n  /** Name of the boundary for identification in logs */\n  name?: string;\n  /** Whether this is a feature-level boundary */\n  isFeatureBoundary?: boolean;\n  /** Function to call when the user chooses to skip a feature (only for feature boundaries) */\n  onSkip?: () => void;\n}\n\n// Error boundary state\ninterface ErrorBoundaryState {\n  /** Whether an error has occurred */\n  hasError: boolean;\n  /** The error that occurred */\n  error: Error | null;\n}\n\n// Styled components\nconst ErrorContainer = styled.div<{ isAppLevel?: boolean }>`\n  padding: 1.5rem;\n  margin: ${(props) => (props.isAppLevel ? '0' : '1rem 0')};\n  border-radius: 0.5rem;\n  background-color: ${(props) => (props.isAppLevel ? '#1a1f2c' : '#f44336')};\n  color: #ffffff;\n  ${(props) =>\n    props.isAppLevel &&\n    `\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    min-height: 100vh;\n    width: 100%;\n    max-width: 600px;\n    margin: 0 auto;\n  `}\n`;\n\nconst ErrorCard = styled.div`\n  background-color: #252a37;\n  border-radius: 0.5rem;\n  padding: 2rem;\n  width: 100%;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n`;\n\nconst ErrorTitle = styled.h3<{ isAppLevel?: boolean }>`\n  margin-top: 0;\n  font-size: ${(props) => (props.isAppLevel ? '1.5rem' : '1.25rem')};\n  font-weight: 700;\n  text-align: ${(props) => (props.isAppLevel ? 'center' : 'left')};\n`;\n\nconst ErrorMessage = styled.p<{ isAppLevel?: boolean }>`\n  margin-bottom: 1rem;\n  text-align: ${(props) => (props.isAppLevel ? 'center' : 'left')};\n`;\n\nconst ErrorDetails = styled.details`\n  margin-bottom: 1rem;\n\n  summary {\n    cursor: pointer;\n    color: #2196f3;\n    font-weight: 500;\n    margin-bottom: 0.5rem;\n  }\n`;\n\nconst ErrorStack = styled.pre`\n  font-size: 0.875rem;\n  background-color: rgba(0, 0, 0, 0.1);\n  padding: 0.5rem;\n  border-radius: 0.25rem;\n  overflow: auto;\n  max-height: 200px;\n`;\n\nconst ButtonContainer = styled.div`\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-start;\n`;\n\nconst RetryButton = styled.button`\n  background-color: #ffffff;\n  color: #f44336;\n  border: none;\n  border-radius: 0.25rem;\n  padding: 0.5rem 1rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #f5f5f5;\n  }\n`;\n\nconst SkipButton = styled.button`\n  padding: 0.5rem 1rem;\n  background-color: transparent;\n  color: #ffffff;\n  border: 1px solid #ffffff;\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst ReloadButton = styled(RetryButton)`\n  margin-top: 1rem;\n  width: 100%;\n`;\n\n/**\n * Default fallback UI for the error boundary\n */\nconst DefaultFallback = ({\n  error,\n  resetError,\n  isAppLevel,\n  name,\n  onSkip,\n}: {\n  error: Error;\n  resetError: () => void;\n  isAppLevel?: boolean;\n  name?: string;\n  onSkip?: () => void;\n}) => {\n  const handleReload = () => {\n    window.location.reload();\n  };\n\n  if (isAppLevel) {\n    return (\n      <ErrorContainer isAppLevel>\n        <ErrorCard>\n          <ErrorTitle isAppLevel>Something went wrong</ErrorTitle>\n          <ErrorMessage isAppLevel>\n            We're sorry, but an unexpected error has occurred. Please try reloading the application.\n          </ErrorMessage>\n          <ErrorDetails>\n            <summary>Technical Details</summary>\n            <ErrorMessage>{error.message}</ErrorMessage>\n            {error.stack && <ErrorStack>{error.stack}</ErrorStack>}\n          </ErrorDetails>\n          <ReloadButton onClick={handleReload}>Reload Application</ReloadButton>\n        </ErrorCard>\n      </ErrorContainer>\n    );\n  }\n\n  return (\n    <ErrorContainer>\n      <ErrorTitle>{name ? `Error in ${name}` : 'Something went wrong'}</ErrorTitle>\n      <ErrorMessage>\n        {name\n          ? `We encountered a problem while loading ${name}. You can try again${\n              onSkip ? ' or skip this feature' : ''\n            }.`\n          : 'An unexpected error occurred. Please try again.'}\n      </ErrorMessage>\n      <ErrorDetails>\n        <summary>Technical Details</summary>\n        <ErrorMessage>{error.message}</ErrorMessage>\n        {error.stack && <ErrorStack>{error.stack}</ErrorStack>}\n      </ErrorDetails>\n      <ButtonContainer>\n        <RetryButton onClick={resetError}>Try Again</RetryButton>\n        {onSkip && <SkipButton onClick={onSkip}>Skip This Feature</SkipButton>}\n      </ButtonContainer>\n    </ErrorContainer>\n  );\n};\n\n/**\n * Error Boundary Component\n *\n * A unified React error boundary component that catches errors in its child component tree\n * and displays a fallback UI instead of crashing the entire application.\n *\n * This component can be used at both the application level and feature level.\n */\nexport class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\n    // Log the error to an error reporting service\n    const { name } = this.props;\n    const boundaryName = name ? `ErrorBoundary(${name})` : 'ErrorBoundary';\n    console.error(`Error caught by ${boundaryName}:`, error, errorInfo);\n\n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // Here we could add Sentry integration\n    // if (typeof window !== 'undefined' && window.Sentry) {\n    //   window.Sentry.withScope((scope) => {\n    //     scope.setTag('boundary', name || 'unnamed');\n    //     scope.setExtra('componentStack', errorInfo.componentStack);\n    //     window.Sentry.captureException(error);\n    //   });\n    // }\n  }\n\n  componentDidUpdate(prevProps: ErrorBoundaryProps): void {\n    // Reset the error state if the children prop changes and resetOnPropsChange is true\n    if (\n      this.state.hasError &&\n      this.props.resetOnPropsChange &&\n      prevProps.children !== this.props.children\n    ) {\n      this.resetError();\n    }\n  }\n\n  componentWillUnmount(): void {\n    // Reset the error state if resetOnUnmount is true\n    if (this.state.hasError && this.props.resetOnUnmount) {\n      this.resetError();\n    }\n  }\n\n  resetError = (): void => {\n    this.setState({\n      hasError: false,\n      error: null,\n    });\n  };\n\n  render(): ReactNode {\n    const { hasError, error } = this.state;\n    const { children, fallback, name, isFeatureBoundary, onSkip } = this.props;\n\n    if (hasError && error) {\n      // Render the fallback UI if an error occurred\n      if (typeof fallback === 'function') {\n        return fallback({ error, resetError: this.resetError });\n      } else if (fallback) {\n        return fallback;\n      }\n\n      // Use the default fallback\n      return (\n        <DefaultFallback\n          error={error}\n          resetError={this.resetError}\n          isAppLevel={!isFeatureBoundary}\n          name={name}\n          onSkip={onSkip}\n        />\n      );\n    }\n\n    // Otherwise, render the children\n    return children;\n  }\n}\n\nexport default ErrorBoundary;\n", "/**\n * Unified Error Boundary\n *\n * A unified error boundary component that can be used at both the application level\n * and feature level, replacing the previous three-layer architecture.\n */\nimport React from 'react';\nimport { ErrorBoundary, ErrorBoundaryProps } from './ErrorBoundary';\n\nexport interface UnifiedErrorBoundaryProps extends Omit<ErrorBoundaryProps, 'isFeatureBoundary'> {\n  /** Whether this is an application-level boundary */\n  isAppLevel?: boolean;\n  /** Whether this is a feature-level boundary */\n  isFeatureBoundary?: boolean;\n}\n\n/**\n * Unified Error Boundary\n *\n * A wrapper around the base ErrorBoundary component that provides a simpler API\n * for common use cases.\n */\nexport const UnifiedErrorBoundary: React.FC<UnifiedErrorBoundaryProps> = ({\n  isAppLevel,\n  isFeatureBoundary,\n  ...props\n}) => {\n  // Determine the boundary type based on props\n  const boundaryType = isAppLevel ? 'app' : isFeatureBoundary ? 'feature' : 'component';\n\n  // Set appropriate defaults based on boundary type\n  const defaultProps: Partial<ErrorBoundaryProps> = {\n    resetOnPropsChange: boundaryType !== 'app', // App-level boundaries should not reset on props change\n    resetOnUnmount: boundaryType !== 'app', // App-level boundaries should not reset on unmount\n    isFeatureBoundary: boundaryType === 'feature',\n  };\n\n  return <ErrorBoundary {...defaultProps} {...props} />;\n};\n\n/**\n * App Error Boundary\n *\n * A specialized error boundary for the application level.\n */\nexport const AppErrorBoundary: React.FC<Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'>> = (\n  props\n) => {\n  return <UnifiedErrorBoundary isAppLevel {...props} />;\n};\n\n/**\n * Feature Error Boundary\n *\n * A specialized error boundary for feature modules.\n */\nexport const FeatureErrorBoundary: React.FC<\n  Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'> & { featureName: string }\n> = ({ featureName, ...props }) => {\n  return <UnifiedErrorBoundary isFeatureBoundary name={featureName} {...props} />;\n};\n\nexport default UnifiedErrorBoundary;\n", "/**\n * Theme Tokens\n *\n * Design tokens for the theme system\n */\n\n// Base colors interface\nexport interface BaseColors {\n  // F1 colors\n  f1Red: string;\n  f1RedDark: string;\n  f1RedLight: string;\n  f1Blue: string;\n  f1BlueDark: string;\n  f1BlueLight: string;\n\n  // Neutrals\n  white: string;\n  black: string;\n  gray50: string;\n  gray100: string;\n  gray200: string;\n  gray300: string;\n  gray400: string;\n  gray500: string;\n  gray600: string;\n  gray700: string;\n  gray800: string;\n  gray900: string;\n\n  // Status colors\n  green: string;\n  greenDark: string;\n  greenLight: string;\n  yellow: string;\n  yellowDark: string;\n  yellowLight: string;\n  red: string;\n  redDark: string;\n  redLight: string;\n  blue: string;\n  blueDark: string;\n  blueLight: string;\n  purple: string;\n  purpleDark: string;\n  purpleLight: string;\n\n  // Transparent colors\n  whiteTransparent10: string;\n  blackTransparent10: string;\n}\n\n// Color mode interface\nexport interface ColorMode {\n  // Base colors\n  background: string;\n  surface: string;\n  cardBackground: string;\n  border: string;\n  divider: string;\n  textPrimary: string;\n  textSecondary: string;\n  textDisabled: string;\n  textInverse: string;\n  success: string;\n  warning: string;\n  error: string;\n  info: string;\n\n  // Chart colors\n  chartGrid: string;\n  chartLine: string;\n\n  // Trading specific colors\n  profit: string;\n  loss: string;\n  neutral: string;\n\n  // Component specific colors\n  tooltipBackground: string;\n  modalBackground: string;\n}\n\n// Base colors\nexport const baseColors: BaseColors = {\n  // F1 colors\n  f1Red: \"#e10600\",\n  f1RedDark: \"#c10500\",\n  f1RedLight: \"#ff3b36\",\n  f1Blue: \"#1e5bc6\",\n  f1BlueDark: \"#1a4da8\",\n  f1BlueLight: \"#4a7dd8\",\n\n  // Neutrals\n  white: \"#ffffff\",\n  black: \"#000000\",\n  gray50: \"#f9fafb\",\n  gray100: \"#f3f4f6\",\n  gray200: \"#e5e7eb\",\n  gray300: \"#d1d5db\",\n  gray400: \"#9ca3af\",\n  gray500: \"#6b7280\",\n  gray600: \"#4b5563\",\n  gray700: \"#374151\",\n  gray800: \"#1f2937\",\n  gray900: \"#111827\",\n\n  // Status colors\n  green: \"#4caf50\",\n  greenDark: \"#388e3c\",\n  greenLight: \"#81c784\",\n  yellow: \"#ffeb3b\",\n  yellowDark: \"#fbc02d\",\n  yellowLight: \"#fff59d\",\n  red: \"#f44336\",\n  redDark: \"#d32f2f\",\n  redLight: \"#e57373\",\n  blue: \"#2196f3\",\n  blueDark: \"#1976d2\",\n  blueLight: \"#64b5f6\",\n  purple: \"#9c27b0\",\n  purpleDark: \"#7b1fa2\",\n  purpleLight: \"#ba68c8\",\n\n  // Transparent colors\n  whiteTransparent10: \"rgba(255, 255, 255, 0.1)\",\n  blackTransparent10: \"rgba(0, 0, 0, 0.1)\",\n};\n\n// Dark mode colors\nexport const darkModeColors: ColorMode = {\n  background: \"#1a1f2c\",\n  surface: \"#252a37\",\n  cardBackground: \"#252a37\",\n  border: \"#333333\",\n  divider: \"rgba(255, 255, 255, 0.1)\",\n  textPrimary: \"#ffffff\",\n  textSecondary: \"#aaaaaa\",\n  textDisabled: \"#666666\",\n  textInverse: \"#1a1f2c\",\n  success: baseColors.green,\n  warning: baseColors.yellow,\n  error: baseColors.red,\n  info: baseColors.blue,\n\n  // Chart colors\n  chartGrid: \"rgba(255, 255, 255, 0.1)\",\n  chartLine: baseColors.f1Red,\n\n  // Trading specific colors\n  profit: baseColors.green,\n  loss: baseColors.red,\n  neutral: baseColors.gray400,\n\n  // Component specific colors\n  tooltipBackground: \"rgba(37, 42, 55, 0.9)\",\n  modalBackground: \"rgba(26, 31, 44, 0.8)\",\n};\n\n// Light mode colors\nexport const lightModeColors: ColorMode = {\n  background: \"#f5f5f5\",\n  surface: \"#ffffff\",\n  cardBackground: \"#ffffff\",\n  border: \"#e0e0e0\",\n  divider: \"rgba(0, 0, 0, 0.1)\",\n  textPrimary: \"#333333\",\n  textSecondary: \"#666666\",\n  textDisabled: \"#999999\",\n  textInverse: \"#ffffff\",\n  success: baseColors.green,\n  warning: baseColors.yellow,\n  error: baseColors.red,\n  info: baseColors.blue,\n\n  // Chart colors\n  chartGrid: \"rgba(0, 0, 0, 0.1)\",\n  chartLine: baseColors.f1Red,\n\n  // Trading specific colors\n  profit: baseColors.green,\n  loss: baseColors.red,\n  neutral: baseColors.gray400,\n\n  // Component specific colors\n  tooltipBackground: \"rgba(255, 255, 255, 0.9)\",\n  modalBackground: \"rgba(255, 255, 255, 0.8)\",\n};\n\n// Spacing\nexport const spacing = {\n  xxs: \"4px\",\n  xs: \"8px\",\n  sm: \"12px\",\n  md: \"16px\",\n  lg: \"24px\",\n  xl: \"32px\",\n  xxl: \"48px\",\n};\n\n// Font sizes\nexport const fontSizes = {\n  xs: \"0.75rem\",\n  sm: \"0.875rem\",\n  md: \"1rem\",\n  lg: \"1.125rem\",\n  xl: \"1.25rem\",\n  xxl: \"1.5rem\",\n  h1: \"2.5rem\",\n  h2: \"2rem\",\n  h3: \"1.75rem\",\n  h4: \"1.5rem\",\n  h5: \"1.25rem\",\n  h6: \"1rem\",\n};\n\n// Font weights\nexport const fontWeights = {\n  light: 300,\n  regular: 400,\n  medium: 500,\n  semibold: 600,\n  bold: 700,\n};\n\n// Line heights\nexport const lineHeights = {\n  tight: 1.25,\n  normal: 1.5,\n  relaxed: 1.75,\n};\n\n// Font families\nexport const fontFamilies = {\n  body: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif\",\n  heading:\n    \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif\",\n  mono: \"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace\",\n};\n\n// Breakpoints\nexport const breakpoints = {\n  xs: \"480px\",\n  sm: \"640px\",\n  md: \"768px\",\n  lg: \"1024px\",\n  xl: \"1280px\",\n};\n\n// Border radius\nexport const borderRadius = {\n  xs: \"2px\",\n  sm: \"4px\",\n  md: \"6px\",\n  lg: \"8px\",\n  xl: \"12px\",\n  pill: \"9999px\",\n  circle: \"50%\",\n};\n\n// Shadows\nexport const shadows = {\n  sm: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n  md: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n  lg: \"0 10px 15px rgba(0, 0, 0, 0.1)\",\n};\n\n// Transitions\nexport const transitions = {\n  fast: \"0.1s\",\n  normal: \"0.3s\",\n  slow: \"0.5s\",\n};\n\n// Z-index\nexport const zIndex = {\n  base: 1,\n  overlay: 10,\n  modal: 20,\n  popover: 30,\n  tooltip: 40,\n  fixed: 100,\n};\n", "/**\n * Formula 1 Theme\n *\n * This file contains the Formula 1 inspired theme for the ADHD Trading Dashboard.\n */\n\nimport { Theme } from './types';\nimport {\n  baseColors,\n  darkModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens';\n\n/**\n * F1 Theme\n *\n * A dark theme inspired by Formula 1 racing with red accents.\n */\nexport const f1Theme: Theme = {\n  name: 'f1',\n  colors: {\n    // Primary colors\n    primary: baseColors.f1Red,\n    primaryDark: baseColors.f1RedDark,\n    primaryLight: baseColors.f1RedLight,\n\n    // Secondary colors\n    secondary: baseColors.f1Blue,\n    secondaryDark: baseColors.f1BlueDark,\n    secondaryLight: baseColors.f1BlueLight,\n\n    // Accent colors\n    accent: baseColors.purple,\n    accentDark: baseColors.purpleDark,\n    accentLight: baseColors.purpleLight,\n\n    // Status colors\n    success: darkModeColors.success,\n    warning: darkModeColors.warning,\n    error: darkModeColors.error,\n    info: darkModeColors.info,\n\n    // Neutral colors\n    background: darkModeColors.background,\n    surface: darkModeColors.surface,\n    cardBackground: darkModeColors.surface,\n    border: darkModeColors.border,\n    divider: 'rgba(255, 255, 255, 0.1)',\n\n    // Text colors\n    textPrimary: darkModeColors.textPrimary,\n    textSecondary: darkModeColors.textSecondary,\n    textDisabled: darkModeColors.textDisabled,\n    textInverse: darkModeColors.textInverse,\n\n    // Chart colors\n    chartGrid: darkModeColors.chartGrid,\n    chartLine: darkModeColors.chartLine,\n    chartAxis: baseColors.gray400,\n    chartTooltip: darkModeColors.tooltipBackground,\n\n    // Trading specific colors\n    profit: darkModeColors.profit,\n    loss: darkModeColors.loss,\n    neutral: darkModeColors.neutral,\n\n    // Tab colors\n    tabActive: baseColors.f1Red,\n    tabInactive: baseColors.gray600,\n\n    // Component specific colors\n    tooltipBackground: darkModeColors.tooltipBackground,\n    modalBackground: darkModeColors.modalBackground,\n    sidebarBackground: baseColors.gray800,\n    headerBackground: 'rgba(0, 0, 0, 0.2)',\n  },\n  spacing,\n  breakpoints,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n};\n", "/**\n * Light Theme\n *\n * This file contains the light theme for the ADHD Trading Dashboard.\n */\n\nimport { Theme } from './types';\nimport {\n  baseColors,\n  lightModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens';\n\n/**\n * Light Theme\n *\n * A light theme with red accents.\n */\nexport const lightTheme: Theme = {\n  name: 'light',\n  colors: {\n    // Primary colors\n    primary: baseColors.f1Red,\n    primaryDark: baseColors.f1RedDark,\n    primaryLight: baseColors.f1RedLight,\n\n    // Secondary colors\n    secondary: baseColors.f1Blue,\n    secondaryDark: baseColors.f1BlueDark,\n    secondaryLight: baseColors.f1BlueLight,\n\n    // Accent colors\n    accent: baseColors.purple,\n    accentDark: baseColors.purpleDark,\n    accentLight: baseColors.purpleLight,\n\n    // Status colors\n    success: lightModeColors.success,\n    warning: lightModeColors.warning,\n    error: lightModeColors.error,\n    info: lightModeColors.info,\n\n    // Neutral colors\n    background: lightModeColors.background,\n    surface: lightModeColors.surface,\n    cardBackground: lightModeColors.surface,\n    border: lightModeColors.border,\n    divider: baseColors.blackTransparent10,\n\n    // Text colors\n    textPrimary: lightModeColors.textPrimary,\n    textSecondary: lightModeColors.textSecondary,\n    textDisabled: lightModeColors.textDisabled,\n    textInverse: lightModeColors.textInverse,\n\n    // Chart colors\n    chartGrid: lightModeColors.chartGrid,\n    chartLine: lightModeColors.chartLine,\n    chartAxis: baseColors.gray600,\n    chartTooltip: lightModeColors.tooltipBackground,\n\n    // Trading specific colors\n    profit: lightModeColors.profit,\n    loss: lightModeColors.loss,\n    neutral: lightModeColors.neutral,\n\n    // Tab colors\n    tabActive: baseColors.f1Red,\n    tabInactive: baseColors.gray400,\n\n    // Component specific colors\n    tooltipBackground: lightModeColors.tooltipBackground,\n    modalBackground: lightModeColors.modalBackground,\n    sidebarBackground: baseColors.white,\n    headerBackground: 'rgba(0, 0, 0, 0.05)',\n  },\n  spacing,\n  breakpoints,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n};\n", "/**\n * Dark Theme\n *\n * This file contains the dark theme for the ADHD Trading Dashboard.\n */\n\nimport { Theme } from './types';\nimport {\n  baseColors,\n  darkModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens';\n\n/**\n * Dark Theme\n *\n * A dark theme with blue accents, distinct from the F1 theme.\n */\nexport const darkTheme: Theme = {\n  name: 'dark',\n  colors: {\n    // Primary colors (using blue as primary instead of red to differentiate from F1 theme)\n    primary: baseColors.f1Blue,\n    primaryDark: baseColors.f1BlueDark,\n    primaryLight: baseColors.f1BlueLight,\n\n    // Secondary colors\n    secondary: baseColors.f1Blue,\n    secondaryDark: baseColors.f1BlueDark,\n    secondaryLight: baseColors.f1BlueLight,\n\n    // Accent colors\n    accent: baseColors.purple,\n    accentDark: baseColors.purpleDark,\n    accentLight: baseColors.purpleLight,\n\n    // Status colors\n    success: darkModeColors.success,\n    warning: darkModeColors.warning,\n    error: darkModeColors.error,\n    info: darkModeColors.info,\n\n    // Neutral colors\n    background: baseColors.gray900, // Slightly different from F1 theme\n    surface: baseColors.gray800,\n    cardBackground: baseColors.gray800,\n    border: baseColors.gray700,\n    divider: 'rgba(255, 255, 255, 0.1)',\n\n    // Text colors\n    textPrimary: baseColors.white,\n    textSecondary: baseColors.gray300,\n    textDisabled: baseColors.gray500,\n    textInverse: baseColors.gray900,\n\n    // Chart colors\n    chartGrid: darkModeColors.chartGrid,\n    chartLine: baseColors.f1Blue, // Using blue instead of red\n    chartAxis: baseColors.gray400,\n    chartTooltip: darkModeColors.tooltipBackground,\n\n    // Trading specific colors\n    profit: darkModeColors.profit,\n    loss: darkModeColors.loss,\n    neutral: darkModeColors.neutral,\n\n    // Tab colors\n    tabActive: baseColors.f1Blue, // Using blue instead of red\n    tabInactive: baseColors.gray600,\n\n    // Component specific colors\n    tooltipBackground: 'rgba(26, 32, 44, 0.9)', // Slightly different from F1 theme\n    modalBackground: 'rgba(26, 32, 44, 0.8)',\n    sidebarBackground: baseColors.gray900,\n    headerBackground: 'rgba(0, 0, 0, 0.3)',\n  },\n  spacing,\n  breakpoints,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n};\n", "/**\n * Global Styles\n *\n * This file contains global styles for the application, including CSS reset and font loading.\n */\nimport { createGlobalStyle } from 'styled-components';\nimport { Theme } from './types';\n\n/**\n * Global Styles Component\n *\n * Provides CSS reset and global styles for the application.\n */\nexport const GlobalStyles = createGlobalStyle<{ theme: Theme }>`\n  /* Note: Font loading should be done in the HTML head, not in createGlobalStyle */\n  /* The @import rule in createGlobalStyle can cause FOUC (Flash of Unstyled Content) */\n\n  /* CSS Reset */\n  *, *::before, *::after {\n    box-sizing: border-box;\n  }\n\n  html, body, div, span, applet, object, iframe,\n  h1, h2, h3, h4, h5, h6, p, blockquote, pre,\n  a, abbr, acronym, address, big, cite, code,\n  del, dfn, em, img, ins, kbd, q, s, samp,\n  small, strike, strong, sub, sup, tt, var,\n  b, u, i, center,\n  dl, dt, dd, ol, ul, li,\n  fieldset, form, label, legend,\n  table, caption, tbody, tfoot, thead, tr, th, td,\n  article, aside, canvas, details, embed,\n  figure, figcaption, footer, header, hgroup,\n  menu, nav, output, ruby, section, summary,\n  time, mark, audio, video {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    font-size: 100%;\n    font: inherit;\n    vertical-align: baseline;\n  }\n\n  /* HTML5 display-role reset for older browsers */\n  article, aside, details, figcaption, figure,\n  footer, header, hgroup, menu, nav, section {\n    display: block;\n  }\n\n  body {\n    line-height: 1.5;\n    font-family: ${({ theme }) => theme.fontFamilies.body};\n    background-color: ${({ theme }) => theme.colors.background};\n    color: ${({ theme }) => theme.colors.textPrimary};\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  ol, ul {\n    list-style: none;\n  }\n\n  blockquote, q {\n    quotes: none;\n  }\n\n  blockquote:before, blockquote:after,\n  q:before, q:after {\n    content: '';\n    content: none;\n  }\n\n  table {\n    border-collapse: collapse;\n    border-spacing: 0;\n  }\n\n  a {\n    color: ${({ theme }) => theme.colors.primary};\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n\n  button, input, select, textarea {\n    font-family: inherit;\n    font-size: inherit;\n    line-height: inherit;\n  }\n\n  /* Scrollbar styling */\n  ::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: ${({ theme }) => theme.colors.background};\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: ${({ theme }) => theme.colors.border};\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: ${({ theme }) => theme.colors.primary};\n  }\n\n  /* Focus styles */\n  :focus {\n    outline: 2px solid ${({ theme }) => theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* Selection styles */\n  ::selection {\n    background-color: ${({ theme }) => theme.colors.primary};\n    color: ${({ theme }) => theme.colors.textInverse};\n  }\n`;\n\nexport default GlobalStyles;\n", "/**\n * Theme Provider Component\n *\n * This component provides the theme context to the application.\n */\n\nimport React, { useState, createContext, useContext } from 'react';\nimport { ThemeProvider as StyledThemeProvider, DefaultTheme } from 'styled-components';\nimport { Theme } from './types';\nimport { f1Theme } from './f1Theme';\nimport { lightTheme } from './lightTheme';\nimport { darkTheme } from './darkTheme';\nimport GlobalStyles from './GlobalStyles';\n\n// Map of available themes\nconst themes: Record<string, Theme> = {\n  f1: f1Theme,\n  light: lightTheme,\n  dark: darkTheme,\n};\n\n// Default theme\nconst defaultTheme = f1Theme;\n\n// Helper to get a theme by name\nconst getTheme = (themeName: string): Theme => {\n  return themes[themeName] || defaultTheme;\n};\n\n// Create theme context\nexport const ThemeContext = createContext<{\n  theme: Theme;\n  setTheme: (theme: Theme | string) => void;\n}>({\n  theme: defaultTheme,\n  setTheme: () => {},\n});\n\n// Hook to use the theme\nexport const useTheme = () => useContext(ThemeContext);\n\ninterface ThemeProviderProps {\n  /** The initial theme to use */\n  initialTheme?: string | Theme;\n  /** Whether to store the theme in local storage */\n  persistTheme?: boolean;\n  /** The key to use for storing the theme in local storage */\n  storageKey?: string;\n  /** The child components */\n  children: React.ReactNode;\n}\n\n/**\n * Theme Provider Component\n *\n * Provides theme context to the application and handles theme switching.\n */\nexport const ThemeProvider = ({\n  initialTheme = defaultTheme,\n  persistTheme = true,\n  storageKey = 'adhd-dashboard-theme',\n  children,\n}: ThemeProviderProps) => {\n  console.log('ThemeProvider rendering with initialTheme:', initialTheme);\n\n  // Initial theme setup\n  const [theme, setThemeState] = useState<Theme>(() => {\n    console.log('ThemeProvider initializing theme state');\n\n    // Try to load from localStorage\n    if (persistTheme && typeof window !== 'undefined') {\n      console.log('ThemeProvider attempting to load theme from localStorage');\n      const storedTheme = window.localStorage.getItem(storageKey);\n      console.log('ThemeProvider storedTheme:', storedTheme);\n\n      if (storedTheme) {\n        try {\n          // Try to get the theme by name first\n          console.log('ThemeProvider trying to get theme by name:', storedTheme);\n          const themeByName = getTheme(storedTheme);\n          if (themeByName) {\n            console.log('ThemeProvider found theme by name:', themeByName.name);\n            return themeByName;\n          }\n\n          // Otherwise, try to parse as JSON\n          console.log('ThemeProvider trying to parse theme as JSON');\n          const parsedTheme = JSON.parse(storedTheme) as Theme;\n          console.log('ThemeProvider parsed theme:', parsedTheme);\n          return parsedTheme;\n        } catch (error) {\n          console.error('Failed to parse stored theme:', error);\n        }\n      }\n    }\n\n    // Fall back to initial theme\n    console.log('ThemeProvider falling back to initial theme');\n    const resolvedTheme = typeof initialTheme === 'string' ? getTheme(initialTheme) : initialTheme;\n    console.log('ThemeProvider resolved theme:', resolvedTheme);\n    return resolvedTheme;\n  });\n\n  // Theme change handler\n  const setTheme = (newTheme: Theme | string) => {\n    const themeObject = typeof newTheme === 'string' ? getTheme(newTheme) : newTheme;\n    setThemeState(themeObject);\n\n    // Save to localStorage if enabled\n    if (persistTheme && typeof window !== 'undefined') {\n      window.localStorage.setItem(storageKey, themeObject.name || JSON.stringify(themeObject));\n    }\n  };\n\n  // Create a wrapper component to avoid TypeScript issues\n  const ThemeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n    console.log('ThemeWrapper rendering with theme:', theme.name);\n\n    // Add visible debugging element\n    const debugStyle = {\n      position: 'fixed',\n      top: '40px',\n      left: 0,\n      padding: '10px',\n      background: theme.colors.primary,\n      color: 'white',\n      zIndex: 9999,\n      fontSize: '16px',\n      fontFamily: 'monospace',\n    };\n\n    return (\n      <StyledThemeProvider theme={theme as DefaultTheme}>\n        <GlobalStyles />\n        <div style={debugStyle}>Theme: {theme.name}</div>\n        {children}\n      </StyledThemeProvider>\n    );\n  };\n\n  // Provide the theme context\n  console.log('ThemeProvider returning context with theme:', theme.name);\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme }}>\n      <ThemeWrapper>{children}</ThemeWrapper>\n    </ThemeContext.Provider>\n  );\n};\n", "/**\n * Sidebar Component\n *\n * This component displays the application sidebar with navigation links.\n */\n\nimport React from \"react\";\nimport { NavLink, useLocation } from \"react-router-dom\";\nimport styled from \"styled-components\";\n\n// Sidebar container\nconst SidebarContainer = styled.aside<{ isOpen: boolean }>`\n  height: 100%;\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-right: 1px solid ${({ theme }) => theme.colors.border};\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: width ${({ theme }) => theme.transitions.normal};\n`;\n\n// Logo container\nconst LogoContainer = styled.div<{ isOpen: boolean }>`\n  height: 64px;\n  display: flex;\n  align-items: center;\n  justify-content: ${({ isOpen }) => (isOpen ? \"flex-start\" : \"center\")};\n  padding: 0 ${({ theme, isOpen }) => (isOpen ? theme.spacing.md : \"0\")};\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\n// Logo\nconst Logo = styled.div<{ isOpen: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: bold;\n  color: ${({ theme }) => theme.colors.primary};\n  white-space: nowrap;\n  overflow: hidden;\n  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};\n  transition: opacity ${({ theme }) => theme.transitions.normal};\n`;\n\n// Navigation container\nconst NavContainer = styled.nav`\n  flex: 1;\n  overflow-y: auto;\n  padding: ${({ theme }) => theme.spacing.md} 0;\n`;\n\n// Navigation item\nconst NavItem = styled(NavLink)<{ $isOpen: boolean }>`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.sm}\n    ${({ theme, $isOpen }) => ($isOpen ? theme.spacing.md : \"0\")};\n  justify-content: ${({ $isOpen }) => ($isOpen ? \"flex-start\" : \"center\")};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  text-decoration: none;\n  transition: background-color ${({ theme }) => theme.transitions.fast},\n    color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.05);\n    color: ${({ theme }) => theme.colors.textPrimary};\n  }\n\n  &.active {\n    color: ${({ theme }) => theme.colors.primary};\n    background-color: rgba(255, 255, 255, 0.05);\n    border-left: 3px solid ${({ theme }) => theme.colors.primary};\n  }\n`;\n\n// Icon\nconst Icon = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\n// Label\nconst Label = styled.span<{ isOpen: boolean }>`\n  white-space: nowrap;\n  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};\n  transition: opacity ${({ theme }) => theme.transitions.normal};\n  overflow: hidden;\n  max-width: ${({ isOpen }) => (isOpen ? \"200px\" : \"0\")};\n`;\n\n// Footer\nconst Footer = styled.div<{ isOpen: boolean }>`\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  white-space: nowrap;\n  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};\n  transition: opacity ${({ theme }) => theme.transitions.normal};\n  text-align: center;\n`;\n\ninterface SidebarProps {\n  isOpen: boolean;\n}\n\n/**\n * Sidebar Component\n */\nconst Sidebar: React.FC<SidebarProps> = ({ isOpen }) => {\n  const location = useLocation();\n\n  const navItems = [\n    { path: \"/\", label: \"Dashboard\", icon: \"📊\" },\n    { path: \"/daily-guide\", label: \"Daily Guide\", icon: \"📅\" },\n    { path: \"/journal\", label: \"Trade Journal\", icon: \"📓\" },\n    { path: \"/analysis\", label: \"Analysis\", icon: \"📈\" },\n    { path: \"/settings\", label: \"Settings\", icon: \"⚙️\" },\n  ];\n\n  return (\n    <SidebarContainer isOpen={isOpen}>\n      <LogoContainer isOpen={isOpen}>\n        <Logo isOpen={isOpen}>ADHD</Logo>\n      </LogoContainer>\n\n      <NavContainer>\n        {navItems.map((item) => (\n          <NavItem\n            key={item.path}\n            to={item.path}\n            $isOpen={isOpen}\n            className={location.pathname === item.path ? \"active\" : \"\"}\n          >\n            <Icon>{item.icon}</Icon>\n            <Label isOpen={isOpen}>{item.label}</Label>\n          </NavItem>\n        ))}\n      </NavContainer>\n\n      <Footer isOpen={isOpen}>v1.0.0</Footer>\n    </SidebarContainer>\n  );\n};\n\nexport default Sidebar;\n", "/**\n * Header Component\n *\n * This component displays the application header with navigation controls.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\n// Header container\nconst HeaderContainer = styled.header`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 64px;\n  padding: 0 ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\n// Left section\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\n// Hamburger menu button\nconst MenuButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  cursor: pointer;\n  transition: color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\n// Logo\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  margin-left: ${({ theme }) => theme.spacing.sm};\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: bold;\n`;\n\n// Right section\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\n// User menu\nconst UserMenu = styled.div`\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\n// Avatar\nconst Avatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: ${({ theme }) => theme.colors.primary};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n`;\n\ninterface HeaderProps {\n  toggleSidebar: () => void;\n  sidebarOpen: boolean;\n}\n\n/**\n * Header Component\n */\nconst Header: React.FC<HeaderProps> = ({ toggleSidebar, sidebarOpen }) => {\n  return (\n    <HeaderContainer>\n      <LeftSection>\n        <MenuButton onClick={toggleSidebar} aria-label=\"Toggle sidebar\">\n          {sidebarOpen ? <span>☰</span> : <span>☰</span>}\n        </MenuButton>\n        <Logo>ADHD Trading Dashboard</Logo>\n      </LeftSection>\n\n      <RightSection>\n        <UserMenu>\n          <Avatar>JD</Avatar>\n        </UserMenu>\n      </RightSection>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n", "/**\n * Main Layout Component\n *\n * This component provides the main layout structure for the application.\n */\n\nimport React, { useState } from \"react\";\nimport { Outlet } from \"react-router-dom\";\nimport styled from \"styled-components\";\nimport Sidebar from \"./Sidebar\";\nimport Header from \"./Header\";\n\n// Main container\nconst LayoutContainer = styled.div`\n  display: flex;\n  height: 100vh;\n  width: 100%;\n  overflow: hidden;\n  background-color: ${({ theme }) => theme.colors.background};\n`;\n\n// Sidebar container\nconst SidebarContainer = styled.div<{ isOpen: boolean }>`\n  width: ${({ isOpen }) => (isOpen ? \"240px\" : \"64px\")};\n  height: 100%;\n  transition: width ${({ theme }) => theme.transitions.normal};\n  flex-shrink: 0;\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {\n    position: fixed;\n    z-index: ${({ theme }) => theme.zIndex.fixed};\n    width: ${({ isOpen }) => (isOpen ? \"240px\" : \"0\")};\n    box-shadow: ${({ isOpen, theme }) => (isOpen ? theme.shadows.lg : \"none\")};\n  }\n`;\n\n// Main content container\nconst ContentContainer = styled.div<{ sidebarOpen: boolean }>`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: margin-left ${({ theme }) => theme.transitions.normal};\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {\n    margin-left: 0;\n    width: 100%;\n  }\n`;\n\n// Header container\nconst HeaderContainer = styled.div`\n  height: 64px;\n  flex-shrink: 0;\n`;\n\n// Main content\nconst MainContent = styled.main`\n  flex: 1;\n  overflow: auto;\n  padding: ${({ theme }) => theme.spacing.lg};\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {\n    padding: ${({ theme }) => theme.spacing.md};\n  }\n`;\n\n// Overlay for mobile sidebar\nconst Overlay = styled.div<{ isVisible: boolean }>`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: ${({ theme }) => theme.zIndex.modal - 1};\n  opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};\n  visibility: ${({ isVisible }) => (isVisible ? \"visible\" : \"hidden\")};\n  transition: opacity ${({ theme }) => theme.transitions.normal},\n    visibility ${({ theme }) => theme.transitions.normal};\n\n  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {\n    display: none;\n  }\n`;\n\n/**\n * Main Layout Component\n */\nconst MainLayout: React.FC = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  // Toggle sidebar\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  // Close sidebar (for mobile)\n  const closeSidebar = () => {\n    setSidebarOpen(false);\n  };\n\n  return (\n    <LayoutContainer>\n      {/* Sidebar */}\n      <SidebarContainer isOpen={sidebarOpen}>\n        <Sidebar isOpen={sidebarOpen} />\n      </SidebarContainer>\n\n      {/* Overlay for mobile */}\n      <Overlay isVisible={sidebarOpen} onClick={closeSidebar} />\n\n      {/* Main content */}\n      <ContentContainer sidebarOpen={sidebarOpen}>\n        <HeaderContainer>\n          <Header toggleSidebar={toggleSidebar} sidebarOpen={sidebarOpen} />\n        </HeaderContainer>\n\n        <MainContent>\n          <Outlet />\n        </MainContent>\n      </ContentContainer>\n    </LayoutContainer>\n  );\n};\n\nexport default MainLayout;\n", "/**\n * Loading Screen Component\n *\n * Displays a loading animation while content is being loaded.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Spinner = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 3px solid rgba(255, 255, 255, 0.1);\n  border-top-color: ${({ theme }) => theme.colors.primary};\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    to {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\nconst LoadingText = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n`;\n\n/**\n * Loading Screen Component\n */\nconst LoadingScreen: React.FC = () => {\n  return (\n    <LoadingContainer>\n      <Spinner />\n      <LoadingText>Loading...</LoadingText>\n    </LoadingContainer>\n  );\n};\n\nexport default LoadingScreen;\n", "/**\n * Application Routes\n *\n * This file defines the application routes using React Router.\n */\n\nimport React, { lazy, Suspense } from \"react\";\nimport { Routes, Route, Navigate } from \"react-router-dom\";\nimport MainLayout from \"../layouts/MainLayout\";\nimport LoadingScreen from \"../components/molecules/LoadingScreen\";\n\n// Lazy-loaded feature components for code splitting\nconst Dashboard = lazy(\n  () => import(\"../features/performance-dashboard/Dashboard\")\n);\nconst DailyGuide = lazy(() => import(\"../features/daily-guide/DailyGuide\"));\nconst TradeJournal = lazy(\n  () => import(\"../features/trade-journal/TradeJournal\")\n);\nconst TradeAnalysis = lazy(\n  () => import(\"../features/trade-analysis/TradeAnalysis\")\n);\nconst TradeForm = lazy(() => import(\"../features/trade-journal/TradeForm\"));\nconst Settings = lazy(() => import(\"../features/settings/Settings\"));\nconst NotFound = lazy(() => import(\"../components/NotFound\"));\n\n/**\n * AppRoutes Component\n *\n * Defines the application routes using React Router.\n */\nconst AppRoutes: React.FC = () => {\n  return (\n    <Suspense fallback={<LoadingScreen />}>\n      <Routes>\n        {/* Main layout routes */}\n        <Route path=\"/\" element={<MainLayout />}>\n          <Route index element={<Dashboard />} />\n          <Route path=\"daily-guide\" element={<DailyGuide />} />\n          <Route path=\"journal\" element={<TradeJournal />} />\n          <Route path=\"analysis\" element={<TradeAnalysis />} />\n          <Route path=\"trade/new\" element={<TradeForm />} />\n          <Route path=\"trade/:id\" element={<TradeForm />} />\n          <Route path=\"settings\" element={<Settings />} />\n          <Route path=\"*\" element={<NotFound />} />\n        </Route>\n\n        {/* Redirect from legacy paths */}\n        <Route path=\"/dashboard\" element={<Navigate to=\"/\" replace />} />\n      </Routes>\n    </Suspense>\n  );\n};\n\nexport default AppRoutes;\n", "/**\n * App Error Boundary\n *\n * A top-level error boundary for the entire application.\n * This is a simplified version that uses the unified error boundary approach.\n */\nimport React from 'react';\nimport { AppErrorBoundary as UnifiedAppErrorBoundary } from '@adhd-trading-dashboard/shared';\n\n/**\n * App Error Boundary Props\n */\nexport interface AppErrorBoundaryProps {\n  children: React.ReactNode;\n}\n\n/**\n * App Error Boundary\n *\n * A top-level error boundary for the entire application.\n */\nexport const AppErrorBoundary: React.FC<AppErrorBoundaryProps> = ({ children }) => {\n  const handleError = (error: Error) => {\n    // Log the error to the console\n    console.error('Application Error:', error);\n\n    // Here you could also log to an error tracking service like Sentry\n    // if (typeof window !== 'undefined' && window.Sentry) {\n    //   window.Sentry.withScope((scope) => {\n    //     scope.setTag('boundary', 'app');\n    //     window.Sentry.captureException(error);\n    //   });\n    // }\n  };\n\n  return (\n    <UnifiedAppErrorBoundary onError={handleError} name=\"Application\">\n      {children}\n    </UnifiedAppErrorBoundary>\n  );\n};\n\nexport default AppErrorBoundary;\n", "import React, { useEffect, useState } from 'react';\nimport { <PERSON><PERSON>erRouter } from 'react-router-dom';\nimport { ThemeProvider } from '@adhd-trading-dashboard/shared';\nimport { AppRoutes } from './routes';\nimport AppErrorBoundary from './components/AppErrorBoundary';\n\n/**\n * Main App component for the ADHD Trading Dashboard\n * Using BrowserRouter for better URL structure\n */\nfunction App() {\n  const [renderCount, setRenderCount] = useState(0);\n\n  // Debug logging for component mounting\n  useEffect(() => {\n    console.log('App component mounted');\n\n    // Log DOM structure to check if elements are being rendered\n    console.log('Root element:', document.getElementById('root'));\n    console.log('Body children count:', document.body.children.length);\n\n    // Increment render count to track re-renders\n    setRenderCount((prev) => prev + 1);\n\n    return () => {\n      console.log('App component unmounted');\n    };\n  }, []);\n\n  // Log each render\n  console.log(`App rendering (count: ${renderCount})`);\n\n  // Add visible debugging element\n  const debugStyle = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    padding: '10px',\n    background: 'red',\n    color: 'white',\n    zIndex: 9999,\n    fontSize: '16px',\n    fontFamily: 'monospace',\n  };\n\n  return (\n    <AppErrorBoundary>\n      <ThemeProvider initialTheme=\"f1\">\n        <BrowserRouter>\n          <div style={debugStyle}>App Rendered: {renderCount}</div>\n          <AppRoutes />\n        </BrowserRouter>\n      </ThemeProvider>\n    </AppErrorBoundary>\n  );\n}\n\nexport default App;\n", "/**\n * reportWebVitals.ts\n *\n * Web Vitals reporting utility\n */\n\nimport { ReportHandler } from \"web-vitals\";\n\nconst reportWebVitals = (onPerfEntry?: ReportHandler) => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import(\"web-vitals\").then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n", "/**\n * ADHD Trading Dashboard - Main Entry Point\n */\n// Import DevTools configuration first\nimport './devtools-config';\n\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\nimport './styles/variables.css';\nimport './styles/global.css';\nimport './styles/f1-theme.css';\n\n// IMPORTANT: All code must be after imports\nconst main = () => {\n  console.log('ADHD Trading Dashboard initializing...');\n\n  // Get the root element\n  const rootElement = document.getElementById('root');\n\n  // Create a fallback root element if needed\n  if (!rootElement) {\n    console.error('Root element not found, creating a fallback element');\n    const fallbackRoot = document.createElement('div');\n    fallbackRoot.id = 'root';\n    document.body.appendChild(fallbackRoot);\n  }\n\n  // Create the React root\n  const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);\n\n  // Render the app\n  root.render(\n    <React.StrictMode>\n      <App />\n    </React.StrictMode>\n  );\n\n  // Performance measurement\n  reportWebVitals();\n};\n\n// Simple error handler\nwindow.addEventListener('error', (event) => {\n  console.error('Error:', event.error || event.message);\n});\n\n// Execute the main function\nmain();\n"], "file": "assets/main-2920b47d.js"}