import{a as M,r as s}from"./react-60374de9.js";/**
 * @remix-run/router v1.2.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},B.apply(this,arguments)}var C;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(C||(C={}));const H="popstate";function ce(e){e===void 0&&(e={});function t(n,a){let{pathname:o,search:l,hash:i}=n.location;return T("",{pathname:o,search:l,hash:i},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:b(a)}return de(t,r,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function he(){return Math.random().toString(36).substr(2,8)}function K(e){return{usr:e.state,key:e.key}}function T(e,t,r,n){return r===void 0&&(r=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?R(t):t,{state:r,key:t&&t.key||n||he()})}function b(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function R(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function fe(e){let t=typeof window<"u"&&typeof window.location<"u"&&window.location.origin!=="null"?window.location.origin:window.location.href,r=typeof e=="string"?e:b(e);return v(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}function de(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:o=!1}=n,l=a.history,i=C.Pop,u=null;function c(){i=C.Pop,u&&u({action:i,location:m.location})}function h(d,g){i=C.Push;let f=T(m.location,d,g);r&&r(f,d);let x=K(f),y=m.createHref(f);try{l.pushState(x,"",y)}catch{a.location.assign(y)}o&&u&&u({action:i,location:m.location})}function p(d,g){i=C.Replace;let f=T(m.location,d,g);r&&r(f,d);let x=K(f),y=m.createHref(f);l.replaceState(x,"",y),o&&u&&u({action:i,location:m.location})}let m={get action(){return i},get location(){return e(a,l)},listen(d){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener(H,c),u=d,()=>{a.removeEventListener(H,c),u=null}},createHref(d){return t(a,d)},encodeLocation(d){let g=fe(typeof d=="string"?d:b(d));return{pathname:g.pathname,search:g.search,hash:g.hash}},push:h,replace:p,go(d){return l.go(d)}};return m}var z;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(z||(z={}));function pe(e,t,r){r===void 0&&(r="/");let n=typeof t=="string"?R(t):t,a=ee(n.pathname||"/",r);if(a==null)return null;let o=Q(e);me(o);let l=null;for(let i=0;l==null&&i<o.length;++i)l=Re(o[i],Le(a));return l}function Q(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(o,l,i)=>{let u={relativePath:i===void 0?o.path||"":i,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};u.relativePath.startsWith("/")&&(v(u.relativePath.startsWith(n),'Absolute route path "'+u.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),u.relativePath=u.relativePath.slice(n.length));let c=E([n,u.relativePath]),h=r.concat(u);o.children&&o.children.length>0&&(v(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Q(o.children,t,h,c)),!(o.path==null&&!o.index)&&t.push({path:c,score:Pe(c,o.index),routesMeta:h})};return e.forEach((o,l)=>{var i;if(o.path===""||!((i=o.path)!=null&&i.includes("?")))a(o,l);else for(let u of Z(o.path))a(o,l,u)}),t}function Z(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let l=Z(n.join("/")),i=[];return i.push(...l.map(u=>u===""?o:[o,u].join("/"))),a&&i.push(...l),i.map(u=>e.startsWith("/")&&u===""?"/":u)}function me(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:we(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const ve=/^:\w+$/,ge=3,ye=2,xe=1,Ce=10,Ee=-2,A=e=>e==="*";function Pe(e,t){let r=e.split("/"),n=r.length;return r.some(A)&&(n+=Ee),t&&(n+=ye),r.filter(a=>!A(a)).reduce((a,o)=>a+(ve.test(o)?ge:o===""?xe:Ce),n)}function we(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Re(e,t){let{routesMeta:r}=e,n={},a="/",o=[];for(let l=0;l<r.length;++l){let i=r[l],u=l===r.length-1,c=a==="/"?t:t.slice(a.length)||"/",h=Se({path:i.relativePath,caseSensitive:i.caseSensitive,end:u},c);if(!h)return null;Object.assign(n,h.params);let p=i.route;o.push({params:n,pathname:E([a,h.pathname]),pathnameBase:je(E([a,h.pathnameBase])),route:p}),h.pathnameBase!=="/"&&(a=E([a,h.pathnameBase]))}return o}function Se(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=be(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:n.reduce((c,h,p)=>{if(h==="*"){let m=i[p]||"";l=o.slice(0,o.length-m.length).replace(/(.)\/+$/,"$1")}return c[h]=Oe(i[p]||"",h),c},{}),pathname:o,pathnameBase:l,pattern:e}}function be(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),J(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^$?{}|()[\]]/g,"\\$&").replace(/\/:(\w+)/g,(l,i)=>(n.push(i),"/([^\\/]+)"));return e.endsWith("*")?(n.push("*"),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Le(e){try{return decodeURI(e)}catch(t){return J(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Oe(e,t){try{return decodeURIComponent(e)}catch(r){return J(!1,'The value for the URL param "'+t+'" will not be decoded because'+(' the string "'+e+'" is a malformed URL segment. This is probably')+(" due to a bad percent encoding ("+r+").")),e}}function ee(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function J(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Ue(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?R(e):e;return{pathname:r?r.startsWith("/")?r:Be(r,t):t,search:$e(n),hash:ke(a)}}function Be(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function W(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function te(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function ne(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=R(e):(a=B({},e),v(!a.pathname||!a.pathname.includes("?"),W("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),W("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),W("#","search","hash",a)));let o=e===""||a.pathname==="",l=o?"/":a.pathname,i;if(n||l==null)i=r;else{let p=t.length-1;if(l.startsWith("..")){let m=l.split("/");for(;m[0]==="..";)m.shift(),p-=1;a.pathname=m.join("/")}i=p>=0?t[p]:"/"}let u=Ue(a,i),c=l&&l!=="/"&&l.endsWith("/"),h=(o||l===".")&&r.endsWith("/");return!u.pathname.endsWith("/")&&(c||h)&&(u.pathname+="/"),u}const E=e=>e.join("/").replace(/\/\/+/g,"/"),je=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),$e=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,ke=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class Ie{constructor(t,r,n,a){a===void 0&&(a=!1),this.status=t,this.statusText=r||"",this.internal=a,n instanceof Error?(this.data=n.toString(),this.error=n):this.data=n}}function Ne(e){return e instanceof Ie}const re=["post","put","patch","delete"];new Set(re);const We=["get",...re];new Set(We);/**
 * React Router v6.6.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},F.apply(this,arguments)}function De(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const Me=typeof Object.is=="function"?Object.is:De,{useState:Te,useEffect:Fe,useLayoutEffect:Ve,useDebugValue:Je}=M;function _e(e,t,r){const n=t(),[{inst:a},o]=Te({inst:{value:n,getSnapshot:t}});return Ve(()=>{a.value=n,a.getSnapshot=t,D(a)&&o({inst:a})},[e,n,t]),Fe(()=>(D(a)&&o({inst:a}),e(()=>{D(a)&&o({inst:a})})),[e]),Je(n),n}function D(e){const t=e.getSnapshot,r=e.value;try{const n=t();return!Me(r,n)}catch{return!0}}function He(e,t,r){return t()}const Ke=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ze=!Ke,Ae=ze?He:_e;"useSyncExternalStore"in M&&(e=>e.useSyncExternalStore)(M);const ae=s.createContext(null),k=s.createContext(null),L=s.createContext(null),I=s.createContext(null),P=s.createContext({outlet:null,matches:[]}),oe=s.createContext(null);function qe(e,t){let{relative:r}=t===void 0?{}:t;S()||v(!1);let{basename:n,navigator:a}=s.useContext(L),{hash:o,pathname:l,search:i}=_(e,{relative:r}),u=l;return n!=="/"&&(u=l==="/"?n:E([n,l])),a.createHref({pathname:u,search:i,hash:o})}function S(){return s.useContext(I)!=null}function O(){return S()||v(!1),s.useContext(I).location}function le(){S()||v(!1);let{basename:e,navigator:t}=s.useContext(L),{matches:r}=s.useContext(P),{pathname:n}=O(),a=JSON.stringify(te(r).map(i=>i.pathnameBase)),o=s.useRef(!1);return s.useEffect(()=>{o.current=!0}),s.useCallback(function(i,u){if(u===void 0&&(u={}),!o.current)return;if(typeof i=="number"){t.go(i);return}let c=ne(i,JSON.parse(a),n,u.relative==="path");e!=="/"&&(c.pathname=c.pathname==="/"?e:E([e,c.pathname])),(u.replace?t.replace:t.push)(c,u.state,u)},[e,t,a,n])}const Ge=s.createContext(null);function Xe(e){let t=s.useContext(P).outlet;return t&&s.createElement(Ge.Provider,{value:e},t)}function mt(){let{matches:e}=s.useContext(P),t=e[e.length-1];return t?t.params:{}}function _(e,t){let{relative:r}=t===void 0?{}:t,{matches:n}=s.useContext(P),{pathname:a}=O(),o=JSON.stringify(te(n).map(l=>l.pathnameBase));return s.useMemo(()=>ne(e,JSON.parse(o),a,r==="path"),[e,o,a,r])}function Ye(e,t){S()||v(!1);let{navigator:r}=s.useContext(L),n=s.useContext(k),{matches:a}=s.useContext(P),o=a[a.length-1],l=o?o.params:{};o&&o.pathname;let i=o?o.pathnameBase:"/";o&&o.route;let u=O(),c;if(t){var h;let f=typeof t=="string"?R(t):t;i==="/"||(h=f.pathname)!=null&&h.startsWith(i)||v(!1),c=f}else c=u;let p=c.pathname||"/",m=i==="/"?p:p.slice(i.length)||"/",d=pe(e,{pathname:m}),g=tt(d&&d.map(f=>Object.assign({},f,{params:Object.assign({},l,f.params),pathname:E([i,r.encodeLocation?r.encodeLocation(f.pathname).pathname:f.pathname]),pathnameBase:f.pathnameBase==="/"?i:E([i,r.encodeLocation?r.encodeLocation(f.pathnameBase).pathname:f.pathnameBase])})),a,n||void 0);return t&&g?s.createElement(I.Provider,{value:{location:F({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:C.Pop}},g):g}function Qe(){let e=ot(),t=Ne(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n};return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unhandled Thrown Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),r?s.createElement("pre",{style:a},r):null,s.createElement("p",null,"💿 Hey developer 👋"),s.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",s.createElement("code",{style:o},"errorElement")," props on ",s.createElement("code",{style:o},"<Route>")))}class Ze extends s.Component{constructor(t){super(t),this.state={location:t.location,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location?{error:t.error,location:t.location}:{error:t.error||r.error,location:r.location}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error?s.createElement(P.Provider,{value:this.props.routeContext},s.createElement(oe.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function et(e){let{routeContext:t,match:r,children:n}=e,a=s.useContext(ae);return a&&a.static&&a.staticContext&&r.route.errorElement&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),s.createElement(P.Provider,{value:t},n)}function tt(e,t,r){if(t===void 0&&(t=[]),e==null)if(r!=null&&r.errors)e=r.matches;else return null;let n=e,a=r==null?void 0:r.errors;if(a!=null){let o=n.findIndex(l=>l.route.id&&(a==null?void 0:a[l.route.id]));o>=0||v(!1),n=n.slice(0,Math.min(n.length,o+1))}return n.reduceRight((o,l,i)=>{let u=l.route.id?a==null?void 0:a[l.route.id]:null,c=r?l.route.errorElement||s.createElement(Qe,null):null,h=t.concat(n.slice(0,i+1)),p=()=>s.createElement(et,{match:l,routeContext:{outlet:o,matches:h}},u?c:l.route.element!==void 0?l.route.element:o);return r&&(l.route.errorElement||i===0)?s.createElement(Ze,{location:r.location,component:c,error:u,children:p(),routeContext:{outlet:null,matches:h}}):p()},null)}var q;(function(e){e.UseRevalidator="useRevalidator"})(q||(q={}));var j;(function(e){e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator"})(j||(j={}));function nt(e){let t=s.useContext(k);return t||v(!1),t}function rt(e){let t=s.useContext(P);return t||v(!1),t}function at(e){let t=rt(),r=t.matches[t.matches.length-1];return r.route.id||v(!1),r.route.id}function ot(){var e;let t=s.useContext(oe),r=nt(j.UseRouteError),n=at(j.UseRouteError);return t||((e=r.errors)==null?void 0:e[n])}function vt(e){let{to:t,replace:r,state:n,relative:a}=e;S()||v(!1);let o=s.useContext(k),l=le();return s.useEffect(()=>{o&&o.navigation.state!=="idle"||l(t,{replace:r,state:n,relative:a})}),null}function gt(e){return Xe(e.context)}function lt(e){v(!1)}function it(e){let{basename:t="/",children:r=null,location:n,navigationType:a=C.Pop,navigator:o,static:l=!1}=e;S()&&v(!1);let i=t.replace(/^\/*/,"/"),u=s.useMemo(()=>({basename:i,navigator:o,static:l}),[i,o,l]);typeof n=="string"&&(n=R(n));let{pathname:c="/",search:h="",hash:p="",state:m=null,key:d="default"}=n,g=s.useMemo(()=>{let f=ee(c,i);return f==null?null:{pathname:f,search:h,hash:p,state:m,key:d}},[i,c,h,p,m,d]);return g==null?null:s.createElement(L.Provider,{value:u},s.createElement(I.Provider,{children:r,value:{location:g,navigationType:a}}))}function yt(e){let{children:t,location:r}=e,n=s.useContext(ae),a=n&&!t?n.router.routes:V(t);return Ye(a,r)}var G;(function(e){e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error"})(G||(G={}));new Promise(()=>{});function V(e,t){t===void 0&&(t=[]);let r=[];return s.Children.forEach(e,(n,a)=>{if(!s.isValidElement(n))return;if(n.type===s.Fragment){r.push.apply(r,V(n.props.children,t));return}n.type!==lt&&v(!1),!n.props.index||!n.props.children||v(!1);let o=[...t,a],l={id:n.props.id||o.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,hasErrorBoundary:n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle};n.props.children&&(l.children=V(n.props.children,o)),r.push(l)}),r}/**
 * React Router DOM v6.6.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$.apply(this,arguments)}function ie(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,o;for(o=0;o<n.length;o++)a=n[o],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function st(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ut(e,t){return e.button===0&&(!t||t==="_self")&&!st(e)}const ct=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset"],ht=["aria-current","caseSensitive","className","end","style","to","children"];function xt(e){let{basename:t,children:r,window:n}=e,a=s.useRef();a.current==null&&(a.current=ce({window:n,v5Compat:!0}));let o=a.current,[l,i]=s.useState({action:o.action,location:o.location});return s.useLayoutEffect(()=>o.listen(i),[o]),s.createElement(it,{basename:t,children:r,location:l.location,navigationType:l.action,navigator:o})}const ft=s.forwardRef(function(t,r){let{onClick:n,relative:a,reloadDocument:o,replace:l,state:i,target:u,to:c,preventScrollReset:h}=t,p=ie(t,ct),m=qe(c,{relative:a}),d=dt(c,{replace:l,state:i,target:u,preventScrollReset:h,relative:a});function g(f){n&&n(f),f.defaultPrevented||d(f)}return s.createElement("a",$({},p,{href:m,onClick:o?n:g,ref:r,target:u}))}),Ct=s.forwardRef(function(t,r){let{"aria-current":n="page",caseSensitive:a=!1,className:o="",end:l=!1,style:i,to:u,children:c}=t,h=ie(t,ht),p=_(u,{relative:h.relative}),m=O(),d=s.useContext(k),{navigator:g}=s.useContext(L),f=g.encodeLocation?g.encodeLocation(p).pathname:p.pathname,x=m.pathname,y=d&&d.navigation&&d.navigation.location?d.navigation.location.pathname:null;a||(x=x.toLowerCase(),y=y?y.toLowerCase():null,f=f.toLowerCase());let w=x===f||!l&&x.startsWith(f)&&x.charAt(f.length)==="/",U=y!=null&&(y===f||!l&&y.startsWith(f)&&y.charAt(f.length)==="/"),se=w?n:void 0,N;typeof o=="function"?N=o({isActive:w,isPending:U}):N=[o,w?"active":null,U?"pending":null].filter(Boolean).join(" ");let ue=typeof i=="function"?i({isActive:w,isPending:U}):i;return s.createElement(ft,$({},h,{"aria-current":se,className:N,ref:r,style:ue,to:u}),typeof c=="function"?c({isActive:w,isPending:U}):c)});var X;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmitImpl="useSubmitImpl",e.UseFetcher="useFetcher"})(X||(X={}));var Y;(function(e){e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Y||(Y={}));function dt(e,t){let{target:r,replace:n,state:a,preventScrollReset:o,relative:l}=t===void 0?{}:t,i=le(),u=O(),c=_(e,{relative:l});return s.useCallback(h=>{if(ut(h,r)){h.preventDefault();let p=n!==void 0?n:b(u)===b(c);i(e,{replace:p,state:a,preventScrollReset:o,relative:l})}},[u,i,c,n,a,r,e,o,l])}export{xt as B,ft as L,Ct as N,gt as O,yt as R,lt as a,vt as b,le as c,mt as d,O as u};
//# sourceMappingURL=router-e715efa2.js.map
