{"version": 3, "file": "DailyGuide-6bd79e86.js", "sources": ["../../../shared/src/components/atoms/Input.tsx", "../../../shared/src/components/molecules/FormField.tsx", "../../src/features/daily-guide/components/MarketOverview.tsx", "../../src/features/daily-guide/components/TradingPlan.tsx", "../../src/features/daily-guide/components/KeyLevels.tsx", "../../src/features/daily-guide/api/dailyGuideApi.ts", "../../src/features/daily-guide/context/DailyGuideContext.tsx", "../../src/features/daily-guide/components/MarketNews.tsx", "../../src/features/daily-guide/components/ui/SectionCard.tsx", "../../src/features/daily-guide/DailyGuide.tsx"], "sourcesContent": ["/**\n * Input Component\n *\n * A customizable input component that follows the design system.\n */\nimport React, { useState, useRef } from 'react';\nimport styled, { css } from 'styled-components';\n\n// Create a custom type that extends the HTML input attributes but overrides the size property\ntype CustomInputHTMLAttributes = Omit<\n  React.InputHTMLAttributes<HTMLInputElement>,\n  'onChange' | 'size'\n> & {\n  size?: 'small' | 'medium' | 'large';\n};\n\nexport interface InputProps extends CustomInputHTMLAttributes {\n  /** The value of the input */\n  value: string;\n  /** Function called when the input value changes */\n  onChange: (value: string) => void;\n  /** The placeholder text */\n  placeholder?: string;\n  /** Whether the input is disabled */\n  disabled?: boolean;\n  /** The error message */\n  error?: string;\n  /** The input type */\n  type?: string;\n  /** The input name */\n  name?: string;\n  /** The input id */\n  id?: string;\n  /** Additional CSS class names */\n  className?: string;\n  /** Whether the input is required */\n  required?: boolean;\n  /** Input autocomplete attribute */\n  autoComplete?: string;\n  /** Label for the input */\n  label?: string;\n  /** Helper text to display below the input */\n  helperText?: string;\n  /** Icon to display at the start of the input */\n  startIcon?: React.ReactNode;\n  /** Icon to display at the end of the input */\n  endIcon?: React.ReactNode;\n  /** Whether the input is in a loading state */\n  loading?: boolean;\n  /** Whether the input is in a success state */\n  success?: boolean;\n  /** Whether the input should have a clear button */\n  clearable?: boolean;\n  /** Function called when the input is cleared */\n  onClear?: () => void;\n  /** Maximum character count */\n  maxLength?: number;\n  /** Whether to show character count */\n  showCharCount?: boolean;\n  /** Size of the input */\n  size?: 'small' | 'medium' | 'large';\n  /** Whether the input should be full width */\n  fullWidth?: boolean;\n}\n\n// Define the props that InputWrapper will accept\ntype InputWrapperProps = {\n  fullWidth?: boolean;\n};\n\nconst InputWrapper = styled.div<InputWrapperProps>`\n  display: flex;\n  flex-direction: column;\n  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};\n  position: relative;\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xxs};\n  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};\n`;\n\n// Define the props that InputContainer will accept\ntype InputContainerProps = {\n  hasError?: boolean;\n  hasSuccess?: boolean;\n  disabled?: boolean;\n  size?: 'small' | 'medium' | 'large';\n  hasStartIcon?: boolean;\n  hasEndIcon?: boolean;\n  isFocused?: boolean;\n};\n\nconst InputContainer = styled.div<InputContainerProps>`\n  display: flex;\n  align-items: center;\n  position: relative;\n  width: 100%;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  border: 1px solid\n    ${({ theme, hasError, hasSuccess, isFocused }) => {\n      if (hasError) return theme.colors.error;\n      if (hasSuccess) return theme.colors.success;\n      if (isFocused) return theme.colors.primary;\n      return theme.colors.border;\n    }};\n  background-color: ${({ theme }) => theme.colors.surface};\n  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};\n\n  ${({ disabled, theme }) =>\n    disabled &&\n    css`\n      opacity: 0.6;\n      background-color: ${theme.colors.background};\n      cursor: not-allowed;\n    `}\n\n  ${({ isFocused, theme, hasError, hasSuccess }) =>\n    isFocused &&\n    css`\n      box-shadow: 0 0 0 2px\n        ${hasError\n          ? `${theme.colors.error}33`\n          : hasSuccess\n          ? `${theme.colors.success}33`\n          : `${theme.colors.primary}33`};\n    `}\n\n  ${({ size }) => {\n    switch (size) {\n      case 'small':\n        return css`\n          height: 32px;\n        `;\n      case 'large':\n        return css`\n          height: 48px;\n        `;\n      default:\n        return css`\n          height: 40px;\n        `;\n    }\n  }}\n`;\n\nconst IconContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\n// Define the props that StyledInput will accept\ntype StyledInputProps = {\n  hasStartIcon?: boolean;\n  hasEndIcon?: boolean;\n  size?: string; // Changed from enum to string to fix type compatibility\n};\n\nconst StyledInput = styled.input<StyledInputProps>`\n  flex: 1;\n  border: none;\n  background: transparent;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  width: 100%;\n  outline: none;\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n\n  &::placeholder {\n    color: ${({ theme }) => theme.colors.textDisabled};\n  }\n\n  ${({ hasStartIcon }) =>\n    hasStartIcon &&\n    css`\n      padding-left: 0;\n    `}\n\n  ${({ hasEndIcon }) =>\n    hasEndIcon &&\n    css`\n      padding-right: 0;\n    `}\n\n  ${({ size, theme }) => {\n    if (size === 'small') {\n      return css`\n        font-size: ${theme.fontSizes.xs};\n        padding: ${theme.spacing.xxs} ${theme.spacing.xs};\n      `;\n    } else if (size === 'large') {\n      return css`\n        font-size: ${theme.fontSizes.md};\n        padding: ${theme.spacing.sm} ${theme.spacing.md};\n      `;\n    } else {\n      return css`\n        font-size: ${theme.fontSizes.sm};\n        padding: ${theme.spacing.xs} ${theme.spacing.sm};\n      `;\n    }\n  }}\n`;\n\nconst ClearButton = styled.button`\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: ${({ theme }) => theme.colors.textDisabled};\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.textSecondary};\n  }\n\n  &:focus {\n    outline: none;\n  }\n`;\n\nconst HelperTextContainer = styled.div<{ hasError?: boolean; hasSuccess?: boolean }>`\n  display: flex;\n  justify-content: space-between;\n  margin-top: ${({ theme }) => theme.spacing.xxs};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme, hasError, hasSuccess }) => {\n    if (hasError) return theme.colors.error;\n    if (hasSuccess) return theme.colors.success;\n    return theme.colors.textSecondary;\n  }};\n`;\n\n/**\n * Input Component\n *\n * A customizable input component that follows the design system.\n */\nexport const Input: React.FC<InputProps> = ({\n  value,\n  onChange,\n  placeholder,\n  disabled = false,\n  error,\n  type = 'text',\n  name,\n  id,\n  className,\n  required = false,\n  autoComplete,\n  label,\n  helperText,\n  startIcon,\n  endIcon,\n  loading = false,\n  success = false,\n  clearable = false,\n  onClear,\n  maxLength,\n  showCharCount = false,\n  size = 'medium',\n  fullWidth = false,\n  ...rest\n}) => {\n  const [isFocused, setIsFocused] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const handleClear = () => {\n    if (onClear) {\n      onClear();\n    } else {\n      onChange('');\n    }\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {\n    setIsFocused(true);\n    if (rest.onFocus) {\n      rest.onFocus(e);\n    }\n  };\n\n  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {\n    setIsFocused(false);\n    if (rest.onBlur) {\n      rest.onBlur(e);\n    }\n  };\n\n  // Clear button should only show when there's a value and the input is not disabled\n  const showClearButton = clearable && value && !disabled;\n\n  // Character count\n  const charCount = value?.length || 0;\n  const showCount = showCharCount || (maxLength !== undefined && maxLength > 0);\n\n  return (\n    <InputWrapper className={className} fullWidth={fullWidth}>\n      {label && (\n        <Label htmlFor={id}>\n          {label}\n          {required && ' *'}\n        </Label>\n      )}\n\n      <InputContainer\n        hasError={!!error}\n        hasSuccess={!!success}\n        disabled={!!disabled}\n        size={size}\n        hasStartIcon={!!startIcon}\n        hasEndIcon={!!(endIcon || showClearButton)}\n        isFocused={!!isFocused}\n      >\n        {startIcon && <IconContainer>{startIcon}</IconContainer>}\n\n        <StyledInput\n          ref={inputRef}\n          type={type}\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          placeholder={placeholder}\n          disabled={!!(disabled || loading)}\n          name={name}\n          id={id}\n          required={!!required}\n          autoComplete={autoComplete}\n          hasStartIcon={!!startIcon}\n          hasEndIcon={!!(endIcon || showClearButton)}\n          // Pass size as a custom prop to avoid conflict with HTML input size\n          size={size as any}\n          maxLength={maxLength}\n          onFocus={handleFocus}\n          onBlur={handleBlur}\n          {...rest}\n        />\n\n        {showClearButton && (\n          <ClearButton type=\"button\" onClick={handleClear} tabIndex={-1}>\n            ✕\n          </ClearButton>\n        )}\n\n        {endIcon && <IconContainer>{endIcon}</IconContainer>}\n      </InputContainer>\n\n      {(error || helperText || showCount) && (\n        <HelperTextContainer hasError={!!error} hasSuccess={!!success}>\n          <div>{error || helperText}</div>\n          {showCount && (\n            <div>\n              {charCount}\n              {maxLength !== undefined && `/${maxLength}`}\n            </div>\n          )}\n        </HelperTextContainer>\n      )}\n    </InputWrapper>\n  );\n};\n", "/**\n * FormField Component\n *\n * A component that combines a label with an input, select, or other form control.\n */\nimport React from \"react\";\nimport styled from \"styled-components\";\n\nexport interface FormFieldProps {\n  /** The form control to render (input, select, etc.) */\n  children: React.ReactNode;\n  /** The label text */\n  label: string;\n  /** Optional helper text */\n  helperText?: string;\n  /** Whether the field is required */\n  required?: boolean;\n  /** The error message */\n  error?: string;\n  /** Additional CSS class names */\n  className?: string;\n  /** The id of the form control */\n  id?: string;\n}\n\ntype FieldContainerProps = {\n  children?: React.ReactNode;\n};\n\nconst FieldContainer = styled.div<FieldContainerProps>`\n  display: flex;\n  flex-direction: column;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\n// Define the props that Label will accept\ntype LabelProps = {\n  hasError?: boolean;\n  children?: React.ReactNode;\n};\n\nconst Label = styled.label<LabelProps>`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  margin-bottom: ${({ theme }) => theme.spacing.xxs};\n  color: ${({ theme, hasError }) =>\n    hasError ? theme.colors.error : theme.colors.textPrimary};\n\n  .required-indicator {\n    color: ${({ theme }) => theme.colors.error};\n    margin-left: ${({ theme }) => theme.spacing.xxs};\n  }\n`;\n\n// Define the props that HelperText will accept\ntype HelperTextProps = {\n  hasError?: boolean;\n  children?: React.ReactNode;\n};\n\nconst HelperText = styled.div<HelperTextProps>`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme, hasError }) =>\n    hasError ? theme.colors.error : theme.colors.textSecondary};\n  margin-top: ${({ theme }) => theme.spacing.xxs};\n`;\n\n/**\n * FormField Component\n *\n * A component that combines a label with an input, select, or other form control.\n */\nexport const FormField: React.FC<FormFieldProps> = ({\n  children,\n  label,\n  helperText,\n  required = false,\n  error,\n  className,\n  id,\n  ...rest\n}) => {\n  // Generate a unique ID if none is provided\n  const fieldId = id || `field-${Math.random().toString(36).substr(2, 9)}`;\n\n  // Clone the child element to pass the id\n  const childElement = React.Children.map(children, (child) => {\n    if (React.isValidElement(child)) {\n      return React.cloneElement(child, {\n        id: fieldId,\n        required,\n        error,\n        ...child.props,\n      });\n    }\n    return child;\n  });\n\n  return (\n    <FieldContainer className={className} {...rest}>\n      <Label htmlFor={fieldId} hasError={!!error}>\n        {label}\n        {required && <span className=\"required-indicator\">*</span>}\n      </Label>\n\n      {childElement}\n\n      {(helperText || error) && (\n        <HelperText hasError={!!error}>{error || helperText}</HelperText>\n      )}\n    </FieldContainer>\n  );\n};\n", "/**\n * Market Overview Component\n *\n * A component for displaying market overview information.\n */\nimport React from 'react';\nimport { Card, Badge } from '@adhd-trading-dashboard/shared';\nimport { MarketOverview as MarketOverviewType, MarketSentiment } from '../types';\nimport styled from 'styled-components';\n\nexport interface MarketOverviewProps {\n  /** The market overview data */\n  marketOverview: MarketOverviewType | null;\n  /** Whether the component is in a loading state */\n  isLoading?: boolean;\n  /** The error message, if any */\n  error?: string | null;\n  /** Function called when the refresh button is clicked */\n  onRefresh?: () => void;\n  /** Additional class name */\n  className?: string;\n}\n\n// Styled components\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst SentimentContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst SentimentLabel = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst LastUpdated = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Summary = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  line-height: 1.5;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst IndexGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst IndexCard = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  display: flex;\n  flex-direction: column;\n`;\n\nconst IndexName = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst IndexValue = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst IndexChange = styled.div<{ value: number }>`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme, value }) => (value >= 0 ? theme.colors.profit : theme.colors.loss)};\n  display: flex;\n  align-items: center;\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst PreviousClose = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst EventsContainer = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst EventsHeader = styled.h4`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst EventsGrid = styled.div`\n  display: grid;\n  grid-template-columns: 80px 1fr 100px 100px 100px;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst EventsGridHeader = styled.div`\n  font-weight: 600;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  padding-bottom: ${({ theme }) => theme.spacing.sm};\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst EventsGridRow = styled.div`\n  display: contents;\n\n  & > div {\n    padding: ${({ theme }) => theme.spacing.sm} 0;\n    border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n    font-size: ${({ theme }) => theme.fontSizes.sm};\n  }\n`;\n\nconst EventTitle = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\n/**\n * Get the badge variant for a market sentiment\n */\nconst getSentimentVariant = (sentiment: MarketSentiment) => {\n  switch (sentiment) {\n    case 'bullish':\n      return 'success';\n    case 'bearish':\n      return 'error';\n    case 'neutral':\n    default:\n      return 'neutral';\n  }\n};\n\n/**\n * Format a date string\n */\nconst formatDate = (dateString?: string): string => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\n/**\n * Market Overview Component\n *\n * A component for displaying market overview information.\n */\nexport const MarketOverview: React.FC<MarketOverviewProps> = ({\n  marketOverview,\n  isLoading = false,\n  error = null,\n  onRefresh,\n  className,\n}) => {\n  // Loading state\n  if (isLoading) {\n    return (\n      <Card title=\"Market Overview\">\n        <div style={{ padding: '24px', textAlign: 'center' }}>Loading market data...</div>\n      </Card>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <Card title=\"Market Overview\">\n        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>\n          Error: {error}\n          {onRefresh && (\n            <button\n              onClick={onRefresh}\n              style={{\n                marginLeft: '16px',\n                padding: '8px 16px',\n                background: '#f0f0f0',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n              }}\n            >\n              Retry\n            </button>\n          )}\n        </div>\n      </Card>\n    );\n  }\n\n  // Empty state\n  if (!marketOverview) {\n    return (\n      <Card title=\"Market Overview\">\n        <div style={{ padding: '24px', textAlign: 'center' }}>\n          No market data available.\n          {onRefresh && (\n            <button\n              onClick={onRefresh}\n              style={{\n                marginLeft: '16px',\n                padding: '8px 16px',\n                background: '#f0f0f0',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n              }}\n            >\n              Refresh\n            </button>\n          )}\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <Card\n      title=\"Market Overview\"\n      actions={onRefresh ? [{ label: 'Refresh', onClick: onRefresh, icon: '🔄' }] : undefined}\n    >\n      <Container className={className}>\n        <Header>\n          <SentimentContainer>\n            <SentimentLabel>Market Sentiment:</SentimentLabel>\n            <Badge variant={getSentimentVariant(marketOverview.sentiment)} solid>\n              {marketOverview.sentiment.toUpperCase()}\n            </Badge>\n          </SentimentContainer>\n          <LastUpdated>Last updated: {formatDate(marketOverview.lastUpdated)}</LastUpdated>\n        </Header>\n\n        <Summary>{marketOverview.summary}</Summary>\n\n        <IndexGrid>\n          {marketOverview.indices.map((index) => (\n            <IndexCard key={index.symbol}>\n              <IndexName>{index.name}</IndexName>\n              <IndexValue>{index.value.toFixed(2)}</IndexValue>\n              <IndexChange value={index.change}>\n                {index.change >= 0 ? '▲ ' : '▼ '}\n                {Math.abs(index.change).toFixed(2)} ({(index.changePercent * 100).toFixed(2)}%)\n              </IndexChange>\n              {index.previousClose && (\n                <PreviousClose>Previous: {index.previousClose.toFixed(2)}</PreviousClose>\n              )}\n            </IndexCard>\n          ))}\n        </IndexGrid>\n\n        {/* Economic Events */}\n        {marketOverview.economicEvents && marketOverview.economicEvents.length > 0 && (\n          <EventsContainer>\n            <EventsHeader>Economic Events</EventsHeader>\n            <EventsGrid>\n              <EventsGridHeader>Time</EventsGridHeader>\n              <EventsGridHeader>Event</EventsGridHeader>\n              <EventsGridHeader>Expected</EventsGridHeader>\n              <EventsGridHeader>Previous</EventsGridHeader>\n              <EventsGridHeader>Actual</EventsGridHeader>\n\n              {marketOverview.economicEvents.map((event, index) => (\n                <EventsGridRow key={index}>\n                  <div>{event.time}</div>\n                  <EventTitle>\n                    {event.title}\n                    {event.importance === 'high' && (\n                      <Badge variant=\"error\" size=\"small\">\n                        High Impact\n                      </Badge>\n                    )}\n                  </EventTitle>\n                  <div>{event.expected || '-'}</div>\n                  <div>{event.previous || '-'}</div>\n                  <div>{event.actual || 'Pending'}</div>\n                </EventsGridRow>\n              ))}\n            </EventsGrid>\n          </EventsContainer>\n        )}\n      </Container>\n    </Card>\n  );\n};\n", "/**\n * Trading Plan Component\n *\n * A component for displaying and managing a trading plan.\n */\nimport React, { useState } from 'react';\nimport { Card, Badge, Button, Input, FormField } from '@adhd-trading-dashboard/shared';\nimport { TradingPlan as TradingPlanType, TradingPlanItem, TradingPlanPriority } from '../types';\nimport styled from 'styled-components';\n\nexport interface TradingPlanProps {\n  /** The trading plan data */\n  tradingPlan: TradingPlanType | null;\n  /** Whether the component is in a loading state */\n  isLoading?: boolean;\n  /** The error message, if any */\n  error?: string | null;\n  /** Function called when a trading plan item is toggled */\n  onItemToggle?: (id: string, completed: boolean) => void;\n  /** Function called when a trading plan item is added */\n  onItemAdd?: (item: TradingPlanItem) => void;\n  /** Function called when a trading plan item is removed */\n  onItemRemove?: (id: string) => void;\n  /** Additional class name */\n  className?: string;\n}\n\n// Styled components\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Section = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h4`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst PlanList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst PlanItem = styled.div<{ completed?: boolean }>`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  opacity: ${({ completed }) => (completed ? 0.6 : 1)};\n  transition: opacity 0.2s ease;\n`;\n\nconst CheckboxContainer = styled.div`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst Checkbox = styled.input`\n  cursor: pointer;\n  width: 18px;\n  height: 18px;\n`;\n\nconst ItemContent = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  flex: 1;\n`;\n\nconst Description = styled.div<{ completed?: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  text-decoration: ${({ completed }) => (completed ? 'line-through' : 'none')};\n`;\n\nconst ItemActions = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RiskManagementGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RiskManagementItem = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RiskManagementLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst RiskManagementValue = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst Notes = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  line-height: 1.5;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  white-space: pre-wrap;\n`;\n\nconst AddItemForm = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n`;\n\nconst FormActions = styled.div`\n  display: flex;\n  justify-content: flex-end;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\n/**\n * Get the badge variant for a priority\n */\nconst getPriorityVariant = (priority: TradingPlanPriority) => {\n  switch (priority) {\n    case 'high':\n      return 'error';\n    case 'medium':\n      return 'warning';\n    case 'low':\n    default:\n      return 'info';\n  }\n};\n\n/**\n * Trading Plan Component\n *\n * A component for displaying and managing a trading plan.\n */\nexport const TradingPlan: React.FC<TradingPlanProps> = ({\n  tradingPlan,\n  isLoading = false,\n  error = null,\n  onItemToggle,\n  onItemAdd,\n  onItemRemove,\n  className,\n}) => {\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newItem, setNewItem] = useState<Omit<TradingPlanItem, 'id'>>({\n    description: '',\n    priority: 'medium',\n    completed: false,\n  });\n\n  const handleAddItem = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!newItem.description.trim() || !onItemAdd) return;\n\n    onItemAdd({\n      ...newItem,\n      id: Date.now().toString(), // Generate a unique ID\n    });\n\n    setNewItem({\n      description: '',\n      priority: 'medium',\n      completed: false,\n    });\n\n    setShowAddForm(false);\n  };\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <Card title=\"Trading Plan\">\n        <div style={{ padding: '24px', textAlign: 'center' }}>Loading trading plan...</div>\n      </Card>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <Card title=\"Trading Plan\">\n        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>Error: {error}</div>\n      </Card>\n    );\n  }\n\n  // Empty state\n  if (!tradingPlan) {\n    return (\n      <Card title=\"Trading Plan\">\n        <EmptyState>\n          No trading plan available.\n          {onItemAdd && (\n            <div style={{ marginTop: '16px' }}>\n              <Button onClick={() => setShowAddForm(true)}>Create Trading Plan</Button>\n            </div>\n          )}\n        </EmptyState>\n      </Card>\n    );\n  }\n\n  return (\n    <Card\n      title=\"Trading Plan\"\n      actions={\n        onItemAdd\n          ? [{ label: 'Add Item', onClick: () => setShowAddForm(true), icon: '➕' }]\n          : undefined\n      }\n    >\n      <Container className={className}>\n        {/* Strategy */}\n        {tradingPlan.strategy && (\n          <Section>\n            <SectionTitle>Strategy</SectionTitle>\n            <Notes>{tradingPlan.strategy}</Notes>\n          </Section>\n        )}\n\n        {/* Trading Plan Items */}\n        <Section>\n          <SectionTitle>Action Items</SectionTitle>\n          <PlanList>\n            {tradingPlan.items.map((item) => (\n              <PlanItem key={item.id} completed={item.completed}>\n                <CheckboxContainer>\n                  <Checkbox\n                    type=\"checkbox\"\n                    checked={!!item.completed}\n                    onChange={(e) => onItemToggle?.(item.id, e.target.checked)}\n                    disabled={!onItemToggle}\n                  />\n                </CheckboxContainer>\n                <ItemContent>\n                  <Description completed={item.completed}>{item.description}</Description>\n                  <Badge variant={getPriorityVariant(item.priority)}>{item.priority}</Badge>\n                </ItemContent>\n                {onItemRemove && (\n                  <ItemActions>\n                    <Button\n                      variant=\"icon\"\n                      onClick={() => onItemRemove(item.id)}\n                      aria-label=\"Remove item\"\n                    >\n                      🗑️\n                    </Button>\n                  </ItemActions>\n                )}\n              </PlanItem>\n            ))}\n          </PlanList>\n        </Section>\n\n        {/* Risk Management */}\n        {tradingPlan.riskManagement && (\n          <Section>\n            <SectionTitle>Risk Management</SectionTitle>\n            <RiskManagementGrid>\n              <RiskManagementItem>\n                <RiskManagementLabel>Max Risk Per Trade</RiskManagementLabel>\n                <RiskManagementValue>\n                  {tradingPlan.riskManagement.maxRiskPerTrade}%\n                </RiskManagementValue>\n              </RiskManagementItem>\n              <RiskManagementItem>\n                <RiskManagementLabel>Max Daily Loss</RiskManagementLabel>\n                <RiskManagementValue>\n                  {tradingPlan.riskManagement.maxDailyLoss}%\n                </RiskManagementValue>\n              </RiskManagementItem>\n              <RiskManagementItem>\n                <RiskManagementLabel>Max Trades</RiskManagementLabel>\n                <RiskManagementValue>{tradingPlan.riskManagement.maxTrades}</RiskManagementValue>\n              </RiskManagementItem>\n              <RiskManagementItem>\n                <RiskManagementLabel>Position Sizing</RiskManagementLabel>\n                <RiskManagementValue>\n                  {tradingPlan.riskManagement.positionSizing}\n                </RiskManagementValue>\n              </RiskManagementItem>\n            </RiskManagementGrid>\n          </Section>\n        )}\n\n        {/* Notes */}\n        {tradingPlan.notes && (\n          <Section>\n            <SectionTitle>Notes</SectionTitle>\n            <Notes>{tradingPlan.notes}</Notes>\n          </Section>\n        )}\n\n        {/* Add Item Form */}\n        {showAddForm && (\n          <AddItemForm onSubmit={handleAddItem}>\n            <FormField label=\"Description\">\n              <Input\n                value={newItem.description}\n                onChange={(value) => setNewItem({ ...newItem, description: value })}\n                placeholder=\"Enter task description\"\n                required\n                fullWidth\n              />\n            </FormField>\n            <FormField label=\"Priority\">\n              <select\n                value={newItem.priority}\n                onChange={(e) =>\n                  setNewItem({ ...newItem, priority: e.target.value as TradingPlanPriority })\n                }\n                style={{\n                  padding: '8px',\n                  borderRadius: '4px',\n                  border: '1px solid #ccc',\n                  width: '100%',\n                }}\n              >\n                <option value=\"high\">High</option>\n                <option value=\"medium\">Medium</option>\n                <option value=\"low\">Low</option>\n              </select>\n            </FormField>\n            <FormActions>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                Cancel\n              </Button>\n              <Button type=\"submit\">Add Item</Button>\n            </FormActions>\n          </AddItemForm>\n        )}\n      </Container>\n    </Card>\n  );\n};\n", "/**\n * Key Levels Component\n *\n * A component for displaying key price levels.\n */\nimport React from 'react';\nimport { Card, Badge } from '@adhd-trading-dashboard/shared';\nimport { KeyPriceLevel } from '../types';\nimport styled from 'styled-components';\n\nexport interface KeyLevelsProps {\n  /** The key price levels */\n  keyLevels: KeyPriceLevel[];\n  /** Whether the component is in a loading state */\n  isLoading?: boolean;\n  /** The error message, if any */\n  error?: string | null;\n  /** Function called when the refresh button is clicked */\n  onRefresh?: () => void;\n  /** Additional class name */\n  className?: string;\n}\n\n// Styled components\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst LevelsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst LevelCard = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  padding: ${({ theme }) => theme.spacing.md};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst Symbol = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  display: flex;\n  align-items: center;\n`;\n\nconst LevelsRow = styled.div`\n  display: flex;\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst LevelLabel = styled.div`\n  width: 100px;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst LevelValue = styled.div<{ type: 'support' | 'resistance' | 'pivot' }>`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme, type }) => {\n    switch (type) {\n      case 'support':\n        return theme.colors.profit;\n      case 'resistance':\n        return theme.colors.loss;\n      case 'pivot':\n        return theme.colors.accent;\n      default:\n        return theme.colors.textPrimary;\n    }\n  }};\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\n/**\n * Key Levels Component\n *\n * A component for displaying key price levels.\n */\nexport const KeyLevels: React.FC<KeyLevelsProps> = ({\n  keyLevels,\n  isLoading = false,\n  error = null,\n  onRefresh,\n  className,\n}) => {\n  // Loading state\n  if (isLoading) {\n    return (\n      <Card title=\"Key Price Levels\">\n        <div style={{ padding: '24px', textAlign: 'center' }}>Loading key levels...</div>\n      </Card>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <Card title=\"Key Price Levels\">\n        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>\n          Error: {error}\n          {onRefresh && (\n            <button\n              onClick={onRefresh}\n              style={{\n                marginLeft: '16px',\n                padding: '8px 16px',\n                background: '#f0f0f0',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n              }}\n            >\n              Retry\n            </button>\n          )}\n        </div>\n      </Card>\n    );\n  }\n\n  // Empty state\n  if (!keyLevels || keyLevels.length === 0) {\n    return (\n      <Card title=\"Key Price Levels\">\n        <EmptyState>\n          No key price levels available.\n          {onRefresh && (\n            <div style={{ marginTop: '16px' }}>\n              <button\n                onClick={onRefresh}\n                style={{\n                  padding: '8px 16px',\n                  background: '#f0f0f0',\n                  border: 'none',\n                  borderRadius: '4px',\n                  cursor: 'pointer',\n                }}\n              >\n                Refresh\n              </button>\n            </div>\n          )}\n        </EmptyState>\n      </Card>\n    );\n  }\n\n  return (\n    <Card\n      title=\"Key Price Levels\"\n      actions={onRefresh ? [{ label: 'Refresh', onClick: onRefresh, icon: '🔄' }] : undefined}\n    >\n      <Container className={className}>\n        <LevelsGrid>\n          {keyLevels.map((level, index) => (\n            <LevelCard key={index}>\n              <Symbol>\n                {level.symbol}\n                <Badge variant=\"primary\" style={{ marginLeft: '8px' }}>\n                  {level.support.length + level.resistance.length} levels\n                </Badge>\n              </Symbol>\n\n              <LevelsRow>\n                <LevelLabel>Resistance</LevelLabel>\n                <LevelValue type=\"resistance\">{level.resistance.join(' | ')}</LevelValue>\n              </LevelsRow>\n\n              {level.pivotPoint && (\n                <LevelsRow>\n                  <LevelLabel>Pivot</LevelLabel>\n                  <LevelValue type=\"pivot\">{level.pivotPoint}</LevelValue>\n                </LevelsRow>\n              )}\n\n              <LevelsRow>\n                <LevelLabel>Support</LevelLabel>\n                <LevelValue type=\"support\">{level.support.join(' | ')}</LevelValue>\n              </LevelsRow>\n            </LevelCard>\n          ))}\n        </LevelsGrid>\n      </Container>\n    </Card>\n  );\n};\n", "/**\n * Daily Guide API\n *\n * API functions for the daily guide feature\n */\n\nimport { DailyGuideData, MarketSentiment, TradingPlanPriority } from '../types';\n\n/**\n * Fetch daily guide data from the API\n *\n * In a real application, this would make an actual API call.\n * For now, we're generating mock data.\n */\nexport const fetchDailyGuideData = async (): Promise<DailyGuideData> => {\n  // Simulate API call delay\n  await new Promise((resolve) => setTimeout(resolve, 800));\n\n  // Randomly decide if we should throw an error (for testing error handling)\n  const shouldError = Math.random() < 0.05; // 5% chance of error\n  if (shouldError) {\n    throw new Error('Failed to fetch daily guide data');\n  }\n\n  return generateMockData();\n};\n\n/**\n * Generate mock data for development and testing\n */\nconst generateMockData = (): DailyGuideData => {\n  // Market data\n  const sentiments: MarketSentiment[] = ['bullish', 'bearish', 'neutral'];\n  const randomSentiment = sentiments[Math.floor(Math.random() * sentiments.length)];\n\n  const marketData = {\n    sentiment: randomSentiment,\n    summary: getSummaryForSentiment(randomSentiment),\n    indices: [\n      {\n        name: 'S&P 500',\n        value: 4500 + Math.random() * 100,\n        change:\n          (Math.random() * 2 - 1) *\n          (randomSentiment === 'bullish' ? 1 : randomSentiment === 'bearish' ? -1 : 0.5),\n        previousClose: 4500 + Math.random() * 50,\n      },\n      {\n        name: 'Nasdaq',\n        value: 14000 + Math.random() * 500,\n        change:\n          (Math.random() * 2 - 1) *\n          (randomSentiment === 'bullish' ? 1 : randomSentiment === 'bearish' ? -1 : 0.5),\n        previousClose: 14000 + Math.random() * 250,\n      },\n      {\n        name: 'Dow Jones',\n        value: 35000 + Math.random() * 500,\n        change:\n          (Math.random() * 2 - 1) *\n          (randomSentiment === 'bullish' ? 1 : randomSentiment === 'bearish' ? -1 : 0.5),\n        previousClose: 35000 + Math.random() * 250,\n      },\n      {\n        name: 'VIX',\n        value: 15 + Math.random() * 10,\n        change:\n          (Math.random() * 2 - 1) *\n          (randomSentiment === 'bullish' ? -1 : randomSentiment === 'bearish' ? 1 : 0.5),\n        previousClose: 15 + Math.random() * 5,\n      },\n    ],\n    lastUpdated: new Date().toISOString(),\n  };\n\n  // Trading plan\n  // Using different priorities for each item\n  // const priorities: TradingPlanPriority[] = ['high', 'medium', 'low'];\n  const tradingPlan = [\n    {\n      id: '1',\n      description: 'Wait for market open before placing any trades',\n      priority: 'high' as TradingPlanPriority,\n      completed: false,\n    },\n    {\n      id: '2',\n      description: 'Focus on tech sector for long opportunities',\n      priority: 'medium' as TradingPlanPriority,\n      completed: false,\n    },\n    {\n      id: '3',\n      description: 'Use tight stop losses due to expected volatility',\n      priority: 'high' as TradingPlanPriority,\n      completed: false,\n    },\n    {\n      id: '4',\n      description: 'Review earnings reports for potential opportunities',\n      priority: 'medium' as TradingPlanPriority,\n      completed: false,\n    },\n    {\n      id: '5',\n      description: 'Avoid over-trading in the first hour',\n      priority: 'low' as TradingPlanPriority,\n      completed: false,\n    },\n  ];\n\n  // Key levels\n  const keyLevels = [\n    {\n      symbol: 'SPY',\n      support: ['450.00', '445.75', '442.30'],\n      resistance: ['455.50', '460.00', '462.75'],\n      pivotPoint: '452.25',\n    },\n    {\n      symbol: 'QQQ',\n      support: ['365.20', '360.00', '355.50'],\n      resistance: ['370.00', '375.35', '380.00'],\n      pivotPoint: '367.75',\n    },\n    {\n      symbol: 'AAPL',\n      support: ['175.00', '170.50', '165.75'],\n      resistance: ['180.00', '185.50', '190.25'],\n      pivotPoint: '177.25',\n    },\n  ];\n\n  // Market news\n  const marketNews = [\n    {\n      id: '1',\n      title: 'Fed signals potential rate hike in upcoming meeting',\n      source: 'Financial Times',\n      url: 'https://example.com/news/1',\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n      impact: 'high' as const,\n    },\n    {\n      id: '2',\n      title: 'Tech stocks rally on strong earnings reports',\n      source: 'Wall Street Journal',\n      url: 'https://example.com/news/2',\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n      impact: 'medium' as const,\n    },\n    {\n      id: '3',\n      title: 'Oil prices stabilize after recent volatility',\n      source: 'Bloomberg',\n      url: 'https://example.com/news/3',\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n      impact: 'low' as const,\n    },\n  ];\n\n  return {\n    marketData,\n    tradingPlan,\n    keyLevels,\n    marketNews,\n  };\n};\n\n/**\n * Get a summary based on market sentiment\n */\nconst getSummaryForSentiment = (sentiment: MarketSentiment): string => {\n  switch (sentiment) {\n    case 'bullish':\n      return 'Markets are showing strong bullish momentum with tech stocks leading the rally. Watch for potential breakouts above key resistance levels.';\n    case 'bearish':\n      return 'Markets are under pressure with broad-based selling. Focus on defensive sectors and watch key support levels for potential bounces.';\n    case 'neutral':\n      return 'Markets are showing mixed signals with sector rotation evident. Range-bound trading is likely until a clear catalyst emerges.';\n    default:\n      return 'Markets are showing mixed signals. Monitor key levels for potential trading opportunities.';\n  }\n};\n", "/**\n * Daily Guide Context\n *\n * Context for managing daily guide state\n */\n\nimport React, {\n  createContext,\n  useContext,\n  useReducer,\n  ReactNode,\n  useCallback,\n  useEffect,\n} from 'react';\nimport {\n  DailyGuideState,\n  DailyGuideAction,\n  // Using these types but not directly referencing them in variable declarations\n  // DailyGuideData,\n  // MarketData,\n  // TradingPlanItem,\n  // KeyLevel,\n  // MarketNews\n} from '../types';\nimport { fetchDailyGuideData } from '../api/dailyGuideApi';\n\n// Initial state\nconst initialState: DailyGuideState = {\n  marketData: null,\n  tradingPlan: [],\n  keyLevels: [],\n  marketNews: [],\n  isLoading: false,\n  error: null,\n  currentDate: new Date().toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }),\n};\n\n// Reducer function\nconst dailyGuideReducer = (state: DailyGuideState, action: DailyGuideAction): DailyGuideState => {\n  switch (action.type) {\n    case 'FETCH_DATA_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'FETCH_DATA_SUCCESS':\n      return {\n        ...state,\n        ...action.payload,\n        isLoading: false,\n        error: null,\n      };\n    case 'FETCH_DATA_ERROR':\n      return {\n        ...state,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'UPDATE_TRADING_PLAN_ITEM':\n      return {\n        ...state,\n        tradingPlan: state.tradingPlan.map((item) =>\n          item.id === action.payload.id ? { ...item, completed: action.payload.completed } : item\n        ),\n      };\n    case 'REFRESH_DATA':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    default:\n      return state;\n  }\n};\n\n// Context\ninterface DailyGuideContextType extends DailyGuideState {\n  fetchGuideData: () => Promise<void>;\n  updateTradingPlanItem: (id: string, completed: boolean) => void;\n  refreshData: () => void;\n}\n\nconst DailyGuideContext = createContext<DailyGuideContextType | undefined>(undefined);\n\n// Provider component\ninterface DailyGuideProviderProps {\n  children: ReactNode;\n}\n\nexport const DailyGuideProvider: React.FC<DailyGuideProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(dailyGuideReducer, initialState);\n\n  const fetchGuideData = useCallback(async () => {\n    dispatch({ type: 'FETCH_DATA_START' });\n    try {\n      const data = await fetchDailyGuideData();\n      dispatch({ type: 'FETCH_DATA_SUCCESS', payload: data });\n    } catch (error) {\n      dispatch({\n        type: 'FETCH_DATA_ERROR',\n        payload: error instanceof Error ? error.message : 'An unknown error occurred',\n      });\n    }\n  }, []);\n\n  const updateTradingPlanItem = useCallback((id: string, completed: boolean) => {\n    dispatch({ type: 'UPDATE_TRADING_PLAN_ITEM', payload: { id, completed } });\n  }, []);\n\n  const refreshData = useCallback(() => {\n    dispatch({ type: 'REFRESH_DATA' });\n    fetchGuideData();\n  }, [fetchGuideData]);\n\n  // Load data on mount\n  useEffect(() => {\n    fetchGuideData();\n  }, [fetchGuideData]);\n\n  const value = {\n    ...state,\n    fetchGuideData,\n    updateTradingPlanItem,\n    refreshData,\n  };\n\n  return <DailyGuideContext.Provider value={value}>{children}</DailyGuideContext.Provider>;\n};\n\n// Custom hook for using the context\nexport const useDailyGuide = (): DailyGuideContextType => {\n  const context = useContext(DailyGuideContext);\n  if (context === undefined) {\n    throw new Error('useDailyGuide must be used within a DailyGuideProvider');\n  }\n  return context;\n};\n", "/**\n * Market News Component\n *\n * Displays recent market news and events\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Using the type directly from the context\n// import { MarketNews as MarketNewsType } from '../types';\nimport { useDailyGuide } from '../context/DailyGuideContext';\n\ninterface MarketNewsProps {\n  className?: string;\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst NewsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst NewsItem = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst NewsHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n`;\n\nconst NewsTitle = styled.a`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  text-decoration: none;\n  flex: 1;\n\n  &:hover {\n    text-decoration: underline;\n    color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\nconst NewsSource = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: ${({ theme }) => theme.spacing.xxs};\n`;\n\nconst NewsTime = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  white-space: nowrap;\n  margin-left: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst ImpactIndicator = styled.div<{ impact: 'high' | 'medium' | 'low' }>`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  margin-right: ${({ theme }) => theme.spacing.xs};\n\n  ${({ impact, theme }) => {\n    switch (impact) {\n      case 'high':\n        return `background-color: ${theme.colors.error};`;\n      case 'medium':\n        return `background-color: ${theme.colors.warning};`;\n      case 'low':\n        return `background-color: ${theme.colors.info};`;\n      default:\n        return '';\n    }\n  }}\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const MarketNews: React.FC<MarketNewsProps> = ({ className }) => {\n  const { marketNews } = useDailyGuide();\n\n  if (!marketNews || marketNews.length === 0) {\n    return <EmptyState>No market news available</EmptyState>;\n  }\n\n  const formatTime = (timestamp: string): string => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));\n\n    if (diffHrs < 1) {\n      const diffMins = Math.floor(diffMs / (1000 * 60));\n      return `${diffMins} min${diffMins !== 1 ? 's' : ''} ago`;\n    } else if (diffHrs < 24) {\n      return `${diffHrs} hr${diffHrs !== 1 ? 's' : ''} ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  };\n\n  return (\n    <Container className={className}>\n      <NewsList>\n        {marketNews.map((news) => (\n          <NewsItem key={news.id}>\n            <NewsHeader>\n              <div>\n                <NewsTitle href={news.url} target=\"_blank\" rel=\"noopener noreferrer\">\n                  <ImpactIndicator impact={news.impact} />\n                  {news.title}\n                </NewsTitle>\n                <NewsSource>{news.source}</NewsSource>\n              </div>\n              <NewsTime>{formatTime(news.timestamp)}</NewsTime>\n            </NewsHeader>\n          </NewsItem>\n        ))}\n      </NewsList>\n    </Container>\n  );\n};\n", "/**\n * Section Card Component\n * \n * A card component for displaying sections in the daily guide\n */\n\nimport React, { ReactNode } from 'react';\nimport styled from 'styled-components';\n\ninterface SectionCardProps {\n  /** The title of the section */\n  title: string;\n  /** The content of the section */\n  children: ReactNode;\n  /** Whether the section is in a loading state */\n  isLoading?: boolean;\n  /** Whether the section has an error */\n  hasError?: boolean;\n  /** Error message to display */\n  errorMessage?: string;\n  /** Action button to display in the header */\n  actionButton?: ReactNode;\n}\n\nconst Container = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Title = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Content = styled.div``;\n\nconst LoadingPlaceholder = styled.div`\n  height: 200px;\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst ErrorContainer = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.error + '10'};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.error};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nexport const SectionCard: React.FC<SectionCardProps> = ({\n  title,\n  children,\n  isLoading = false,\n  hasError = false,\n  errorMessage = 'An error occurred while loading data',\n  actionButton,\n}) => {\n  return (\n    <Container>\n      <Header>\n        <Title>{title}</Title>\n        {actionButton && actionButton}\n      </Header>\n      \n      {hasError && (\n        <ErrorContainer>\n          <p>{errorMessage}</p>\n        </ErrorContainer>\n      )}\n      \n      {isLoading ? (\n        <LoadingPlaceholder>Loading data...</LoadingPlaceholder>\n      ) : (\n        <Content>{children}</Content>\n      )}\n    </Container>\n  );\n};\n", "/**\n * Daily Guide Page\n *\n * This page displays daily trading guidance and market insights.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Button } from '@adhd-trading-dashboard/shared';\nimport { MarketOverview } from './components/MarketOverview';\nimport { TradingPlan } from './components/TradingPlan';\nimport { KeyLevels } from './components/KeyLevels';\nimport { MarketNews } from './components/MarketNews';\nimport { SectionCard } from './components/ui';\nimport { DailyGuideProvider, useDailyGuide } from './context/DailyGuideContext';\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xxl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst DateDisplay = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst RefreshButton = styled(Button)`\n  margin-left: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DailyGuideContent: React.FC = () => {\n  const { isLoading, error, currentDate, refreshData } = useDailyGuide();\n\n  const handleRefresh = () => {\n    refreshData();\n  };\n\n  return (\n    <PageContainer>\n      <PageHeader>\n        <Title>Daily Trading Guide</Title>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <DateDisplay>{currentDate}</DateDisplay>\n          <RefreshButton\n            variant=\"outline\"\n            size=\"small\"\n            onClick={handleRefresh}\n            disabled={isLoading}\n          >\n            Refresh\n          </RefreshButton>\n        </div>\n      </PageHeader>\n\n      <SectionCard\n        title=\"Market Overview\"\n        isLoading={isLoading}\n        hasError={!!error}\n        errorMessage={error || ''}\n      >\n        <MarketOverview />\n      </SectionCard>\n\n      <SectionCard\n        title=\"Trading Plan\"\n        isLoading={isLoading}\n        hasError={!!error}\n        errorMessage={error || ''}\n      >\n        <TradingPlan />\n      </SectionCard>\n\n      <SectionCard\n        title=\"Key Levels\"\n        isLoading={isLoading}\n        hasError={!!error}\n        errorMessage={error || ''}\n      >\n        <KeyLevels />\n      </SectionCard>\n\n      <SectionCard\n        title=\"Market News\"\n        isLoading={isLoading}\n        hasError={!!error}\n        errorMessage={error || ''}\n      >\n        <MarketNews />\n      </SectionCard>\n    </PageContainer>\n  );\n};\n\nconst DailyGuide: React.FC = () => {\n  return (\n    <DailyGuideProvider>\n      <DailyGuideContent />\n    </DailyGuideProvider>\n  );\n};\n\nexport default DailyGuide;\n"], "names": ["InputWrapper", "div", "withConfig", "displayName", "componentId", "fullWidth", "Label", "label", "theme", "fontSizes", "sm", "colors", "textSecondary", "spacing", "xxs", "fontWeights", "medium", "InputContainer", "borderRadius", "<PERSON><PERSON><PERSON><PERSON>", "hasSuccess", "isFocused", "error", "success", "primary", "border", "surface", "transitions", "fast", "disabled", "css", "background", "size", "IconContainer", "xs", "StyledInput", "input", "textPrimary", "textDisabled", "hasStartIcon", "hasEndIcon", "md", "ClearButton", "button", "HelperTextContainer", "Input", "value", "onChange", "placeholder", "type", "name", "id", "className", "required", "autoComplete", "helperText", "startIcon", "endIcon", "loading", "clearable", "onClear", "max<PERSON><PERSON><PERSON>", "showCharCount", "rest", "setIsFocused", "useState", "inputRef", "useRef", "handleClear", "current", "focus", "handleFocus", "e", "onFocus", "handleBlur", "onBlur", "showClearButton", "charCount", "length", "showCount", "undefined", "jsxs", "jsx", "target", "FieldC<PERSON>r", "HelperText", "FormField", "children", "fieldId", "Math", "random", "toString", "substr", "childElement", "React", "Children", "map", "child", "isValidElement", "cloneElement", "props", "Container", "Header", "Sentiment<PERSON><PERSON><PERSON>", "Sentiment<PERSON><PERSON><PERSON>", "span", "LastUpdated", "Summary", "p", "IndexGrid", "IndexCard", "IndexName", "IndexValue", "lg", "IndexChange", "profit", "loss", "PreviousClose", "EventsContainer", "EventsHeader", "h4", "EventsGrid", "EventsGridHeader", "EventsGridRow", "EventTitle", "getSentimentVariant", "sentiment", "formatDate", "dateString", "Date", "toLocaleTimeString", "hour", "minute", "MarketOverview", "marketOverview", "isLoading", "onRefresh", "Card", "padding", "textAlign", "color", "marginLeft", "cursor", "onClick", "icon", "Badge", "toUpperCase", "lastUpdated", "summary", "indices", "index", "toFixed", "change", "abs", "changePercent", "previousClose", "symbol", "economicEvents", "event", "time", "title", "importance", "expected", "previous", "actual", "Section", "SectionTitle", "PlanList", "PlanItem", "completed", "CheckboxContainer", "Checkbox", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Description", "ItemActions", "RiskManagementGrid", "RiskManagementItem", "RiskManagementLabel", "RiskManagementValue", "Notes", "AddItemForm", "form", "FormActions", "EmptyState", "getPriorityVariant", "priority", "TradingPlan", "tradingPlan", "onItemToggle", "onItemAdd", "onItemRemove", "showAddForm", "setShowAddForm", "newItem", "setNewItem", "description", "handleAddItem", "preventDefault", "trim", "now", "strategy", "items", "item", "checked", "<PERSON><PERSON>", "riskManagement", "maxRiskPerTrade", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxTrades", "positionSizing", "notes", "width", "marginTop", "LevelsGrid", "LevelCard", "shadows", "Symbol", "LevelsRow", "LevelLabel", "LevelValue", "accent", "KeyLevels", "keyLevels", "level", "support", "resistance", "join", "pivotPoint", "fetchDailyGuideData", "Promise", "resolve", "setTimeout", "Error", "generateMockData", "sentiments", "randomSentiment", "floor", "marketData", "getSummaryForSentiment", "toISOString", "marketNews", "source", "url", "timestamp", "impact", "initialState", "currentDate", "toLocaleDateString", "weekday", "year", "month", "day", "dailyGuideReducer", "state", "action", "payload", "DailyGuideContext", "createContext", "DailyGuideProvider", "dispatch", "useReducer", "fetchGuideData", "useCallback", "data", "message", "updateTradingPlanItem", "refreshData", "useEffect", "useDailyGuide", "context", "useContext", "NewsList", "NewsItem", "NewsHeader", "NewsTitle", "a", "NewsSource", "NewsTime", "ImpactIndicator", "warning", "info", "MarketNews", "formatTime", "date", "diffMs", "getTime", "diffHrs", "diffMins", "news", "Title", "h2", "Content", "LoadingPlaceholder", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SectionCard", "errorMessage", "actionButton", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "h1", "xxl", "DateDisplay", "RefreshButton", "styled", "DailyGuideContent", "handleRefresh", "display", "alignItems", "DailyGuide"], "mappings": "mMAsEA,MAAMA,GAAsBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,4CAAA,qBAAA,EAGpB,CAAC,CAAEC,UAAAA,CAAU,IAAOA,EAAY,OAAS,MAAO,EAIrDC,GAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,gBAAA,GAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,IAC/B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,cAANP,YAAAA,EAAmBQ,SAAU,IAAG,EAc1DC,GAAwBhB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,8EAAA,qBAAA,qBAAA,mBAAA,IAAA,IAAA,IAAA,EAAA,EAKd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAE/C,CAAC,CAAEF,MAAAA,EAAOW,SAAAA,EAAUC,WAAAA,EAAYC,UAAAA,CAAU,IACtCF,EAAiBX,EAAMG,OAAOW,MAC9BF,EAAmBZ,EAAMG,OAAOY,QAChCF,EAAkBb,EAAMG,OAAOa,QAC5BhB,EAAMG,OAAOc,OAEJ,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMG,OAAOe,QAC9B,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,cAANnB,YAAAA,EAAmBoB,OAAQ,aAE1D,CAAC,CAAEC,SAAAA,EAAUrB,MAAAA,CAAM,IACnBqB,GACAC,EAEsBtB,CAAAA,gCAAAA,sBAAAA,EAAAA,EAAMG,OAAOoB,UAAU,EAI7C,CAAC,CAAEV,UAAAA,EAAWb,MAAAA,EAAOW,SAAAA,EAAUC,WAAAA,CAAW,IAC1CC,GACAS,gCAEMX,EACE,GAAGX,EAAMG,OAAOW,UAChBF,EACA,GAAGZ,EAAMG,OAAOY,YAChB,GAAGf,EAAMG,OAAOa,WAAW,EAGnC,CAAC,CAAEQ,KAAAA,CAAK,IAAM,CACd,OAAQA,EAAI,CACV,IAAK,QACIF,OAAAA,EAAG,CAAA,cAAA,CAAA,EAGZ,IAAK,QACIA,OAAAA,EAAG,CAAA,cAAA,CAAA,EAGZ,QACSA,OAAAA,EAAG,CAAA,cAAA,CAAA,CAGd,CACF,CAAC,EAGGG,EAAuBhC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oEAAA,UAAA,GAAA,EAIjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACjC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAU9CuB,GAAqBC,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,mDAAA,gFAAA,KAAA,IAAA,IAAA,EAAA,EAIrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAS1B,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAO2B,aAGrC,CAAC,CAAEC,aAAAA,CAAa,IAChBA,GACAT,EAAG,CAAA,iBAAA,CAAA,EAIH,CAAC,CAAEU,WAAAA,CAAW,IACdA,GACAV,EAAG,CAAA,kBAAA,CAAA,EAIH,CAAC,CAAEE,KAAAA,EAAMxB,MAAAA,CAAM,IACXwB,IAAS,QACJF,EACQtB,CAAAA,aAAAA,YAAAA,IAAAA,GAAAA,EAAAA,EAAMC,UAAUyB,GAClB1B,EAAMK,QAAQC,IAAON,EAAMK,QAAQqB,EAAE,EAEzCF,IAAS,QACXF,EACQtB,CAAAA,aAAAA,YAAAA,IAAAA,GAAAA,EAAAA,EAAMC,UAAUgC,GAClBjC,EAAMK,QAAQH,GAAMF,EAAMK,QAAQ4B,EAAE,EAG1CX,EACQtB,CAAAA,aAAAA,YAAAA,IAAAA,GAAAA,EAAAA,EAAMC,UAAUC,GAClBF,EAAMK,QAAQqB,GAAM1B,EAAMK,QAAQH,EAAE,CAGpD,EAGGgC,GAAqBC,EAAAA,OAAMzC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oDAAA,cAAA,yEAAA,0BAAA,EAItB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAO2B,aACxB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GAM/B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAQhDgC,GAA6B3C,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yDAAA,cAAA,UAAA,GAAA,EAGtB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,IAC9B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,EAAOW,SAAAA,EAAUC,WAAAA,CAAW,IAClCD,EAAiBX,EAAMG,OAAOW,MAC9BF,EAAmBZ,EAAMG,OAAOY,QAC7Bf,EAAMG,OAAOC,aACrB,EAQUiC,GAA8BA,CAAC,CAC1CC,MAAAA,EACAC,SAAAA,EACAC,YAAAA,EACAnB,SAAAA,EAAW,GACXP,MAAAA,EACA2B,KAAAA,EAAO,OACPC,KAAAA,EACAC,GAAAA,EACAC,UAAAA,EACAC,SAAAA,EAAW,GACXC,aAAAA,EACA/C,MAAAA,EACAgD,WAAAA,EACAC,UAAAA,EACAC,QAAAA,EACAC,QAAAA,EAAU,GACVnC,QAAAA,EAAU,GACVoC,UAAAA,EAAY,GACZC,QAAAA,EACAC,UAAAA,EACAC,cAAAA,EAAgB,GAChB9B,KAAAA,EAAO,SACP3B,UAAAA,EAAY,GACZ,GAAG0D,CACL,IAAM,CACJ,KAAM,CAAC1C,EAAW2C,CAAY,EAAIC,WAAS,EAAK,EAC1CC,EAAWC,SAAyB,IAAI,EAExCC,EAAcA,IAAM,CACpBR,EACMA,IAERb,EAAS,EAAE,EAETmB,EAASG,SACXH,EAASG,QAAQC,OACnB,EAGIC,GAAeC,GAA0C,CAC7DR,EAAa,EAAI,EACbD,EAAKU,SACPV,EAAKU,QAAQD,CAAC,CAChB,EAGIE,GAAcF,GAA0C,CAC5DR,EAAa,EAAK,EACdD,EAAKY,QACPZ,EAAKY,OAAOH,CAAC,CACf,EAIII,EAAkBjB,GAAab,GAAS,CAACjB,EAGzCgD,IAAY/B,GAAAA,YAAAA,EAAOgC,SAAU,EAC7BC,EAAYjB,GAAkBD,IAAcmB,QAAanB,EAAY,EAGzE,OAAAoB,EAAA,KAACjF,GAAa,CAAA,UAAAoD,EAAsB,UAAA/C,EACjCE,SAAAA,CACCA,GAAA0E,EAAA,KAAC3E,GAAM,CAAA,QAAS6C,EACb5C,SAAAA,CAAAA,EACA8C,GAAY,IAAA,EACf,EAGF4B,EAAAA,KAAChE,GACC,CAAA,SAAU,CAAC,CAACK,EACZ,WAAY,CAAC,CAACC,EACd,SAAU,CAAC,CAACM,EACZ,KAAAG,EACA,aAAc,CAAC,CAACwB,EAChB,WAAY,CAAC,EAAEC,GAAWmB,GAC1B,UAAW,CAAC,CAACvD,EAEZmC,SAAAA,CAAaA,GAAA0B,EAAAA,IAACjD,GAAeuB,SAAUA,CAAA,CAAA,EAExC0B,EAAA,IAAC/C,GAAA,CACC,IAAK+B,EACL,KAAAjB,EACA,MAAAH,EACA,SAAW0B,GAAMzB,EAASyB,EAAEW,OAAOrC,KAAK,EACxC,YAAAE,EACA,SAAU,CAAC,EAAEnB,GAAY6B,GACzB,KAAAR,EACA,GAAAC,EACA,SAAU,CAAC,CAACE,EACZ,aAAAC,EACA,aAAc,CAAC,CAACE,EAChB,WAAY,CAAC,EAAEC,GAAWmB,GAE1B,KAAA5C,EACA,UAAA6B,EACA,QAASU,GACT,OAAQG,GACJX,GAAAA,CAAAA,CAAK,EAGVa,SACElC,GAAY,CAAA,KAAK,SAAS,QAAS0B,EAAa,SAAU,GAAG,SAE9D,GAAA,CAAA,EAGDX,GAAYyB,EAAAA,IAAAjD,EAAA,CAAewB,SAAQA,CAAA,CAAA,CAAA,EACtC,GAEEnC,GAASiC,GAAcwB,IACvBE,EAAAA,KAACrC,GAAoB,CAAA,SAAU,CAAC,CAACtB,EAAO,WAAY,CAAC,CAACC,EACpD,SAAA,CAAC2D,EAAAA,IAAA,MAAA,CAAK5D,YAASiC,CAAW,CAAA,EACzBwB,UACE,MACEF,CAAAA,SAAAA,CAAAA,GACAhB,IAAcmB,QAAa,IAAInB,GAAAA,EAClC,CAAA,EAEJ,CAEJ,CAAA,CAAA,CAEJ,ECtVMuB,GAAwBnF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oDAAA,GAAA,EAGd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAS5CnC,GAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,kCAAA,UAAA,8BAAA,gBAAA,IAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAE3B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,IACrC,CAAC,CAAEN,MAAAA,EAAOW,SAAAA,CAAS,IAC1BA,EAAWX,EAAMG,OAAOW,MAAQd,EAAMG,OAAO0B,YAGpC,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOW,MACtB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,GAAG,EAU7CuE,GAAoBpF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,GAAA,EACd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,EAAOW,SAAAA,CAAS,IAC1BA,EAAWX,EAAMG,OAAOW,MAAQd,EAAMG,OAAOC,cACjC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,GAAG,EAQnCwE,EAAsCA,CAAC,CAClDC,SAAAA,EACAhF,MAAAA,EACAgD,WAAAA,EACAF,SAAAA,EAAW,GACX/B,MAAAA,EACA8B,UAAAA,EACAD,GAAAA,EACA,GAAGY,CACL,IAAM,CAEEyB,MAAAA,EAAUrC,GAAM,SAASsC,KAAKC,OAASC,EAAAA,SAAS,EAAE,EAAEC,OAAO,EAAG,CAAC,IAG/DC,EAAeC,EAAMC,SAASC,IAAIT,EAAqBU,GACvDH,EAAMI,eAAeD,CAAK,EACrBH,EAAMK,aAAaF,EAAO,CAC/B9C,GAAIqC,EACJnC,SAAAA,EACA/B,MAAAA,EACA,GAAG2E,EAAMG,KAAAA,CACV,EAEIH,CACR,EAED,OACGhB,EAAAA,KAAAG,GAAA,CAAe,UAAAhC,EAA0BW,GAAAA,EACxC,SAAA,CAAAkB,OAAC3E,IAAM,QAASkF,EAAS,SAAU,CAAC,CAAClE,EAClCf,SAAAA,CAAAA,EACA8C,GAAY6B,EAAA,IAAC,OAAK,CAAA,UAAU,qBAAqB,SAAC,IAAA,CAAA,EACrD,EAECW,GAECtC,GAAcjC,IACb4D,EAAA,IAAAG,GAAA,CAAW,SAAU,CAAC,CAAC/D,EAAQA,SAAAA,GAASiC,CAAW,CAAA,CAExD,CAAA,CAAA,CAEJ,ECxFM8C,GAAmBpG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlC6D,GAAgBrG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,SAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIN,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAG5C6F,GAA4BtG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAG5B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGlC8F,GAAwBC,EAAAA,KAAIvG,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9C8F,GAAqBzG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9C+F,GAAiBC,EAAAA,EAAC1G,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACT,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CwE,GAAmB5G,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,8EAAA,eAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACtB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGzCqE,GAAmB7G,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,sCAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,EAAE,EAKjDqG,GAAmB9G,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,GAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAG5C8E,GAAoB/G,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EACd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUwG,GAEnC,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5C6E,GAAqBjH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,+CAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,EAAOsC,MAAAA,CAAM,IAAOA,GAAS,EAAItC,EAAMG,OAAOwG,OAAS3G,EAAMG,OAAOyG,KAGlE,CAAC,CAAE5G,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGzCmF,GAAuBpH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,GAAA,EACjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACvB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGzCoF,GAAyBrH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EAClB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,EAAE,EAGzCM,GAAsBC,EAAAA,GAAEtH,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,eAAA,KAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAC9B,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzC+G,GAAoBxH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,qEAAA,GAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGlCgH,EAA0BzH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6BAAA,UAAA,mBAAA,4BAAA,GAAA,EAEpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACnB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,MAAM,EAGzDkG,GAAuB1H,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oCAAA,8BAAA,cAAA,IAAA,EAIjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACb,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,EAAE,EAI5CkH,GAAoB3H,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAMlCmH,GAAuBC,GAA+B,CAC1D,OAAQA,EAAS,CACf,IAAK,UACI,MAAA,UACT,IAAK,UACI,MAAA,QACT,IAAK,UACL,QACS,MAAA,SACX,CACF,EAKMC,GAAcC,GACbA,EACQ,IAAIC,KAAKD,CAAU,EACpBE,mBAAmB,QAAS,CACtCC,KAAM,UACNC,OAAQ,SAAA,CACT,EALuB,MAabC,GAAgDA,CAAC,CAC5DC,eAAAA,EACAC,UAAAA,EAAY,GACZjH,MAAAA,EAAQ,KACRkH,UAAAA,EACApF,UAAAA,CACF,IAEMmF,QAECE,EAAK,CAAA,MAAM,kBACV,SAAAvD,EAAA,IAAC,OAAI,MAAO,CAAEwD,QAAS,OAAQC,UAAW,QAAS,EAAG,kCAAsB,CAC9E,CAAA,EAKArH,QAECmH,EAAK,CAAA,MAAM,kBACV,SAAAxD,EAAA,KAAC,OAAI,MAAO,CAAEyD,QAAS,OAAQC,UAAW,SAAUC,MAAO,SAAY,EAAA,SAAA,CAAA,UAC7DtH,EACPkH,GACCtD,EAAA,IAAC,SACC,CAAA,QAASsD,EACT,MAAO,CACLK,WAAY,OACZH,QAAS,WACT3G,WAAY,UACZN,OAAQ,OACRP,aAAc,MACd4H,OAAQ,SAAA,EACR,SAGJ,QAAA,CAAA,CAEJ,CAAA,CACF,CAAA,EAKCR,QA0BFG,EACC,CAAA,MAAM,kBACN,QAASD,EAAY,CAAC,CAAEjI,MAAO,UAAWwI,QAASP,EAAWQ,KAAM,IAAA,CAAM,EAAIhE,OAE9E,SAAAC,EAAA,KAACoB,IAAU,UAAAjD,EACT,SAAA,CAAA6B,OAACqB,GACC,CAAA,SAAA,CAAArB,OAACsB,GACC,CAAA,SAAA,CAAArB,EAAAA,IAACsB,IAAe,SAAiB,mBAAA,CAAA,EAChCtB,EAAAA,IAAA+D,EAAA,CAAM,QAASpB,GAAoBS,EAAeR,SAAS,EAAG,MAAK,GACjEQ,SAAAA,EAAeR,UAAUoB,YAC5B,CAAA,CAAA,CAAA,EACF,SACCxC,GAAY,CAAA,SAAA,CAAA,iBAAeqB,GAAWO,EAAea,WAAW,CAAA,EAAE,CAAA,EACrE,EAEAjE,EAAAA,IAACyB,GAAS2B,CAAAA,SAAAA,EAAec,OAAQ,CAAA,QAEhCvC,GACEyB,CAAAA,SAAAA,EAAee,QAAQrD,IAAKsD,UAC1BxC,GACC,CAAA,SAAA,CAAC5B,EAAAA,IAAA6B,GAAA,CAAWuC,WAAMpG,IAAK,CAAA,QACtB8D,GAAYsC,CAAAA,SAAAA,EAAMxG,MAAMyG,QAAQ,CAAC,EAAE,EACnCtE,EAAA,KAAAiC,GAAA,CAAY,MAAOoC,EAAME,OACvBF,SAAAA,CAAME,EAAAA,QAAU,EAAI,KAAO,KAC3B/D,KAAKgE,IAAIH,EAAME,MAAM,EAAED,QAAQ,CAAC,EAAE,MAAID,EAAMI,cAAgB,KAAKH,QAAQ,CAAC,EAAE,IAAA,EAC/E,EACCD,EAAMK,eACL1E,EAAAA,KAACoC,GAAc,CAAA,SAAA,CAAA,aAAWiC,EAAMK,cAAcJ,QAAQ,CAAC,CAAA,EAAE,CAR7CD,CAAAA,EAAAA,EAAMM,MAUtB,CACD,EACH,EAGCtB,EAAeuB,gBAAkBvB,EAAeuB,eAAe/E,OAAS,UACtEwC,GACC,CAAA,SAAA,CAAApC,EAAAA,IAACqC,IAAa,SAAe,iBAAA,CAAA,SAC5BE,GACC,CAAA,SAAA,CAAAvC,EAAAA,IAACwC,GAAiB,SAAI,MAAA,CAAA,EACtBxC,EAAAA,IAACwC,GAAiB,SAAK,OAAA,CAAA,EACvBxC,EAAAA,IAACwC,GAAiB,SAAQ,UAAA,CAAA,EAC1BxC,EAAAA,IAACwC,GAAiB,SAAQ,UAAA,CAAA,EAC1BxC,EAAAA,IAACwC,GAAiB,SAAM,QAAA,CAAA,EAEvBY,EAAeuB,eAAe7D,IAAI,CAAC8D,EAAOR,WACxC3B,GACC,CAAA,SAAA,CAACzC,EAAAA,IAAA,MAAA,CAAK4E,WAAMC,IAAK,CAAA,SAChBnC,GACEkC,CAAAA,SAAAA,CAAME,EAAAA,MACNF,EAAMG,aAAe,QACpB/E,EAAAA,IAAC+D,GAAM,QAAQ,QAAQ,KAAK,QAAO,SAEnC,aAAA,CAAA,CAAA,EAEJ,EACC/D,EAAA,IAAA,MAAA,CAAK4E,SAAMI,EAAAA,UAAY,IAAI,EAC3BhF,EAAA,IAAA,MAAA,CAAK4E,SAAMK,EAAAA,UAAY,IAAI,EAC3BjF,EAAA,IAAA,MAAA,CAAK4E,SAAMM,EAAAA,QAAU,UAAU,CAAA,CAAA,EAZdd,CAapB,CACD,CAAA,EACH,CAAA,EACF,CAAA,CAEJ,CAAA,CACF,CAAA,QAxFGb,EAAK,CAAA,MAAM,kBACV,SAAAxD,EAAA,KAAC,OAAI,MAAO,CAAEyD,QAAS,OAAQC,UAAW,QAAW,EAAA,SAAA,CAAA,4BAElDH,GACCtD,EAAA,IAAC,SACC,CAAA,QAASsD,EACT,MAAO,CACLK,WAAY,OACZH,QAAS,WACT3G,WAAY,UACZN,OAAQ,OACRP,aAAc,MACd4H,OAAQ,SAAA,EACR,SAGJ,UAAA,CAAA,CAEJ,CAAA,CACF,CAAA,EChNAzC,GAAmBpG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlC4H,EAAiBpK,EAAAA,IAAGC,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACP,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAG5C6H,EAAsB9C,EAAAA,GAAEtH,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,eAAA,YAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAC9B,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GAClC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5CkI,GAAkBtK,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGlB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGlC8J,GAAkBvK,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2CAAA,qBAAA,kBAAA,YAAA,gCAAA,EAGd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACxC,CAAC,CAAE+J,UAAAA,CAAU,IAAOA,EAAY,GAAM,CAAE,EAI/CC,GAA2BzK,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gBAAA,GAAA,EAClB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAG3CiK,GAAkBvI,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAI5B,EAAA,CAAA,wCAAA,CAAA,EAEKwK,GAAqB3K,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,UAAA,EAGrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAIlC2I,GAAqB5K,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,oBAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAClB,CAAC,CAAEoI,UAAAA,CAAU,IAAOA,EAAY,eAAiB,MAAO,EAGvEK,GAAqB7K,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EAErB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGlCqK,GAA4B9K,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8EAAA,eAAA,GAAA,EAG5B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACtB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzCsK,EAA4B/K,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,sCAAA,EACxB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,EAAE,EAKjDuK,EAA6BhL,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,GAAA,EACvB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAG5CgJ,EAA6BjL,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EACvB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5C8I,EAAelL,EAAAA,IAAGC,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,cAAA,0BAAA,wBAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACtC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5C+I,GAAqBC,EAAAA,KAAInL,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,YAAA,qBAAA,kBAAA,GAAA,EAGtB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACtB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GAChC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,EAAE,EAGjD4K,GAAqBrL,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6CAAA,GAAA,EAGrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGlC6K,GAAoBtL,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,GAE/B,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAO9C4K,GAAsBC,GAAkC,CAC5D,OAAQA,EAAQ,CACd,IAAK,OACI,MAAA,QACT,IAAK,SACI,MAAA,UACT,IAAK,MACL,QACS,MAAA,MACX,CACF,EAOaC,GAA0CA,CAAC,CACtDC,YAAAA,EACApD,UAAAA,EAAY,GACZjH,MAAAA,EAAQ,KACRsK,aAAAA,EACAC,UAAAA,EACAC,aAAAA,EACA1I,UAAAA,CACF,IAAM,CACJ,KAAM,CAAC2I,EAAaC,CAAc,EAAI/H,WAAS,EAAK,EAC9C,CAACgI,EAASC,CAAU,EAAIjI,WAAsC,CAClEkI,YAAa,GACbV,SAAU,SACVhB,UAAW,EAAA,CACZ,EAEK2B,EAAiB5H,GAAuB,CAC5CA,EAAE6H,eAAe,EAEb,GAACJ,EAAQE,YAAYG,KAAAA,GAAU,CAACT,KAE1BA,EAAA,CACR,GAAGI,EACH9I,GAAI8E,KAAKsE,IAAI,EAAE5G,SAAS,CAAA,CACzB,EAEUuG,EAAA,CACTC,YAAa,GACbV,SAAU,SACVhB,UAAW,EAAA,CACZ,EAEDuB,EAAe,EAAK,EAAA,EAItB,OAAIzD,QAECE,EAAK,CAAA,MAAM,eACV,SAAAvD,EAAA,IAAC,OAAI,MAAO,CAAEwD,QAAS,OAAQC,UAAW,QAAS,EAAG,mCAAuB,CAC/E,CAAA,EAKArH,QAECmH,EAAK,CAAA,MAAM,eACV,SAAAxD,EAAA,KAAC,OAAI,MAAO,CAAEyD,QAAS,OAAQC,UAAW,SAAUC,MAAO,SAAa,EAAA,SAAA,CAAA,UAAQtH,CAAAA,CAAM,CAAA,CACxF,CAAA,EAKCqK,QAgBFlD,EACC,CAAA,MAAM,eACN,QACEoD,EACI,CAAC,CAAEtL,MAAO,WAAYwI,QAASA,IAAMiD,EAAe,EAAI,EAAGhD,KAAM,GAAA,CAAK,EACtEhE,OAGN,SAAAC,EAAA,KAACoB,IAAU,UAAAjD,EAERuI,SAAAA,CAAYa,EAAAA,iBACVnC,EACC,CAAA,SAAA,CAAAnF,EAAAA,IAACoF,GAAa,SAAQ,UAAA,CAAA,EACtBpF,EAAAA,IAACiG,EAAOQ,CAAAA,SAAAA,EAAYa,QAAS,CAAA,CAAA,EAC/B,SAIDnC,EACC,CAAA,SAAA,CAAAnF,EAAAA,IAACoF,GAAa,SAAY,cAAA,CAAA,EAC1BpF,EAAA,IAACqF,GACEoB,CAAAA,SAAAA,EAAYc,MAAMzG,OAChBf,EAAA,KAAAuF,GAAA,CAAuB,UAAWkC,EAAKjC,UACtC,SAAA,CAACvF,EAAAA,IAAAwF,GAAA,CACC,eAACC,GACC,CAAA,KAAK,WACL,QAAS,CAAC,CAAC+B,EAAKjC,UAChB,YAAiBmB,GAAAA,YAAAA,EAAec,EAAKvJ,GAAIqB,EAAEW,OAAOwH,SAClD,SAAU,CAACf,CAAAA,CAAa,CAE5B,CAAA,SACChB,GACC,CAAA,SAAA,CAAA1F,MAAC2F,GAAY,CAAA,UAAW6B,EAAKjC,UAAYiC,WAAKP,YAAY,EAC1DjH,EAAAA,IAAC+D,GAAM,QAASuC,GAAmBkB,EAAKjB,QAAQ,EAAIiB,WAAKjB,SAAS,CAAA,EACpE,EACCK,GACE5G,EAAAA,IAAA4F,GAAA,CACC,SAAC5F,EAAAA,IAAA0H,EAAA,CACC,QAAQ,OACR,QAAS,IAAMd,EAAaY,EAAKvJ,EAAE,EACnC,aAAW,cAAa,cAG1B,CAAA,EACF,CAtBWuJ,CAAAA,EAAAA,EAAKvJ,EAwBpB,CACD,EACH,CAAA,EACF,EAGCwI,EAAYkB,gBACX5H,EAAAA,KAACoF,EACC,CAAA,SAAA,CAAAnF,EAAAA,IAACoF,GAAa,SAAe,iBAAA,CAAA,SAC5BS,GACC,CAAA,SAAA,CAAA9F,OAAC+F,EACC,CAAA,SAAA,CAAA9F,EAAAA,IAAC+F,GAAoB,SAAkB,oBAAA,CAAA,SACtCC,EACES,CAAAA,SAAAA,CAAAA,EAAYkB,eAAeC,gBAAgB,GAAA,EAC9C,CAAA,EACF,SACC9B,EACC,CAAA,SAAA,CAAA9F,EAAAA,IAAC+F,GAAoB,SAAc,gBAAA,CAAA,SAClCC,EACES,CAAAA,SAAAA,CAAAA,EAAYkB,eAAeE,aAAa,GAAA,EAC3C,CAAA,EACF,SACC/B,EACC,CAAA,SAAA,CAAA9F,EAAAA,IAAC+F,GAAoB,SAAU,YAAA,CAAA,EAC9B/F,EAAA,IAAAgG,EAAA,CAAqBS,SAAYkB,EAAAA,eAAeG,UAAU,CAAA,EAC7D,SACChC,EACC,CAAA,SAAA,CAAA9F,EAAAA,IAAC+F,GAAoB,SAAe,iBAAA,CAAA,EACnC/F,EAAA,IAAAgG,EAAA,CACES,SAAYkB,EAAAA,eAAeI,eAC9B,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAIDtB,EAAYuB,OACXjI,EAAAA,KAACoF,EACC,CAAA,SAAA,CAAAnF,EAAAA,IAACoF,GAAa,SAAK,OAAA,CAAA,EACnBpF,EAAAA,IAACiG,EAAOQ,CAAAA,SAAAA,EAAYuB,KAAM,CAAA,CAAA,EAC5B,EAIDnB,GACC9G,EAAA,KAACmG,GAAY,CAAA,SAAUgB,EACrB,SAAA,CAAClH,EAAA,IAAAI,EAAA,CAAU,MAAM,cACf,SAACJ,EAAAA,IAAArC,GAAA,CACC,MAAOoJ,EAAQE,YACf,SAAWrJ,GAAUoJ,EAAW,CAAE,GAAGD,EAASE,YAAarJ,CAAAA,CAAO,EAClE,YAAY,yBACZ,SAAQ,GACR,UAAS,EAAA,CAAA,EAEb,EACAoC,EAAA,IAACI,EAAU,CAAA,MAAM,WACf,SAAAL,EAAAA,KAAC,SACC,CAAA,MAAOgH,EAAQR,SACf,SAAWjH,GACT0H,EAAW,CAAE,GAAGD,EAASR,SAAUjH,EAAEW,OAAOrC,KAA8B,CAAA,EAE5E,MAAO,CACL4F,QAAS,MACTxH,aAAc,MACdO,OAAQ,iBACR0L,MAAO,MAGT,EAAA,SAAA,CAACjI,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAI,OAAA,EACxBA,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAM,SAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAM,MAAM,SAAG,MAAA,CAAA,CAAA,CACzB,CACF,CAAA,SACCoG,GACC,CAAA,SAAA,CAACpG,EAAAA,IAAA0H,EAAA,CAAO,QAAQ,UAAU,QAAS,IAAMZ,EAAe,EAAK,EAAE,SAE/D,QAAA,CAAA,EACC9G,EAAA,IAAA0H,EAAA,CAAO,KAAK,SAAS,SAAQ,WAAA,CAAA,EAChC,CAAA,EACF,CAAA,CAEJ,CAAA,CACF,CAAA,EA/IG1H,EAAA,IAAAuD,EAAA,CAAK,MAAM,eACV,gBAAC8C,GAAU,CAAA,SAAA,CAAA,6BAERM,GACE3G,EAAA,IAAA,MAAA,CAAI,MAAO,CAAEkI,UAAW,MAAO,EAC9B,eAACR,EAAO,CAAA,QAAS,IAAMZ,EAAe,EAAI,EAAG,SAAA,qBAAA,CAAmB,CAClE,CAAA,CAAA,CAEJ,CAAA,CACF,CAAA,CAwIN,ECzVM3F,GAAmBpG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlC4K,GAAoBpN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlC6K,GAAmBrN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EACN,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACxC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GAC1B,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAM+M,QAAQ7M,EAAE,EAGzC8M,GAAgBvN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,SAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,kBAAA,mCAAA,EACV,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUwG,GAEnC,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACpB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAK5CgL,EAAmBxN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,8BAAA,GAAA,EAET,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAG5CwL,EAAoBzN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yBAAA,0BAAA,GAAA,EAEd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9C+M,EAAoB1N,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EACd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,EAAOyC,KAAAA,CAAK,IAAM,CAC5B,OAAQA,EAAI,CACV,IAAK,UACH,OAAOzC,EAAMG,OAAOwG,OACtB,IAAK,aACH,OAAO3G,EAAMG,OAAOyG,KACtB,IAAK,QACH,OAAO5G,EAAMG,OAAOiN,OACtB,QACE,OAAOpN,EAAMG,OAAO0B,WACxB,CACF,CAAC,EAGGkJ,GAAoBtL,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,GAE/B,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EASvCiN,GAAsCA,CAAC,CAClDC,UAAAA,EACAvF,UAAAA,EAAY,GACZjH,MAAAA,EAAQ,KACRkH,UAAAA,EACApF,UAAAA,CACF,IAEMmF,QAECE,EAAK,CAAA,MAAM,mBACV,SAAAvD,EAAA,IAAC,OAAI,MAAO,CAAEwD,QAAS,OAAQC,UAAW,QAAS,EAAG,iCAAqB,CAC7E,CAAA,EAKArH,QAECmH,EAAK,CAAA,MAAM,mBACV,SAAAxD,EAAA,KAAC,OAAI,MAAO,CAAEyD,QAAS,OAAQC,UAAW,SAAUC,MAAO,SAAY,EAAA,SAAA,CAAA,UAC7DtH,EACPkH,GACCtD,EAAA,IAAC,SACC,CAAA,QAASsD,EACT,MAAO,CACLK,WAAY,OACZH,QAAS,WACT3G,WAAY,UACZN,OAAQ,OACRP,aAAc,MACd4H,OAAQ,SAAA,EACR,SAGJ,QAAA,CAAA,CAEJ,CAAA,CACF,CAAA,EAKA,CAACgF,GAAaA,EAAUhJ,SAAW,EAElCI,EAAA,IAAAuD,EAAA,CAAK,MAAM,mBACV,gBAAC8C,GAAU,CAAA,SAAA,CAAA,iCAER/C,GACEtD,EAAA,IAAA,MAAA,CAAI,MAAO,CAAEkI,UAAW,MAAA,EACvB,SAAAlI,EAAAA,IAAC,SACC,CAAA,QAASsD,EACT,MAAO,CACLE,QAAS,WACT3G,WAAY,UACZN,OAAQ,OACRP,aAAc,MACd4H,OAAQ,SAAA,EACR,kBAGJ,CAAA,EACF,CAAA,CAEJ,CAAA,CACF,CAAA,QAKDL,EACC,CAAA,MAAM,mBACN,QAASD,EAAY,CAAC,CAAEjI,MAAO,UAAWwI,QAASP,EAAWQ,KAAM,IAAA,CAAM,EAAIhE,OAE9E,SAAAE,EAAAA,IAACmB,IAAU,UAAAjD,EACT,SAAA8B,MAACmI,GACES,CAAAA,SAAAA,EAAU9H,IAAI,CAAC+H,EAAOzE,WACpBgE,GACC,CAAA,SAAA,CAAArI,OAACuI,GACEO,CAAAA,SAAAA,CAAMnE,EAAAA,OACN3E,EAAAA,KAAAgE,EAAA,CAAM,QAAQ,UAAU,MAAO,CAAEJ,WAAY,KAC3CkF,EAAAA,SAAAA,CAAMC,EAAAA,QAAQlJ,OAASiJ,EAAME,WAAWnJ,OAAO,SAAA,EAClD,CAAA,EACF,SAEC2I,EACC,CAAA,SAAA,CAAAvI,EAAAA,IAACwI,GAAW,SAAU,YAAA,CAAA,EACtBxI,EAAAA,IAACyI,GAAW,KAAK,aAAcI,WAAME,WAAWC,KAAK,KAAK,EAAE,CAAA,EAC9D,EAECH,EAAMI,YACLlJ,EAAAA,KAACwI,EACC,CAAA,SAAA,CAAAvI,EAAAA,IAACwI,GAAW,SAAK,OAAA,CAAA,EAChBxI,EAAA,IAAAyI,EAAA,CAAW,KAAK,QAASI,WAAMI,WAAW,CAAA,EAC7C,SAGDV,EACC,CAAA,SAAA,CAAAvI,EAAAA,IAACwI,GAAW,SAAO,SAAA,CAAA,EACnBxI,EAAAA,IAACyI,GAAW,KAAK,UAAWI,WAAMC,QAAQE,KAAK,KAAK,EAAE,CAAA,EACxD,CAAA,CAAA,EAvBc5E,CAwBhB,CACD,CACH,CAAA,CACF,CAAA,CACF,CAAA,ECvLS8E,GAAsB,SAAqC,CAMtE,GAJA,MAAM,IAAIC,QAASC,GAAYC,WAAWD,EAAS,GAAG,CAAC,EAGnC7I,KAAKC,OAAAA,EAAW,IAE5B,MAAA,IAAI8I,MAAM,kCAAkC,EAGpD,OAAOC,GAAiB,CAC1B,EAKMA,GAAmBA,IAAsB,CAE7C,MAAMC,EAAgC,CAAC,UAAW,UAAW,SAAS,EAChEC,EAAkBD,EAAWjJ,KAAKmJ,MAAMnJ,KAAKC,SAAWgJ,EAAW5J,MAAM,CAAC,EAE1E+J,EAAa,CACjB/G,UAAW6G,EACXvF,QAAS0F,GAAuBH,CAAe,EAC/CtF,QAAS,CACP,CACEnG,KAAM,UACNJ,MAAO,KAAO2C,KAAKC,OAAW,EAAA,IAC9B8D,QACG/D,KAAKC,OAAAA,EAAW,EAAI,IACpBiJ,IAAoB,UAAY,EAAIA,IAAoB,UAAY,GAAK,IAC5EhF,cAAe,KAAOlE,KAAKC,OAAW,EAAA,EAAA,EAExC,CACExC,KAAM,SACNJ,MAAO,KAAQ2C,KAAKC,OAAW,EAAA,IAC/B8D,QACG/D,KAAKC,OAAAA,EAAW,EAAI,IACpBiJ,IAAoB,UAAY,EAAIA,IAAoB,UAAY,GAAK,IAC5EhF,cAAe,KAAQlE,KAAKC,OAAW,EAAA,GAAA,EAEzC,CACExC,KAAM,YACNJ,MAAO,KAAQ2C,KAAKC,OAAW,EAAA,IAC/B8D,QACG/D,KAAKC,OAAAA,EAAW,EAAI,IACpBiJ,IAAoB,UAAY,EAAIA,IAAoB,UAAY,GAAK,IAC5EhF,cAAe,KAAQlE,KAAKC,OAAW,EAAA,GAAA,EAEzC,CACExC,KAAM,MACNJ,MAAO,GAAK2C,KAAKC,OAAW,EAAA,GAC5B8D,QACG/D,KAAKC,OAAAA,EAAW,EAAI,IACpBiJ,IAAoB,UAAY,GAAKA,IAAoB,UAAY,EAAI,IAC5EhF,cAAe,GAAKlE,KAAKC,OAAW,EAAA,CAAA,CACrC,EAEHyD,YAAa,IAAIlB,KAAK,EAAE8G,YAAY,CAAA,EAMhCpD,EAAc,CAClB,CACExI,GAAI,IACJgJ,YAAa,iDACbV,SAAU,OACVhB,UAAW,EAAA,EAEb,CACEtH,GAAI,IACJgJ,YAAa,8CACbV,SAAU,SACVhB,UAAW,EAAA,EAEb,CACEtH,GAAI,IACJgJ,YAAa,mDACbV,SAAU,OACVhB,UAAW,EAAA,EAEb,CACEtH,GAAI,IACJgJ,YAAa,sDACbV,SAAU,SACVhB,UAAW,EAAA,EAEb,CACEtH,GAAI,IACJgJ,YAAa,uCACbV,SAAU,MACVhB,UAAW,EAAA,CACZ,EAIGqD,EAAY,CAChB,CACElE,OAAQ,MACRoE,QAAS,CAAC,SAAU,SAAU,QAAQ,EACtCC,WAAY,CAAC,SAAU,SAAU,QAAQ,EACzCE,WAAY,QAAA,EAEd,CACEvE,OAAQ,MACRoE,QAAS,CAAC,SAAU,SAAU,QAAQ,EACtCC,WAAY,CAAC,SAAU,SAAU,QAAQ,EACzCE,WAAY,QAAA,EAEd,CACEvE,OAAQ,OACRoE,QAAS,CAAC,SAAU,SAAU,QAAQ,EACtCC,WAAY,CAAC,SAAU,SAAU,QAAQ,EACzCE,WAAY,QAAA,CACb,EAIGa,EAAa,CACjB,CACE7L,GAAI,IACJ6G,MAAO,sDACPiF,OAAQ,kBACRC,IAAK,6BACLC,UAAW,IAAIlH,KAAKA,KAAKsE,IAAQ,EAAA,EAAI,GAAK,GAAK,GAAI,EAAEwC,YAAY,EACjEK,OAAQ,MAAA,EAEV,CACEjM,GAAI,IACJ6G,MAAO,+CACPiF,OAAQ,sBACRC,IAAK,6BACLC,UAAW,IAAIlH,KAAKA,KAAKsE,IAAQ,EAAA,EAAI,GAAK,GAAK,GAAI,EAAEwC,YAAY,EACjEK,OAAQ,QAAA,EAEV,CACEjM,GAAI,IACJ6G,MAAO,+CACPiF,OAAQ,YACRC,IAAK,6BACLC,UAAW,IAAIlH,KAAKA,KAAKsE,IAAQ,EAAA,EAAI,GAAK,GAAK,GAAI,EAAEwC,YAAY,EACjEK,OAAQ,KAAA,CACT,EAGI,MAAA,CACLP,WAAAA,EACAlD,YAAAA,EACAmC,UAAAA,EACAkB,WAAAA,CAAAA,CAEJ,EAKMF,GAA0BhH,GAAuC,CACrE,OAAQA,EAAS,CACf,IAAK,UACI,MAAA,6IACT,IAAK,UACI,MAAA,sIACT,IAAK,UACI,MAAA,gIACT,QACS,MAAA,4FACX,CACF,EC5JMuH,GAAgC,CACpCR,WAAY,KACZlD,YAAa,CAAE,EACfmC,UAAW,CAAE,EACbkB,WAAY,CAAE,EACdzG,UAAW,GACXjH,MAAO,KACPgO,YAAa,IAAIrH,OAAOsH,mBAAmB,QAAS,CAClDC,QAAS,OACTC,KAAM,UACNC,MAAO,OACPC,IAAK,SAAA,CACN,CACH,EAGMC,GAAoBA,CAACC,EAAwBC,IAA8C,CAC/F,OAAQA,EAAO7M,KAAI,CACjB,IAAK,mBACI,MAAA,CACL,GAAG4M,EACHtH,UAAW,GACXjH,MAAO,IAAA,EAEX,IAAK,qBACI,MAAA,CACL,GAAGuO,EACH,GAAGC,EAAOC,QACVxH,UAAW,GACXjH,MAAO,IAAA,EAEX,IAAK,mBACI,MAAA,CACL,GAAGuO,EACHtH,UAAW,GACXjH,MAAOwO,EAAOC,OAAAA,EAElB,IAAK,2BACI,MAAA,CACL,GAAGF,EACHlE,YAAakE,EAAMlE,YAAY3F,OAC7B0G,EAAKvJ,KAAO2M,EAAOC,QAAQ5M,GAAK,CAAE,GAAGuJ,EAAMjC,UAAWqF,EAAOC,QAAQtF,WAAciC,CACrF,CAAA,EAEJ,IAAK,eACI,MAAA,CACL,GAAGmD,EACHtH,UAAW,GACXjH,MAAO,IAAA,EAEX,QACSuO,OAAAA,CACX,CACF,EASMG,EAAoBC,EAAAA,cAAiDjL,MAAS,EAOvEkL,GAAwDA,CAAC,CAAE3K,SAAAA,CAAS,IAAM,CACrF,KAAM,CAACsK,EAAOM,CAAQ,EAAIC,EAAAA,WAAWR,GAAmBP,EAAY,EAE9DgB,EAAiBC,EAAAA,YAAY,SAAY,CACpCH,EAAA,CAAElN,KAAM,kBAAA,CAAoB,EACjC,GAAA,CACIsN,MAAAA,EAAO,MAAMnC,KACV+B,EAAA,CAAElN,KAAM,qBAAsB8M,QAASQ,CAAAA,CAAM,QAC/CjP,GACE6O,EAAA,CACPlN,KAAM,mBACN8M,QAASzO,aAAiBkN,MAAQlN,EAAMkP,QAAU,2BAAA,CACnD,CACH,CACF,EAAG,CAAE,CAAA,EAECC,EAAwBH,EAAAA,YAAY,CAACnN,EAAYsH,IAAuB,CACnE0F,EAAA,CAAElN,KAAM,2BAA4B8M,QAAS,CAAE5M,GAAAA,EAAIsH,UAAAA,CAAU,CAAA,CAAG,CAC3E,EAAG,CAAE,CAAA,EAECiG,EAAcJ,EAAAA,YAAY,IAAM,CAC3BH,EAAA,CAAElN,KAAM,cAAA,CAAgB,EAClBoN,GAAA,EACd,CAACA,CAAc,CAAC,EAGnBM,EAAAA,UAAU,IAAM,CACCN,GAAA,EACd,CAACA,CAAc,CAAC,EAEnB,MAAMvN,EAAQ,CACZ,GAAG+M,EACHQ,eAAAA,EACAI,sBAAAA,EACAC,YAAAA,CAAAA,EAGF,OAAQxL,EAAAA,IAAA8K,EAAkB,SAAlB,CAA2B,MAAAlN,EAAeyC,SAAAA,CAAS,CAAA,CAC7D,EAGaqL,EAAgBA,IAA6B,CAClDC,MAAAA,EAAUC,aAAWd,CAAiB,EAC5C,GAAIa,IAAY7L,OACR,MAAA,IAAIwJ,MAAM,wDAAwD,EAEnEqC,OAAAA,CACT,EC/HMxK,GAAmBpG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlCsO,GAAkB9Q,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGlB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGlCsQ,GAAkB/Q,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,2CAAA,GAAA,EACd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAG5C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGlC+O,GAAoBhR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAI5B,EAAA,CAAA,oEAAA,CAAA,EAEK8Q,GAAmBC,EAAAA,EAACjR,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,wEAAA,IAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAM1B,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAI1C4P,GAAoBnR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,GAAA,EACd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACvB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,GAAG,EAG1CuQ,GAAkBpR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,mCAAA,GAAA,EACZ,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAEtB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAG1C4Q,GAAyBrR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uDAAA,IAAA,EAAA,EAIhB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GAE3C,CAAC,CAAEkN,OAAAA,EAAQ5O,MAAAA,CAAM,IAAM,CACvB,OAAQ4O,EAAM,CACZ,IAAK,OACI,MAAA,qBAAqB5O,EAAMG,OAAOW,SAC3C,IAAK,SACI,MAAA,qBAAqBd,EAAMG,OAAO4Q,WAC3C,IAAK,MACI,MAAA,qBAAqB/Q,EAAMG,OAAO6Q,QAC3C,QACS,MAAA,EACX,CACF,CAAC,EAGGjG,GAAoBtL,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,GAE/B,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAIvC6Q,GAAwCA,CAAC,CAAErO,UAAAA,CAAU,IAAM,CAChE,KAAA,CAAE4L,WAAAA,GAAe4B,EAAc,EAErC,GAAI,CAAC5B,GAAcA,EAAWlK,SAAW,EAChC,OAAAI,EAAA,IAACqG,IAAW,SAAwB,0BAAA,CAAA,EAGvCmG,MAAAA,EAAcvC,GAA8B,CAC1CwC,MAAAA,EAAO,IAAI1J,KAAKkH,CAAS,EAEzByC,MADU3J,OACG4J,QAAQ,EAAIF,EAAKE,QAAQ,EACtCC,EAAUrM,KAAKmJ,MAAMgD,GAAU,IAAO,GAAK,GAAG,EAEpD,GAAIE,EAAU,EAAG,CACf,MAAMC,EAAWtM,KAAKmJ,MAAMgD,EAAU,GAAU,EAChD,MAAO,GAAGG,QAAeA,IAAa,EAAI,IAAM,aAClD,QAAWD,EAAU,GACZ,GAAGA,OAAaA,IAAY,EAAI,IAAM,SAEtCH,EAAKpC,oBACd,EAIA,OAAArK,EAAA,IAACmB,GAAU,CAAA,UAAAjD,EACT,SAAC8B,MAAA6L,GAAA,CACE/B,SAAWhJ,EAAAA,IACVgM,GAAA9M,EAAAA,IAAC8L,GACC,CAAA,SAAA/L,EAAA,KAACgM,GACC,CAAA,SAAA,CAAAhM,OAAC,MACC,CAAA,SAAA,CAAAA,EAAAA,KAACiM,IAAU,KAAMc,EAAK9C,IAAK,OAAO,SAAS,IAAI,sBAC7C,SAAA,CAAChK,EAAAA,IAAAoM,GAAA,CAAgB,OAAQU,EAAK5C,MAAO,CAAA,EACpC4C,EAAKhI,KAAAA,EACR,EACA9E,EAAAA,IAACkM,GAAYY,CAAAA,SAAAA,EAAK/C,MAAO,CAAA,CAAA,EAC3B,EACC/J,EAAA,IAAAmM,GAAA,CAAUK,SAAWM,EAAAA,EAAK7C,SAAS,EAAE,CAAA,CACxC,CAAA,CAVa6C,EAAAA,EAAK7O,EAWpB,CACD,EACH,CACF,CAAA,CAEJ,ECnHMkD,GAAmBpG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,kBAAA,GAAA,EACN,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOe,QAC/B,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMU,aAAauB,GACxC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,GAC1B,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAM+M,QAAQ7M,GAC1B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,EAAE,EAG5CX,GAAgBrG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,SAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIN,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAG5CwP,GAAeC,EAAAA,GAAEhS,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACR,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUwG,GAEnC,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5C8P,GAAiBlS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAE,EAAA,CAAA,EAAA,CAAA,EAEtBgS,GAA4BnS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iCAAA,kBAAA,iEAAA,GAAA,EAEf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC/B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAI1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9CyR,GAAwBpS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,UAAA,kBAAA,GAAA,EACpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOW,MAAQ,KACvC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOW,MACpB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGrC6P,EAA0CA,CAAC,CACtDtI,MAAAA,EACAzE,SAAAA,EACAgD,UAAAA,EAAY,GACZpH,SAAAA,EAAW,GACXoR,aAAAA,EAAe,uCACfC,aAAAA,CACF,WAEKnM,GACC,CAAA,SAAA,CAAApB,OAACqB,GACC,CAAA,SAAA,CAAApB,EAAAA,IAAC+M,IAAOjI,SAAMA,CAAA,CAAA,EACbwI,GAAgBA,CAAAA,EACnB,EAECrR,GACE+D,EAAA,IAAAmN,GAAA,CACC,SAACnN,EAAAA,IAAA,IAAA,CAAGqN,UAAa,CAAA,EACnB,EAGDhK,EACErD,EAAA,IAAAkN,GAAA,CAAmB,2BAAe,EAEnClN,MAACiN,IAAS5M,SAAAA,EAAS,CAEvB,CAAA,CAAA,EC5EEkN,GAAuBxS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,2CAAA,GAAA,EAGvB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,GAGzB,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,EAAE,EAGtCyL,GAAoBzS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIV,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQoG,EAAE,EAG5CgL,GAAeU,EAAAA,GAAEzS,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACR,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUmS,IAEnC,CAAC,CAAEpS,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CwQ,GAAqB5S,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9CkS,GAAgBC,EAAOnG,CAAM,EAAC1M,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,eAAA,GAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAG1CuQ,GAA8BA,IAAM,CAClC,KAAA,CAAEzK,UAAAA,EAAWjH,MAAAA,EAAOgO,YAAAA,EAAaoB,YAAAA,GAAgBE,EAAc,EAE/DqC,EAAgBA,IAAM,CACdvC,GAAA,EAGd,cACG+B,GACC,CAAA,SAAA,CAAAxN,OAACyN,GACC,CAAA,SAAA,CAAAxN,EAAAA,IAAC+M,IAAM,SAAmB,qBAAA,CAAA,EAC1BhN,OAAC,OAAI,MAAO,CAAEiO,QAAS,OAAQC,WAAY,QACzC,EAAA,SAAA,CAAAjO,EAAAA,IAAC2N,IAAavD,SAAYA,CAAA,CAAA,EAC1BpK,EAAAA,IAAC4N,GACC,CAAA,QAAQ,UACR,KAAK,QACL,QAASG,EACT,SAAU1K,EAAU,SAGtB,SAAA,CAAA,CAAA,EACF,CAAA,EACF,EAECrD,EAAA,IAAAoN,EAAA,CACC,MAAM,kBACN,UAAA/J,EACA,SAAU,CAAC,CAACjH,EACZ,aAAcA,GAAS,GAEvB,SAAA4D,EAAA,IAACmD,IAAc,CAAA,EACjB,EAECnD,EAAA,IAAAoN,EAAA,CACC,MAAM,eACN,UAAA/J,EACA,SAAU,CAAC,CAACjH,EACZ,aAAcA,GAAS,GAEvB,SAAA4D,EAAA,IAACwG,IAAW,CAAA,EACd,EAECxG,EAAA,IAAAoN,EAAA,CACC,MAAM,aACN,UAAA/J,EACA,SAAU,CAAC,CAACjH,EACZ,aAAcA,GAAS,GAEvB,SAAA4D,EAAA,IAAC2I,IAAS,CAAA,EACZ,EAEC3I,EAAA,IAAAoN,EAAA,CACC,MAAM,cACN,UAAA/J,EACA,SAAU,CAAC,CAACjH,EACZ,aAAcA,GAAS,GAEvB,SAAA4D,EAAA,IAACuM,IAAU,CAAA,EACb,CACF,CAAA,CAAA,CAEJ,EAEM2B,GAAuBA,IAExBlO,EAAA,IAAAgL,GAAA,CACC,SAAChL,EAAA,IAAA8N,GAAA,CAAA,CAAiB,CACpB,CAAA"}