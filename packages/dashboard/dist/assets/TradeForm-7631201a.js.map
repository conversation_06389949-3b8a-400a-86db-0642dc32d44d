{"version": 3, "mappings": "+QAgBgBA,YACdC,EACAC,EACAC,EACA,CACA,KAAM,CAACC,EAAWC,CAAY,EAAIC,WAASJ,CAAU,EAC/C,CAACK,EAAOC,CAAQ,EAAIF,WAAwB,IAAI,EAChD,CAACG,EAASC,CAAU,EAAIJ,WAAwB,IAAI,EACpD,CAACK,EAAWC,CAAY,EAAIN,WAAuB,IAAI,EAGvD,CAACO,EAAYC,CAAa,EAAIR,WAA0B,CAE5DS,SAAUC,OAAOC,YAAcC,QAAM,GAAG,EAAE,CAAC,EAC3CC,OAAQ,GACRC,UAAW,OACXC,SAAU,GAGVC,WAAY,GACZC,UAAW,GACXC,SAAU,GACVC,WAAY,GACZC,OAAQ,GAGRC,UAAW,GACXC,QAAS,GACTC,MAAO,GACPC,UAAW,GACXC,SAAU,GACVC,OAAQ,GACRC,OAAQ,SACRC,UAAW,GACXC,aAAc,cACdC,WAAY,GACZC,eAAgB,IAGhBC,qBAAsB,GACtBC,iBAAkB,GAClBC,uBAAwB,GACxBC,mBAAoB,GACpBC,eAAgB,GAChBC,eAAgB,CAAE,EAClBC,cAAe,GACfC,gBAAiB,GACjBC,cAAe,GAGfC,sBAAuB,GACvBC,yBAA0B,GAC1BC,sBAAuB,GACvBC,mBAAoB,GACpBC,qBAAsB,GACtBC,wBAAyB,GACzBC,qBAAsB,GACtBC,oBAAqB,GAGrBC,QAAS,GACTC,YAAa,GACbC,YAAa,GACbC,WAAY,CAAE,EACdC,eAAgB,GAChBC,iBAAkB,GAClBC,aAAc,GACdC,mBAAoB,GACpBC,iBAAkB,IAClBC,SAAU,GAGVC,MAAO,GACPC,KAAM,CAAE,EACRC,OAAQ,MACT,EAGDC,YAAU,IAAM,EACQ,SAAY,iDAMhC,GALQC,YACN,sCAAsCnE,gBAAyBD,IACjE,EAGI,CAACA,EAAS,CACZqE,QAAQ/D,MAAM,6CAA6C,EAC3D,OAGF,GAAIN,IAAY,MAAO,CACrBqE,QAAQD,IAAI,2CAA2C,EACvD,OAGF,GAAInE,EACE,IACMmE,YAAI,yCAAyCpE,GAAS,EAC9DI,EAAa,EAAI,EAGjB,MAAMkE,EAAcC,OAAOvE,CAAO,EAAEwE,KAAK,EACzC,GAAI,CAACF,EACG,UAAIG,MAAM,uCAAuC,EAGjDL,YAAI,8CAA8CE,GAAa,EACvE,MAAMI,EAAQ,MAAMC,GAAaC,aAAaN,CAAW,EACjDF,YAAI,wBAAyBM,CAAK,EAEtCA,GACFL,QAAQD,IAAI,iCAAiC,EAC7CzD,EAAa+D,CAAK,EAElBL,QAAQD,IAAI,sCAAsC,EAEpCvD,GAEZC,KAAM4D,EAAM5D,KACZI,OAAQwD,EAAMxD,OACdC,UAAWuD,EAAMvD,YAAc,OAAS,OAAS,QACjDC,SAAUmD,OAAOG,EAAMG,IAAI,EAG3BxD,WAAYkD,OAAOG,EAAMI,KAAK,EAC9BxD,UAAWiD,OAAOG,EAAMK,IAAI,EAC5BxD,SAAUmD,EAAMnD,SAAWgD,OAAOG,EAAMnD,QAAQ,EAAI,GACpDC,WAAYkD,EAAMlD,WAAa+C,OAAOG,EAAMlD,UAAU,EAAI,GAC1DC,OAAQ8C,OAAOG,EAAMM,UAAU,EAG/BtD,UAAWgD,EAAMhD,WAAa,GAC9BC,QAAS+C,EAAM/C,SAAW,GAC1BC,MAAO8C,EAAM9C,OAAS,GACtBC,UAAW6C,EAAM7C,WAAa,GAC9BC,SAAU4C,EAAM5C,UAAY,GAC5BC,OAAQ2C,EAAM3C,QAAU,GACxBC,OAAQ0C,EAAM1C,QAAU,SACxBC,UAAWyC,EAAMzC,UAAYsC,OAAOG,EAAMzC,SAAS,EAAI,GACvDC,aAAcwC,EAAMxC,cAAgB,cACpCC,WAAYuC,EAAMvC,WAAaoC,OAAOG,EAAMvC,UAAU,EAAI,GAC1DC,eAAgBsC,EAAMtC,eAAiBmC,OAAOG,EAAMtC,cAAc,EAAI,IAGtEC,qBAAsBqC,EAAMrC,sBAAwB,GACpDC,iBAAkBoC,EAAMpC,kBAAoB,GAC5CC,uBAAwBmC,EAAMnC,wBAA0B,GACxDC,mBAAoBkC,EAAMlC,oBAAsB,GAChDC,eAAgBiC,EAAMjC,gBAAkB,GACxCC,eAAgBgC,EAAMhC,gBAAkB,CAAE,EAC1CC,cAAe+B,EAAM/B,eAAiB,GACtCC,gBAAiB8B,EAAM9B,iBAAmB,GAC1CC,cAAe6B,EAAM7B,eAAiB,GAGtCC,wBAAuB4B,IAAMO,sBAANP,cAA2BQ,SAASC,UAAW,GACtEpC,2BAA0B2B,IAAMO,sBAANP,cAA2BQ,SAASE,aAAc,GAC5EpC,wBAAuB0B,IAAMO,sBAANP,cAA2BQ,SAASG,UAAW,GACtEpC,qBAAoByB,IAAMO,sBAANP,cAA2BQ,SAASI,OAAQ,GAChEpC,uBAAsBwB,IAAMO,sBAANP,cAA2BQ,SAASK,SAAU,GACpEpC,0BAAyBuB,IAAMO,sBAANP,cAA2BQ,SAASM,YAAa,GAC1EpC,uBAAsBsB,IAAMO,sBAANP,cAA2BQ,SAASO,SAAU,GACpEpC,sBAAqBqB,IAAMO,sBAANP,cAA2BV,QAAS,GAGzDV,UAASoB,IAAMgB,cAANhB,cAAmBpB,UAAW,GACvCC,cAAamB,KAAMgB,cAANhB,eAAmBnB,cAAe,GAC/CC,cAAakB,IAAMgB,cAANhB,cAAmBlB,cAAe,GAC/CC,aAAYiB,KAAMgB,cAANhB,eAAmBjB,aAAc,CAAE,EAC/CC,iBAAgBgB,KAAMgB,cAANhB,eAAmBiB,cAAe,GAClDhC,mBAAkBe,KAAMgB,cAANhB,eAAmBkB,gBAAiB,GACtDhC,eAAcc,KAAMgB,cAANhB,eAAmBmB,YAAa,GAC9ChC,qBAAoBa,KAAMgB,cAANhB,eAAmBoB,kBAAmB,GAC1DhC,kBAAkBY,KAAMgB,cAANhB,SAAmBqB,cACjCxB,OAAOG,EAAMgB,YAAYK,aAAa,EACtC,IACJhC,WAAUW,KAAMgB,cAANhB,eAAmBV,QAAS,GAGtCA,MAAOU,EAAMV,OAAS,GACtBC,KAAMS,EAAMT,MAAQ,CAAE,EACtBC,OAAQQ,EAAMM,WAAa,EAAI,MAAQN,EAAMM,WAAa,EAAI,OAAS,YACxE,IAEO1E,cAAM,iBAAiBN,0BAAgC,EAC/DO,EAAS,iBAAiBP,cAAoB,SAEzCgG,GACC1F,cAAM,uBAAwB0F,CAAG,EACzCzF,EAAS,8CAA8C,SAC/C,CACR8D,QAAQD,IAAI,4BAA4B,EACxChE,EAAa,EAAK,CACpB,CACF,IAGY,EACb,CAACH,EAAYD,CAAO,CAAC,EAMlBiG,QAAeC,cAClBC,GAAqF,CAC9E,MAAEC,OAAMC,SAAUF,EAAEG,OAC1BzF,EAAyB0F,KACvB,GAAGA,EACH,CAACH,CAAI,EAAGC,CACR,GACJ,EACA,CACF,GAEO,OACLzF,aACAC,gBACAoF,eACA9F,YACAC,eACAE,QACAC,WACAC,UACAC,aACAC,YAEJ,CCnOO,SAAS8F,IAAqB,CACnC,KAAM,CAACC,EAAkBC,CAAmB,EAAIrG,WAA2B,CAAE,GAQvEsG,EAAqBT,cACzB,CAACtF,EAA6BgG,IAA+B,CAC3D,MAAMC,EAA2B,GAGjC,OAAQD,EAAS,CACf,IAAK,QAEEhG,EAAWE,OAAM+F,EAAO/F,KAAO,oBAC/BF,EAAWM,SAAQ2F,EAAO3F,OAAS,sBACnCN,EAAWS,aAAYwF,EAAOxF,WAAa,2BAC3CT,EAAWU,YAAWuF,EAAOvF,UAAY,0BACzCV,EAAWQ,WAAUyF,EAAOzF,SAAW,wBAC5C,MAEF,IAAK,SAECR,EAAWiB,WAAajB,EAAWkB,UACjClB,EAAWkB,SAAWlB,EAAWiB,YACnCgF,EAAO/E,SAAW,sCAIlBlB,EAAWmB,QAAUnB,EAAWiB,WAC9BjB,EAAWiB,UAAYjB,EAAWmB,SACpC8E,EAAOhF,UAAY,+CAGvB,MAEF,IAAK,WAECjB,EAAWyB,sBAAwB,CAACzB,EAAW0B,mBACjDuE,EAAOvE,iBAAmB,sCAGxB1B,EAAW2B,wBAA0B,CAAC3B,EAAW4B,qBACnDqE,EAAOrE,mBAAqB,wCAK5B5B,EAAW0B,kBACX1B,EAAW4B,oBACX5B,EAAW0B,mBAAqB1B,EAAW4B,qBAE3CqE,EAAOrE,mBAAqB,uDAE9B,MAEF,IAAK,eAEC5B,EAAW0C,UACR1C,EAAW2C,cACdsD,EAAOtD,YAAc,gCAGlB3C,EAAW4C,cACdqD,EAAOrD,YAAc,iCAGnB,CAAC5C,EAAW6C,YAAc7C,EAAW6C,WAAWqD,SAAW,KAC7DD,EAAOpD,WAAa,2CAMtB7C,EAAW+B,eACX/B,EAAW+B,gBAAkB,aAC7B,CAAC/B,EAAWgC,kBAEZiE,EAAOjE,gBAAkB,qCAE3B,KACJ,CAGA8D,SAA8BH,IAE5B,MAAMQ,EAAY,CAAE,GAAGR,GAGvBS,cAAOC,KAAKJ,CAAM,EAAEK,QAAiBC,IACzBA,GAAG,EAAIN,EAAOM,CAAG,EAC5B,EAEMJ,EACR,EAEMC,OAAOC,KAAKJ,CAAM,EAAEC,SAAW,CACxC,EACA,CACF,GAOMM,EAAuBlB,cAAatF,GAAyC,CACjF,MAAMiG,EAA2B,GAGjC,OAAKjG,EAAWE,OAAM+F,EAAO/F,KAAO,oBAC/BF,EAAWM,SAAQ2F,EAAO3F,OAAS,sBACnCN,EAAWS,aAAYwF,EAAOxF,WAAa,2BAC3CT,EAAWU,YAAWuF,EAAOvF,UAAY,0BACzCV,EAAWQ,WAAUyF,EAAOzF,SAAW,wBAG5CsF,EAA+BH,KAC7B,GAAGA,EACH,GAAGM,CACH,IAEKG,OAAOC,KAAKJ,CAAM,EAAEC,SAAW,CACxC,EAAG,CAAE,GAMCO,EAAkBnB,cAAaoB,GAAsB,CACzDZ,EAA8BH,IAC5B,MAAMQ,EAAY,CAAE,GAAGR,GACvB,cAAOQ,EAAUO,CAAS,EACnBP,EACR,CACH,EAAG,CAAE,GAKCQ,EAAiBrB,cAAY,IAAM,CACvCQ,EAAoB,CAAE,EACxB,EAAG,CAAE,GAEE,OACLD,mBACAE,qBACAS,uBACAC,kBACAE,iBACAb,sBAEJ,CC7JgBc,YACd5G,EACAC,EACA,CAEAsD,mBAAU,IAAM,CACVvD,KAAWuB,YAAcvB,EAAWa,OAAQ,CACxCU,QAAasF,WAAW7G,EAAWuB,UAAU,EAC7CV,EAASgG,WAAW7G,EAAWa,MAAM,EAE3C,GAAIU,EAAa,EAAG,CAClB,MAAMF,GAAaR,EAASU,GAAYuF,QAAQ,CAAC,EACjD7G,EAAyB0F,KACvB,GAAGA,EACHtE,WACA,KAEN,EACC,CAACrB,EAAWuB,WAAYvB,EAAWa,OAAQZ,CAAa,CAAC,EAG5DsD,YAAU,IAAM,GAIb,CAACvD,EAAWiB,UAAWjB,EAAWkB,SAAUlB,EAAWmB,MAAM,CAAC,EA8B1D,CACL4F,oBAzB0BA,IAAM,CAChC,GAAI/G,EAAWS,YAAcT,EAAWU,WAAaV,EAAWQ,SAAU,CAClEC,QAAaoG,WAAW7G,EAAWS,UAAU,EAC7CC,EAAYmG,WAAW7G,EAAWU,SAAS,EAC3CF,EAAWqG,WAAW7G,EAAWQ,QAAQ,EAE3C,IAACwG,MAAMvG,CAAU,GAAK,CAACuG,MAAMtG,CAAS,GAAK,CAACsG,MAAMxG,CAAQ,EAAG,CAC3D4D,MAEApE,EAAWO,YAAc,OAC3B6D,GAAc1D,EAAYD,GAAcD,EAExC4D,GAAc3D,EAAaC,GAAaF,EAG1CP,EAAyB0F,KACvB,GAAGA,EACH9E,OAAQuD,EAAW0C,QAAQ,CAAC,EAC5BxD,OAAQc,EAAa,EAAI,MAAQA,EAAa,EAAI,OAAS,WAC3D,KAEN,CAIA2C,CAEJ,CChDgBE,YACdjH,EACAX,EACAC,EACAQ,EACA0G,EACAT,EACAC,EACAkB,EACAvH,EACAE,EACA,CACA,MAAMsH,EAAWC,KACX,CAACC,EAAcC,CAAe,EAAI7H,WAAS,EAAK,EAsL/C,OACL8H,aAjLmBjC,cACnB,MAAOC,GAAuB,CAIxB,GAHJA,EAAEiC,eAAe,EAGb,CAAChB,IAAwB,CAC3B7G,EAAS,4DAA4D,EACrEuH,EAAa,OAAO,EACpB,OAIF,GAAIlB,IAAc,SAAW,CAACD,IAAsB,CAClDpG,EAAS,wEAAwE,EACjF,OAGF2H,EAAgB,EAAI,EACpB3H,EAAS,IAAI,EACbE,EAAW,IAAI,EAEX,IAEF,MAAM4H,EAAqB,CACzBlD,QAASvE,EAAWkC,uBAAyB,GAC7CsC,WAAYxE,EAAWmC,0BAA4B,GACnDsC,QAASzE,EAAWoC,uBAAyB,GAC7CsC,KAAM1E,EAAWqC,oBAAsB,GACvCsC,OAAQ3E,EAAWsC,sBAAwB,GAC3CsC,UAAW5E,EAAWuC,yBAA2B,GACjDsC,OAAQ7E,EAAWwC,sBAAwB,IAIvCkF,EAAoBtB,OAAOuB,OAAOF,CAAkB,EAAEG,MAAOnC,GAAUA,IAAU,EAAE,EAGnFoC,EAAmB,CAAE,GAAG7H,GAG9B,GAAI0H,EAAmB,CAEf,MAAEI,sBAAqBC,wBAAyB,MAAMC,eAE5D,+BAGMC,EAAaH,EAAoBL,CAAkB,EACnDS,EAASH,EAAqBE,CAAU,EAG9CJ,EAAiBxD,oBAAsB,CACrC8D,MAAOF,EACPC,SACA5D,SAAUmD,EACVrE,MAAOpD,EAAWyC,qBAAuB,IAKzCzC,EAAW0C,UAEbmF,EAAiB/C,YAAc,CAC7BpC,QAAS1C,EAAW0C,QACpBC,YAAa3C,EAAW2C,aAAe,GACvCC,YAAa5C,EAAW4C,aAAe,GACvCC,WAAY7C,EAAW6C,YAAc,CAAE,EACvCkC,YAAa/E,EAAW8C,gBAAkB,GAC1CkC,cAAehF,EAAW+C,kBAAoB,GAC9CkC,UAAWjF,EAAWgD,cAAgB,GACtCkC,gBAAiBlF,EAAWiD,oBAAsB,GAClDkC,cAAenF,EAAWkD,iBAAmBkF,SAASpI,EAAWkD,gBAAgB,EAAI,EACrFE,MAAOpD,EAAWmD,UAAY,KAI1BK,YAAI,kBAAmBqE,CAAgB,EAG/C,MAAMQ,EAAsC,CAE1C/H,OAAQN,EAAWM,OACnBJ,KAAMF,EAAWE,KACjBK,UAAWP,EAAWO,YAAc,OAAS,OAAS,QACtD0D,KAAMmE,SAASpI,EAAWQ,QAAQ,GAAK,EAGvC0D,MAAO2C,WAAW7G,EAAWS,UAAU,GAAK,EAC5C0D,KAAM0C,WAAW7G,EAAWU,SAAS,GAAK,EAC1CC,SAAUkG,WAAW7G,EAAWW,UAAY,GAAG,GAAK,EACpDC,WAAYiG,WAAW7G,EAAWY,YAAc,GAAG,GAAK,EACxDwD,WAAYyC,WAAW7G,EAAWa,MAAM,GAAK,EAG7CC,UAAWd,EAAWc,UACtBC,QAASf,EAAWe,QACpBC,MAAOhB,EAAWgB,MAClBC,UAAWjB,EAAWiB,UACtBC,SAAUlB,EAAWkB,SACrBC,OAAQnB,EAAWmB,OACnBC,OAAQpB,EAAWoB,OACnBC,UAAWrB,EAAWqB,UAAYwF,WAAW7G,EAAWqB,SAAS,EAAIiH,OACrEhH,aAActB,EAAWsB,aACzBC,WAAYvB,EAAWuB,WAAasF,WAAW7G,EAAWuB,UAAU,EAAI+G,OACxE9G,eAAgBxB,EAAWwB,eAAiB4G,SAASpI,EAAWwB,cAAc,EAAI8G,OAClFjE,oBAAqBwD,EAAiBxD,oBACtCS,YAAa+C,EAAiB/C,YAG9BrD,qBAAsBzB,EAAWyB,qBACjCC,iBAAkB1B,EAAW0B,iBAC7BC,uBAAwB3B,EAAW2B,uBACnCC,mBAAoB5B,EAAW4B,mBAC/BC,eAAgB7B,EAAW6B,eAC3BC,eAAgB9B,EAAW8B,eAC3BC,cAAe/B,EAAW+B,cAC1BC,gBAAiBhC,EAAWgC,gBAC5BC,cAAejC,EAAWiC,cAG1BsG,SAAUvI,EAAWgB,OAAS,UAC9BoC,MAAOpD,EAAWoD,MAClBC,KAAMrD,EAAWqD,MAAQ,IAGvBmF,MAGAnJ,GAAcS,GACH0I,QAAMzE,GAAa0E,UAAU,CACxC,GAAGJ,EACHK,GAAI5I,EAAU4I,GACf,EACOlF,YAAI,0CAA2CgF,CAAU,IAGpDA,QAAMzE,GAAa0E,UAAUJ,CAAgB,EAClD7E,YAAI,4CAA6CgF,CAAU,GAKnE3I,EADER,EACS,aAAamJ,EAAWlI,aAAakI,EAAWtI,6BAGzD,iBAAiBsI,EAAWlI,aAAakI,EAAWtI,4BAHiC,EAQzFyI,WAAW,IAAM,CAEflF,QAAQD,IAAI,6DAA6D,EACzE2D,EAAS,UAAU,GAClB,IAAI,QACA/B,GACPzF,EAAS,yCAAyC,EAC1CD,cAAM,yBAA0B0F,CAAG,SACnC,CACRkC,EAAgB,EAAK,CACvB,CAEF,GACEtH,EACAwG,EACAT,EACAC,EACAkB,EACAvH,EACAE,EACAR,EACAS,EACAqH,CAAQ,CAEZ,EAIEE,eAEJ,CCjNO,MAAMuB,GAAqB,CAChC,CAAEnD,MAAO,eAAgBoD,MAAO,cAAe,EAC/C,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,kBAAmBoD,MAAO,iBAAkB,EACrD,CAAEpD,MAAO,iBAAkBoD,MAAO,gBAAiB,EACnD,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,qBAAsBoD,MAAO,oBAAqB,EAC3D,CAAEpD,MAAO,QAASoD,MAAO,OAAQ,CAAC,EAGvBC,GAAkB,CAC7B,CAAErD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,gBAAiBoD,MAAO,eAAgB,EACjD,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,cAAeoD,MAAO,aAAc,EAC7C,CAAEpD,MAAO,YAAaoD,MAAO,WAAY,CAAC,EAG/BE,GAAgB,CAC3B,CAAEtD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,qBAAsBoD,MAAO,oBAAqB,EAC3D,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,cAAeoD,MAAO,aAAc,EAC7C,CAAEpD,MAAO,qBAAsBoD,MAAO,oBAAqB,EAC3D,CAAEpD,MAAO,QAASoD,MAAO,OAAQ,CAAC,EAGvBG,GAAiB,CAC5B,CAAEvD,MAAO,SAAUoD,MAAO,QAAS,EACnC,CAAEpD,MAAO,UAAWoD,MAAO,SAAU,EACrC,CAAEpD,MAAO,UAAWoD,MAAO,SAAU,EACrC,CAAEpD,MAAO,QAASoD,MAAO,OAAQ,EACjC,CAAEpD,MAAO,SAAUoD,MAAO,QAAS,EACnC,CAAEpD,MAAO,QAASoD,MAAO,OAAQ,CAAC,EAGvBI,GAAwB,CACnC,CAAExD,MAAO,cAAeoD,MAAO,aAAc,EAC7C,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,iBAAkBoD,MAAO,gBAAiB,EACnD,CAAEpD,MAAO,eAAgBoD,MAAO,cAAe,CAAC,EAGrCK,GAA0BC,MAAMC,KAAK,CAAElD,OAAQ,EAAG,EAAG,CAACmD,EAAGC,KAAO,CAC3E7D,MAAO9B,OAAO2F,EAAI,CAAC,EACnBT,MAAOlF,OAAO2F,EAAI,CAAC,CACrB,EAAE,EAMWC,GAAgBnK,GAAqB,CAC/BgI,GAAY,EAC7B,KAAM,CAACpB,EAAWkB,CAAY,EAAIzH,WAAiB,OAAO,EAGlD+D,YAAI,gDAAgDpE,IAAU,EAGtE,MAAMoK,EAAcC,OAAOC,SAASC,KAAKC,UAAU,CAAC,EAC5CpG,YAAI,sCAAsCgG,GAAa,EAG/D,IAAIK,EAAmBzK,EACvB,GAAI,CAACyK,GAAoBL,EAAYM,SAAS,cAAc,EAAG,CAEvDC,QAAUP,EAAYQ,MAAM,yBAAyB,EACvDD,GAAWA,EAAQ,CAAC,IACtBF,EAAmBE,EAAQ,CAAC,EACpBvG,YAAI,gCAAgCqG,GAAkB,GAKlE,MAAMvK,EAAauK,IAAqB,OAASL,EAAYM,SAAS,YAAY,EAC5EzK,EACHwK,GAAoBA,IAAqB,OACzCL,EAAYM,SAAS,cAAc,GAAK,CAACN,EAAYM,SAAS,iBAAiB,EAE1EtG,YAAI,8BAA8BlE,kBAA2BD,GAAY,EAG3E,MACJW,aACAC,gBACAoF,eACA9F,YACAG,QACAC,WACAC,UACAC,aACAC,aACEX,GAAiB0K,EAAkBxK,CAAsB,EAGvD,CACJwG,mBACAE,mBAAoBkE,EACpBzD,wBACEZ,GAAmB,EAGjB,CAAEmB,uBAAwBH,GAAqB5G,EAAYC,CAAa,EAGxE8F,EAAqBA,IAAMkE,EAAYjK,EAAYgG,CAAS,EAG5DkE,EAAoBA,IAAM1D,EAAqBxG,CAAU,EAGzD,CAAEuH,eAAcF,cAAa,EAAIJ,GACrCjH,EACAX,EACAC,EACAQ,EACAoK,EACAnE,EACAC,EACAkB,EACAvH,EACAE,CACF,EA+BO,OACLG,aACAC,gBACAoF,eACAkC,eACAF,eACA9H,YACAG,QACAE,UACAiG,mBACAvG,aACA0G,YACAmE,gBAxCuBC,GAAmB,CAClC5G,YAAI,6BAA6BwC,QAAgBoE,GAAQ,EAI7DxK,IACF6D,QAAQD,IAAI,4CAA4C,EACxD3D,EAAW,IAAI,GAIbH,IACF+D,QAAQD,IAAI,0CAA0C,EACtD7D,EAAS,IAAI,GAKXqG,IACMxC,YAAI,2BAA2BwC,qBAA6B,EACjDD,KAIbvC,YAAI,0BAA0B4G,GAAQ,EAC9ClD,EAAakD,CAAM,GAgBnBrD,sBAEJ,ECrKMsD,GAAsBC,MAAGC,iEAI9B,sDAEKC,GAAiBF,MAAGC,8HAEG,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOC,OACtC,CAAC,CAAEF,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAG5CC,GAAmBC,SAAMR,4PAClB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GAAM,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GAGhD,CAAC,CAAEI,SAAQR,OAAM,IAAOQ,EAASR,EAAMC,OAAOQ,QAAU,cAC1E,CAAC,CAAED,SAAQR,OAAM,IAAOQ,EAASR,EAAMC,OAAOQ,QAAUT,EAAMC,OAAOS,cAC/D,CAAC,CAAEF,SAAQR,OAAM,IAC9BQ,EAASR,EAAMW,YAAYC,SAAWZ,EAAMW,YAAYE,QAExC,CAAC,CAAEb,OAAM,IAAMA,EAAMc,YAAYC,KAGxC,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAOQ,QAK5B,CAAC,CAAET,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAI1CO,GAAoBnB,MAAGC,oFAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAStCU,GAAoCA,CAAC,CACzCC,OACAC,aACAC,YACA7F,UAAW8F,EACXC,YACF,IAAM,OAEE,MAACC,EAAmBC,CAAoB,EAAIxM,WAASmM,GAAcD,EAAK,CAAC,EAAEjD,EAAE,EAG7E1C,EAAY8F,IAAwBxD,OAAYwD,EAAsBE,EAEtE7B,EAAkBA,CAAC5E,EAAqB2G,IAAkB,CAE9D3G,EAAEiC,eAAe,EACjBjC,EAAE4G,gBAAgB,EAEV3I,YAAI,gBAAgB0I,GAAO,EAG/BH,GACFA,EAAWG,CAAK,EACR1I,YAAI,2BAA2B0I,GAAO,IAG9CD,EAAqBC,CAAK,EAClB1I,YAAI,kCAAkC0I,GAAO,EACvD,EAIA,cAAC7B,IAAa,YACZ,UAAC+B,UACET,SAAKU,MACJC,SAACxB,IAEC,OAAQ9E,IAAcsG,EAAI5D,GAC1B,QAAUnD,GAAM4E,EAAgB5E,EAAG+G,EAAI5D,EAAE,EACzC,KAAK,SACL,KAAK,GACL,SAAU,EACV,cAAa4D,EAAI5D,GAEhB4D,SAAIzD,SARAyD,EAAI5D,EAUZ,GACH,EACA0D,MAACX,IAAYE,UAAKY,YAAcD,EAAI5D,KAAO1C,CAAS,IAAlCuG,cAAqCC,OAAQ,EACjE,GAEJ,ECxGaC,GAA2C,CACtDC,UAAW,EACXC,KAAM,EACNC,QAAS,EACTC,KAAM,EACNC,aAAc,CAChB,EAKaC,GAAsB,CACjC,CAAEtH,MAAO,YAAaoD,MAAO,eAAgB,EAC7C,CAAEpD,MAAO,OAAQoD,MAAO,UAAW,EACnC,CAAEpD,MAAO,UAAWoD,MAAO,aAAc,EACzC,CAAEpD,MAAO,OAAQoD,MAAO,UAAW,EACnC,CAAEpD,MAAO,eAAgBoD,MAAO,kBAAmB,CAAC,EAMzCmE,EAA2B,CACtCzI,QAAS,CACP0I,MAAO,kBACPC,YAAa,6CACbR,UAAW,oDACXC,KAAM,4CACNC,QAAS,iDACTC,KAAM,+DACNC,aAAc,wDAChB,EACAtI,WAAY,CACVyI,MAAO,qBACPC,YAAa,uDACbR,UAAW,kDACXC,KAAM,gDACNC,QAAS,wCACTC,KAAM,wCACNC,aAAc,uBAChB,EACArI,QAAS,CACPwI,MAAO,iBACPC,YAAa,mEACbR,UAAW,yDACXC,KAAM,sDACNC,QAAS,uDACTC,KAAM,oDACNC,aAAc,gDAChB,EACApI,KAAM,CACJuI,MAAO,eACPC,YAAa,+CACbR,UAAW,kDACXC,KAAM,2CACNC,QAAS,oDACTC,KAAM,qCACNC,aAAc,+CAChB,EACAnI,OAAQ,CACNsI,MAAO,mBACPC,YAAa,iDACbR,UAAW,qCACXC,KAAM,iCACNC,QAAS,qCACTC,KAAM,kCACNC,aAAc,6BAChB,EACAlI,UAAW,CACTqI,MAAO,sBACPC,YAAa,8DACbR,UAAW,kDACXC,KAAM,wCACNC,QAAS,gEACTC,KAAM,sCACNC,aAAc,0EAChB,EACAjI,OAAQ,CACNoI,MAAO,iBACPC,YAAa,uCACbR,UAAW,wCACXC,KAAM,wCACNC,QAAS,6BACTC,KAAM,0CACNC,aAAc,yCAChB,CACF,EAKahF,GAAuBxD,GAC3B8B,OAAOuB,OAAOrD,CAAQ,EAAE6I,OAAO,CAAChF,EAAOiF,IACrCjF,GAASsE,GAAaW,CAAK,GAAK,GACtC,CAAC,EAMOrF,GAAwBE,GAA+B,CAGlE,MAAMoF,EAAmBjH,OAAOC,KAAK2G,CAAwB,EAAE9G,OAAS,EAClEgC,EAASoF,KAAKC,MAAOtF,EAAaoF,EAAoB,EAAE,EAG9D,OAAOC,KAAKE,IAAI,EAAGF,KAAKG,IAAI,GAAIvF,CAAM,CAAC,CACzC,EAKawF,GAAwBxF,GAC/BA,GAAU,EAAU,cACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,gBACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,YACjB,eAMIyF,GAAkBzF,GACzBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACjB,mQCrIH0F,GAA2BtD,MAAGC,0IAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACnB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5CC,GAAwBC,KAAExD,2HACjB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5CC,GAA8BC,IAAC5D,qHACtB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGrCC,GAAoB/D,MAAGC,gIAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGzCsD,GAAqBhE,MAAGC,uMAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxDC,GAAoBC,QAAKnE,sGACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAI3C2D,GAA6BrE,MAAGC,wEAGrC,2CAEKqE,GAAoB/F,QAAK0B,8GAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5CY,GAA0BC,OAAIvE,mHACrB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUI,GACnC,CAAC,CAAE3D,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAW9C4D,EAAsDA,CAAC,CAC3DC,YACAvJ,QACAwJ,WACAvI,WACF,IAAM,CACEwI,QAAgBlC,EAAyBgC,CAAS,EAGlDG,EAAqB5J,GAA2C,CAC3DmB,IAAWnB,EAAEG,OAAOD,KAAK,GAGpC,cACGmI,GACC,WAACxB,UAAgB8C,WAAcjC,KAAM,GACrCb,MAAC8B,GAAsBgB,YAAchC,WAAY,SAEhDmB,GACEtB,aAAoBV,IAAK+C,UACvBd,GACC,iBAACG,IACC,KAAK,QACL,GAAI,GAAG/H,KAAa0I,EAAO3J,QAC3B,KAAMiB,EACN,MAAO0I,EAAO3J,MACd,QAASA,IAAU2J,EAAO3J,MAC1B,SAAU0J,CAAkB,UAE7BR,GACC,iBAACC,IAAW,QAAS,GAAGlI,KAAa0I,EAAO3J,QACzC2J,WAAOvG,KACV,GACCuD,UACE8C,SAAcE,IAAO3J,KAAmB,EAC3C,GACF,CAhBgB2J,KAAO3J,KAiBzB,CACD,EACH,CACF,GAEJ,ECvGM4J,GAA6B/E,MAAGC,0HAG7B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGlCyB,GAAsBhF,MAAGC,0FACZ,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C0B,GAAoBxB,KAAExD,+HACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUH,GAEnC,CAAC,CAAEpD,OAAM,IAAMA,EAAMC,OAAOuD,YACvB,CAAC,CAAExD,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGzCwE,GAAmBrB,IAAC5D,sHACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GACnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAK9CsE,GAAiBC,KAAEnF,qHAEC,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOC,OAC1C,CAAC,CAAEF,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGrC8E,GAAsBrF,MAAGC,qLAGtB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GAChB,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAO8D,WACrC,CAAC,CAAE/D,OAAM,IAAMA,EAAMG,QAAQiD,GACvB,CAAC,CAAEpD,OAAM,IAAMA,EAAM8D,aAAa1D,GACrC,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGzC+B,GAAoB7B,KAAExD,uHACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUH,GAEnC,CAAC,CAAEpD,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5C4B,GAAsBvF,MAAGC,gHAGtB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQkF,EAAE,EAGlCC,GAAoBzF,MAAGC,sOAGlB,CAAC,CAAEyF,OAAM,IAAMA,EAOJ,CAAC,CAAEA,OAAM,IAAMA,CAAK,EAGpCC,GAAmB3F,MAAGC,gHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC8B,GAA0B5F,MAAGC,qHACpB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUH,GAEnC,CAAC,CAAEpD,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5CkC,GAAwB7F,MAAGC,mGAClB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CiF,GAAsB9F,MAAGC,wFACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGzCwC,GAAoBxH,QAAK0B,+IAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,YAEpB,CAAC,CAAExD,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAG5CsF,GAAuBC,WAAQhG,6PAGxB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GACtC,CAAC,CAAEP,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YACjB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAO8D,WAK9B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAmBjDsF,GAAoEA,CAAC,CACzExQ,aACAiP,UACF,IAAM,CACJ,KAAM,CAAChH,EAAYwI,CAAa,EAAIhR,WAAS,CAAC,EACxC,CAACyI,EAAQwI,CAAS,EAAIjR,WAAS,CAAC,EAGtC8D,YAAU,IAAM,CACd,MAAMe,EAAuC,CAC3CC,QAAUvE,EAAWkC,uBAAyB,GAC9CsC,WAAaxE,EAAWmC,0BAA4B,GACpDsC,QAAUzE,EAAWoC,uBAAyB,GAC9CsC,KAAO1E,EAAWqC,oBAAsB,GACxCsC,OAAS3E,EAAWsC,sBAAwB,GAC5CsC,UAAY5E,EAAWuC,yBAA2B,GAClDsC,OAAS7E,EAAWwC,sBAAwB,IAM9C,GAF0B4D,OAAOuB,OAAOrD,CAAQ,EAAEsD,MAAOnC,GAAUA,IAAU,EAAE,EAExD,CACf2H,QAAQtF,GAAoBxD,CAAQ,EACpCqM,EAAmB5I,GAAqBqF,CAAK,EAEnDqD,EAAcrD,CAAK,EACnBsD,EAAUC,CAAgB,EAGjB1B,mBAAkB0B,EAAiBC,SAAU,GACxD,EACC,CACD5Q,EAAWkC,sBACXlC,EAAWmC,yBACXnC,EAAWoC,sBACXpC,EAAWqC,mBACXrC,EAAWsC,qBACXtC,EAAWuC,wBACXvC,EAAWwC,qBACXyM,CAAQ,CACT,EAGK4B,QAAqBtL,GAA8C,CAC9D0J,wBAAuB1J,EAAEG,OAAOD,KAAK,GAGhD,cACG4J,GACC,kBAACC,GACC,iBAACC,IAAW,SAA0B,+BACtCnD,MAACoD,IAAS,SAIV,8LACF,QAECC,GAAO,IAGRrD,MAAC2C,EACC,WAAU,UACV,MAAO/O,EAAWkC,uBAAyB,GAC3C,WACA,UAAU,uBAAuB,GAGnCkK,MAAC2C,EACC,WAAU,aACV,MAAO/O,EAAWmC,0BAA4B,GAC9C,WACA,UAAU,0BAA0B,GAGtCiK,MAAC2C,EACC,WAAU,UACV,MAAO/O,EAAWoC,uBAAyB,GAC3C,WACA,UAAU,uBAAuB,GAGnCgK,MAAC2C,EACC,WAAU,OACV,MAAO/O,EAAWqC,oBAAsB,GACxC,WACA,UAAU,oBAAoB,GAGhC+J,MAAC2C,EACC,WAAU,SACV,MAAO/O,EAAWsC,sBAAwB,GAC1C,WACA,UAAU,sBAAsB,GAGlC8J,MAAC2C,EACC,WAAU,YACV,MAAO/O,EAAWuC,yBAA2B,GAC7C,WACA,UAAU,yBAAyB,GAGrC6J,MAAC2C,EACC,WAAU,SACV,MAAO/O,EAAWwC,sBAAwB,GAC1C,WACA,UAAU,sBAAsB,GAIjC0F,EAAS,GACR4I,OAACnB,GACC,iBAACC,IAAW,SAAqB,iCAChCC,GACC,iBAACE,GAAW,OAAOpC,GAAezF,CAAM,EAAIA,SAAOA,WAClD+H,GACC,WAAC7D,UAAkBsB,SAAqBxF,IAAM,CAAE,UAC/CiI,GAAc,2BACClI,EAAW,WAAS7B,OAAOC,KAAK2G,CAAwB,EAAE9G,OAAS,GACnF,GACF,GACF,GACF,SAIDkK,GACC,WAAChE,UAAW,QAAQ,sBAAsB,SAAgB,qBACzDA,UACC,GAAG,sBACH,KAAK,sBACL,MAAOpM,EAAWyC,qBAAuB,GACzC,SAAUoO,EACV,YAAY,wDAAuD,GAEvE,CACF,GAEJ,EClRaE,GAAmB,CAC9B,CAAEtL,MAAO,QAASoD,MAAO,iDAAkD,EAC3E,CAAEpD,MAAO,MAAOoD,MAAO,iDAAkD,EACzE,CAAEpD,MAAO,WAAYoD,MAAO,+DAAiE,EAC7F,CAAEpD,MAAO,YAAaoD,MAAO,oDAAqD,CAAC,EAMxEmI,GAAuB,CAClC,CAAEvL,MAAO,SAAUoD,MAAO,sDAAuD,EACjF,CAAEpD,MAAO,WAAYoD,MAAO,0DAA2D,EACvF,CAAEpD,MAAO,OAAQoD,MAAO,+CAAgD,CAAC,EAM9DoI,GAAuB,CAClC,CAAExL,MAAO,qBAAsBoD,MAAO,2DAA4D,EAClG,CAAEpD,MAAO,mBAAoBoD,MAAO,4DAA6D,EACjG,CAAEpD,MAAO,gBAAiBoD,MAAO,iDAAkD,EACnF,CAAEpD,MAAO,eAAgBoD,MAAO,sDAAuD,CAAC,EAM7EqI,GAAsB,CACjC,CAAEzL,MAAO,mBAAoBoD,MAAO,kBAAmB,EACvD,CAAEpD,MAAO,kBAAmBoD,MAAO,iBAAkB,EACrD,CAAEpD,MAAO,OAAQoD,MAAO,gCAAiC,EACzD,CAAEpD,MAAO,UAAWoD,MAAO,qBAAsB,EACjD,CAAEpD,MAAO,wBAAyBoD,MAAO,uBAAwB,EACjE,CAAEpD,MAAO,yBAA0BoD,MAAO,wBAAyB,EACnE,CAAEpD,MAAO,0BAA2BoD,MAAO,yBAA0B,EACrE,CAAEpD,MAAO,eAAgBoD,MAAO,6BAA8B,EAC9D,CAAEpD,MAAO,kBAAmBoD,MAAO,sCAAuC,EAC1E,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,QAASoD,MAAO,OAAQ,CAAC,EAMKM,MAAMC,KAAK,CAAElD,OAAQ,EAAG,EAAG,CAACmD,EAAGC,KAAO,CAC7E7D,MAAO9B,OAAO2F,EAAI,CAAC,EACnBT,MAAOlF,OAAO2F,EAAI,CAAC,CACrB,EAAE,EAKK,MAAM6H,GAAgC,CAC3C,MAAS,iGACT,IAAO,iGACP,SAAY,+FACZ,UAAa,4FACf,EAKaC,GAAkC,CAC7C,OAAU,0FACV,SAAY,uFACZ,KAAQ,mFACV,EAKaC,GACX,gJAMWC,GACX,wJA8BWC,GAA4BrJ,GACnCA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACjB,UAMIsJ,GAAkCtJ,GACzCA,GAAU,EAAU,cACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,gBACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,YACjB,cCpIH0F,GAA2BtD,MAAGC,0IAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACnB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C4D,GAAsB1D,KAAExD,yHACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5CyD,GAAqBvD,IAAC5D,4GACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGrCC,GAAoB/D,MAAGC,gIAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGzCsD,GAAqBhE,MAAGC,uMAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxDC,GAAoBC,QAAKnE,sGACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAI3C2D,GAA6BrE,MAAGC,wEAGrC,2CAEKqE,GAAoB/F,QAAK0B,8GAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5CY,GAA0BC,OAAIvE,mHACrB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUI,GACnC,CAAC,CAAE3D,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAS9CwG,GAAkDA,CAAC,CAAElM,QAAOwJ,UAAS,IAAM,CAEzE2C,QAAuBrM,GAA2C,CAC7D0J,YAAW1J,EAAEG,OAAOD,KAAK,GAGpC,cACGmI,GACC,iBAAC6D,IAAa,SAAQ,aACtBrF,MAACsF,IAAW,SAEZ,0EAECtF,UACE2E,SAAiB1E,OAAgB+C,IAChC,KAAM,CAACvG,EAAOqE,CAAW,EAAIkC,EAAOvG,MAAMxI,MAAM,KAAK,EAErD,cACGiO,GACC,iBAACG,IACC,KAAK,QACL,GAAI,WAAWW,EAAO3J,QACtB,KAAK,UACL,MAAO2J,EAAO3J,MACd,QAASA,IAAU2J,EAAO3J,MAC1B,SAAUmM,EAAoB,SAE/BjD,GACC,iBAACC,GAAW,SAAS,WAAWQ,EAAO3J,QACpCoD,SACHA,IACAuD,MAACyC,IACE3B,SACHA,KACF,IAhBgBkC,EAAO3J,KAiBzB,CAEH,GACH,CACF,GAEJ,EC3GMmI,GAA2BtD,MAAGC,0IAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACnB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C4D,GAAsB1D,KAAExD,yHACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5CyD,GAAqBvD,IAAC5D,4GACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGrCC,GAAoB/D,MAAGC,gIAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGzCsD,GAAqBhE,MAAGC,uMAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxDC,GAAoBC,QAAKnE,sGACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAI3C2D,GAA6BrE,MAAGC,wEAGrC,2CAEKqE,GAAoB/F,QAAK0B,8GAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5CY,GAA0BC,OAAIvE,mHACrB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUI,GACnC,CAAC,CAAE3D,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAS9C0G,GAA0DA,CAAC,CAAEpM,QAAOwJ,UAAS,IAAM,CAEjF6C,QAA2BvM,GAA2C,CACjE0J,gBAAe1J,EAAEG,OAAOD,KAAK,GAGxC,cACGmI,GACC,iBAAC6D,IAAa,SAAY,iBAC1BrF,MAACsF,IAAW,SAEZ,wDAECtF,UACE4E,SAAqB3E,OAAgB+C,IACpC,KAAM,CAACvG,EAAOqE,CAAW,EAAIkC,EAAOvG,MAAMxI,MAAM,KAAK,EAErD,cACGiO,GACC,iBAACG,IACC,KAAK,QACL,GAAI,eAAeW,EAAO3J,QAC1B,KAAK,cACL,MAAO2J,EAAO3J,MACd,QAASA,IAAU2J,EAAO3J,MAC1B,SAAUqM,EAAwB,SAEnCnD,GACC,iBAACC,GAAW,SAAS,eAAeQ,EAAO3J,QACxCoD,SACHA,IACAuD,MAACyC,IACE3B,SACHA,KACF,IAhBgBkC,EAAO3J,KAiBzB,CAEH,GACH,CACF,GAEJ,EC3GMmI,GAA2BtD,MAAGC,0IAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACnB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C4D,GAAsB1D,KAAExD,yHACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5CyD,GAAqBvD,IAAC5D,4GACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGrCC,GAAoB/D,MAAGC,gIAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGzCsD,GAAqBhE,MAAGC,uMAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxDC,GAAoBC,QAAKnE,sGACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAI3C2D,GAA6BrE,MAAGC,wEAGrC,2CAEKqE,GAAoB/F,QAAK0B,8GAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5CY,GAA0BC,OAAIvE,mHACrB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUI,GACnC,CAAC,CAAE3D,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAS9C4G,GAA0DA,CAAC,CAAEtM,QAAOwJ,UAAS,IAAM,CAEjF+C,QAA2BzM,GAA2C,CACjE0J,gBAAe1J,EAAEG,OAAOD,KAAK,GAGxC,cACGmI,GACC,iBAAC6D,IAAa,SAAY,iBAC1BrF,MAACsF,IAAW,SAEZ,gEAECtF,UACE6E,SAAqB5E,OAAgB+C,IACpC,KAAM,CAACvG,EAAOqE,CAAW,EAAIkC,EAAOvG,MAAMxI,MAAM,KAAK,EAErD,cACGiO,GACC,iBAACG,IACC,KAAK,QACL,GAAI,eAAeW,EAAO3J,QAC1B,KAAK,cACL,MAAO2J,EAAO3J,MACd,QAASA,IAAU2J,EAAO3J,MAC1B,SAAUuM,EAAwB,SAEnCrD,GACC,iBAACC,GAAW,SAAS,eAAeQ,EAAO3J,QACxCoD,SACHA,IACAuD,MAACyC,IACE3B,SACHA,KACF,IAhBgBkC,EAAO3J,KAiBzB,CAEH,GACH,CACF,GAEJ,EC3GMmI,GAA2BtD,MAAGC,0IAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACnB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C4D,GAAsB1D,KAAExD,yHACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5CyD,GAAqBvD,IAAC5D,4GACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGrC6D,GAAuB3H,MAAGC,mIAGvB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGzCkH,GAAwB5H,MAAGC,sMAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxD2D,GAAuBzD,QAAKnE,0FAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAG3CoH,GAAuBvJ,QAAK0B,iGACnB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAQ5CoE,GAAwDA,CAAC,CAAE5M,QAAOwJ,UAAS,IAAM,CAE/EqD,QAAwB/M,GAA2C,CACjEgN,QAAehN,EAAEG,OAAOD,MACxB+M,EAAYjN,EAAEG,OAAO+M,QAEvBC,MAEAF,EAESE,GAAC,GAAGjN,EAAO8M,CAAY,EAGlCG,EAAWjN,EAAMkN,OAAeC,OAASL,CAAY,EAGvDtD,EAAS,aAAcyD,CAAQ,GAGjC,cACG9E,GACC,iBAAC6D,IAAa,SAAW,gBACzBrF,MAACsF,IAAW,SAEZ,kFAECO,GACEf,aAAoB7E,IAAK+C,UACvB8C,GACC,iBAACC,IACC,KAAK,WACL,GAAI,cAAc/C,EAAO3J,QACzB,KAAK,aACL,MAAO2J,EAAO3J,MACd,QAASA,EAAMqE,SAASsF,EAAO3J,KAAK,EACpC,SAAU6M,CAAqB,SAEhCF,GAAc,SAAS,cAAchD,EAAO3J,QAC1C2J,WAAOvG,KACV,EAXmBuG,KAAO3J,KAY5B,CACD,EACH,CACF,GAEJ,EC7FMoN,GAA2BvI,MAAGC,2IAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACnB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C4D,GAAsB1D,KAAExD,0HACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5CyD,EAAqBvD,IAAC5D,6GACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGrC0E,EAAmBxI,MAAGC,mIAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACnB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAG5CkI,EAAelK,QAAK0B,0GACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C6H,EAAkBzC,WAAQhG,uPAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GACtC,CAAC,CAAEP,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YACjB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAO8D,WAK9B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAgBjD+H,GAA0DA,CAAC,CAAEjT,aAAYiP,UAAS,IAAM,CAE5F,MAAMiE,EAA4BA,IAC5BlT,EAAW0C,SAAWyO,GAA8BnR,EAAW0C,OAAqD,EAC/GyO,GAA8BnR,EAAW0C,OAAqD,EAEhG,8DAIHyQ,EAA8BA,IAC9BnT,EAAW2C,aAAeyO,GAAgCpR,EAAW2C,WAA2D,EAC3HyO,GAAgCpR,EAAW2C,WAA2D,EAExG,gEAIHyQ,EAAwB7N,GAA8C,CAC1E0J,EAAS1J,EAAEG,OAAOF,KAAMD,EAAEG,OAAOD,KAAK,GAGxC,cACGoN,GACC,iBAACpB,IAAa,SAAiB,sBAC/BrF,MAACsF,GAAW,SAEZ,yEAECoB,EACC,WAAC1G,SAAM,QAAQ,iBAAiB,SAAY,iBAC5CA,MAACsF,EAAawB,YAA4B,IACzC9G,SACC,GAAG,iBACH,KAAK,iBACL,MAAOpM,EAAW8C,gBAAkB,GACpC,SAAUsQ,EACV,YAAY,+BAA8B,GAE9C,SAECN,EACC,WAAC1G,SAAM,QAAQ,mBAAmB,SAAc,mBAChDA,MAACsF,EAAayB,YAA8B,IAC3C/G,SACC,GAAG,mBACH,KAAK,mBACL,MAAOpM,EAAW+C,kBAAoB,GACtC,SAAUqQ,EACV,YAAY,iCAAgC,GAEhD,SAECN,EACC,WAAC1G,SAAM,QAAQ,eAAe,SAAwB,6BACtDA,MAACsF,GAAaL,SAA4BA,KACzCjF,SACC,GAAG,eACH,KAAK,eACL,MAAOpM,EAAWgD,cAAgB,GAClC,SAAUoQ,EACV,YAAY,2CAA0C,GAE1D,SAECN,EACC,WAAC1G,SAAM,QAAQ,qBAAqB,SAAgB,qBACpDA,MAACsF,GAAaJ,SAAiCA,KAC9ClF,SACC,GAAG,qBACH,KAAK,qBACL,MAAOpM,EAAWiD,oBAAsB,GACxC,SAAUmQ,EACV,YAAY,mCAAkC,GAElD,CACF,GAEJ,EC7IMC,GAAyB/I,MAAGC,yIAGzB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACnB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C4D,GAAsB1D,KAAExD,0HACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5CyD,GAAqBvD,IAAC5D,6GACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGrCkF,GAA+BhJ,MAAGC,4IAG/B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACtB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGzC0I,GAAyBjJ,MAAGC,oHAGzB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlC2I,GAAgB9E,QAAKnE,+YAKX,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO8D,WAU1B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,QAQ5B,CAAC,CAAET,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAK/CuI,GAAqBnJ,MAAGC,sOAGnB,CAAC,CAAEyF,OAAM,IAAMA,EAOJ,CAAC,CAAEA,OAAM,IAAMA,CAAK,EAGpC0D,GAA2BpJ,MAAGC,uJACrB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,YACvB,CAAC,CAAExD,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAIzC2I,GAAwBrJ,MAAGC,0FACjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGzCwC,GAAoBxH,QAAK0B,+IAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAEpB,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAG5CkC,GAAuBC,WAAQhG,8PAGxB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GACtC,CAAC,CAAEP,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YACjB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAO8D,WAK9B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAUjD0I,GAAgEA,CAAC,CACrEnO,QACArC,QACA6L,UACF,IAAM,CAEE4E,QAAsBtO,GAA2C,CAC5D0J,qBAAoB1J,EAAEG,OAAOD,KAAK,GAIvCoL,EAAqBtL,GAA8C,CAC9D0J,aAAY1J,EAAEG,OAAOD,KAAK,GAI/BqO,EAAcrO,EAAQ2C,SAAS3C,CAAK,EAAI,EAGxCsO,EAAcxC,GAAyBuC,CAAW,EAGlDE,EAAoBxC,GAA+BsC,CAAW,EAEpE,cACGT,GACC,iBAAC5B,IAAa,SAAwB,6BACtCrF,MAACsF,IAAW,SAGZ,wIAEC4B,GACC,kBAACC,GACC,WAACnH,UACC,KAAK,QACL,IAAI,IACJ,IAAI,KACJ,MAAO3G,GAAS,IAChB,SAAUoO,CAAmB,GAE9BzH,UAAY,MAAO2H,EACjBD,SACHA,KACF,EAEA1H,MAACsH,IACEM,SACHA,KACF,SAECL,GACC,WAACvH,UAAW,QAAQ,WAAW,SAAgB,qBAC/CA,MAACkE,GACC,IAAG,WACH,KAAK,WACL,MAAOlN,EACP,SAAUyN,EACV,YAAY,oDAAoD,IAEpE,CACF,GAEJ,ECnLMgC,GAA2BvI,MAAGC,wHAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGlCyB,GAAsBhF,MAAGC,0FACZ,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5C0B,GAAoBxB,KAAExD,+HACb,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUH,GAEnC,CAAC,CAAEpD,OAAM,IAAMA,EAAMC,OAAOuD,YACvB,CAAC,CAAExD,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGzCwE,GAAmBrB,IAAC5D,sHACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GACnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAK9CsE,EAAiBC,KAAEnF,qHAEC,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOC,OAC1C,CAAC,CAAEF,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGrCoJ,GAAyB3J,MAAGC,wKACvB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOhL,MACxB,CAAC,CAAE+K,OAAM,IAAMA,EAAMuD,UAAUhD,GACjC,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOwJ,WAC/B,CAAC,CAAEzJ,OAAM,IAAMA,EAAM8D,aAAavD,GAClC,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAsB5CsJ,GAA0CA,CAAC,CAC/CnU,aACAiP,WACApJ,kBACF,WAEKgN,GACC,kBAACvD,GACC,iBAACC,IAAW,SAAgC,qCAC5CnD,MAACoD,IAAS,SAIV,wMACF,EAGC3J,EAAiBf,aACfsH,UAAiBvG,WAAiBf,YAAY,QAGhD2K,EAAO,UAGPkC,GACC,OAAO3R,EAAW0C,SAAW,GAC7B,WAAmB,QAGpB+M,EAAO,UAGPoC,GACC,OAAO7R,EAAW2C,aAAe,GACjC,WAAmB,QAGpB8M,EAAO,UAGPsC,GACC,OAAO/R,EAAW4C,aAAe,GACjC,WAAmB,QAGpB6M,EAAO,UAGP4C,GACC,OAAOrS,EAAW6C,YAAc,GAChC,WAAmB,QAGpB4M,EAAO,IAGRrD,MAAC6G,GACC,cACA,UAAmB,SAGpBxD,EAAO,IAGRrD,MAACwH,GACC,OAAO5T,EAAWkD,kBAAoB,IACtC,MAAOlD,EAAWmD,UAAY,GAC9B,UAAmB,EAEvB,ICpIEiR,GAAoB9J,MAAGC,uJAIV,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAG5CwG,GAAeC,KAAE/J,mHACR,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUuG,IAEnC,CAAC,CAAE9J,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAY5CuG,GAAkDA,CAAC,CAAElV,aAAYU,YAAW,IAE9EoM,MAACgI,GACC,gBAACC,GACE/U,YAAa,gBAAkB,eAAeU,EAAWM,WAAWN,EAAWE,QAClF,CACF,GC3BEuU,EAAiBnK,MAAGC,kJAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlCiI,EAAmBxI,MAAGC,iHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,EAAelK,QAAK0B,0GACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CuJ,EAAehG,QAAKnE,2LACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAKjDyJ,GAAgBC,SAAMrK,4LACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAKjD+I,EAAyBnF,OAAIvE,mHACxB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOhL,MACxB,CAAC,CAAE+K,OAAM,IAAMA,EAAMuD,UAAUhD,EAAE,EAc1C6J,GAA4DA,CAAC,CACjE7U,aACAqF,eACAQ,mBACAkB,qBACF,IAAM,CAEE+N,QAAqBvP,GAA2C,CACpEF,EAAaE,CAAC,EACVwB,GAEF4B,WAAW5B,EAAqB,CAAC,CACnC,EAGF,OAEI+J,oCAAC2D,EACC,kBAAC3B,EACC,WAAC1G,SAAM,QAAQ,OAAO,SAAI,SACzBA,SACC,GAAG,OACH,KAAK,OACL,KAAK,OACL,MAAOpM,EAAWE,KAClB,SAAUmF,EACV,SAAQ,KAETQ,EAAiB3F,MAASkM,SAAiBvG,WAAiB3F,KAAK,GACpE,SAEC4S,EACC,WAAC1G,SAAM,QAAQ,SAAS,SAAM,WAC7BA,SACC,GAAG,SACH,KAAK,SACL,KAAK,OACL,MAAOpM,EAAWM,OAClB,SAAU+E,EACV,SAAQ,KAETQ,EAAiBvF,QAAW8L,SAAiBvG,WAAiBvF,OAAO,GACxE,GACF,SAECmU,EACC,kBAAC3B,EACC,WAAC1G,SAAM,QAAQ,YAAY,SAAS,cACnC0E,WACC,GAAG,YACH,KAAK,YACL,MAAO9Q,EAAWO,UAClB,SAAU8E,EACV,SAAQ,GAER,UAAC+G,gBAAO,MAAM,OAAO,SAAI,SACxBA,gBAAO,MAAM,QAAQ,SAAK,WAC7B,GACF,SAEC0G,EACC,WAAC1G,SAAM,QAAQ,SAAS,SAAM,WAC7B0E,WACC,GAAG,SACH,KAAK,SACL,MAAO9Q,EAAWsD,OAClB,SAAU+B,EACV,SAAQ,GAER,UAAC+G,gBAAO,MAAM,MAAM,SAAG,QACtBA,gBAAO,MAAM,OAAO,SAAI,SACxBA,gBAAO,MAAM,YAAY,SAAS,eACrC,GACF,GACF,SAECqI,EACC,kBAAC3B,EACC,WAAC1G,SAAM,QAAQ,aAAa,SAAW,sBACtCsI,EACC,IAAG,aACH,KAAK,aACL,KAAK,SACL,KAAK,OACL,MAAO1U,EAAWS,WAClB,SAAUqU,EACV,SAAQ,KAETjP,EAAiBpF,YACf2L,SAAiBvG,WAAiBpF,WAAW,GAElD,SAECqS,EACC,WAAC1G,SAAM,QAAQ,YAAY,SAAU,qBACpCsI,EACC,IAAG,YACH,KAAK,YACL,KAAK,SACL,KAAK,OACL,MAAO1U,EAAWU,UAClB,SAAUoU,EACV,SAAQ,KAETjP,EAAiBnF,WACf0L,SAAiBvG,WAAiBnF,UAAU,GAEjD,GACF,SAEC+T,EACC,kBAAC3B,EACC,WAAC1G,SAAM,QAAQ,WAAW,SAAQ,aACjCA,SACC,GAAG,WACH,KAAK,WACL,KAAK,SACL,MAAOpM,EAAWQ,SAClB,SAAUsU,EACV,SAAQ,KAETjP,EAAiBrF,UACf4L,SAAiBvG,WAAiBrF,SAAS,GAEhD,SAECsS,EACC,WAAC1G,SAAM,QAAQ,SAAS,SAAe,0BACtCsI,EACC,IAAG,SACH,KAAK,SACL,KAAK,SACL,KAAK,OACL,MAAO1U,EAAWa,OAClB,SAAUwE,EACV,SAAQ,MAEZ,GACF,CACF,GAEJ,EC7LM0P,GAA6BzK,MAAGC,0HAG7B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,yGACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAC7B,CAAC,CAAEP,OAAM,IAAMA,EAAMW,YAAY4J,OACvC,CAAC,CAAEvK,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5CgH,GAAmBvG,QAAKnE,4RACjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GACtC,CAAC,CAAEP,OAAM,IAAMA,EAAMuD,UAAUnD,GACnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,YACjB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOwK,QACrB,CAAC,CAAEzK,OAAM,IAAMA,EAAMc,YAAYC,KAI1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAOQ,QAIxB,CAAC,CAAET,OAAM,IAAMA,EAAMC,OAAOyK,SAAS,EAUvDC,GAAwCA,CAAC,CAC7C1M,KACAlD,OACAC,QACAwJ,WACApG,QACAwM,WAAW,GACXC,WAAW,GACXzJ,YACA0J,cAAc,QACd9H,MACAD,KACF,IAEIsD,OAACiE,IAAoB,YAClBlM,UACCA,UAACkK,GAAM,SAASrK,EACbG,YACAwM,GAAajJ,cAAK,MAAO,CAAE4D,MAAO,OAAS,SAAE,QAChD,EAED5D,UACC,KACA,OACA,KAAK,OACL,QACA,WACA,WACA,WACA,cACA,MACA,KAAS,EAEb,IClEEoJ,GAAyBlL,MAAGC,sHAGzB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,yGACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAC7B,CAAC,CAAEP,OAAM,IAAMA,EAAMW,YAAY4J,OACvC,CAAC,CAAEvK,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5C0G,GAAgBC,SAAMrK,yRACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GACtC,CAAC,CAAEP,OAAM,IAAMA,EAAMuD,UAAUnD,GACnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,YACjB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOwK,QACrB,CAAC,CAAEzK,OAAM,IAAMA,EAAMc,YAAYC,KAI1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAOQ,QAIxB,CAAC,CAAET,OAAM,IAAMA,EAAMC,OAAOyK,SAAS,EAUvDM,EAAgDA,CAAC,CACrD/M,KACAlD,OACAC,QACAwJ,WACAyG,UACA7M,QACAwM,WAAW,GACXC,WAAW,GACXzJ,YACA0J,aACF,IAEIzE,OAAC0E,IAAgB,YACd3M,UACCA,UAACkK,GAAM,SAASrK,EACbG,YACAwM,GAAajJ,cAAK,MAAO,CAAE4D,MAAO,OAAS,SAAE,QAChD,SAED2E,GACC,MACA,OACA,QACA,WACA,WACA,WAECY,mBACE,SAAO,OAAM,GAAG,SAAQ,GACtBA,SACHA,IAEDG,EAAQrJ,IACP+C,SAAC,SAA0B,OAAOA,EAAO3J,MACtC2J,SAAOvG,SADGuG,EAAO3J,KAEpB,CACD,GACH,CACF,ICzFEgP,GAAiBnK,MAAGC,iJAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlCiI,EAAmBxI,MAAGC,gHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC6F,GAAyBnF,OAAIvE,kHACxB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOhL,MACxB,CAAC,CAAE+K,OAAM,IAAMA,EAAMuD,UAAUhD,EAAE,EAa1C2K,GAA8DA,CAAC,CACnE3V,aACAqF,eACAQ,kBACF,IAGMiL,oCAAC2D,GACC,kBAAC3B,EACC,iBAACsC,GACC,IAAG,SACH,KAAK,SACL,MAAM,qBACN,MAAOpV,EAAWmB,QAAU,GAC5B,SAAUkE,EAAa,EAExBQ,EAAiB1E,QAAWiL,UAAiBvG,WAAiB1E,OAAO,GACxE,SAEC2R,EACC,iBAACsC,GACC,IAAG,YACH,KAAK,YACL,MAAM,aACN,MAAOpV,EAAWiB,WAAa,GAC/B,SAAUoE,EAAa,EAExBQ,EAAiB5E,WACfmL,UAAiBvG,WAAiB5E,UAAU,GAEjD,SAEC6R,EACC,iBAACsC,GACC,IAAG,WACH,KAAK,WACL,MAAM,YACN,MAAOpV,EAAWkB,UAAY,GAC9B,SAAUmE,EAAa,EAExBQ,EAAiB3E,UACfkL,UAAiBvG,WAAiB3E,SAAS,GAEhD,GACF,SAECuT,GACC,iBAAC3B,GACC,SAAC1G,SACC,GAAG,UACH,KAAK,UACL,MAAM,uBACN,MAAOpM,EAAWe,SAAW,GAC7B,SAAUsE,EACV,QAASyD,GACT,YAAY,iBAAgB,CAEhC,SAECgK,EACC,gBAAC2C,GACC,GAAG,SACH,KAAK,SACL,MAAM,SACN,MAAOzV,EAAWoB,QAAU,SAC5B,SAAUiE,EACV,QAAS2D,EAAe,GAE5B,GACF,CACF,ICpGEyL,GAAiBnK,MAAGC,iJAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlCiI,EAAmBxI,MAAGC,gHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,EAAelK,QAAK0B,yGACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CuJ,EAAehG,QAAKnE,0LACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAKjD0K,GAAkB9G,OAAIvE,+FAEjB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAY9C0K,GAA0DA,CAAC,CAC/D7V,aACAqF,eACAQ,kBACF,IAGMiL,oCAAC2D,GACC,kBAAC3B,EACC,WAAC1G,SAAM,QAAQ,WAAW,SAAS,cAClCA,SACC,GAAG,WACH,KAAK,WACL,KAAK,SACL,KAAK,OACL,MAAOpM,EAAWW,UAAY,GAC9B,SAAU0E,EAAa,GAE3B,SAECyN,EACC,WAAC1G,SAAM,QAAQ,aAAa,SAAW,gBACtCA,SACC,GAAG,aACH,KAAK,aACL,KAAK,SACL,KAAK,OACL,MAAOpM,EAAWY,YAAc,GAChC,SAAUyE,EAAa,GAE3B,GACF,SAECoP,GACC,kBAAC3B,EACC,WAAC1G,SAAM,QAAQ,aAAa,SAAa,kBACxCA,SACC,GAAG,aACH,KAAK,aACL,KAAK,SACL,KAAK,OACL,MAAOpM,EAAWuB,YAAc,GAChC,SAAU8D,EAAa,GAE3B,SAECyN,EACC,WAAC1G,SAAM,QAAQ,YAAY,SAAU,qBACpCsI,EACC,IAAG,YACH,KAAK,YACL,KAAK,SACL,KAAK,OACL,MAAO1U,EAAWqB,WAAa,GAC/B,SAAUgE,EACV,SAAQ,KAEV+G,MAACwJ,IAAS,SAAwC,8CACpD,GACF,CACF,ICjGSE,GAAyB,CACpC,CAAErQ,MAAO,YAAaoD,MAAO,uBAAwB,EACrD,CAAEpD,MAAO,UAAWoD,MAAO,qBAAsB,EACjD,CAAEpD,MAAO,QAASoD,MAAO,sBAAuB,CAAC,EAMtCkN,GAA0B,CACrC,CAAEtQ,MAAO,0BAA2BoD,MAAO,yBAA0B,EACrE,CAAEpD,MAAO,uBAAwBoD,MAAO,sBAAuB,EAC/D,CAAEpD,MAAO,4BAA6BoD,MAAO,2BAA4B,EACzE,CAAEpD,MAAO,sBAAuBoD,MAAO,qBAAsB,EAC7D,CAAEpD,MAAO,sBAAuBoD,MAAO,qBAAsB,EAC7D,CAAEpD,MAAO,+BAAgCoD,MAAO,8BAA+B,CAAC,EAMrEmN,GAAwB,CACnC,CAAEvQ,MAAO,oCAAqCoD,MAAO,mCAAoC,EACzF,CAAEpD,MAAO,sCAAuCoD,MAAO,qCAAsC,EAC7F,CAAEpD,MAAO,kCAAmCoD,MAAO,iCAAkC,EACrF,CAAEpD,MAAO,wCAAyCoD,MAAO,uCAAwC,EACjG,CAAEpD,MAAO,0BAA2BoD,MAAO,yBAA0B,CAAC,EAM3DoN,GAAsB,CACjC,CAAExQ,MAAO,gBAAiBoD,MAAO,eAAgB,EACjD,CAAEpD,MAAO,iBAAkBoD,MAAO,gBAAiB,EACnD,CAAEpD,MAAO,uBAAwBoD,MAAO,sBAAuB,EAC/D,CAAEpD,MAAO,sBAAuBoD,MAAO,qBAAsB,EAC7D,CAAEpD,MAAO,4BAA6BoD,MAAO,2BAA4B,EACzE,CAAEpD,MAAO,2BAA4BoD,MAAO,0BAA2B,CAAC,EAM7DqN,GAAoB,CAC/B,CAAEzQ,MAAO,GAAIoD,MAAO,qBAAsB,EAC1C,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,gBAAiBoD,MAAO,eAAgB,EACjD,CAAEpD,MAAO,0BAA2BoD,MAAO,yBAA0B,EACrE,CAAEpD,MAAO,+BAAgCoD,MAAO,8BAA+B,EAC/E,CAAEpD,MAAO,YAAaoD,MAAO,WAAY,EACzC,CAAEpD,MAAO,cAAeoD,MAAO,aAAc,EAC7C,CAAEpD,MAAO,gBAAiBoD,MAAO,eAAgB,EACjD,CAAEpD,MAAO,eAAgBoD,MAAO,cAAe,EAC/C,CAAEpD,MAAO,YAAaoD,MAAO,WAAY,CAAC,EAM/BsN,GAAyB,CACpC,CAAE1Q,MAAO,cAAeoD,MAAO,aAAc,EAC7C,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,YAAaoD,MAAO,WAAY,EACzC,CAAEpD,MAAO,uBAAwBoD,MAAO,sBAAuB,EAC/D,CAAEpD,MAAO,oBAAqBoD,MAAO,mBAAoB,EACzD,CAAEpD,MAAO,uBAAwBoD,MAAO,sBAAuB,CAAC,EAMrDuN,GAA8B,CACzC,CAAE3Q,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,kBAAmBoD,MAAO,iBAAkB,EACrD,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,CAAC,EAM7BwN,GAAuB,CAClC,CAAE5Q,MAAO,oBAAqBoD,MAAO,mBAAoB,EACzD,CAAEpD,MAAO,sBAAuBoD,MAAO,qBAAsB,EAC7D,CAAEpD,MAAO,2BAA4BoD,MAAO,0BAA2B,EACvE,CAAEpD,MAAO,oBAAqBoD,MAAO,mBAAoB,EACzD,CAAEpD,MAAO,oBAAqBoD,MAAO,mBAAoB,CAAC,EAM/CyN,GAAwB,CACnC,CAAE7Q,MAAO,gBAAiBoD,MAAO,eAAgB,EACjD,CAAEpD,MAAO,kBAAmBoD,MAAO,iBAAkB,EACrD,CAAEpD,MAAO,uBAAwBoD,MAAO,sBAAuB,EAC/D,CAAEpD,MAAO,gBAAiBoD,MAAO,eAAgB,EACjD,CAAEpD,MAAO,gBAAiBoD,MAAO,eAAgB,CAAC,EAMvC0N,GAAsB,CACjC,CAAE9Q,MAAO,iBAAkBoD,MAAO,gBAAiB,EACnD,CAAEpD,MAAO,YAAaoD,MAAO,WAAY,EACzC,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,0BAA2BoD,MAAO,yBAA0B,EACrE,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,CAAC,EAM7B2N,GAAkB,CAC7B,GAAGL,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,EAAmB,EAMXE,GAAqB,CAChC,CAAEhR,MAAO,GAAIoD,MAAO,qBAAsB,EAC1C,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,mBAAoBoD,MAAO,kBAAmB,EACvD,CAAEpD,MAAO,YAAaoD,MAAO,WAAY,CAAC,EAM/B6N,GAA0B,CACrC,CAAEjR,MAAO,GAAIoD,MAAO,qBAAsB,EAC1C,CAAEpD,MAAO,OAAQoD,MAAO,MAAO,EAC/B,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,OAAQoD,MAAO,MAAO,EAC/B,CAAEpD,MAAO,WAAYoD,MAAO,UAAW,EACvC,CAAEpD,MAAO,cAAeoD,MAAO,aAAc,EAC7C,CAAEpD,MAAO,aAAcoD,MAAO,YAAa,EAC3C,CAAEpD,MAAO,YAAaoD,MAAO,WAAY,EACzC,CAAEpD,MAAO,uBAAwBoD,MAAO,sBAAuB,EAC/D,CAAEpD,MAAO,oBAAqBoD,MAAO,mBAAoB,EACzD,CAAEpD,MAAO,uBAAwBoD,MAAO,sBAAuB,CAAC,EAMrD8N,GAA6BC,GAAqB,CAC7D,OAAQA,EAAQ,CACd,IAAK,YACIb,UACT,IAAK,UACIC,UACT,IAAK,QACIC,UACT,QACE,MAAO,EACX,CACF,EAKaY,GAAuBC,GAAuB,CACzD,OAAQA,EAAU,CAChB,IAAK,aACIN,UACT,IAAK,mBACIN,UACT,QACE,MAAO,EACX,CACF,EC1LMtI,GAA2BtD,MAAGC,wHAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlC4G,GAAsB1D,KAAExD,yHACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5C8I,GAAsBzM,MAAGC,+LACT,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO8D,WACvB,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,QAC1C,CAAC,CAAET,OAAM,IAAMA,EAAMG,QAAQI,GACvB,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GAEjC,CAAC,CAAEJ,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C2H,GAAmBxI,MAAGC,gHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,yGACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CwJ,GAAgBC,SAAMrK,2LACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAKjDmD,GAAoB/D,MAAGC,2KAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,GAC3B,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GACpB,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOC,MAAM,EAGvD2D,GAAqBhE,MAAGC,mMAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxDC,GAAoBC,QAAKnE,uFACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAG3C4D,GAAoB/F,QAAK0B,8FAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAW5C+I,GAA4DA,CAAC,CAAEvR,QAAOwJ,UAAS,IAAM,CACzF,KAAM,CAACgI,EAAcC,CAAe,EAAIzX,WAA6C,CAAE,GAGvF8D,YAAU,IAAM,CACVkC,EAAMmR,SACQD,KAA0BlR,EAAMmR,QAAQ,CAAC,EAEzDM,EAAgB,CAAE,EACpB,EACC,CAACzR,EAAMmR,QAAQ,CAAC,EAGbO,QAAwB5R,GAA4C,CAClE6R,QAAc7R,EAAEG,OAAOD,MAC7BwJ,EAAS,uBAAwBmI,CAAW,EAG5CnI,EAAS,mBAAoB,EAAE,GAI3BoI,EAAyB9R,GAA2C,CAC/D0J,qBAAoB1J,EAAEG,OAAOD,KAAK,GAG7C,cACGmI,GACC,iBAAC6D,IAAa,SAA4B,iCAE1CrF,MAAC2K,IAAY,SAGb,wIAECjE,GACC,WAAC1G,UAAM,QAAQ,uBAAuB,SAAwB,6BAC9D0E,OAAC6D,GACC,IAAG,uBACH,KAAK,uBACL,MAAOlP,EAAMmR,UAAY,GACzB,SAAUO,EAEV,UAAC/K,gBAAO,MAAM,GAAG,SAA6B,kCAC7C0J,GAAuBzJ,IACtB+C,SAAC,SAA0B,OAAOA,EAAO3J,MACtC2J,SAAOvG,SADGuG,EAAO3J,KAEpB,CACD,GACH,GACF,EAECA,EAAMmR,UAAYK,EAAa/Q,OAAS,GACtCkG,UACE6K,SAAa5K,MACZ+C,UAACd,GACC,iBAACG,IACC,KAAK,QACL,GAAI,WAAWW,EAAO3J,QACtB,KAAK,mBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAM6R,OAASlI,EAAO3J,MAC/B,SAAU4R,EAAsB,QAEjCzI,GAAW,SAAS,WAAWQ,EAAO3J,QACpC2J,WAAOvG,KACV,EAXgBuG,KAAO3J,KAYzB,CACD,EACH,CAEJ,GAEJ,EC/JMmI,GAA2BtD,MAAGC,yHAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlC4G,GAAsB1D,KAAExD,0HACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5C8I,GAAsBzM,MAAGC,gMACT,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO8D,WACvB,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,QAC1C,CAAC,CAAET,OAAM,IAAMA,EAAMG,QAAQI,GACvB,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GAEjC,CAAC,CAAEJ,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C2H,GAAmBxI,MAAGC,iHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,0GACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CwJ,GAAgBC,SAAMrK,4LACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAKjDmD,GAAoB/D,MAAGC,4KAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,GAC3B,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GACpB,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOC,MAAM,EAGvD2D,GAAqBhE,MAAGC,oMAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxDC,GAAoBC,QAAKnE,wFACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAG3C4D,GAAoB/F,QAAK0B,+FAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAG5CgG,GAAyBnF,OAAIvE,oHACxB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOhL,MACxB,CAAC,CAAE+K,OAAM,IAAMA,EAAMuD,UAAUhD,EAAE,EAiB1CuM,GAAgEA,CAAC,CACrE9R,QACA+R,eACAvI,WACAvP,OACF,IAAM,CACJ,KAAM,CAACuX,EAAcC,CAAe,EAAIzX,WAA6C,CAAE,GAGvF8D,YAAU,IAAM,CACVkC,EAAMmR,SACQD,KAA0BlR,EAAMmR,QAAQ,CAAC,EAEzDM,EAAgB,CAAE,EACpB,EACC,CAACzR,EAAMmR,QAAQ,CAAC,EAGbO,QAAwB5R,GAA4C,CAClE6R,QAAc7R,EAAEG,OAAOD,MAC7BwJ,EAAS,yBAA0BmI,CAAW,EAG9CnI,EAAS,qBAAsB,EAAE,GAI7BoI,EAAyB9R,GAA2C,CAC/D0J,uBAAsB1J,EAAEG,OAAOD,KAAK,GAG/C,cACGmI,GACC,iBAAC6D,IAAa,SAA8B,mCAE5CrF,MAAC2K,IAAY,SAGb,0JAECjE,GACC,WAAC1G,UAAM,QAAQ,yBAAyB,SAA0B,+BAClE0E,OAAC6D,GACC,IAAG,yBACH,KAAK,yBACL,MAAOlP,EAAMmR,UAAY,GACzB,SAAUO,EAEV,UAAC/K,gBAAO,MAAM,GAAG,SAA+B,oCAC/C0J,GAAuBzJ,IACtB+C,SAAC,SAA0B,OAAOA,EAAO3J,MACtC2J,SAAOvG,SADGuG,EAAO3J,KAEpB,CACD,GACH,GACF,EAECA,EAAMmR,UAAYK,EAAa/Q,OAAS,GACtCkG,UACE6K,SAAa5K,MACZ+C,UAACd,GACC,iBAACG,IACC,KAAK,QACL,GAAI,aAAaW,EAAO3J,QACxB,KAAK,qBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAM6R,OAASlI,EAAO3J,MAC/B,SAAU4R,EAAsB,QAEjCzI,GAAW,SAAS,aAAaQ,EAAO3J,QACtC2J,WAAOvG,KACV,EAXgBuG,KAAO3J,KAYzB,CACD,EACH,EAGD/F,GAAU0M,UAAiB1M,SAAMA,GACpC,GAEJ,ECpLMkO,GAA2BtD,MAAGC,yHAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlC4G,GAAsB1D,KAAExD,0HACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5C8I,GAAsBzM,MAAGC,gMACT,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO8D,WACvB,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,QAC1C,CAAC,CAAET,OAAM,IAAMA,EAAMG,QAAQI,GACvB,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GAEjC,CAAC,CAAEJ,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C2H,GAAmBxI,MAAGC,iHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,0GACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CwJ,GAAgBC,SAAMrK,4LACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAUjDuM,GAAsDA,CAAC,CAAEhS,QAAOwJ,UAAS,IAAM,CAE7EyI,QAAyBnS,GAA4C,CAChE0J,mBAAkB1J,EAAEG,OAAOD,KAAK,GAG3C,cACGmI,GACC,iBAAC6D,IAAa,SAAe,oBAE7BrF,MAAC2K,IAAY,SAEb,mFAECjE,GACC,WAAC1G,UAAM,QAAQ,iBAAiB,SAAsB,2BACtDA,MAACuI,GACC,IAAG,iBACH,KAAK,iBACL,MAAOlP,GAAS,GAChB,SAAUiS,EAETxB,YAAkB7J,IACjB+C,SAAC,SAA0B,OAAOA,EAAO3J,MACtC2J,WAAOvG,KADGuG,IAAO3J,KAEpB,CACD,CACH,IACF,CACF,GAEJ,EC9EMmI,GAA2BtD,MAAGC,yHAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlC4G,GAAsB1D,KAAExD,0HACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5C8I,GAAsBzM,MAAGC,gMACT,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO8D,WACvB,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,QAC1C,CAAC,CAAET,OAAM,IAAMA,EAAMG,QAAQI,GACvB,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GAEjC,CAAC,CAAEJ,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C8G,GAAuB3H,MAAGC,qHAGvB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAGlC2M,EAAuBrN,MAAGC,4FACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAG5C+M,EAAuBC,KAAEtN,8KAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,cAC3B,CAAC,CAAEV,OAAM,IAAMA,EAAMG,QAAQI,GACrB,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQwD,GACpB,CAAC,CAAE3D,OAAM,IAAMA,EAAMC,OAAOC,MAAM,EAGzDuH,EAAwB5H,MAAGC,uMAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxD2D,EAAuBzD,QAAKnE,2FAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAG3CoH,EAAuBvJ,QAAK0B,kGACnB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAQ5C6J,GAA0CA,CAAC,CAAErS,QAAOwJ,UAAS,IAAM,CAEjEqD,QAAwB/M,GAA2C,CACjEwS,QAAUxS,EAAEG,OAAOD,MACnB+M,EAAYjN,EAAEG,OAAO+M,QAEvBC,MAEAF,EAESE,GAAC,GAAGjN,EAAOsS,CAAO,EAG7BrF,EAAWjN,EAAMkN,OAAeC,OAASmF,CAAO,EAGlD9I,EAAS,iBAAkByD,CAAQ,GAGrC,cACG9E,GACC,iBAAC6D,IAAa,SAAuB,4BAErCrF,MAAC2K,IAAY,SAEb,kFAEC9E,GAEC,kBAAC0F,EACC,iBAACC,GAAc,SAAgB,qBAC9BzB,GAAuB9J,IACtB+C,UAAC8C,EACC,iBAACC,GACC,KAAK,WACL,GAAI,OAAO/C,EAAO3J,QAClB,KAAK,iBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAMqE,SAASsF,EAAO3J,KAAK,EACpC,SAAU6M,CAAqB,SAEhCF,EAAc,SAAS,OAAOhD,EAAO3J,QACnC2J,WAAOvG,KACV,KAXmBuG,EAAO3J,KAY5B,CACD,GACH,SAGCkS,EACC,iBAACC,GAAc,SAAuB,4BACrCxB,GAA4B/J,IAC3B+C,UAAC8C,EACC,iBAACC,GACC,KAAK,WACL,GAAI,OAAO/C,EAAO3J,QAClB,KAAK,iBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAMqE,SAASsF,EAAO3J,KAAK,EACpC,SAAU6M,CAAqB,SAEhCF,EAAc,SAAS,OAAOhD,EAAO3J,QACnC2J,WAAOvG,KACV,KAXmBuG,EAAO3J,KAY5B,CACD,GACH,SAGCkS,EACC,iBAACC,GAAc,SAAoB,yBAClCvB,GAAqBhK,IACpB+C,UAAC8C,EACC,iBAACC,GACC,KAAK,WACL,GAAI,OAAO/C,EAAO3J,QAClB,KAAK,iBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAMqE,SAASsF,EAAO3J,KAAK,EACpC,SAAU6M,CAAqB,SAEhCF,EAAc,SAAS,OAAOhD,EAAO3J,QACnC2J,WAAOvG,KACV,KAXmBuG,EAAO3J,KAY5B,CACD,GACH,SAGCkS,EACC,iBAACC,GAAc,SAAa,kBAC3BtB,GAAsBjK,IACrB+C,UAAC8C,EACC,iBAACC,GACC,KAAK,WACL,GAAI,OAAO/C,EAAO3J,QAClB,KAAK,iBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAMqE,SAASsF,EAAO3J,KAAK,EACpC,SAAU6M,CAAqB,SAEhCF,EAAc,SAAS,OAAOhD,EAAO3J,QACnC2J,WAAOvG,KACV,KAXmBuG,EAAO3J,KAY5B,CACD,GACH,SAGCkS,EACC,iBAACC,GAAc,SAAkB,uBAChCrB,GAAoBlK,IACnB+C,UAAC8C,EACC,iBAACC,GACC,KAAK,WACL,GAAI,OAAO/C,EAAO3J,QAClB,KAAK,iBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAMqE,SAASsF,EAAO3J,KAAK,EACpC,SAAU6M,CAAqB,SAEhCF,EAAc,SAAS,OAAOhD,EAAO3J,QACnC2J,WAAOvG,KACV,KAXmBuG,EAAO3J,KAY5B,CACD,GACH,GACF,CACF,GAEJ,ECzMMmI,GAA2BtD,MAAGC,yHAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlC4G,GAAsB1D,KAAExD,0HACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5C8I,GAAsBzM,MAAGC,gMACT,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO8D,WACvB,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,QAC1C,CAAC,CAAET,OAAM,IAAMA,EAAMG,QAAQI,GACvB,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GAEjC,CAAC,CAAEJ,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C2H,GAAmBxI,MAAGC,iHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,0GACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CwJ,GAAgBC,SAAMrK,4LACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAKjDmD,GAAoB/D,MAAGC,4KAGpB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACtB,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,GAC3B,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GACpB,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOC,MAAM,EAGvD2D,GAAqBhE,MAAGC,oMAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,GACvB,CAAC,CAAE3D,OAAM,IAAMA,EAAM8D,aAAavD,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMc,YAAYC,KAG1C,CAAC,CAAEf,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxDC,GAAoBC,QAAKnE,wFACb,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAG3C4D,GAAoB/F,QAAK0B,+FAChB,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAW5C+J,GAAsDA,CAAC,CAAEvS,QAAOwJ,UAAS,IAAM,CACnF,KAAM,CAACgJ,EAAiBC,CAAkB,EAAIzY,WAA6C,CAAE,GAG7F8D,YAAU,IAAM,CACVkC,EAAMqR,YAAcrR,EAAMqR,aAAe,YACxBD,KAAoBpR,EAAMqR,UAAU,CAAC,EAExDoB,EAAmB,CAAE,EACvB,EACC,CAACzS,EAAMqR,UAAU,CAAC,EAGfqB,QAA0B5S,GAA4C,CACpE6S,QAAgB7S,EAAEG,OAAOD,MAC/BwJ,EAAS,gBAAiBmJ,CAAa,EAGvCnJ,EAAS,kBAAmB,EAAE,GAI1BoJ,EAA4B9S,GAA2C,CAClE0J,oBAAmB1J,EAAEG,OAAOD,KAAK,GAG5C,cACGmI,GACC,iBAAC6D,IAAa,SAAe,oBAE7BrF,MAAC2K,IAAY,SAEb,qFAECjE,GACC,WAAC1G,UAAM,QAAQ,gBAAgB,SAAgB,qBAC/CA,MAACuI,GACC,IAAG,gBACH,KAAK,gBACL,MAAOlP,EAAMqR,YAAc,GAC3B,SAAUqB,EAET1B,YAAmBpK,IAAK+C,GACtBhD,gBAA0B,MAAOgD,EAAO3J,MACtC2J,WAAOvG,KADGuG,IAAO3J,KAEpB,CACD,CACH,IACF,EAECA,EAAMqR,YAAcrR,EAAMqR,aAAe,aAAemB,EAAgB/R,OAAS,GAE9EkG,kCAAC0G,GACC,iBAACC,GACEtN,YAAMqR,aAAe,aAAe,mBAAqB,yBAC5D,EACA1K,MAACiC,IACE4J,SAAgB5L,SACf+C,EAAO3J,cACJ6I,GACC,iBAACG,IACC,KAAK,QACL,GAAI,aAAaW,EAAO3J,QACxB,KAAK,kBACL,MAAO2J,EAAO3J,MACd,QAASA,EAAM6S,eAAiBlJ,EAAO3J,MACvC,SAAU4S,EAAyB,QAEpCzJ,GAAW,SAAS,aAAaQ,EAAO3J,QACtC2J,WAAOvG,KACV,EAXgBuG,KAAO3J,KAYzB,CAEH,EACH,GACF,CACF,EAEJ,GAEJ,ECzKMmI,GAA2BtD,MAAGC,yHAG3B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlC4G,GAAsB1D,KAAExD,0HACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,WAAW,EAI5C8I,GAAsBzM,MAAGC,gMACT,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO8D,WACvB,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOQ,QAC1C,CAAC,CAAET,OAAM,IAAMA,EAAMG,QAAQI,GACvB,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,GAEjC,CAAC,CAAEJ,OAAM,IAAMA,EAAMuD,UAAUhD,GACnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C2H,GAAmBxI,MAAGC,iHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,0GACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9CwJ,GAAgBC,SAAMrK,4LACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAGnB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAUjDqN,GAA8DA,CAAC,CAAE9S,QAAOwJ,UAAS,IAAM,CAErFuJ,QAA6BjT,GAA4C,CACpE0J,kBAAiB1J,EAAEG,OAAOD,KAAK,GAG1C,cACGmI,GACC,iBAAC6D,IAAa,SAAe,oBAE7BrF,MAAC2K,IAAY,SAGb,qIAECjE,GACC,WAAC1G,UAAM,QAAQ,gBAAgB,SAAgB,qBAC/CA,MAACuI,GACC,IAAG,gBACH,KAAK,gBACL,MAAOlP,GAAS,GAChB,SAAU+S,EAET9B,YAAwBrK,IACvB+C,SAAC,SAA0B,OAAOA,EAAO3J,MACtC2J,WAAOvG,KADGuG,IAAO3J,KAEpB,CACD,CACH,IACF,CACF,GAEJ,EChFMgT,GAA0BnO,MAAGC,wHAG1B,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGlC4B,EAAiBC,KAAEnF,sHAEC,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOC,OAC1C,CAAC,CAAEF,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGrCoJ,GAAyB3J,MAAGC,yKACvB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOhL,MACxB,CAAC,CAAE+K,OAAM,IAAMA,EAAMuD,UAAUhD,GACjC,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOwJ,WAC/B,CAAC,CAAEzJ,OAAM,IAAMA,EAAM8D,aAAavD,GAClC,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAqB5C6N,GAAwEA,CAAC,CAC7E1Y,aACAiP,WACApJ,kBACF,IAAM,CAEE8S,QAAoBA,CAACC,EAAenT,IAAe,CACvDwJ,EAAS2J,EAAOnT,CAAK,GAGvB,cACGgT,GAEE5S,aAAiBgT,qBAChBzM,MAAC6H,GAAiBpO,YAAiBgT,oBAAoB,EAIzDzM,MAAC4K,IACC,MAAO,CACLJ,SAAU5W,EAAWyB,qBACrB6V,KAAMtX,EAAW0B,kBAEnB,SAAUiX,EAAkB,QAG7BlJ,EAAO,IAGRrD,MAACmL,IACC,MAAO,CACLX,SAAU5W,EAAW2B,uBACrB2V,KAAMtX,EAAW4B,oBAEnB,aAAc,CACZgV,SAAU5W,EAAWyB,qBACrB6V,KAAMtX,EAAW0B,gBAEnB,WAAUiX,EACV,MAAO9S,EAAiBjE,kBAAmB,SAG5C6N,EAAO,UAGPgI,GACC,OAAOzX,EAAW6B,gBAAkB,GACpC,SAAU8W,EAAkB,QAG7BlJ,EAAO,IAGRrD,MAAC0L,IACC,MAAO9X,EAAW8B,gBAAkB,GACpC,SAAU6W,EAAkB,QAG7BlJ,EAAO,IAGRrD,MAAC4L,IACC,MAAO,CACLlB,WAAY9W,EAAW+B,cACvBuW,aAActY,EAAWgC,iBAE3B,SAAU2W,EAAkB,QAG7BlJ,EAAO,UAGP8I,GACC,OAAOvY,EAAWiC,eAAiB,GACnC,SAAU0W,EAAkB,CAEhC,GAEJ,ECjHMlE,GAAiBnK,MAAGC,kJAGjB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGlCiI,EAAmBxI,MAAGC,iHAGnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQwD,EAAE,EAGlC2E,GAAelK,QAAK0B,0GACX,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUhD,GAEnC,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOS,aAAa,EAG9C6H,GAAkBzC,WAAQhG,+MACnB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8D,WAC5B,CAAC,CAAE/D,OAAM,IAAMA,EAAMC,OAAOC,OAC/B,CAAC,CAAEF,OAAM,IAAMA,EAAM8D,aAAavD,GAC1C,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOuD,YAInB,CAAC,CAAExD,OAAM,IAAMA,EAAMC,OAAOQ,OAAO,EAKjDuG,GAAsB1D,KAAExD,oIACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GAEnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,YAC3B,CAAC,CAAExD,OAAM,IAAMA,EAAMG,QAAQC,GAAQ,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQI,EAAE,EAG1EyE,GAAiBC,KAAEnF,sHAEC,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOC,OAC1C,CAAC,CAAEF,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAerCiO,GAAkEA,CAAC,CACvE9Y,aACAqF,eACAQ,mBACA5F,eACF,IAIM6Q,mCAACW,IAAa,SAAc,0BAC3BgD,GACC,iBAAC3B,GACC,SAAC1G,SACC,GAAG,YACH,KAAK,YACL,MAAM,aACN,MAAOpM,EAAWc,WAAa,GAC/B,SAAUuE,EACV,QAASuD,GACT,YAAY,oBAAmB,CAEnC,GAEAwD,MAAC0G,GACC,SAAC1G,SACC,GAAG,QACH,KAAK,QACL,MAAM,QACN,MAAOpM,EAAWgB,OAAS,GAC3B,SAAUqE,EACV,QAAS0D,GACT,YAAY,eAAc,CAE9B,IACF,SAEC0L,GACC,iBAAC3B,GACC,SAAC1G,SACC,GAAG,eACH,KAAK,eACL,MAAM,gBACN,MAAOpM,EAAWsB,cAAgB,cAClC,SAAU+D,EACV,QAAS4D,EAAsB,GAEnC,QAEC6J,EACC,gBAAC2C,GACC,GAAG,iBACH,KAAK,iBACL,MAAM,yBACN,MAAOzV,EAAWwB,gBAAkB,IACpC,SAAU6D,EACV,QAAS6D,EAAwB,GAErC,GACF,QAECuG,GAAO,IAGRrD,MAACqF,IAAa,SAAoB,+BACjCiH,GACC,cACA,SAAU,CAACE,EAAOnT,IAAU,CAC1BxF,EAAyB0F,KACvB,GAAGA,EACH,CAACiT,CAAK,EAAGnT,CACT,KAEJ,kBAAmC,SAGpCgK,GAAO,IAGRrD,MAACqF,IAAa,SAAK,iBAClBqB,EACC,WAAC1G,UAAM,QAAQ,QAAQ,SAAW,gBAClCA,MAAC4G,GAAS,IAAG,QAAQ,KAAK,QAAQ,MAAOhT,EAAWoD,MAAO,SAAUiC,CAAa,IACpF,CACF,ICrJE0T,GAAqBzO,MAAGC,oIAGrB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,GACtB,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGzCmL,GAAgBjO,SAAMR,mKACf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQI,GAAM,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQiD,GAC1D,CAAC,CAAEpD,OAAM,IAAMA,EAAM8D,aAAa1D,GAGpB,CAAC,CAAEJ,OAAM,IAAMA,EAAMc,YAAYC,IAAI,EAGhEyN,GAAeC,EAAOF,EAAM,EAACzO,kKAEb,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOC,OACvC,CAAC,CAAEF,OAAM,IAAMA,EAAMC,OAAOS,cAGf,CAAC,CAAEV,OAAM,IAAMA,EAAMC,OAAO8D,UAAU,EAIxD2K,GAAeD,EAAOF,EAAM,EAACzO,mJACb,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOQ,QAK1B,CAAC,CAAET,OAAM,IAAMA,EAAMC,OAAO0O,WAAW,EAazDC,GAAoDA,CAAC,CACzDhS,eACA9H,YACAD,YACF,IAAM,CACJ,MAAM6H,EAAWC,KAEjB,cACG2R,GACC,iBAACE,GACC,MAAK,SACL,QAAS,IAAM,CACbxV,QAAQD,IAAI,8CAA8C,EAC1D2D,EAAS,UAAU,GACnB,SAGJ,WACCiF,UACC,KAAK,SACL,SAAU/E,GAAgB9H,EAC1B,cAAaD,EAAa,mBAAqB,sBAE9C+H,WAAe,YAAc/H,EAAa,YAAc,eAC3D,CACF,GAEJ,ECxEMga,GAAsBhP,MAAGC,mKACpB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOhL,MACxB,CAAC,CAAE+K,OAAM,IAAMA,EAAMuD,UAAUhD,GAC9B,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQwD,GAChC,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAOwJ,YAAc,yBAC7C,CAAC,CAAEzJ,OAAM,IAAMA,EAAM8D,aAAavD,EAAE,EAGjDuO,GAAwBjP,MAAGC,qKACtB,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAO9K,QACxB,CAAC,CAAE6K,OAAM,IAAMA,EAAMuD,UAAUhD,GAC9B,CAAC,CAAEP,OAAM,IAAMA,EAAMG,QAAQwD,GAChC,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQI,GACpB,CAAC,CAAEP,OAAM,IAAMA,EAAMC,OAAO8O,cAAgB,yBAC/C,CAAC,CAAE/O,OAAM,IAAMA,EAAM8D,aAAavD,EAAE,EAGjDyO,GAAiBnP,MAAGC,+IACf,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOS,cACxB,CAAC,CAAEV,OAAM,IAAMA,EAAMuD,UAAUI,GAC9B,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQwD,GAC1B,CAAC,CAAE3D,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAa5C6O,GAAsDA,CAAC,CAC3Dha,QACAE,UACA+Z,cAAc,EAChB,IAGOja,6BAASA,SAAC4Z,IAAc5Z,SAAMA,IAC9BE,GAAYwM,UAAgBxM,SAAQA,IACpC+Z,GAAgBvN,UAAQ,SAA+B,mCAC1D,IC7CEwN,GAAwBtP,MAAGC,4QAYd,CAAC,CAAEE,OAAM,IAAMA,EAAM8D,aAAa1D,EAAE,EAGjDgP,GAAwBvP,MAAGC,6SAGP,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOQ,QAInC,CAAC,CAAET,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EAY5CiP,GAAqBxP,MAAGC,gHACf,CAAC,CAAEE,OAAM,IAAMA,EAAMuD,UAAUnD,GACnC,CAAC,CAAEJ,OAAM,IAAMA,EAAMC,OAAOuD,YACtB,CAAC,CAAExD,OAAM,IAAMA,EAAMW,YAAY4J,MAAM,EAUlD+E,GAAoDA,CAAC,CAAExa,WAAU,IAChEA,SAGFqa,GACC,iBAACC,GAAc,IACfzN,MAAC0N,IAAY,SAAqB,yBACpC,IANqB,KChCnBE,GAAuB1P,MAAGC,oHAGvB,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQiD,EAAE,EAGlCoM,GAAwB3P,MAAGC,8JACX,CAAC,CAAEE,OAAM,IAAMA,EAAMC,OAAOwK,QAC/B,CAAC,CAAEzK,OAAM,IAAMA,EAAM8D,aAAa1D,GACxC,CAAC,CAAEJ,OAAM,IAAMA,EAAMG,QAAQiD,GAC1B,CAAC,CAAEpD,OAAM,IAAMA,EAAMyP,QAAQlP,EAAE,EAIzCmP,GAAcC,OAAI7P,2GAGf,CAAC,CAAEE,OAAM,IAAMA,EAAMG,QAAQC,EAAE,EASlCwP,GAAsBA,IAAM,CAC1B,MAAE3R,MAAO4R,GAA0B,EAGjC9W,YAAI,mDAAmDkF,IAAK,EACpEjF,QAAQD,IAAI,gBAAgBiG,OAAOC,SAAS6Q,MAAM,EAClD9W,QAAQD,IAAI,iBAAiBkF,GAAMA,IAAO,OAAO,EACzClF,YAAI,iBAAiBkF,IAAO,OAAO,EAErC,MACJ1I,aACAC,gBACAoF,eACAkC,eACAF,eACA9H,YACAG,QACAE,UACAiG,mBACAvG,aACA0G,YACAmE,kBACApD,uBACEwC,GAAab,CAAE,EAEnB,cACGsR,GACC,WAAC5N,UAAgB,aAAwB,YAAuB,UAE/D6N,GACC,iBAACF,IAAiB,YAAqB,EAEvCjJ,OAACqJ,GAAK,UAAU5S,EACd,UAAC6E,UAAkB,QAAc,SAAiB,GAElDA,MAACV,GACC,MAAM,CACJ,CACEhD,GAAI,QACJG,MAAO,cACP2D,QACGJ,UACC,aACA,eACA,mBACA,sBAAyC,GAI/C,CACE1D,GAAI,SACJG,MAAO,SACP2D,QACEJ,MAACuJ,GACC,cACA,eACA,mBAAmC,GAIzC,CACEjN,GAAI,OACJG,MAAO,kBACP2D,QACEJ,MAACyJ,GACC,cACA,eACA,mBAAmC,GAIzC,CACEnN,GAAI,WACJG,MAAO,WACP2D,QACGJ,UACC,aACA,eACA,mBACA,gBAA6B,GAInC,CACE1D,GAAI,kBACJG,MAAO,kBACP2D,QACGJ,UACC,aACA,SAAU,CAACwM,EAAOnT,IAAU,CAC1BxF,EAAyB0F,KACvB,GAAGA,EACH,CAACiT,CAAK,EAAGnT,CACT,KACF,GAIR,CACEiD,GAAI,eACJG,MAAO,eACP2D,QACGJ,UACC,aACA,SAAU,CAACwM,EAAOnT,IAAU,CAC1BxF,EAAyB0F,KACvB,GAAGA,EACH,CAACiT,CAAK,EAAGnT,CACT,KAEJ,kBAAmC,GAGxC,EAEH,WAAW,QACX,YACA,WAAY0E,CAAgB,SAG7BuP,GAAkB,OAAO,KAAM,QAAS,KAAM,YAAa,GAAK,EAEhEtN,UACC,eACA,YACA,YAAuB,IAE3B,GACF,CACF,GAEJ", "names": ["useTradeFormData", "tradeId", "isEditMode", "isNewTrade", "isLoading", "setIsLoading", "useState", "error", "setError", "success", "setSuccess", "tradeData", "setTradeData", "formValues", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "date", "Date", "toISOString", "split", "symbol", "direction", "quantity", "entryPrice", "exitPrice", "stopLoss", "takeProfit", "profit", "modelType", "session", "setup", "entryTime", "exitTime", "rdTime", "market", "rMultiple", "entryVersion", "riskPoints", "patternQuality", "primarySetupCategory", "primarySetupType", "secondarySetupCategory", "secondarySetupType", "liquidityTaken", "additionalFVGs", "dolTargetType", "specificDOLType", "parentPDArray", "patternQualityClarity", "patternQualityConfluence", "patternQualityContext", "patternQualityRisk", "patternQualityReward", "patternQualityTimeframe", "patternQualityVolume", "patternQualityNotes", "dolType", "dol<PERSON><PERSON><PERSON><PERSON>", "dolReaction", "dolContext", "dolPriceAction", "dolVolumeProfile", "dolTimeOfDay", "dolMarketStructure", "dolEffectiveness", "dolNotes", "notes", "tags", "result", "useEffect", "log", "console", "sanitizedId", "String", "trim", "Error", "trade", "tradeStorage", "getTradeById", "size", "entry", "exit", "profitLoss", "patternQualityScore", "criteria", "clarity", "confluence", "context", "risk", "reward", "timeframe", "volume", "dolAnalysis", "priceAction", "volumeProfile", "timeOfDay", "marketStructure", "effectiveness", "err", "handleChange", "useCallback", "e", "name", "value", "target", "prev", "useTradeValidation", "validationErrors", "setValidationErrors", "validateCurrentTab", "activeTab", "errors", "length", "newErrors", "Object", "keys", "for<PERSON>ach", "key", "validateBasicInfoTab", "clearFieldError", "fieldName", "clearAllErrors", "useTradeCalculations", "parseFloat", "toFixed", "calculateProfitLoss", "isNaN", "useTradeSubmission", "setActiveTab", "navigate", "useNavigate", "isSubmitting", "setIsSubmitting", "handleSubmit", "preventDefault", "patternQualityData", "allCriteriaFilled", "values", "every", "submission<PERSON><PERSON>ues", "calculateTotalScore", "convertScoreToRating", "__vitePreload", "totalScore", "rating", "total", "parseInt", "updatedTradeData", "undefined", "strategy", "savedTrade", "saveTrade", "id", "setTimeout", "MODEL_TYPE_OPTIONS", "label", "SESSION_OPTIONS", "SETUP_OPTIONS", "MARKET_OPTIONS", "ENTRY_VERSION_OPTIONS", "PATTERN_QUALITY_OPTIONS", "Array", "from", "_", "i", "useTradeForm", "currentPath", "window", "location", "hash", "substring", "effectiveTradeId", "includes", "matches", "match", "validateTab", "validateBasicInfo", "handleTabChange", "newTab", "TabContainer", "div", "withConfig", "TabList", "theme", "colors", "border", "spacing", "md", "TabButton", "button", "sm", "active", "primary", "textSecondary", "fontWeights", "semibold", "regular", "transitions", "fast", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabPanel", "tabs", "defaultTab", "className", "controlledActiveTab", "onTabClick", "internalActiveTab", "setInternalActiveTab", "tabId", "stopPropagation", "jsx", "map", "tab", "find", "content", "SCORE_VALUES", "excellent", "good", "average", "poor", "unacceptable", "SCORE_RANGE_OPTIONS", "PATTERN_QUALITY_CRITERIA", "title", "description", "reduce", "score", "maxPossibleScore", "Math", "round", "max", "min", "getRatingDescription", "getRatingColor", "SelectorContainer", "lg", "CriterionTitle", "h3", "fontSizes", "textPrimary", "CriterionDescription", "p", "xs", "RadioGroup", "RadioOption", "borderRadius", "background", "RadioInput", "input", "RadioLabelContainer", "RadioLabel", "RadioDescription", "span", "CriterionSelector", "criterion", "onChange", "criterionData", "handleScoreChange", "option", "AssessmentContainer", "Introduction", "IntroTitle", "IntroText", "Divider", "hr", "ScoreSection", "ScoreTitle", "ScoreDetails", "xl", "ScoreValue", "color", "ScoreInfo", "ScoreDescription", "ScoreBreakdown", "NotesSection", "NotesLabel", "NotesTextarea", "textarea", "PatternQualityAssessment", "setTotalScore", "setRating", "calculatedRating", "toString", "handleNotesChange", "jsxs", "DOL_TYPE_OPTIONS", "DOL_STRENGTH_OPTIONS", "DOL_REACTION_OPTIONS", "DOL_CONTEXT_OPTIONS", "DOL_PRICE_ACTION_DESCRIPTIONS", "DOL_VOLUME_PROFILE_DESCRIPTIONS", "DOL_TIME_OF_DAY_DESCRIPTION", "DOL_MARKET_STRUCTURE_DESCRIPTION", "getDOLEffectivenessColor", "getDOLEffectivenessDescription", "SectionTitle", "Description", "DOLTypeSelector", "handleDOLTypeChange", "DOLStrengthSelector", "handleDOLStrengthChange", "DOLReactionSelector", "handleDOLReactionChange", "CheckboxGroup", "CheckboxOption", "CheckboxInput", "CheckboxLabel", "DOLContextSelector", "handleCheckboxChange", "contextValue", "isChecked", "checked", "newValue", "filter", "item", "AnalysisContainer", "FormGroup", "Label", "TextArea", "DOLDetailedAnalysis", "getPriceActionDescription", "getVolumeProfileDescription", "handleTextAreaChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RatingS<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Slide<PERSON>", "RatingValue", "RatingDescription", "NotesContainer", "DOLEffectivenessRating", "handleRatingChange", "ratingValue", "ratingColor", "ratingDescription", "ValidationError", "errorLight", "DOLAnalysis", "<PERSON><PERSON><PERSON><PERSON>", "Title", "h1", "xxl", "TradeFormHeader", "FormRow", "Input", "Select", "select", "TradeFormBasicFields", "handlePriceChange", "TimePickerContainer", "medium", "TimeInput", "surface", "chartGrid", "TimePicker", "required", "disabled", "placeholder", "SelectContainer", "SelectDropdown", "options", "TradeFormTimingFields", "HelpText", "TradeFormRiskFields", "SETUP_CATEGORY_OPTIONS", "STRUCTURE_SETUP_OPTIONS", "SESSION_SETUP_OPTIONS", "MODEL_SETUP_OPTIONS", "LIQUIDITY_OPTIONS", "TIME_BASED_FVG_OPTIONS", "CURRENT_SESSION_FVG_OPTIONS", "PREV_DAY_FVG_OPTIONS", "THREE_DAY_FVG_OPTIONS", "SPECIAL_FVG_OPTIONS", "ALL_FVG_OPTIONS", "DOL_TARGET_OPTIONS", "PARENT_PD_ARRAY_OPTIONS", "getSetupOptionsByCategory", "category", "getDOLTargetOptions", "targetType", "GuidanceNote", "PrimarySetupSelector", "setupOptions", "setSetupOptions", "handleCategoryChange", "newCategory", "handleSetupTypeChange", "type", "SecondarySetupSelector", "primarySetup", "LiquiditySelector", "handleLiquidityChange", "CategoryGroup", "CategoryTitle", "h4", "FVGSelector", "fvgType", "DOLTargetSelector", "specificOptions", "setSpecificOptions", "handleTargetTypeChange", "newTargetType", "handleSpecificTypeChange", "specificType", "ParentPDArraySelector", "handleParentPDArrayChange", "SectionContainer", "SetupClassificationSection", "handleFieldChange", "field", "setupClassification", "TradeFormStrategyFields", "ButtonGroup", "<PERSON><PERSON>", "CancelButton", "styled", "SubmitButton", "primaryDark", "TradeFormActions", "ErrorMessage", "SuccessMessage", "successLight", "TabInfo", "TradeFormMessages", "showTabInfo", "LoadingOverlay", "LoadingSpinner", "LoadingText", "TradeFormLoading", "<PERSON><PERSON><PERSON><PERSON>", "ContentSection", "shadows", "Form", "form", "TradeForm", "useParams", "href"], "sources": ["../../src/features/trade-journal/hooks/useTradeFormData.ts", "../../src/features/trade-journal/hooks/useTradeValidation.ts", "../../src/features/trade-journal/hooks/useTradeCalculations.ts", "../../src/features/trade-journal/hooks/useTradeSubmission.ts", "../../src/features/trade-journal/hooks/useTradeForm.ts", "../../src/features/trade-journal/components/TabPanel.tsx", "../../src/features/trade-journal/constants/patternQuality.ts", "../../src/features/trade-journal/components/pattern-quality/CriterionSelector.tsx", "../../src/features/trade-journal/components/pattern-quality/PatternQualityAssessment.tsx", "../../src/features/trade-journal/constants/dolAnalysis.ts", "../../src/features/trade-journal/components/dol-analysis/DOLTypeSelector.tsx", "../../src/features/trade-journal/components/dol-analysis/DOLStrengthSelector.tsx", "../../src/features/trade-journal/components/dol-analysis/DOLReactionSelector.tsx", "../../src/features/trade-journal/components/dol-analysis/DOLContextSelector.tsx", "../../src/features/trade-journal/components/dol-analysis/DOLDetailedAnalysis.tsx", "../../src/features/trade-journal/components/dol-analysis/DOLEffectivenessRating.tsx", "../../src/features/trade-journal/components/dol-analysis/DOLAnalysis.tsx", "../../src/features/trade-journal/components/form/TradeFormHeader.tsx", "../../src/features/trade-journal/components/form/TradeFormBasicFields.tsx", "../../src/features/trade-journal/components/TimePicker.tsx", "../../src/features/trade-journal/components/SelectDropdown.tsx", "../../src/features/trade-journal/components/form/TradeFormTimingFields.tsx", "../../src/features/trade-journal/components/form/TradeFormRiskFields.tsx", "../../src/features/trade-journal/constants/setupClassification.ts", "../../src/features/trade-journal/components/setup-classification/PrimarySetupSelector.tsx", "../../src/features/trade-journal/components/setup-classification/SecondarySetupSelector.tsx", "../../src/features/trade-journal/components/setup-classification/LiquiditySelector.tsx", "../../src/features/trade-journal/components/setup-classification/FVGSelector.tsx", "../../src/features/trade-journal/components/setup-classification/DOLTargetSelector.tsx", "../../src/features/trade-journal/components/setup-classification/ParentPDArraySelector.tsx", "../../src/features/trade-journal/components/setup-classification/SetupClassificationSection.tsx", "../../src/features/trade-journal/components/form/TradeFormStrategyFields.tsx", "../../src/features/trade-journal/components/form/TradeFormActions.tsx", "../../src/features/trade-journal/components/form/TradeFormMessages.tsx", "../../src/features/trade-journal/components/form/TradeFormLoading.tsx", "../../src/features/trade-journal/TradeForm.tsx"], "sourcesContent": ["/**\n * Trade Form Data Hook\n *\n * Custom hook for managing trade form state\n */\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { TradeFormValues, Trade } from '../types';\nimport { tradeStorage } from '../../../services/tradeStorage';\n\n/**\n * Hook for managing trade form data\n * @param tradeId The ID of the trade to load (optional)\n * @param isEditMode Whether the form is in edit mode\n * @param isNewTrade Whether the form is for a new trade\n */\nexport function useTradeFormData(\n  tradeId: string | undefined,\n  isEditMode: boolean,\n  isNewTrade: boolean\n) {\n  const [isLoading, setIsLoading] = useState(isEditMode);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [tradeData, setTradeData] = useState<Trade | null>(null);\n\n  // Initialize form values with defaults\n  const [formValues, setFormValues] = useState<TradeFormValues>({\n    // Basic Information\n    date: new Date().toISOString().split('T')[0],\n    symbol: '',\n    direction: 'long',\n    quantity: '',\n\n    // Price Information\n    entryPrice: '',\n    exitPrice: '',\n    stopLoss: '',\n    takeProfit: '',\n    profit: '',\n\n    // Extended Trading Fields\n    modelType: '',\n    session: '',\n    setup: '',\n    entryTime: '',\n    exitTime: '',\n    rdTime: '',\n    market: 'Stocks',\n    rMultiple: '',\n    entryVersion: 'First Entry',\n    riskPoints: '',\n    patternQuality: '5',\n\n    // Setup Classification Fields\n    primarySetupCategory: '',\n    primarySetupType: '',\n    secondarySetupCategory: '',\n    secondarySetupType: '',\n    liquidityTaken: '',\n    additionalFVGs: [],\n    dolTargetType: '',\n    specificDOLType: '',\n    parentPDArray: '',\n\n    // Pattern Quality Assessment Fields\n    patternQualityClarity: '',\n    patternQualityConfluence: '',\n    patternQualityContext: '',\n    patternQualityRisk: '',\n    patternQualityReward: '',\n    patternQualityTimeframe: '',\n    patternQualityVolume: '',\n    patternQualityNotes: '',\n\n    // DOL Analysis Fields\n    dolType: '',\n    dolStrength: '',\n    dolReaction: '',\n    dolContext: [],\n    dolPriceAction: '',\n    dolVolumeProfile: '',\n    dolTimeOfDay: '',\n    dolMarketStructure: '',\n    dolEffectiveness: '5',\n    dolNotes: '',\n\n    // Additional Information\n    notes: '',\n    tags: [],\n    result: 'win',\n  });\n\n  // Load trade data if in edit mode\n  useEffect(() => {\n    const loadTradeData = async () => {\n      console.log(\n        `loadTradeData called - isEditMode: ${isEditMode}, tradeId: \"${tradeId}\"`\n      );\n\n      // Validate tradeId before proceeding\n      if (!tradeId) {\n        console.error('loadTradeData: tradeId is undefined or null');\n        return;\n      }\n\n      if (tradeId === 'new') {\n        console.log('Creating new trade, skipping data loading');\n        return;\n      }\n\n      if (isEditMode) {\n        try {\n          console.log(`Attempting to load trade data for ID: ${tradeId}`);\n          setIsLoading(true);\n\n          // Ensure tradeId is a valid string\n          const sanitizedId = String(tradeId).trim();\n          if (!sanitizedId) {\n            throw new Error('Invalid trade ID: empty or whitespace');\n          }\n\n          console.log(`Calling tradeStorage.getTradeById with ID: ${sanitizedId}`);\n          const trade = await tradeStorage.getTradeById(sanitizedId);\n          console.log(`Trade data retrieved:`, trade);\n\n          if (trade) {\n            console.log(`Trade found, setting trade data`);\n            setTradeData(trade);\n\n            console.log(`Converting trade data to form values`);\n            // Convert trade data to form values\n            setFormValues({\n              // Basic Information\n              date: trade.date,\n              symbol: trade.symbol,\n              direction: trade.direction === 'Long' ? 'long' : 'short',\n              quantity: String(trade.size),\n\n              // Price Information\n              entryPrice: String(trade.entry),\n              exitPrice: String(trade.exit),\n              stopLoss: trade.stopLoss ? String(trade.stopLoss) : '',\n              takeProfit: trade.takeProfit ? String(trade.takeProfit) : '',\n              profit: String(trade.profitLoss),\n\n              // Extended Trading Fields\n              modelType: trade.modelType || '',\n              session: trade.session || '',\n              setup: trade.setup || '',\n              entryTime: trade.entryTime || '',\n              exitTime: trade.exitTime || '',\n              rdTime: trade.rdTime || '',\n              market: trade.market || 'Stocks',\n              rMultiple: trade.rMultiple ? String(trade.rMultiple) : '',\n              entryVersion: trade.entryVersion || 'First Entry',\n              riskPoints: trade.riskPoints ? String(trade.riskPoints) : '',\n              patternQuality: trade.patternQuality ? String(trade.patternQuality) : '5',\n\n              // Setup Classification Fields\n              primarySetupCategory: trade.primarySetupCategory || '',\n              primarySetupType: trade.primarySetupType || '',\n              secondarySetupCategory: trade.secondarySetupCategory || '',\n              secondarySetupType: trade.secondarySetupType || '',\n              liquidityTaken: trade.liquidityTaken || '',\n              additionalFVGs: trade.additionalFVGs || [],\n              dolTargetType: trade.dolTargetType || '',\n              specificDOLType: trade.specificDOLType || '',\n              parentPDArray: trade.parentPDArray || '',\n\n              // Pattern Quality Assessment Fields\n              patternQualityClarity: trade.patternQualityScore?.criteria.clarity || '',\n              patternQualityConfluence: trade.patternQualityScore?.criteria.confluence || '',\n              patternQualityContext: trade.patternQualityScore?.criteria.context || '',\n              patternQualityRisk: trade.patternQualityScore?.criteria.risk || '',\n              patternQualityReward: trade.patternQualityScore?.criteria.reward || '',\n              patternQualityTimeframe: trade.patternQualityScore?.criteria.timeframe || '',\n              patternQualityVolume: trade.patternQualityScore?.criteria.volume || '',\n              patternQualityNotes: trade.patternQualityScore?.notes || '',\n\n              // DOL Analysis Fields\n              dolType: trade.dolAnalysis?.dolType || '',\n              dolStrength: trade.dolAnalysis?.dolStrength || '',\n              dolReaction: trade.dolAnalysis?.dolReaction || '',\n              dolContext: trade.dolAnalysis?.dolContext || [],\n              dolPriceAction: trade.dolAnalysis?.priceAction || '',\n              dolVolumeProfile: trade.dolAnalysis?.volumeProfile || '',\n              dolTimeOfDay: trade.dolAnalysis?.timeOfDay || '',\n              dolMarketStructure: trade.dolAnalysis?.marketStructure || '',\n              dolEffectiveness: trade.dolAnalysis?.effectiveness\n                ? String(trade.dolAnalysis.effectiveness)\n                : '5',\n              dolNotes: trade.dolAnalysis?.notes || '',\n\n              // Additional Information\n              notes: trade.notes || '',\n              tags: trade.tags || [],\n              result: trade.profitLoss > 0 ? 'win' : trade.profitLoss < 0 ? 'loss' : 'breakeven',\n            });\n          } else {\n            console.error(`Trade with ID ${tradeId} not found in IndexedDB`);\n            setError(`Trade with ID ${tradeId} not found.`);\n          }\n        } catch (err) {\n          console.error('Error loading trade:', err);\n          setError('Failed to load trade data. Please try again.');\n        } finally {\n          console.log(`Setting isLoading to false`);\n          setIsLoading(false);\n        }\n      }\n    };\n\n    loadTradeData();\n  }, [isEditMode, tradeId]);\n\n  /**\n   * Handle form field changes\n   * @param e The change event\n   */\n  const handleChange = useCallback(\n    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n      const { name, value } = e.target;\n      setFormValues((prev) => ({\n        ...prev,\n        [name]: value,\n      }));\n    },\n    []\n  );\n\n  return {\n    formValues,\n    setFormValues,\n    handleChange,\n    isLoading,\n    setIsLoading,\n    error,\n    setError,\n    success,\n    setSuccess,\n    tradeData,\n  };\n}\n\nexport type TradeFormDataHook = ReturnType<typeof useTradeFormData>;\n", "/**\n * Trade Validation Hook\n *\n * Custom hook for validating trade form data\n */\n\nimport { useState, useCallback } from 'react';\nimport { TradeFormValues } from '../types';\n\nexport interface ValidationErrors {\n  [key: string]: string;\n}\n\n/**\n * Hook for validating trade form data\n */\nexport function useTradeValidation() {\n  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});\n\n  /**\n   * Validate only the current tab\n   * @param formValues The form values to validate\n   * @param activeTab The active tab to validate\n   * @returns Whether the validation passed\n   */\n  const validateCurrentTab = useCallback(\n    (formValues: TradeFormValues, activeTab: string): boolean => {\n      const errors: ValidationErrors = {};\n\n      // Validate based on active tab\n      switch (activeTab) {\n        case 'basic':\n          // Basic Info tab - required fields\n          if (!formValues.date) errors.date = 'Date is required';\n          if (!formValues.symbol) errors.symbol = 'Symbol is required';\n          if (!formValues.entryPrice) errors.entryPrice = 'Entry price is required';\n          if (!formValues.exitPrice) errors.exitPrice = 'Exit price is required';\n          if (!formValues.quantity) errors.quantity = 'Quantity is required';\n          break;\n\n        case 'timing':\n          // Timing tab - validate only if fields are filled\n          if (formValues.entryTime && formValues.exitTime) {\n            if (formValues.exitTime < formValues.entryTime) {\n              errors.exitTime = 'Exit time must be after entry time';\n            }\n          }\n\n          if (formValues.rdTime && formValues.entryTime) {\n            if (formValues.entryTime < formValues.rdTime) {\n              errors.entryTime = 'Entry time must be after risk/decision time';\n            }\n          }\n          break;\n\n        case 'strategy':\n          // Strategy tab - validate only if fields are filled\n          if (formValues.primarySetupCategory && !formValues.primarySetupType) {\n            errors.primarySetupType = 'Please select a primary setup type';\n          }\n\n          if (formValues.secondarySetupCategory && !formValues.secondarySetupType) {\n            errors.secondarySetupType = 'Please select a secondary setup type';\n          }\n\n          // Validate primary and secondary setup types are different\n          if (\n            formValues.primarySetupType &&\n            formValues.secondarySetupType &&\n            formValues.primarySetupType === formValues.secondarySetupType\n          ) {\n            errors.secondarySetupType = 'Primary and secondary setup types must be different';\n          }\n          break;\n\n        case 'dol-analysis':\n          // DOL Analysis tab - validate only if fields are filled\n          if (formValues.dolType) {\n            if (!formValues.dolStrength) {\n              errors.dolStrength = 'Please select a DOL strength';\n            }\n\n            if (!formValues.dolReaction) {\n              errors.dolReaction = 'Please select a DOL reaction';\n            }\n\n            if (!formValues.dolContext || formValues.dolContext.length === 0) {\n              errors.dolContext = 'Please select at least one DOL context';\n            }\n          }\n\n          // Validate DOL target type and specific type\n          if (\n            formValues.dolTargetType &&\n            formValues.dolTargetType !== 'RD Target' &&\n            !formValues.specificDOLType\n          ) {\n            errors.specificDOLType = 'Please select a specific DOL type';\n          }\n          break;\n      }\n\n      // Update validation errors for current tab only\n      setValidationErrors((prev) => {\n        // Remove any previous errors for fields in the current tab\n        const newErrors = { ...prev };\n\n        // Add new errors for the current tab\n        Object.keys(errors).forEach((key) => {\n          newErrors[key] = errors[key];\n        });\n\n        return newErrors;\n      });\n\n      return Object.keys(errors).length === 0;\n    },\n    []\n  );\n\n  /**\n   * Validate only the Basic Info tab (required for submission)\n   * @param formValues The form values to validate\n   * @returns Whether the validation passed\n   */\n  const validateBasicInfoTab = useCallback((formValues: TradeFormValues): boolean => {\n    const errors: ValidationErrors = {};\n\n    // Required fields validation\n    if (!formValues.date) errors.date = 'Date is required';\n    if (!formValues.symbol) errors.symbol = 'Symbol is required';\n    if (!formValues.entryPrice) errors.entryPrice = 'Entry price is required';\n    if (!formValues.exitPrice) errors.exitPrice = 'Exit price is required';\n    if (!formValues.quantity) errors.quantity = 'Quantity is required';\n\n    // Update validation errors for basic info fields\n    setValidationErrors((prev) => ({\n      ...prev,\n      ...errors,\n    }));\n\n    return Object.keys(errors).length === 0;\n  }, []);\n\n  /**\n   * Clear validation errors for a specific field\n   * @param fieldName The field name to clear errors for\n   */\n  const clearFieldError = useCallback((fieldName: string) => {\n    setValidationErrors((prev) => {\n      const newErrors = { ...prev };\n      delete newErrors[fieldName];\n      return newErrors;\n    });\n  }, []);\n\n  /**\n   * Clear all validation errors\n   */\n  const clearAllErrors = useCallback(() => {\n    setValidationErrors({});\n  }, []);\n\n  return {\n    validationErrors,\n    validateCurrentTab,\n    validateBasicInfoTab,\n    clearFieldError,\n    clearAllErrors,\n    setValidationErrors,\n  };\n}\n\nexport type TradeValidationHook = ReturnType<typeof useTradeValidation>;\n", "/**\n * Trade Calculations Hook\n *\n * Custom hook for calculating trade metrics\n */\n\nimport { useEffect } from 'react';\nimport { TradeFormValues } from '../types';\n\n/**\n * Hook for calculating trade metrics\n * @param formValues The form values to calculate metrics for\n * @param setFormValues Function to update form values\n */\nexport function useTradeCalculations(\n  formValues: TradeFormValues,\n  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>\n) {\n  // Calculate R-Multiple when risk points and profit change\n  useEffect(() => {\n    if (formValues.riskPoints && formValues.profit) {\n      const riskPoints = parseFloat(formValues.riskPoints);\n      const profit = parseFloat(formValues.profit);\n\n      if (riskPoints > 0) {\n        const rMultiple = (profit / riskPoints).toFixed(2);\n        setFormValues((prev) => ({\n          ...prev,\n          rMultiple,\n        }));\n      }\n    }\n  }, [formValues.riskPoints, formValues.profit, setFormValues]);\n\n  // Validate time relationships\n  useEffect(() => {\n    // This effect doesn't update form values directly,\n    // but it's related to calculations and validation of time relationships\n    // The actual validation errors are handled in useTradeValidation\n  }, [formValues.entryTime, formValues.exitTime, formValues.rdTime]);\n\n  /**\n   * Calculate profit/loss based on entry price, exit price, and quantity\n   * This function can be called when those values change\n   */\n  const calculateProfitLoss = () => {\n    if (formValues.entryPrice && formValues.exitPrice && formValues.quantity) {\n      const entryPrice = parseFloat(formValues.entryPrice);\n      const exitPrice = parseFloat(formValues.exitPrice);\n      const quantity = parseFloat(formValues.quantity);\n      \n      if (!isNaN(entryPrice) && !isNaN(exitPrice) && !isNaN(quantity)) {\n        let profitLoss: number;\n        \n        if (formValues.direction === 'long') {\n          profitLoss = (exitPrice - entryPrice) * quantity;\n        } else {\n          profitLoss = (entryPrice - exitPrice) * quantity;\n        }\n        \n        setFormValues((prev) => ({\n          ...prev,\n          profit: profitLoss.toFixed(2),\n          result: profitLoss > 0 ? 'win' : profitLoss < 0 ? 'loss' : 'breakeven',\n        }));\n      }\n    }\n  };\n\n  return {\n    calculateProfitLoss,\n  };\n}\n\nexport type TradeCalculationsHook = ReturnType<typeof useTradeCalculations>;\n", "/**\n * Trade Submission Hook\n *\n * Custom hook for handling trade form submission\n */\n\nimport { useState, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { TradeFormValues, Trade } from '../types';\nimport { tradeStorage } from '../../../services/tradeStorage';\n\n/**\n * Hook for handling trade form submission\n * @param formValues The form values to submit\n * @param isEditMode Whether the form is in edit mode\n * @param isNewTrade Whether the form is for a new trade\n * @param tradeData The existing trade data (if editing)\n * @param validateBasicInfoTab Function to validate the basic info tab\n * @param validateCurrentTab Function to validate the current tab\n * @param activeTab The active tab\n * @param setActiveTab Function to set the active tab\n * @param setError Function to set the error message\n * @param setSuccess Function to set the success message\n */\nexport function useTradeSubmission(\n  formValues: TradeFormValues,\n  isEditMode: boolean,\n  isNewTrade: boolean,\n  tradeData: Trade | null,\n  validateBasicInfoTab: () => boolean,\n  validateCurrentTab: () => boolean,\n  activeTab: string,\n  setActiveTab: (tab: string) => void,\n  setError: (error: string | null) => void,\n  setSuccess: (success: string | null) => void\n) {\n  const navigate = useNavigate();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Handle form submission\n   * @param e The form event\n   */\n  const handleSubmit = useCallback(\n    async (e: React.FormEvent) => {\n      e.preventDefault();\n\n      // Only validate the Basic Info tab for submission\n      if (!validateBasicInfoTab()) {\n        setError('Please complete the required fields in the Basic Info tab.');\n        setActiveTab('basic'); // Switch to basic tab to show errors\n        return;\n      }\n\n      // Validate current tab if it's not the basic tab\n      if (activeTab !== 'basic' && !validateCurrentTab()) {\n        setError('Please fix the validation errors in the current tab before submitting.');\n        return;\n      }\n\n      setIsSubmitting(true);\n      setError(null);\n      setSuccess(null);\n\n      try {\n        // Prepare pattern quality score data if all criteria are filled\n        const patternQualityData = {\n          clarity: formValues.patternQualityClarity || '',\n          confluence: formValues.patternQualityConfluence || '',\n          context: formValues.patternQualityContext || '',\n          risk: formValues.patternQualityRisk || '',\n          reward: formValues.patternQualityReward || '',\n          timeframe: formValues.patternQualityTimeframe || '',\n          volume: formValues.patternQualityVolume || '',\n        };\n\n        // Check if all pattern quality criteria are filled\n        const allCriteriaFilled = Object.values(patternQualityData).every((value) => value !== '');\n\n        // Create a copy of form values for submission\n        const submissionValues = { ...formValues };\n\n        // Add pattern quality score data if all criteria are filled\n        if (allCriteriaFilled) {\n          // Import calculation functions\n          const { calculateTotalScore, convertScoreToRating } = await import(\n            '../constants/patternQuality'\n          );\n\n          // Calculate total score and rating\n          const totalScore = calculateTotalScore(patternQualityData);\n          const rating = convertScoreToRating(totalScore);\n\n          // Add pattern quality score data to submission values\n          submissionValues.patternQualityScore = {\n            total: totalScore,\n            rating,\n            criteria: patternQualityData,\n            notes: formValues.patternQualityNotes || '',\n          };\n        }\n\n        // Add DOL analysis data if type is selected\n        if (formValues.dolType) {\n          // Add DOL analysis data to submission values\n          submissionValues.dolAnalysis = {\n            dolType: formValues.dolType,\n            dolStrength: formValues.dolStrength || '',\n            dolReaction: formValues.dolReaction || '',\n            dolContext: formValues.dolContext || [],\n            priceAction: formValues.dolPriceAction || '',\n            volumeProfile: formValues.dolVolumeProfile || '',\n            timeOfDay: formValues.dolTimeOfDay || '',\n            marketStructure: formValues.dolMarketStructure || '',\n            effectiveness: formValues.dolEffectiveness ? parseInt(formValues.dolEffectiveness) : 5,\n            notes: formValues.dolNotes || '',\n          };\n        }\n\n        console.log('Form submitted:', submissionValues);\n\n        // Prepare trade data object\n        const updatedTradeData: Omit<Trade, 'id'> = {\n          // Basic Information\n          symbol: formValues.symbol,\n          date: formValues.date,\n          direction: formValues.direction === 'long' ? 'Long' : 'Short',\n          size: parseInt(formValues.quantity) || 0,\n\n          // Price Information\n          entry: parseFloat(formValues.entryPrice) || 0,\n          exit: parseFloat(formValues.exitPrice) || 0,\n          stopLoss: parseFloat(formValues.stopLoss || '0') || 0,\n          takeProfit: parseFloat(formValues.takeProfit || '0') || 0,\n          profitLoss: parseFloat(formValues.profit) || 0,\n\n          // Extended Trading Fields\n          modelType: formValues.modelType as any,\n          session: formValues.session as any,\n          setup: formValues.setup,\n          entryTime: formValues.entryTime,\n          exitTime: formValues.exitTime,\n          rdTime: formValues.rdTime,\n          market: formValues.market as any,\n          rMultiple: formValues.rMultiple ? parseFloat(formValues.rMultiple) : undefined,\n          entryVersion: formValues.entryVersion as any,\n          riskPoints: formValues.riskPoints ? parseFloat(formValues.riskPoints) : undefined,\n          patternQuality: formValues.patternQuality ? parseInt(formValues.patternQuality) : undefined,\n          patternQualityScore: submissionValues.patternQualityScore,\n          dolAnalysis: submissionValues.dolAnalysis,\n\n          // Setup Classification Fields\n          primarySetupCategory: formValues.primarySetupCategory as any,\n          primarySetupType: formValues.primarySetupType,\n          secondarySetupCategory: formValues.secondarySetupCategory as any,\n          secondarySetupType: formValues.secondarySetupType,\n          liquidityTaken: formValues.liquidityTaken as any,\n          additionalFVGs: formValues.additionalFVGs as any,\n          dolTargetType: formValues.dolTargetType as any,\n          specificDOLType: formValues.specificDOLType,\n          parentPDArray: formValues.parentPDArray as any,\n\n          // Additional Information\n          strategy: formValues.setup || 'Unknown',\n          notes: formValues.notes,\n          tags: formValues.tags || [],\n        };\n\n        let savedTrade: Trade;\n\n        // If editing an existing trade, include the ID\n        if (isEditMode && tradeData) {\n          savedTrade = await tradeStorage.saveTrade({\n            ...updatedTradeData,\n            id: tradeData.id, // Preserve the original ID\n          });\n          console.log('Trade updated in IndexedDB successfully', savedTrade);\n        } else {\n          // Creating a new trade\n          savedTrade = await tradeStorage.saveTrade(updatedTradeData);\n          console.log('New trade saved to IndexedDB successfully', savedTrade);\n        }\n\n        // Set success message based on whether we're editing or creating\n        if (isEditMode) {\n          setSuccess(`Trade for ${savedTrade.symbol} on ${savedTrade.date} updated successfully!`);\n        } else {\n          setSuccess(\n            `New trade for ${savedTrade.symbol} on ${savedTrade.date} created successfully!`\n          );\n        }\n\n        // Wait a moment to show the success message before navigating\n        setTimeout(() => {\n          // Navigate back to journal page after successful submission\n          console.log('Navigating back to journal page after successful submission');\n          navigate('/journal');\n        }, 1500);\n      } catch (err) {\n        setError('Failed to save trade. Please try again.');\n        console.error('Error submitting form:', err);\n      } finally {\n        setIsSubmitting(false);\n      }\n    },\n    [\n      formValues,\n      validateBasicInfoTab,\n      validateCurrentTab,\n      activeTab,\n      setActiveTab,\n      setError,\n      setSuccess,\n      isEditMode,\n      tradeData,\n      navigate,\n    ]\n  );\n\n  return {\n    handleSubmit,\n    isSubmitting,\n  };\n}\n\nexport type TradeSubmissionHook = ReturnType<typeof useTradeSubmission>;\n", "/**\n * Trade Form Hook\n *\n * Custom hook for managing trade form state and logic\n */\n\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useTradeFormData } from './useTradeFormData';\nimport { useTradeValidation } from './useTradeValidation';\nimport { useTradeCalculations } from './useTradeCalculations';\nimport { useTradeSubmission } from './useTradeSubmission';\n\n// Options for dropdowns\nexport const MODEL_TYPE_OPTIONS = [\n  { value: 'Price Action', label: 'Price Action' },\n  { value: 'Momentum', label: 'Momentum' },\n  { value: 'Trend Following', label: 'Trend Following' },\n  { value: 'Mean Reversion', label: 'Mean Reversion' },\n  { value: 'Breakout', label: 'Breakout' },\n  { value: 'Support/Resistance', label: 'Support/Resistance' },\n  { value: 'Other', label: 'Other' },\n];\n\nexport const SESSION_OPTIONS = [\n  { value: 'Pre-Market', label: 'Pre-Market' },\n  { value: 'Regular Hours', label: 'Regular Hours' },\n  { value: 'Power Hour', label: 'Power Hour' },\n  { value: 'After Hours', label: 'After Hours' },\n  { value: 'Overnight', label: 'Overnight' },\n];\n\nexport const SETUP_OPTIONS = [\n  { value: 'Breakout', label: 'Breakout' },\n  { value: 'Pullback', label: 'Pullback' },\n  { value: 'Reversal', label: 'Reversal' },\n  { value: 'Trend Continuation', label: 'Trend Continuation' },\n  { value: 'Gap Fill', label: 'Gap Fill' },\n  { value: 'VWAP Bounce', label: 'VWAP Bounce' },\n  { value: 'Support/Resistance', label: 'Support/Resistance' },\n  { value: 'Other', label: 'Other' },\n];\n\nexport const MARKET_OPTIONS = [\n  { value: 'Stocks', label: 'Stocks' },\n  { value: 'Options', label: 'Options' },\n  { value: 'Futures', label: 'Futures' },\n  { value: 'Forex', label: 'Forex' },\n  { value: 'Crypto', label: 'Crypto' },\n  { value: 'Other', label: 'Other' },\n];\n\nexport const ENTRY_VERSION_OPTIONS = [\n  { value: 'First Entry', label: 'First Entry' },\n  { value: 'Re-Entry', label: 'Re-Entry' },\n  { value: 'Scale In', label: 'Scale In' },\n  { value: 'Averaging Down', label: 'Averaging Down' },\n  { value: 'Averaging Up', label: 'Averaging Up' },\n];\n\nexport const PATTERN_QUALITY_OPTIONS = Array.from({ length: 10 }, (_, i) => ({\n  value: String(i + 1),\n  label: String(i + 1),\n}));\n\n/**\n * Main hook for managing trade form state and logic\n * @param tradeId The ID of the trade to load (optional)\n */\nexport const useTradeForm = (tradeId?: string) => {\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState<string>('basic');\n\n  // Enhanced debugging for tradeId parameter and hash-based routing\n  console.log(`useTradeForm hook initialized with tradeId: \"${tradeId}\"`);\n\n  // For hash-based routing, check the hash part of the URL\n  const currentPath = window.location.hash.substring(1); // Remove the leading #\n  console.log(`Current hash path in useTradeForm: ${currentPath}`);\n\n  // Extract trade ID from URL if not provided directly\n  let effectiveTradeId = tradeId;\n  if (!effectiveTradeId && currentPath.includes('/trade/edit/')) {\n    // Extract ID from URL path for edit mode\n    const matches = currentPath.match(/\\/trade\\/edit\\/([^\\/]+)/);\n    if (matches && matches[1]) {\n      effectiveTradeId = matches[1];\n      console.log(`Extracted trade ID from URL: ${effectiveTradeId}`);\n    }\n  }\n\n  // Determine if we're creating a new trade or editing an existing one\n  const isNewTrade = effectiveTradeId === 'new' || currentPath.includes('/trade/new');\n  const isEditMode =\n    (effectiveTradeId && effectiveTradeId !== 'new') ||\n    (currentPath.includes('/trade/edit/') && !currentPath.includes('/trade/edit/new'));\n\n  console.log(`useTradeForm - isNewTrade: ${isNewTrade}, isEditMode: ${isEditMode}`);\n\n  // Use the form data hook to manage form state\n  const {\n    formValues,\n    setFormValues,\n    handleChange,\n    isLoading,\n    error,\n    setError,\n    success,\n    setSuccess,\n    tradeData,\n  } = useTradeFormData(effectiveTradeId, isEditMode, isNewTrade);\n\n  // Use the validation hook to validate form data\n  const {\n    validationErrors,\n    validateCurrentTab: validateTab,\n    validateBasicInfoTab,\n  } = useTradeValidation();\n\n  // Use the calculations hook to calculate trade metrics\n  const { calculateProfitLoss } = useTradeCalculations(formValues, setFormValues);\n\n  // Wrapper function for validateCurrentTab to pass the current form values and active tab\n  const validateCurrentTab = () => validateTab(formValues, activeTab);\n\n  // Wrapper function for validateBasicInfoTab to pass the current form values\n  const validateBasicInfo = () => validateBasicInfoTab(formValues);\n\n  // Use the submission hook to handle form submission\n  const { handleSubmit, isSubmitting } = useTradeSubmission(\n    formValues,\n    isEditMode,\n    isNewTrade,\n    tradeData,\n    validateBasicInfo,\n    validateCurrentTab,\n    activeTab,\n    setActiveTab,\n    setError,\n    setSuccess\n  );\n\n  // Handle tab change\n  const handleTabChange = (newTab: string) => {\n    console.log(`Tab change requested from ${activeTab} to ${newTab}`); // Debug logging\n\n    // Clear any success message when changing tabs\n    // This prevents the form from appearing to be submitted when just changing tabs\n    if (success) {\n      console.log('Clearing success message during tab change'); // Debug logging\n      setSuccess(null);\n    }\n\n    // Clear any error message when changing tabs\n    if (error) {\n      console.log('Clearing error message during tab change'); // Debug logging\n      setError(null);\n    }\n\n    // Optionally validate current tab before switching\n    // But allow switching regardless of validation state\n    if (activeTab) {\n      console.log(`Validating current tab (${activeTab}) before switching`); // Debug logging\n      validateCurrentTab();\n    }\n\n    // Update active tab\n    console.log(`Setting active tab to: ${newTab}`); // Debug logging\n    setActiveTab(newTab);\n  };\n\n  return {\n    formValues,\n    setFormValues,\n    handleChange,\n    handleSubmit,\n    isSubmitting,\n    isLoading,\n    error,\n    success,\n    validationErrors,\n    isNewTrade,\n    activeTab,\n    handleTabChange,\n    calculateProfitLoss,\n  };\n};\n", "/**\n * TabPanel Component\n *\n * A reusable tab panel component for progressive disclosure UI.\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\n\ninterface TabPanelProps {\n  tabs: {\n    id: string;\n    label: string;\n    content: React.ReactNode;\n  }[];\n  defaultTab?: string;\n  className?: string;\n  activeTab?: string;\n  onTabClick?: (tabId: string) => void;\n}\n\nconst TabContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n`;\n\nconst TabList = styled.div`\n  display: flex;\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst TabButton = styled.button<{ active: boolean }>`\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};\n  background: none;\n  border: none;\n  border-bottom: 2px solid ${({ active, theme }) => (active ? theme.colors.primary : 'transparent')};\n  color: ${({ active, theme }) => (active ? theme.colors.primary : theme.colors.textSecondary)};\n  font-weight: ${({ active, theme }) =>\n    active ? theme.fontWeights.semibold : theme.fontWeights.regular};\n  cursor: pointer;\n  transition: all ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.primary};\n  }\n\n  &:focus {\n    outline: none;\n    color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\nconst TabContent = styled.div`\n  padding: ${({ theme }) => theme.spacing.sm} 0;\n`;\n\n/**\n * TabPanel Component\n *\n * A reusable tab panel component for progressive disclosure UI.\n * Can be used in controlled or uncontrolled mode.\n */\nconst TabPanel: React.FC<TabPanelProps> = ({\n  tabs,\n  defaultTab,\n  className,\n  activeTab: controlledActiveTab,\n  onTabClick,\n}) => {\n  // Internal state for uncontrolled mode\n  const [internalActiveTab, setInternalActiveTab] = useState(defaultTab || tabs[0].id);\n\n  // Use controlled or uncontrolled active tab\n  const activeTab = controlledActiveTab !== undefined ? controlledActiveTab : internalActiveTab;\n\n  const handleTabChange = (e: React.MouseEvent, tabId: string) => {\n    // Prevent the default action (form submission)\n    e.preventDefault();\n    e.stopPropagation(); // Also stop propagation to prevent bubbling\n\n    console.log(`Tab clicked: ${tabId}`); // Debug logging\n\n    // If onTabClick is provided, call it (controlled mode)\n    if (onTabClick) {\n      onTabClick(tabId);\n      console.log(`Called onTabClick with: ${tabId}`); // Debug logging\n    } else {\n      // Otherwise update internal state (uncontrolled mode)\n      setInternalActiveTab(tabId);\n      console.log(`Updated internal tab state to: ${tabId}`); // Debug logging\n    }\n  };\n\n  return (\n    <TabContainer className={className}>\n      <TabList>\n        {tabs.map((tab) => (\n          <TabButton\n            key={tab.id}\n            active={activeTab === tab.id}\n            onClick={(e) => handleTabChange(e, tab.id)}\n            type=\"button\" // Explicitly set type to button to prevent form submission\n            form=\"\" // Disconnect from any form to prevent submission\n            tabIndex={0} // Ensure it's focusable\n            data-tab-id={tab.id} // Add data attribute for easier debugging\n          >\n            {tab.label}\n          </TabButton>\n        ))}\n      </TabList>\n      <TabContent>{tabs.find((tab) => tab.id === activeTab)?.content}</TabContent>\n    </TabContainer>\n  );\n};\n\nexport default TabPanel;\n", "/**\n * Pattern Quality Assessment Constants\n * \n * Constants for the pattern quality assessment feature\n */\n\nimport { ScoreRange } from '../types';\n\n/**\n * Score values for each score range\n */\nexport const SCORE_VALUES: Record<ScoreRange, number> = {\n  excellent: 5,\n  good: 4,\n  average: 3,\n  poor: 2,\n  unacceptable: 1,\n};\n\n/**\n * Score range options for dropdowns\n */\nexport const SCORE_RANGE_OPTIONS = [\n  { value: 'excellent', label: 'Excellent (5)' },\n  { value: 'good', label: 'Good (4)' },\n  { value: 'average', label: 'Average (3)' },\n  { value: 'poor', label: 'Poor (2)' },\n  { value: 'unacceptable', label: 'Unacceptable (1)' },\n];\n\n/**\n * Pattern Quality Criteria Definitions\n */\nexport const PATTERN_QUALITY_CRITERIA = {\n  clarity: {\n    title: 'Pattern Clarity',\n    description: 'How clear and well-defined is the pattern?',\n    excellent: 'Pattern is extremely clear with perfect formation',\n    good: 'Pattern is clear with minor imperfections',\n    average: 'Pattern is recognizable but has some ambiguity',\n    poor: '<PERSON>tern is difficult to recognize with significant ambiguity',\n    unacceptable: 'Pattern is barely recognizable or completely ambiguous',\n  },\n  confluence: {\n    title: 'Confluence Factors',\n    description: 'How many supporting factors align with this pattern?',\n    excellent: 'Multiple strong confluence factors (5+ factors)',\n    good: 'Several good confluence factors (3-4 factors)',\n    average: 'Some confluence factors (2-3 factors)',\n    poor: 'Minimal confluence (1-2 weak factors)',\n    unacceptable: 'No confluence factors',\n  },\n  context: {\n    title: 'Market Context',\n    description: 'How well does the pattern fit within the broader market context?',\n    excellent: 'Perfect alignment with market structure and conditions',\n    good: 'Good alignment with market structure and conditions',\n    average: 'Reasonable alignment with some contradicting factors',\n    poor: 'Poor alignment with several contradicting factors',\n    unacceptable: 'Pattern contradicts the broader market context',\n  },\n  risk: {\n    title: 'Risk Profile',\n    description: 'How well-defined and manageable is the risk?',\n    excellent: 'Extremely clear stop location with minimal risk',\n    good: 'Clear stop location with reasonable risk',\n    average: 'Identifiable stop location but with moderate risk',\n    poor: 'Unclear stop location or high risk',\n    unacceptable: 'No clear stop location or extremely high risk',\n  },\n  reward: {\n    title: 'Reward Potential',\n    description: 'What is the potential reward relative to risk?',\n    excellent: 'Exceptional reward potential (5R+)',\n    good: 'Strong reward potential (3-5R)',\n    average: 'Reasonable reward potential (2-3R)',\n    poor: 'Limited reward potential (1-2R)',\n    unacceptable: 'Poor reward potential (<1R)',\n  },\n  timeframe: {\n    title: 'Timeframe Alignment',\n    description: 'How well does the pattern align across multiple timeframes?',\n    excellent: 'Strong alignment across all relevant timeframes',\n    good: 'Good alignment across most timeframes',\n    average: 'Alignment on primary timeframe with some higher/lower support',\n    poor: 'Limited alignment across timeframes',\n    unacceptable: 'Pattern only appears on a single timeframe with contradictions on others',\n  },\n  volume: {\n    title: 'Volume Profile',\n    description: 'How does volume support the pattern?',\n    excellent: 'Volume perfectly confirms the pattern',\n    good: 'Volume generally supports the pattern',\n    average: 'Volume is neutral or mixed',\n    poor: 'Volume somewhat contradicts the pattern',\n    unacceptable: 'Volume strongly contradicts the pattern',\n  },\n};\n\n/**\n * Calculate total score from criteria\n */\nexport const calculateTotalScore = (criteria: Record<string, ScoreRange>): number => {\n  return Object.values(criteria).reduce((total, score) => {\n    return total + (SCORE_VALUES[score] || 0);\n  }, 0);\n};\n\n/**\n * Convert total score to 1-10 rating\n */\nexport const convertScoreToRating = (totalScore: number): number => {\n  // Maximum possible score is 35 (7 criteria * 5 points)\n  // Convert to a 1-10 scale\n  const maxPossibleScore = Object.keys(PATTERN_QUALITY_CRITERIA).length * 5;\n  const rating = Math.round((totalScore / maxPossibleScore) * 10);\n  \n  // Ensure rating is between 1 and 10\n  return Math.max(1, Math.min(10, rating));\n};\n\n/**\n * Get rating description based on rating value\n */\nexport const getRatingDescription = (rating: number): string => {\n  if (rating >= 9) return 'Exceptional';\n  if (rating >= 8) return 'Excellent';\n  if (rating >= 7) return 'Very Good';\n  if (rating >= 6) return 'Good';\n  if (rating >= 5) return 'Average';\n  if (rating >= 4) return 'Below Average';\n  if (rating >= 3) return 'Poor';\n  if (rating >= 2) return 'Very Poor';\n  return 'Unacceptable';\n};\n\n/**\n * Get color for rating\n */\nexport const getRatingColor = (rating: number): string => {\n  if (rating >= 8) return '#4CAF50'; // Green\n  if (rating >= 6) return '#8BC34A'; // Light Green\n  if (rating >= 5) return '#FFC107'; // Amber\n  if (rating >= 3) return '#FF9800'; // Orange\n  return '#F44336'; // Red\n};\n", "/**\n * Criterion Selector Component\n * \n * Component for selecting a score for a specific pattern quality criterion\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { ScoreRange } from '../../types';\nimport { SCORE_RANGE_OPTIONS, PATTERN_QUALITY_CRITERIA } from '../../constants/patternQuality';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst CriterionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst CriterionDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface CriterionSelectorProps {\n  criterion: keyof typeof PATTERN_QUALITY_CRITERIA;\n  value: ScoreRange | '';\n  onChange: (field: string, value: string) => void;\n  fieldName: string;\n}\n\nconst CriterionSelector: React.FC<CriterionSelectorProps> = ({ \n  criterion, \n  value, \n  onChange,\n  fieldName\n}) => {\n  const criterionData = PATTERN_QUALITY_CRITERIA[criterion];\n\n  // Handle score change\n  const handleScoreChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange(fieldName, e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <CriterionTitle>{criterionData.title}</CriterionTitle>\n      <CriterionDescription>{criterionData.description}</CriterionDescription>\n      \n      <RadioGroup>\n        {SCORE_RANGE_OPTIONS.map((option) => (\n          <RadioOption key={option.value}>\n            <RadioInput\n              type=\"radio\"\n              id={`${fieldName}_${option.value}`}\n              name={fieldName}\n              value={option.value}\n              checked={value === option.value}\n              onChange={handleScoreChange}\n            />\n            <RadioLabelContainer>\n              <RadioLabel htmlFor={`${fieldName}_${option.value}`}>\n                {option.label}\n              </RadioLabel>\n              <RadioDescription>\n                {criterionData[option.value as ScoreRange]}\n              </RadioDescription>\n            </RadioLabelContainer>\n          </RadioOption>\n        ))}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default CriterionSelector;\n", "/**\n * Pattern Quality Assessment Component\n *\n * Main component for the pattern quality assessment section\n */\n\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport CriterionSelector from './CriterionSelector';\nimport {\n  PATTERN_QUALITY_CRITERIA,\n  calculateTotalScore,\n  convertScoreToRating,\n  getRatingDescription,\n  getRatingColor,\n} from '../../constants/patternQuality';\nimport { ScoreRange } from '../../types';\n\nconst AssessmentContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Introduction = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst IntroTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst IntroText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0;\n  line-height: 1.5;\n`;\n\nconst Divider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst ScoreSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  padding: ${({ theme }) => theme.spacing.lg};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst ScoreTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst ScoreDetails = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.xl};\n`;\n\nconst ScoreValue = styled.div<{ color: string }>`\n  font-size: 3rem;\n  font-weight: 700;\n  color: ${({ color }) => color};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  border: 4px solid ${({ color }) => color};\n`;\n\nconst ScoreInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst ScoreDescription = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst ScoreBreakdown = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst NotesSection = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst NotesLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  display: block;\n  margin-bottom: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst NotesTextarea = styled.textarea`\n  width: 100%;\n  min-height: 100px;\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.background};\n  resize: vertical;\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\ninterface PatternQualityAssessmentProps {\n  formValues: {\n    patternQualityClarity?: string;\n    patternQualityConfluence?: string;\n    patternQualityContext?: string;\n    patternQualityRisk?: string;\n    patternQualityReward?: string;\n    patternQualityTimeframe?: string;\n    patternQualityVolume?: string;\n    patternQualityNotes?: string;\n    patternQuality?: string;\n  };\n  onChange: (field: string, value: string) => void;\n}\n\nconst PatternQualityAssessment: React.FC<PatternQualityAssessmentProps> = ({\n  formValues,\n  onChange,\n}) => {\n  const [totalScore, setTotalScore] = useState(0);\n  const [rating, setRating] = useState(0);\n\n  // Calculate score and rating when criteria change\n  useEffect(() => {\n    const criteria: Record<string, ScoreRange> = {\n      clarity: (formValues.patternQualityClarity || '') as ScoreRange,\n      confluence: (formValues.patternQualityConfluence || '') as ScoreRange,\n      context: (formValues.patternQualityContext || '') as ScoreRange,\n      risk: (formValues.patternQualityRisk || '') as ScoreRange,\n      reward: (formValues.patternQualityReward || '') as ScoreRange,\n      timeframe: (formValues.patternQualityTimeframe || '') as ScoreRange,\n      volume: (formValues.patternQualityVolume || '') as ScoreRange,\n    };\n\n    // Only calculate if all criteria are filled\n    const allCriteriaFilled = Object.values(criteria).every((value) => value !== '');\n\n    if (allCriteriaFilled) {\n      const score = calculateTotalScore(criteria);\n      const calculatedRating = convertScoreToRating(score);\n\n      setTotalScore(score);\n      setRating(calculatedRating);\n\n      // Update the patternQuality field with the calculated rating\n      onChange('patternQuality', calculatedRating.toString());\n    }\n  }, [\n    formValues.patternQualityClarity,\n    formValues.patternQualityConfluence,\n    formValues.patternQualityContext,\n    formValues.patternQualityRisk,\n    formValues.patternQualityReward,\n    formValues.patternQualityTimeframe,\n    formValues.patternQualityVolume,\n    onChange,\n  ]);\n\n  // Handle notes change\n  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange('patternQualityNotes', e.target.value);\n  };\n\n  return (\n    <AssessmentContainer>\n      <Introduction>\n        <IntroTitle>Pattern Quality Assessment</IntroTitle>\n        <IntroText>\n          Evaluate the quality of your trade setup by rating each criterion below. This assessment\n          will help you objectively analyze your trade patterns and improve your decision-making\n          process.\n        </IntroText>\n      </Introduction>\n\n      <Divider />\n\n      {/* Criterion Selectors */}\n      <CriterionSelector\n        criterion=\"clarity\"\n        value={formValues.patternQualityClarity || ''}\n        onChange={onChange}\n        fieldName=\"patternQualityClarity\"\n      />\n\n      <CriterionSelector\n        criterion=\"confluence\"\n        value={formValues.patternQualityConfluence || ''}\n        onChange={onChange}\n        fieldName=\"patternQualityConfluence\"\n      />\n\n      <CriterionSelector\n        criterion=\"context\"\n        value={formValues.patternQualityContext || ''}\n        onChange={onChange}\n        fieldName=\"patternQualityContext\"\n      />\n\n      <CriterionSelector\n        criterion=\"risk\"\n        value={formValues.patternQualityRisk || ''}\n        onChange={onChange}\n        fieldName=\"patternQualityRisk\"\n      />\n\n      <CriterionSelector\n        criterion=\"reward\"\n        value={formValues.patternQualityReward || ''}\n        onChange={onChange}\n        fieldName=\"patternQualityReward\"\n      />\n\n      <CriterionSelector\n        criterion=\"timeframe\"\n        value={formValues.patternQualityTimeframe || ''}\n        onChange={onChange}\n        fieldName=\"patternQualityTimeframe\"\n      />\n\n      <CriterionSelector\n        criterion=\"volume\"\n        value={formValues.patternQualityVolume || ''}\n        onChange={onChange}\n        fieldName=\"patternQualityVolume\"\n      />\n\n      {/* Score Display */}\n      {rating > 0 && (\n        <ScoreSection>\n          <ScoreTitle>Pattern Quality Score</ScoreTitle>\n          <ScoreDetails>\n            <ScoreValue color={getRatingColor(rating)}>{rating}</ScoreValue>\n            <ScoreInfo>\n              <ScoreDescription>{getRatingDescription(rating)}</ScoreDescription>\n              <ScoreBreakdown>\n                Total Score: {totalScore} out of {Object.keys(PATTERN_QUALITY_CRITERIA).length * 5}\n              </ScoreBreakdown>\n            </ScoreInfo>\n          </ScoreDetails>\n        </ScoreSection>\n      )}\n\n      {/* Notes Section */}\n      <NotesSection>\n        <NotesLabel htmlFor=\"patternQualityNotes\">Additional Notes</NotesLabel>\n        <NotesTextarea\n          id=\"patternQualityNotes\"\n          name=\"patternQualityNotes\"\n          value={formValues.patternQualityNotes || ''}\n          onChange={handleNotesChange}\n          placeholder=\"Add any additional notes about the pattern quality...\"\n        />\n      </NotesSection>\n    </AssessmentContainer>\n  );\n};\n\nexport default PatternQualityAssessment;\n", "/**\n * DOL (Draw on Liquidity) Analysis Constants\n * \n * Constants for the DOL analysis feature\n */\n\nimport { DOLType, DOLStrength, DOLReaction, DOLContext } from '../types';\n\n/**\n * DOL Type Options\n */\nexport const DOL_TYPE_OPTIONS = [\n  { value: 'Sweep', label: 'Sweep - Price moves through the liquidity level' },\n  { value: 'Tap', label: 'Tap - Price touches the liquidity level exactly' },\n  { value: 'Approach', label: 'Approach - Price approaches but doesn\\'t quite reach the level' },\n  { value: 'Rejection', label: 'Rejection - Price rejects from the liquidity level' },\n];\n\n/**\n * DOL Strength Options\n */\nexport const DOL_STRENGTH_OPTIONS = [\n  { value: 'Strong', label: 'Strong - Significant price movement with high volume' },\n  { value: 'Moderate', label: 'Moderate - Noticeable price movement with average volume' },\n  { value: 'Weak', label: 'Weak - Minimal price movement with low volume' },\n];\n\n/**\n * DOL Reaction Options\n */\nexport const DOL_REACTION_OPTIONS = [\n  { value: 'Immediate Reversal', label: 'Immediate Reversal - Price reverses direction immediately' },\n  { value: 'Delayed Reversal', label: 'Delayed Reversal - Price reverses after some consolidation' },\n  { value: 'Consolidation', label: 'Consolidation - Price consolidates at the level' },\n  { value: 'Continuation', label: 'Continuation - Price continues in the same direction' },\n];\n\n/**\n * DOL Context Options\n */\nexport const DOL_CONTEXT_OPTIONS = [\n  { value: 'High Volume Node', label: 'High Volume Node' },\n  { value: 'Low Volume Node', label: 'Low Volume Node' },\n  { value: 'VPOC', label: 'Volume Point of Control (VPOC)' },\n  { value: 'VAH/VAL', label: 'Value Area High/Low' },\n  { value: 'Previous Day High/Low', label: 'Previous Day High/Low' },\n  { value: 'Previous Week High/Low', label: 'Previous Week High/Low' },\n  { value: 'Previous Month High/Low', label: 'Previous Month High/Low' },\n  { value: 'Round Number', label: 'Round Number (00, 50, etc.)' },\n  { value: 'Technical Level', label: 'Technical Level (Support/Resistance)' },\n  { value: 'News Event', label: 'News Event' },\n  { value: 'Other', label: 'Other' },\n];\n\n/**\n * DOL Effectiveness Rating Options\n */\nexport const DOL_EFFECTIVENESS_OPTIONS = Array.from({ length: 10 }, (_, i) => ({\n  value: String(i + 1),\n  label: String(i + 1),\n}));\n\n/**\n * DOL Price Action Descriptions\n */\nexport const DOL_PRICE_ACTION_DESCRIPTIONS = {\n  'Sweep': 'Describe how price moved through the liquidity level. Was it a clean sweep or did it struggle?',\n  'Tap': 'Describe how price interacted with the liquidity level. Was it a precise tap or did it linger?',\n  'Approach': 'Describe how price approached the liquidity level. How close did it get and why did it stop?',\n  'Rejection': 'Describe how price rejected from the liquidity level. Was it a sharp rejection or gradual?',\n};\n\n/**\n * DOL Volume Profile Descriptions\n */\nexport const DOL_VOLUME_PROFILE_DESCRIPTIONS = {\n  'Strong': 'Describe the volume profile during the liquidity interaction. Was there a volume spike?',\n  'Moderate': 'Describe the volume profile during the liquidity interaction. Was volume consistent?',\n  'Weak': 'Describe the volume profile during the liquidity interaction. Why was volume low?',\n};\n\n/**\n * DOL Time of Day Significance\n */\nexport const DOL_TIME_OF_DAY_DESCRIPTION = \n  'Describe the significance of the time of day for this liquidity interaction. ' +\n  'Was it during a key market session or near a session transition?';\n\n/**\n * DOL Market Structure Description\n */\nexport const DOL_MARKET_STRUCTURE_DESCRIPTION = \n  'Describe the market structure context for this liquidity interaction. ' +\n  'Was price in an uptrend, downtrend, or range? Were there any key levels nearby?';\n\n/**\n * Get description for DOL type\n */\nexport const getDOLTypeDescription = (dolType: string): string => {\n  const option = DOL_TYPE_OPTIONS.find(option => option.value === dolType);\n  return option ? option.label.split(' - ')[1] : '';\n};\n\n/**\n * Get description for DOL strength\n */\nexport const getDOLStrengthDescription = (dolStrength: string): string => {\n  const option = DOL_STRENGTH_OPTIONS.find(option => option.value === dolStrength);\n  return option ? option.label.split(' - ')[1] : '';\n};\n\n/**\n * Get description for DOL reaction\n */\nexport const getDOLReactionDescription = (dolReaction: string): string => {\n  const option = DOL_REACTION_OPTIONS.find(option => option.value === dolReaction);\n  return option ? option.label.split(' - ')[1] : '';\n};\n\n/**\n * Get color for DOL effectiveness rating\n */\nexport const getDOLEffectivenessColor = (rating: number): string => {\n  if (rating >= 8) return '#4CAF50'; // Green\n  if (rating >= 6) return '#8BC34A'; // Light Green\n  if (rating >= 5) return '#FFC107'; // Amber\n  if (rating >= 3) return '#FF9800'; // Orange\n  return '#F44336'; // Red\n};\n\n/**\n * Get description for DOL effectiveness rating\n */\nexport const getDOLEffectivenessDescription = (rating: number): string => {\n  if (rating >= 9) return 'Exceptional';\n  if (rating >= 8) return 'Excellent';\n  if (rating >= 7) return 'Very Good';\n  if (rating >= 6) return 'Good';\n  if (rating >= 5) return 'Average';\n  if (rating >= 4) return 'Below Average';\n  if (rating >= 3) return 'Poor';\n  if (rating >= 2) return 'Very Poor';\n  return 'Ineffective';\n};\n", "/**\n * DOL Type Selector Component\n * \n * Component for selecting the DOL type\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_TYPE_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface DOLTypeSelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLTypeSelector: React.FC<DOLTypeSelectorProps> = ({ value, onChange }) => {\n  // Handle DOL type change\n  const handleDOLTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolType', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Type</SectionTitle>\n      <Description>\n        Select the type of liquidity interaction that occurred in this trade.\n      </Description>\n      \n      <RadioGroup>\n        {DOL_TYPE_OPTIONS.map((option) => {\n          const [label, description] = option.label.split(' - ');\n          \n          return (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`dolType_${option.value}`}\n                name=\"dolType\"\n                value={option.value}\n                checked={value === option.value}\n                onChange={handleDOLTypeChange}\n              />\n              <RadioLabelContainer>\n                <RadioLabel htmlFor={`dolType_${option.value}`}>\n                  {label}\n                </RadioLabel>\n                <RadioDescription>\n                  {description}\n                </RadioDescription>\n              </RadioLabelContainer>\n            </RadioOption>\n          );\n        })}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLTypeSelector;\n", "/**\n * DOL Strength Selector Component\n * \n * Component for selecting the DOL strength\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_STRENGTH_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface DOLStrengthSelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLStrengthSelector: React.FC<DOLStrengthSelectorProps> = ({ value, onChange }) => {\n  // Handle DOL strength change\n  const handleDOLStrengthChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolStrength', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Strength</SectionTitle>\n      <Description>\n        Evaluate the strength of the liquidity interaction.\n      </Description>\n      \n      <RadioGroup>\n        {DOL_STRENGTH_OPTIONS.map((option) => {\n          const [label, description] = option.label.split(' - ');\n          \n          return (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`dolStrength_${option.value}`}\n                name=\"dolStrength\"\n                value={option.value}\n                checked={value === option.value}\n                onChange={handleDOLStrengthChange}\n              />\n              <RadioLabelContainer>\n                <RadioLabel htmlFor={`dolStrength_${option.value}`}>\n                  {label}\n                </RadioLabel>\n                <RadioDescription>\n                  {description}\n                </RadioDescription>\n              </RadioLabelContainer>\n            </RadioOption>\n          );\n        })}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLStrengthSelector;\n", "/**\n * DOL Reaction Selector Component\n * \n * Component for selecting the DOL reaction\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_REACTION_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface DOLReactionSelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLReactionSelector: React.FC<DOLReactionSelectorProps> = ({ value, onChange }) => {\n  // Handle DOL reaction change\n  const handleDOLReactionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolReaction', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Reaction</SectionTitle>\n      <Description>\n        Describe how price reacted after the liquidity interaction.\n      </Description>\n      \n      <RadioGroup>\n        {DOL_REACTION_OPTIONS.map((option) => {\n          const [label, description] = option.label.split(' - ');\n          \n          return (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`dolReaction_${option.value}`}\n                name=\"dolReaction\"\n                value={option.value}\n                checked={value === option.value}\n                onChange={handleDOLReactionChange}\n              />\n              <RadioLabelContainer>\n                <RadioLabel htmlFor={`dolReaction_${option.value}`}>\n                  {label}\n                </RadioLabel>\n                <RadioDescription>\n                  {description}\n                </RadioDescription>\n              </RadioLabelContainer>\n            </RadioOption>\n          );\n        })}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLReactionSelector;\n", "/**\n * DOL Context Selector Component\n * \n * Component for selecting the DOL context\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_CONTEXT_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst CheckboxGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CheckboxOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst CheckboxInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CheckboxLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\ninterface DOLContextSelectorProps {\n  value: string[];\n  onChange: (field: string, value: string[]) => void;\n}\n\nconst DOLContextSelector: React.FC<DOLContextSelectorProps> = ({ value, onChange }) => {\n  // Handle checkbox change\n  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const contextValue = e.target.value;\n    const isChecked = e.target.checked;\n    \n    let newValue: string[];\n    \n    if (isChecked) {\n      // Add to array if checked\n      newValue = [...value, contextValue];\n    } else {\n      // Remove from array if unchecked\n      newValue = value.filter(item => item !== contextValue);\n    }\n    \n    onChange('dolContext', newValue);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Context</SectionTitle>\n      <Description>\n        Select all contextual factors that apply to this liquidity interaction.\n      </Description>\n      \n      <CheckboxGroup>\n        {DOL_CONTEXT_OPTIONS.map((option) => (\n          <CheckboxOption key={option.value}>\n            <CheckboxInput\n              type=\"checkbox\"\n              id={`dolContext_${option.value}`}\n              name=\"dolContext\"\n              value={option.value}\n              checked={value.includes(option.value)}\n              onChange={handleCheckboxChange}\n            />\n            <CheckboxLabel htmlFor={`dolContext_${option.value}`}>\n              {option.label}\n            </CheckboxLabel>\n          </CheckboxOption>\n        ))}\n      </CheckboxGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLContextSelector;\n", "/**\n * <PERSON><PERSON> Detailed Analysis Component\n * \n * Component for entering detailed DOL analysis information\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { \n  DOL_PRICE_ACTION_DESCRIPTIONS, \n  DOL_VOLUME_PROFILE_DESCRIPTIONS,\n  DOL_TIME_OF_DAY_DESCRIPTION,\n  DOL_MARKET_STRUCTURE_DESCRIPTION\n} from '../../constants/dolAnalysis';\n\nconst AnalysisContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 80px;\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.background};\n  resize: vertical;\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\ninterface DOLDetailedAnalysisProps {\n  formValues: {\n    dolType?: string;\n    dolStrength?: string;\n    dolPriceAction?: string;\n    dolVolumeProfile?: string;\n    dolTimeOfDay?: string;\n    dolMarketStructure?: string;\n  };\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLDetailedAnalysis: React.FC<DOLDetailedAnalysisProps> = ({ formValues, onChange }) => {\n  // Get the appropriate price action description based on DOL type\n  const getPriceActionDescription = () => {\n    if (formValues.dolType && DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType as keyof typeof DOL_PRICE_ACTION_DESCRIPTIONS]) {\n      return DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType as keyof typeof DOL_PRICE_ACTION_DESCRIPTIONS];\n    }\n    return 'Describe the price action during the liquidity interaction.';\n  };\n\n  // Get the appropriate volume profile description based on DOL strength\n  const getVolumeProfileDescription = () => {\n    if (formValues.dolStrength && DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength as keyof typeof DOL_VOLUME_PROFILE_DESCRIPTIONS]) {\n      return DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength as keyof typeof DOL_VOLUME_PROFILE_DESCRIPTIONS];\n    }\n    return 'Describe the volume profile during the liquidity interaction.';\n  };\n\n  // Handle text area change\n  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange(e.target.name, e.target.value);\n  };\n\n  return (\n    <AnalysisContainer>\n      <SectionTitle>Detailed Analysis</SectionTitle>\n      <Description>\n        Provide detailed information about the liquidity interaction.\n      </Description>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolPriceAction\">Price Action</Label>\n        <Description>{getPriceActionDescription()}</Description>\n        <TextArea\n          id=\"dolPriceAction\"\n          name=\"dolPriceAction\"\n          value={formValues.dolPriceAction || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the price action...\"\n        />\n      </FormGroup>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolVolumeProfile\">Volume Profile</Label>\n        <Description>{getVolumeProfileDescription()}</Description>\n        <TextArea\n          id=\"dolVolumeProfile\"\n          name=\"dolVolumeProfile\"\n          value={formValues.dolVolumeProfile || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the volume profile...\"\n        />\n      </FormGroup>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolTimeOfDay\">Time of Day Significance</Label>\n        <Description>{DOL_TIME_OF_DAY_DESCRIPTION}</Description>\n        <TextArea\n          id=\"dolTimeOfDay\"\n          name=\"dolTimeOfDay\"\n          value={formValues.dolTimeOfDay || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the time of day significance...\"\n        />\n      </FormGroup>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolMarketStructure\">Market Structure</Label>\n        <Description>{DOL_MARKET_STRUCTURE_DESCRIPTION}</Description>\n        <TextArea\n          id=\"dolMarketStructure\"\n          name=\"dolMarketStructure\"\n          value={formValues.dolMarketStructure || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the market structure...\"\n        />\n      </FormGroup>\n    </AnalysisContainer>\n  );\n};\n\nexport default DOLDetailedAnalysis;\n", "/**\n * DOL Effectiveness Rating Component\n * \n * Component for rating the effectiveness of the DOL analysis\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { \n  DOL_EFFECTIVENESS_OPTIONS, \n  getDOLEffectivenessColor,\n  getDOLEffectivenessDescription\n} from '../../constants/dolAnalysis';\n\nconst RatingContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RatingSliderContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SliderContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Slider = styled.input`\n  flex: 1;\n  height: 8px;\n  -webkit-appearance: none;\n  appearance: none;\n  background: ${({ theme }) => theme.colors.background};\n  outline: none;\n  border-radius: 4px;\n  \n  &::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: ${({ theme }) => theme.colors.primary};\n    cursor: pointer;\n  }\n  \n  &::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: ${({ theme }) => theme.colors.primary};\n    cursor: pointer;\n  }\n`;\n\nconst RatingValue = styled.div<{ color: string }>`\n  font-size: 2rem;\n  font-weight: 700;\n  color: ${({ color }) => color};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  border: 3px solid ${({ color }) => color};\n`;\n\nconst RatingDescription = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n  text-align: center;\n`;\n\nconst NotesContainer = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst NotesLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  display: block;\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst NotesTextarea = styled.textarea`\n  width: 100%;\n  min-height: 100px;\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.background};\n  resize: vertical;\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\ninterface DOLEffectivenessRatingProps {\n  value: string;\n  notes: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLEffectivenessRating: React.FC<DOLEffectivenessRatingProps> = ({ \n  value, \n  notes,\n  onChange \n}) => {\n  // Handle rating change\n  const handleRatingChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolEffectiveness', e.target.value);\n  };\n\n  // Handle notes change\n  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange('dolNotes', e.target.value);\n  };\n\n  // Get rating value as number\n  const ratingValue = value ? parseInt(value) : 5;\n  \n  // Get color based on rating\n  const ratingColor = getDOLEffectivenessColor(ratingValue);\n  \n  // Get description based on rating\n  const ratingDescription = getDOLEffectivenessDescription(ratingValue);\n\n  return (\n    <RatingContainer>\n      <SectionTitle>DOL Effectiveness Rating</SectionTitle>\n      <Description>\n        Rate how effectively price interacted with the liquidity level and how well\n        this interaction aligned with your trade thesis.\n      </Description>\n      \n      <RatingSliderContainer>\n        <SliderContainer>\n          <Slider\n            type=\"range\"\n            min=\"1\"\n            max=\"10\"\n            value={value || '5'}\n            onChange={handleRatingChange}\n          />\n          <RatingValue color={ratingColor}>\n            {ratingValue}\n          </RatingValue>\n        </SliderContainer>\n        \n        <RatingDescription>\n          {ratingDescription}\n        </RatingDescription>\n      </RatingSliderContainer>\n      \n      <NotesContainer>\n        <NotesLabel htmlFor=\"dolNotes\">Additional Notes</NotesLabel>\n        <NotesTextarea\n          id=\"dolNotes\"\n          name=\"dolNotes\"\n          value={notes}\n          onChange={handleNotesChange}\n          placeholder=\"Add any additional notes about the DOL analysis...\"\n        />\n      </NotesContainer>\n    </RatingContainer>\n  );\n};\n\nexport default DOLEffectivenessRating;\n", "/**\n * DOL Analysis Component\n * \n * Main component for the DOL analysis section that combines all the individual components\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport DOLTypeSelector from './DOLTypeSelector';\nimport DOLStrengthSelector from './DOLStrengthSelector';\nimport DOLReactionSelector from './DOLReactionSelector';\nimport DOLContextSelector from './DOLContextSelector';\nimport DOLDetailedAnalysis from './DOLDetailedAnalysis';\nimport DOLEffectivenessRating from './DOLEffectivenessRating';\n\nconst AnalysisContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Introduction = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst IntroTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst IntroText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0;\n  line-height: 1.5;\n`;\n\nconst Divider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst ValidationError = styled.div`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.errorLight};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\ninterface DOLAnalysisProps {\n  formValues: {\n    dolType?: string;\n    dolStrength?: string;\n    dolReaction?: string;\n    dolContext?: string[];\n    dolPriceAction?: string;\n    dolVolumeProfile?: string;\n    dolTimeOfDay?: string;\n    dolMarketStructure?: string;\n    dolEffectiveness?: string;\n    dolNotes?: string;\n  };\n  onChange: (field: string, value: any) => void;\n  validationErrors: {\n    [key: string]: string;\n  };\n}\n\nconst DOLAnalysis: React.FC<DOLAnalysisProps> = ({\n  formValues,\n  onChange,\n  validationErrors,\n}) => {\n  return (\n    <AnalysisContainer>\n      <Introduction>\n        <IntroTitle>Draw on Liquidity (DOL) Analysis</IntroTitle>\n        <IntroText>\n          Analyze how price interacted with liquidity levels in this trade. This analysis\n          will help you understand market behavior around key levels and improve your\n          ability to anticipate price movements.\n        </IntroText>\n      </Introduction>\n\n      {/* Display validation errors if any */}\n      {validationErrors.dolAnalysis && (\n        <ValidationError>{validationErrors.dolAnalysis}</ValidationError>\n      )}\n\n      <Divider />\n\n      {/* DOL Type Selector */}\n      <DOLTypeSelector\n        value={formValues.dolType || ''}\n        onChange={onChange}\n      />\n\n      <Divider />\n\n      {/* DOL Strength Selector */}\n      <DOLStrengthSelector\n        value={formValues.dolStrength || ''}\n        onChange={onChange}\n      />\n\n      <Divider />\n\n      {/* DOL Reaction Selector */}\n      <DOLReactionSelector\n        value={formValues.dolReaction || ''}\n        onChange={onChange}\n      />\n\n      <Divider />\n\n      {/* DOL Context Selector */}\n      <DOLContextSelector\n        value={formValues.dolContext || []}\n        onChange={onChange}\n      />\n\n      <Divider />\n\n      {/* DOL Detailed Analysis */}\n      <DOLDetailedAnalysis\n        formValues={formValues}\n        onChange={onChange}\n      />\n\n      <Divider />\n\n      {/* DOL Effectiveness Rating */}\n      <DOLEffectivenessRating\n        value={formValues.dolEffectiveness || '5'}\n        notes={formValues.dolNotes || ''}\n        onChange={onChange}\n      />\n    </AnalysisContainer>\n  );\n};\n\nexport default DOLAnalysis;\n", "/**\n * Trade Form Header Component\n *\n * Displays the header for the trade form with title and actions\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { TradeFormValues } from '../../types';\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xxl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\ninterface TradeFormHeaderProps {\n  isNewTrade: boolean;\n  formValues: TradeFormValues;\n}\n\n/**\n * Trade Form Header Component\n */\nconst TradeFormHeader: React.FC<TradeFormHeaderProps> = ({ isNewTrade, formValues }) => {\n  return (\n    <PageHeader>\n      <Title>\n        {isNewTrade ? 'Add New Trade' : `Edit Trade: ${formValues.symbol} (${formValues.date})`}\n      </Title>\n    </PageHeader>\n  );\n};\n\nexport default TradeFormHeader;\n", "/**\n * Trade Form Basic Fields Component\n *\n * Displays the basic information fields for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Input = styled.input`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst ValidationError = styled.span`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  margin-top: 2px;\n`;\n\ninterface TradeFormBasicFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;\n  validationErrors: ValidationErrors;\n  calculateProfitLoss?: () => void;\n}\n\n/**\n * Trade Form Basic Fields Component\n */\nconst TradeFormBasicFields: React.FC<TradeFormBasicFieldsProps> = ({\n  formValues,\n  handleChange,\n  validationErrors,\n  calculateProfitLoss,\n}) => {\n  // Handle changes that should trigger profit calculation\n  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    handleChange(e);\n    if (calculateProfitLoss) {\n      // Use setTimeout to ensure the form values are updated before calculation\n      setTimeout(calculateProfitLoss, 0);\n    }\n  };\n\n  return (\n    <>\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor=\"date\">Date</Label>\n          <Input\n            id=\"date\"\n            name=\"date\"\n            type=\"date\"\n            value={formValues.date}\n            onChange={handleChange}\n            required\n          />\n          {validationErrors.date && <ValidationError>{validationErrors.date}</ValidationError>}\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor=\"symbol\">Symbol</Label>\n          <Input\n            id=\"symbol\"\n            name=\"symbol\"\n            type=\"text\"\n            value={formValues.symbol}\n            onChange={handleChange}\n            required\n          />\n          {validationErrors.symbol && <ValidationError>{validationErrors.symbol}</ValidationError>}\n        </FormGroup>\n      </FormRow>\n\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor=\"direction\">Direction</Label>\n          <Select\n            id=\"direction\"\n            name=\"direction\"\n            value={formValues.direction}\n            onChange={handleChange}\n            required\n          >\n            <option value=\"long\">Long</option>\n            <option value=\"short\">Short</option>\n          </Select>\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor=\"result\">Result</Label>\n          <Select\n            id=\"result\"\n            name=\"result\"\n            value={formValues.result}\n            onChange={handleChange}\n            required\n          >\n            <option value=\"win\">Win</option>\n            <option value=\"loss\">Loss</option>\n            <option value=\"breakeven\">Breakeven</option>\n          </Select>\n        </FormGroup>\n      </FormRow>\n\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor=\"entryPrice\">Entry Price</Label>\n          <Input\n            id=\"entryPrice\"\n            name=\"entryPrice\"\n            type=\"number\"\n            step=\"0.01\"\n            value={formValues.entryPrice}\n            onChange={handlePriceChange}\n            required\n          />\n          {validationErrors.entryPrice && (\n            <ValidationError>{validationErrors.entryPrice}</ValidationError>\n          )}\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor=\"exitPrice\">Exit Price</Label>\n          <Input\n            id=\"exitPrice\"\n            name=\"exitPrice\"\n            type=\"number\"\n            step=\"0.01\"\n            value={formValues.exitPrice}\n            onChange={handlePriceChange}\n            required\n          />\n          {validationErrors.exitPrice && (\n            <ValidationError>{validationErrors.exitPrice}</ValidationError>\n          )}\n        </FormGroup>\n      </FormRow>\n\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor=\"quantity\">Quantity</Label>\n          <Input\n            id=\"quantity\"\n            name=\"quantity\"\n            type=\"number\"\n            value={formValues.quantity}\n            onChange={handlePriceChange}\n            required\n          />\n          {validationErrors.quantity && (\n            <ValidationError>{validationErrors.quantity}</ValidationError>\n          )}\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor=\"profit\">Profit/Loss ($)</Label>\n          <Input\n            id=\"profit\"\n            name=\"profit\"\n            type=\"number\"\n            step=\"0.01\"\n            value={formValues.profit}\n            onChange={handleChange}\n            required\n          />\n        </FormGroup>\n      </FormRow>\n    </>\n  );\n};\n\nexport default TradeFormBasicFields;\n", "/**\n * TimePicker Component\n *\n * A reusable time picker component for selecting hours and minutes.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\ninterface TimePickerProps {\n  id: string;\n  name: string;\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  label?: string;\n  required?: boolean;\n  disabled?: boolean;\n  className?: string;\n  placeholder?: string;\n  min?: string;\n  max?: string;\n}\n\nconst TimePickerContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst TimeInput = styled.input`\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.surface};\n  transition: border-color ${({ theme }) => theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n\n  &:disabled {\n    background-color: ${({ theme }) => theme.colors.chartGrid};\n    cursor: not-allowed;\n  }\n`;\n\n/**\n * TimePicker Component\n *\n * A reusable time picker component for selecting hours and minutes.\n */\nconst TimePicker: React.FC<TimePickerProps> = ({\n  id,\n  name,\n  value,\n  onChange,\n  label,\n  required = false,\n  disabled = false,\n  className,\n  placeholder = \"HH:MM\",\n  min,\n  max,\n}) => {\n  return (\n    <TimePickerContainer className={className}>\n      {label && (\n        <Label htmlFor={id}>\n          {label}\n          {required && <span style={{ color: \"red\" }}> *</span>}\n        </Label>\n      )}\n      <TimeInput\n        id={id}\n        name={name}\n        type=\"time\"\n        value={value}\n        onChange={onChange}\n        required={required}\n        disabled={disabled}\n        placeholder={placeholder}\n        min={min}\n        max={max}\n      />\n    </TimePickerContainer>\n  );\n};\n\nexport default TimePicker;\n", "/**\n * SelectDropdown Component\n *\n * A reusable dropdown component for selecting from a list of options.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\ninterface SelectOption {\n  value: string;\n  label: string;\n}\n\ninterface SelectDropdownProps {\n  id: string;\n  name: string;\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\n  options: SelectOption[];\n  label?: string;\n  required?: boolean;\n  disabled?: boolean;\n  className?: string;\n  placeholder?: string;\n}\n\nconst SelectContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.surface};\n  transition: border-color ${({ theme }) => theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n\n  &:disabled {\n    background-color: ${({ theme }) => theme.colors.chartGrid};\n    cursor: not-allowed;\n  }\n`;\n\n/**\n * SelectDropdown Component\n *\n * A reusable dropdown component for selecting from a list of options.\n */\nconst SelectDropdown: React.FC<SelectDropdownProps> = ({\n  id,\n  name,\n  value,\n  onChange,\n  options,\n  label,\n  required = false,\n  disabled = false,\n  className,\n  placeholder,\n}) => {\n  return (\n    <SelectContainer className={className}>\n      {label && (\n        <Label htmlFor={id}>\n          {label}\n          {required && <span style={{ color: \"red\" }}> *</span>}\n        </Label>\n      )}\n      <Select\n        id={id}\n        name={name}\n        value={value}\n        onChange={onChange}\n        required={required}\n        disabled={disabled}\n      >\n        {placeholder && (\n          <option value=\"\" disabled>\n            {placeholder}\n          </option>\n        )}\n        {options.map((option) => (\n          <option key={option.value} value={option.value}>\n            {option.label}\n          </option>\n        ))}\n      </Select>\n    </SelectContainer>\n  );\n};\n\nexport default SelectDropdown;\n", "/**\n * Trade Form Timing Fields Component\n *\n * Displays the timing fields for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\nimport { SESSION_OPTIONS, MARKET_OPTIONS } from '../../hooks';\nimport TimePicker from '../TimePicker';\nimport SelectDropdown from '../SelectDropdown';\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst ValidationError = styled.span`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  margin-top: 2px;\n`;\n\ninterface TradeFormTimingFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;\n  validationErrors: ValidationErrors;\n}\n\n/**\n * Trade Form Timing Fields Component\n */\nconst TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps> = ({\n  formValues,\n  handleChange,\n  validationErrors,\n}) => {\n  return (\n    <>\n      <FormRow>\n        <FormGroup>\n          <TimePicker\n            id=\"rdTime\"\n            name=\"rdTime\"\n            label=\"Risk/Decision Time\"\n            value={formValues.rdTime || ''}\n            onChange={handleChange}\n          />\n          {validationErrors.rdTime && <ValidationError>{validationErrors.rdTime}</ValidationError>}\n        </FormGroup>\n\n        <FormGroup>\n          <TimePicker\n            id=\"entryTime\"\n            name=\"entryTime\"\n            label=\"Entry Time\"\n            value={formValues.entryTime || ''}\n            onChange={handleChange}\n          />\n          {validationErrors.entryTime && (\n            <ValidationError>{validationErrors.entryTime}</ValidationError>\n          )}\n        </FormGroup>\n\n        <FormGroup>\n          <TimePicker\n            id=\"exitTime\"\n            name=\"exitTime\"\n            label=\"Exit Time\"\n            value={formValues.exitTime || ''}\n            onChange={handleChange}\n          />\n          {validationErrors.exitTime && (\n            <ValidationError>{validationErrors.exitTime}</ValidationError>\n          )}\n        </FormGroup>\n      </FormRow>\n\n      <FormRow>\n        <FormGroup>\n          <SelectDropdown\n            id=\"session\"\n            name=\"session\"\n            label=\"Session (Time Block)\"\n            value={formValues.session || ''}\n            onChange={handleChange}\n            options={SESSION_OPTIONS}\n            placeholder=\"Select Session\"\n          />\n        </FormGroup>\n\n        <FormGroup>\n          <SelectDropdown\n            id=\"market\"\n            name=\"market\"\n            label=\"Market\"\n            value={formValues.market || 'Stocks'}\n            onChange={handleChange}\n            options={MARKET_OPTIONS}\n          />\n        </FormGroup>\n      </FormRow>\n    </>\n  );\n};\n\nexport default TradeFormTimingFields;\n", "/**\n * Trade Form Risk Fields Component\n *\n * Displays the risk management fields for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Input = styled.input`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst HelpText = styled.span`\n  font-size: 0.8rem;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\ninterface TradeFormRiskFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;\n  validationErrors: ValidationErrors;\n}\n\n/**\n * Trade Form Risk Fields Component\n */\nconst TradeFormRiskFields: React.FC<TradeFormRiskFieldsProps> = ({\n  formValues,\n  handleChange,\n  validationErrors,\n}) => {\n  return (\n    <>\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor=\"stopLoss\">Stop Loss</Label>\n          <Input\n            id=\"stopLoss\"\n            name=\"stopLoss\"\n            type=\"number\"\n            step=\"0.01\"\n            value={formValues.stopLoss || ''}\n            onChange={handleChange}\n          />\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor=\"takeProfit\">Take Profit</Label>\n          <Input\n            id=\"takeProfit\"\n            name=\"takeProfit\"\n            type=\"number\"\n            step=\"0.01\"\n            value={formValues.takeProfit || ''}\n            onChange={handleChange}\n          />\n        </FormGroup>\n      </FormRow>\n\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor=\"riskPoints\">Risk (Points)</Label>\n          <Input\n            id=\"riskPoints\"\n            name=\"riskPoints\"\n            type=\"number\"\n            step=\"0.01\"\n            value={formValues.riskPoints || ''}\n            onChange={handleChange}\n          />\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor=\"rMultiple\">R-Multiple</Label>\n          <Input\n            id=\"rMultiple\"\n            name=\"rMultiple\"\n            type=\"number\"\n            step=\"0.01\"\n            value={formValues.rMultiple || ''}\n            onChange={handleChange}\n            disabled\n          />\n          <HelpText>Auto-calculated from Risk Points and P/L</HelpText>\n        </FormGroup>\n      </FormRow>\n    </>\n  );\n};\n\nexport default TradeFormRiskFields;\n", "/**\n * Setup Classification Constants\n * \n * Constants for the setup classification feature\n */\n\nimport { \n  StructureSetupType, \n  SessionSetupType, \n  ModelSetupType, \n  LiquidityType, \n  FVGType, \n  DOLTargetType, \n  ParentPDArrayType \n} from '../types';\n\n/**\n * Setup Category Options\n */\nexport const SETUP_CATEGORY_OPTIONS = [\n  { value: 'structure', label: 'Structure-Based Setup' },\n  { value: 'session', label: 'Session-Based Setup' },\n  { value: 'model', label: 'Model-Specific Setup' },\n];\n\n/**\n * Structure-Based Setup Options\n */\nexport const STRUCTURE_SETUP_OPTIONS = [\n  { value: 'High/Low Reversal Setup', label: 'High/Low Reversal Setup' },\n  { value: 'FVG Redelivery Setup', label: 'FVG Redelivery Setup' },\n  { value: 'Strong-FVG Reversal Setup', label: 'Strong-FVG Reversal Setup' },\n  { value: 'NWOG Reaction Setup', label: 'NWOG Reaction Setup' },\n  { value: 'NDOG Reaction Setup', label: 'NDOG Reaction Setup' },\n  { value: 'Multi-Array Confluence Setup', label: 'Multi-Array Confluence Setup' },\n];\n\n/**\n * Session-Based Setup Options\n */\nexport const SESSION_SETUP_OPTIONS = [\n  { value: 'Opening Range Setup (09:30-10:10)', label: 'Opening Range Setup (09:30-10:10)' },\n  { value: 'Morning Session Setup (10:50-11:10)', label: 'Morning Session Setup (10:50-11:10)' },\n  { value: 'Lunch Macro Setup (11:45-13:15)', label: 'Lunch Macro Setup (11:45-13:15)' },\n  { value: 'Afternoon Session Setup (13:30-15:00)', label: 'Afternoon Session Setup (13:30-15:00)' },\n  { value: 'MOC Setup (15:30-16:15)', label: 'MOC Setup (15:30-16:15)' },\n];\n\n/**\n * Model-Specific Setup Options\n */\nexport const MODEL_SETUP_OPTIONS = [\n  { value: 'Simple FVG-RD', label: 'Simple FVG-RD' },\n  { value: 'Complex FVG-RD', label: 'Complex FVG-RD' },\n  { value: 'True-RD Continuation', label: 'True-RD Continuation' },\n  { value: 'IMM-RD Continuation', label: 'IMM-RD Continuation' },\n  { value: 'Dispersed-RD Continuation', label: 'Dispersed-RD Continuation' },\n  { value: 'Wide-Gap-RD Continuation', label: 'Wide-Gap-RD Continuation' },\n];\n\n/**\n * Liquidity Options\n */\nexport const LIQUIDITY_OPTIONS = [\n  { value: '', label: 'None/Not Applicable' },\n  { value: 'London-H/L', label: 'London-H/L' },\n  { value: 'Premarket-H/L', label: 'Premarket-H/L' },\n  { value: '09:30-Opening-Range-H/L', label: '09:30-Opening-Range-H/L' },\n  { value: 'Post-10:00am-Lunch-Macro-H/L', label: 'Post-10:00am-Lunch-Macro-H/L' },\n  { value: 'Lunch-H/L', label: 'Lunch-H/L' },\n  { value: 'Monthly-H/L', label: 'Monthly-H/L' },\n  { value: 'Prev-Week-H/L', label: 'Prev-Week-H/L' },\n  { value: 'Prev-Day-H/L', label: 'Prev-Day-H/L' },\n  { value: 'Macro-H/L', label: 'Macro-H/L' },\n];\n\n/**\n * FVG Type Options - Time-based FVGs\n */\nexport const TIME_BASED_FVG_OPTIONS = [\n  { value: 'Monthly-FVG', label: 'Monthly-FVG' },\n  { value: 'Weekly-FVG', label: 'Weekly-FVG' },\n  { value: 'Daily-FVG', label: 'Daily-FVG' },\n  { value: 'Daily-Top/Bottom-FVG', label: 'Daily-Top/Bottom-FVG' },\n  { value: '1h-Top/Bottom-FVG', label: '1h-Top/Bottom-FVG' },\n  { value: '15min-Top/Bottom-FVG', label: '15min-Top/Bottom-FVG' },\n];\n\n/**\n * FVG Type Options - Current Session FPFVGs\n */\nexport const CURRENT_SESSION_FVG_OPTIONS = [\n  { value: 'MNOR-FVG', label: 'MNOR-FVG' },\n  { value: 'Asia-FPFVG', label: 'Asia-FPFVG' },\n  { value: 'Premarket-FPFVG', label: 'Premarket-FPFVG' },\n  { value: 'AM-FPFVG', label: 'AM-FPFVG' },\n  { value: 'PM-FPFVG', label: 'PM-FPFVG' },\n];\n\n/**\n * FVG Type Options - Previous Day FPFVGs\n */\nexport const PREV_DAY_FVG_OPTIONS = [\n  { value: 'Prev-Day-MNOR-FVG', label: 'Prev-Day-MNOR-FVG' },\n  { value: 'Prev-Day-Asia-FPFVG', label: 'Prev-Day-Asia-FPFVG' },\n  { value: 'Prev-Day-Premarket-FPFVG', label: 'Prev-Day-Premarket-FPFVG' },\n  { value: 'Prev-Day-AM-FPFVG', label: 'Prev-Day-AM-FPFVG' },\n  { value: 'Prev-Day-PM-FPFVG', label: 'Prev-Day-PM-FPFVG' },\n];\n\n/**\n * FVG Type Options - 3Day FPFVGs\n */\nexport const THREE_DAY_FVG_OPTIONS = [\n  { value: '3Day-MNOR-FVG', label: '3Day-MNOR-FVG' },\n  { value: '3Day-Asia-FPFVG', label: '3Day-Asia-FPFVG' },\n  { value: '3Day-Premarket-FPFVG', label: '3Day-Premarket-FPFVG' },\n  { value: '3Day-AM-FPFVG', label: '3Day-AM-FPFVG' },\n  { value: '3Day-PM-FPFVG', label: '3Day-PM-FPFVG' },\n];\n\n/**\n * FVG Type Options - Special FVGs\n */\nexport const SPECIAL_FVG_OPTIONS = [\n  { value: 'Top/Bottom-FVG', label: 'Top/Bottom-FVG' },\n  { value: 'Macro-FVG', label: 'Macro-FVG' },\n  { value: 'News-FVG', label: 'News-FVG' },\n  { value: '10min-Prior-To-News-FVG', label: '10min-Prior-To-News-FVG' },\n  { value: 'Strong-FVG', label: 'Strong-FVG' },\n  { value: 'RDRB-FVG', label: 'RDRB-FVG' },\n];\n\n/**\n * All FVG Options combined\n */\nexport const ALL_FVG_OPTIONS = [\n  ...TIME_BASED_FVG_OPTIONS,\n  ...CURRENT_SESSION_FVG_OPTIONS,\n  ...PREV_DAY_FVG_OPTIONS,\n  ...THREE_DAY_FVG_OPTIONS,\n  ...SPECIAL_FVG_OPTIONS,\n];\n\n/**\n * DOL Target Options\n */\nexport const DOL_TARGET_OPTIONS = [\n  { value: '', label: 'None/Not Applicable' },\n  { value: 'FVG Target', label: 'FVG Target' },\n  { value: 'Liquidity Target', label: 'Liquidity Target' },\n  { value: 'RD Target', label: 'RD Target' },\n];\n\n/**\n * Parent PD Array Options\n */\nexport const PARENT_PD_ARRAY_OPTIONS = [\n  { value: '', label: 'None/Not Applicable' },\n  { value: 'NWOG', label: 'NWOG' },\n  { value: 'Old-NWOG', label: 'Old-NWOG' },\n  { value: 'NDOG', label: 'NDOG' },\n  { value: 'Old-NDOG', label: 'Old-NDOG' },\n  { value: 'Monthly-FVG', label: 'Monthly-FVG' },\n  { value: 'Weekly-FVG', label: 'Weekly-FVG' },\n  { value: 'Daily-FVG', label: 'Daily-FVG' },\n  { value: 'Daily-Top/Bottom-FVG', label: 'Daily-Top/Bottom-FVG' },\n  { value: '1h-Top/Bottom-FVG', label: '1h-Top/Bottom-FVG' },\n  { value: '15min-Top/Bottom-FVG', label: '15min-Top/Bottom-FVG' },\n];\n\n/**\n * Get setup options based on category\n */\nexport const getSetupOptionsByCategory = (category: string) => {\n  switch (category) {\n    case 'structure':\n      return STRUCTURE_SETUP_OPTIONS;\n    case 'session':\n      return SESSION_SETUP_OPTIONS;\n    case 'model':\n      return MODEL_SETUP_OPTIONS;\n    default:\n      return [];\n  }\n};\n\n/**\n * Get DOL target options based on target type\n */\nexport const getDOLTargetOptions = (targetType: string) => {\n  switch (targetType) {\n    case 'FVG Target':\n      return ALL_FVG_OPTIONS;\n    case 'Liquidity Target':\n      return LIQUIDITY_OPTIONS;\n    default:\n      return [];\n  }\n};\n", "/**\n * Primary Setup Selector Component\n * \n * Component for selecting the primary setup category and type\n */\n\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport { \n  SETUP_CATEGORY_OPTIONS, \n  getSetupOptionsByCategory \n} from '../../constants/setupClassification';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst GuidanceNote = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-left: 4px solid ${({ theme }) => theme.colors.primary};\n  padding: ${({ theme }) => theme.spacing.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n  padding-left: ${({ theme }) => theme.spacing.md};\n  border-left: 3px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\ninterface PrimarySetupSelectorProps {\n  value: {\n    category?: string;\n    type?: string;\n  };\n  onChange: (field: string, value: string) => void;\n}\n\nconst PrimarySetupSelector: React.FC<PrimarySetupSelectorProps> = ({ value, onChange }) => {\n  const [setupOptions, setSetupOptions] = useState<{ value: string; label: string }[]>([]);\n\n  // Update setup options when category changes\n  useEffect(() => {\n    if (value.category) {\n      setSetupOptions(getSetupOptionsByCategory(value.category));\n    } else {\n      setSetupOptions([]);\n    }\n  }, [value.category]);\n\n  // Handle category change\n  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newCategory = e.target.value;\n    onChange('primarySetupCategory', newCategory);\n    \n    // Reset setup type when category changes\n    onChange('primarySetupType', '');\n  };\n\n  // Handle setup type change\n  const handleSetupTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('primarySetupType', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>Primary Setup Classification</SectionTitle>\n      \n      <GuidanceNote>\n        This is the main element that drove your entry decision. Think about\n        the most critical factor that made you enter the trade.\n      </GuidanceNote>\n      \n      <FormGroup>\n        <Label htmlFor=\"primarySetupCategory\">Select Primary Category:</Label>\n        <Select\n          id=\"primarySetupCategory\"\n          name=\"primarySetupCategory\"\n          value={value.category || ''}\n          onChange={handleCategoryChange}\n        >\n          <option value=\"\">-- Select Primary Category --</option>\n          {SETUP_CATEGORY_OPTIONS.map((option) => (\n            <option key={option.value} value={option.value}>\n              {option.label}\n            </option>\n          ))}\n        </Select>\n      </FormGroup>\n      \n      {value.category && setupOptions.length > 0 && (\n        <RadioGroup>\n          {setupOptions.map((option) => (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`primary_${option.value}`}\n                name=\"primarySetupType\"\n                value={option.value}\n                checked={value.type === option.value}\n                onChange={handleSetupTypeChange}\n              />\n              <RadioLabel htmlFor={`primary_${option.value}`}>\n                {option.label}\n              </RadioLabel>\n            </RadioOption>\n          ))}\n        </RadioGroup>\n      )}\n    </SelectorContainer>\n  );\n};\n\nexport default PrimarySetupSelector;\n", "/**\n * Secondary Setup Selector Component\n * \n * Component for selecting the secondary setup category and type\n */\n\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport { \n  SETUP_CATEGORY_OPTIONS, \n  getSetupOptionsByCategory \n} from '../../constants/setupClassification';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst GuidanceNote = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-left: 4px solid ${({ theme }) => theme.colors.primary};\n  padding: ${({ theme }) => theme.spacing.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n  padding-left: ${({ theme }) => theme.spacing.md};\n  border-left: 3px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst ValidationError = styled.span`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  margin-top: 2px;\n`;\n\ninterface SecondarySetupSelectorProps {\n  value: {\n    category?: string;\n    type?: string;\n  };\n  primarySetup: {\n    category?: string;\n    type?: string;\n  };\n  onChange: (field: string, value: string) => void;\n  error?: string;\n}\n\nconst SecondarySetupSelector: React.FC<SecondarySetupSelectorProps> = ({ \n  value, \n  primarySetup, \n  onChange,\n  error\n}) => {\n  const [setupOptions, setSetupOptions] = useState<{ value: string; label: string }[]>([]);\n\n  // Update setup options when category changes\n  useEffect(() => {\n    if (value.category) {\n      setSetupOptions(getSetupOptionsByCategory(value.category));\n    } else {\n      setSetupOptions([]);\n    }\n  }, [value.category]);\n\n  // Handle category change\n  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newCategory = e.target.value;\n    onChange('secondarySetupCategory', newCategory);\n    \n    // Reset setup type when category changes\n    onChange('secondarySetupType', '');\n  };\n\n  // Handle setup type change\n  const handleSetupTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('secondarySetupType', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>Secondary Setup Classification</SectionTitle>\n      \n      <GuidanceNote>\n        This is the supporting element that added context or confirmation to\n        your entry decision. It should be different from your Primary Setup Type.\n      </GuidanceNote>\n      \n      <FormGroup>\n        <Label htmlFor=\"secondarySetupCategory\">Select Secondary Category:</Label>\n        <Select\n          id=\"secondarySetupCategory\"\n          name=\"secondarySetupCategory\"\n          value={value.category || ''}\n          onChange={handleCategoryChange}\n        >\n          <option value=\"\">-- Select Secondary Category --</option>\n          {SETUP_CATEGORY_OPTIONS.map((option) => (\n            <option key={option.value} value={option.value}>\n              {option.label}\n            </option>\n          ))}\n        </Select>\n      </FormGroup>\n      \n      {value.category && setupOptions.length > 0 && (\n        <RadioGroup>\n          {setupOptions.map((option) => (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`secondary_${option.value}`}\n                name=\"secondarySetupType\"\n                value={option.value}\n                checked={value.type === option.value}\n                onChange={handleSetupTypeChange}\n              />\n              <RadioLabel htmlFor={`secondary_${option.value}`}>\n                {option.label}\n              </RadioLabel>\n            </RadioOption>\n          ))}\n        </RadioGroup>\n      )}\n      \n      {error && <ValidationError>{error}</ValidationError>}\n    </SelectorContainer>\n  );\n};\n\nexport default SecondarySetupSelector;\n", "/**\n * Liquidity Selector Component\n * \n * Component for selecting the liquidity taken/swept\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { LIQUIDITY_OPTIONS } from '../../constants/setupClassification';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst GuidanceNote = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-left: 4px solid ${({ theme }) => theme.colors.primary};\n  padding: ${({ theme }) => theme.spacing.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\ninterface LiquiditySelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst LiquiditySelector: React.FC<LiquiditySelectorProps> = ({ value, onChange }) => {\n  // Handle liquidity change\n  const handleLiquidityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    onChange('liquidityTaken', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>Liquidity Taken</SectionTitle>\n      \n      <GuidanceNote>\n        If your trade took/swept a specific type of liquidity, select it below.\n      </GuidanceNote>\n      \n      <FormGroup>\n        <Label htmlFor=\"liquidityTaken\">Liquidity Taken/Swept:</Label>\n        <Select\n          id=\"liquidityTaken\"\n          name=\"liquidityTaken\"\n          value={value || ''}\n          onChange={handleLiquidityChange}\n        >\n          {LIQUIDITY_OPTIONS.map((option) => (\n            <option key={option.value} value={option.value}>\n              {option.label}\n            </option>\n          ))}\n        </Select>\n      </FormGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default LiquiditySelector;\n", "/**\n * FVG Selector Component\n * \n * Component for selecting additional FVGs present in the trade\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { \n  TIME_BASED_FVG_OPTIONS,\n  CURRENT_SESSION_FVG_OPTIONS,\n  PREV_DAY_FVG_OPTIONS,\n  THREE_DAY_FVG_OPTIONS,\n  SPECIAL_FVG_OPTIONS\n} from '../../constants/setupClassification';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst GuidanceNote = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-left: 4px solid ${({ theme }) => theme.colors.primary};\n  padding: ${({ theme }) => theme.spacing.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst CheckboxGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CategoryGroup = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst CategoryTitle = styled.h4`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.sm} 0;\n  padding-bottom: ${({ theme }) => theme.spacing.xs};\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst CheckboxOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst CheckboxInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CheckboxLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\ninterface FVGSelectorProps {\n  value: string[];\n  onChange: (field: string, value: string[]) => void;\n}\n\nconst FVGSelector: React.FC<FVGSelectorProps> = ({ value, onChange }) => {\n  // Handle checkbox change\n  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const fvgType = e.target.value;\n    const isChecked = e.target.checked;\n    \n    let newValue: string[];\n    \n    if (isChecked) {\n      // Add to array if checked\n      newValue = [...value, fvgType];\n    } else {\n      // Remove from array if unchecked\n      newValue = value.filter(item => item !== fvgType);\n    }\n    \n    onChange('additionalFVGs', newValue);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>Additional FVGs Present</SectionTitle>\n      \n      <GuidanceNote>\n        Select all additional FVG types that were present in this trade setup.\n      </GuidanceNote>\n      \n      <CheckboxGroup>\n        {/* Time-based FVGs */}\n        <CategoryGroup>\n          <CategoryTitle>Time-frame FVGs:</CategoryTitle>\n          {TIME_BASED_FVG_OPTIONS.map((option) => (\n            <CheckboxOption key={option.value}>\n              <CheckboxInput\n                type=\"checkbox\"\n                id={`fvg_${option.value}`}\n                name=\"additionalFVGs\"\n                value={option.value}\n                checked={value.includes(option.value)}\n                onChange={handleCheckboxChange}\n              />\n              <CheckboxLabel htmlFor={`fvg_${option.value}`}>\n                {option.label}\n              </CheckboxLabel>\n            </CheckboxOption>\n          ))}\n        </CategoryGroup>\n        \n        {/* Current Session FPFVGs */}\n        <CategoryGroup>\n          <CategoryTitle>Current Session FPFVGs:</CategoryTitle>\n          {CURRENT_SESSION_FVG_OPTIONS.map((option) => (\n            <CheckboxOption key={option.value}>\n              <CheckboxInput\n                type=\"checkbox\"\n                id={`fvg_${option.value}`}\n                name=\"additionalFVGs\"\n                value={option.value}\n                checked={value.includes(option.value)}\n                onChange={handleCheckboxChange}\n              />\n              <CheckboxLabel htmlFor={`fvg_${option.value}`}>\n                {option.label}\n              </CheckboxLabel>\n            </CheckboxOption>\n          ))}\n        </CategoryGroup>\n        \n        {/* Previous Day FPFVGs */}\n        <CategoryGroup>\n          <CategoryTitle>Previous Day FPFVGs:</CategoryTitle>\n          {PREV_DAY_FVG_OPTIONS.map((option) => (\n            <CheckboxOption key={option.value}>\n              <CheckboxInput\n                type=\"checkbox\"\n                id={`fvg_${option.value}`}\n                name=\"additionalFVGs\"\n                value={option.value}\n                checked={value.includes(option.value)}\n                onChange={handleCheckboxChange}\n              />\n              <CheckboxLabel htmlFor={`fvg_${option.value}`}>\n                {option.label}\n              </CheckboxLabel>\n            </CheckboxOption>\n          ))}\n        </CategoryGroup>\n        \n        {/* 3Day FPFVGs */}\n        <CategoryGroup>\n          <CategoryTitle>3-Day FPFVGs:</CategoryTitle>\n          {THREE_DAY_FVG_OPTIONS.map((option) => (\n            <CheckboxOption key={option.value}>\n              <CheckboxInput\n                type=\"checkbox\"\n                id={`fvg_${option.value}`}\n                name=\"additionalFVGs\"\n                value={option.value}\n                checked={value.includes(option.value)}\n                onChange={handleCheckboxChange}\n              />\n              <CheckboxLabel htmlFor={`fvg_${option.value}`}>\n                {option.label}\n              </CheckboxLabel>\n            </CheckboxOption>\n          ))}\n        </CategoryGroup>\n        \n        {/* Special FVGs */}\n        <CategoryGroup>\n          <CategoryTitle>Special FVG Types:</CategoryTitle>\n          {SPECIAL_FVG_OPTIONS.map((option) => (\n            <CheckboxOption key={option.value}>\n              <CheckboxInput\n                type=\"checkbox\"\n                id={`fvg_${option.value}`}\n                name=\"additionalFVGs\"\n                value={option.value}\n                checked={value.includes(option.value)}\n                onChange={handleCheckboxChange}\n              />\n              <CheckboxLabel htmlFor={`fvg_${option.value}`}>\n                {option.label}\n              </CheckboxLabel>\n            </CheckboxOption>\n          ))}\n        </CategoryGroup>\n      </CheckboxGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default FVGSelector;\n", "/**\n * DOL Target Selector Component\n * \n * Component for selecting the DOL target type and specific type\n */\n\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport { \n  DOL_TARGET_OPTIONS, \n  getDOLTargetOptions \n} from '../../constants/setupClassification';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst GuidanceNote = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-left: 4px solid ${({ theme }) => theme.colors.primary};\n  padding: ${({ theme }) => theme.spacing.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n  padding-left: ${({ theme }) => theme.spacing.md};\n  border-left: 3px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\ninterface DOLTargetSelectorProps {\n  value: {\n    targetType?: string;\n    specificType?: string;\n  };\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLTargetSelector: React.FC<DOLTargetSelectorProps> = ({ value, onChange }) => {\n  const [specificOptions, setSpecificOptions] = useState<{ value: string; label: string }[]>([]);\n\n  // Update specific options when target type changes\n  useEffect(() => {\n    if (value.targetType && value.targetType !== 'RD Target') {\n      setSpecificOptions(getDOLTargetOptions(value.targetType));\n    } else {\n      setSpecificOptions([]);\n    }\n  }, [value.targetType]);\n\n  // Handle target type change\n  const handleTargetTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newTargetType = e.target.value;\n    onChange('dolTargetType', newTargetType);\n    \n    // Reset specific type when target type changes\n    onChange('specificDOLType', '');\n  };\n\n  // Handle specific type change\n  const handleSpecificTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('specificDOLType', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Target Type</SectionTitle>\n      \n      <GuidanceNote>\n        Select the primary target type for your Draw on Liquidity (DOL) analysis.\n      </GuidanceNote>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolTargetType\">DOL Target Type:</Label>\n        <Select\n          id=\"dolTargetType\"\n          name=\"dolTargetType\"\n          value={value.targetType || ''}\n          onChange={handleTargetTypeChange}\n        >\n          {DOL_TARGET_OPTIONS.map((option) => (\n            <option key={option.value} value={option.value}>\n              {option.label}\n            </option>\n          ))}\n        </Select>\n      </FormGroup>\n      \n      {value.targetType && value.targetType !== 'RD Target' && specificOptions.length > 0 && (\n        <>\n          <FormGroup>\n            <Label>\n              {value.targetType === 'FVG Target' ? 'FVG Target Type:' : 'Liquidity Target Type:'}\n            </Label>\n            <RadioGroup>\n              {specificOptions.map((option) => (\n                option.value && (\n                  <RadioOption key={option.value}>\n                    <RadioInput\n                      type=\"radio\"\n                      id={`dolTarget_${option.value}`}\n                      name=\"specificDOLType\"\n                      value={option.value}\n                      checked={value.specificType === option.value}\n                      onChange={handleSpecificTypeChange}\n                    />\n                    <RadioLabel htmlFor={`dolTarget_${option.value}`}>\n                      {option.label}\n                    </RadioLabel>\n                  </RadioOption>\n                )\n              ))}\n            </RadioGroup>\n          </FormGroup>\n        </>\n      )}\n    </SelectorContainer>\n  );\n};\n\nexport default DOLTargetSelector;\n", "/**\n * Parent PD Array Selector Component\n * \n * Component for selecting the parent PD array\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { PARENT_PD_ARRAY_OPTIONS } from '../../constants/setupClassification';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst GuidanceNote = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-left: 4px solid ${({ theme }) => theme.colors.primary};\n  padding: ${({ theme }) => theme.spacing.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\ninterface ParentPDArraySelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst ParentPDArraySelector: React.FC<ParentPDArraySelectorProps> = ({ value, onChange }) => {\n  // Handle parent PD array change\n  const handleParentPDArrayChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    onChange('parentPDArray', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>Parent PD Array</SectionTitle>\n      \n      <GuidanceNote>\n        If your trade used a specific Parent PD Array, please select it below.\n        This is often relevant for Structure-Based Setups.\n      </GuidanceNote>\n      \n      <FormGroup>\n        <Label htmlFor=\"parentPDArray\">Parent PD Array:</Label>\n        <Select\n          id=\"parentPDArray\"\n          name=\"parentPDArray\"\n          value={value || ''}\n          onChange={handleParentPDArrayChange}\n        >\n          {PARENT_PD_ARRAY_OPTIONS.map((option) => (\n            <option key={option.value} value={option.value}>\n              {option.label}\n            </option>\n          ))}\n        </Select>\n      </FormGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default ParentPDArraySelector;\n", "/**\n * Setup Classification Section Component\n * \n * Main component for the setup classification section that combines all the individual components\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport PrimarySetupSelector from './PrimarySetupSelector';\nimport SecondarySetupSelector from './SecondarySetupSelector';\nimport LiquiditySelector from './LiquiditySelector';\nimport FVGSelector from './FVGSelector';\nimport DOLTargetSelector from './DOLTargetSelector';\nimport ParentPDArraySelector from './ParentPDArraySelector';\n\nconst SectionContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Divider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst ValidationError = styled.div`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.errorLight};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\ninterface SetupClassificationSectionProps {\n  formValues: {\n    primarySetupCategory?: string;\n    primarySetupType?: string;\n    secondarySetupCategory?: string;\n    secondarySetupType?: string;\n    liquidityTaken?: string;\n    additionalFVGs?: string[];\n    dolTargetType?: string;\n    specificDOLType?: string;\n    parentPDArray?: string;\n  };\n  onChange: (field: string, value: any) => void;\n  validationErrors: {\n    [key: string]: string;\n  };\n}\n\nconst SetupClassificationSection: React.FC<SetupClassificationSectionProps> = ({\n  formValues,\n  onChange,\n  validationErrors,\n}) => {\n  // Handle field change\n  const handleFieldChange = (field: string, value: any) => {\n    onChange(field, value);\n  };\n\n  return (\n    <SectionContainer>\n      {/* Display validation errors if any */}\n      {validationErrors.setupClassification && (\n        <ValidationError>{validationErrors.setupClassification}</ValidationError>\n      )}\n\n      {/* Primary Setup Selector */}\n      <PrimarySetupSelector\n        value={{\n          category: formValues.primarySetupCategory,\n          type: formValues.primarySetupType,\n        }}\n        onChange={handleFieldChange}\n      />\n\n      <Divider />\n\n      {/* Secondary Setup Selector */}\n      <SecondarySetupSelector\n        value={{\n          category: formValues.secondarySetupCategory,\n          type: formValues.secondarySetupType,\n        }}\n        primarySetup={{\n          category: formValues.primarySetupCategory,\n          type: formValues.primarySetupType,\n        }}\n        onChange={handleFieldChange}\n        error={validationErrors.secondarySetupType}\n      />\n\n      <Divider />\n\n      {/* Liquidity Selector */}\n      <LiquiditySelector\n        value={formValues.liquidityTaken || ''}\n        onChange={handleFieldChange}\n      />\n\n      <Divider />\n\n      {/* FVG Selector */}\n      <FVGSelector\n        value={formValues.additionalFVGs || []}\n        onChange={handleFieldChange}\n      />\n\n      <Divider />\n\n      {/* DOL Target Selector */}\n      <DOLTargetSelector\n        value={{\n          targetType: formValues.dolTargetType,\n          specificType: formValues.specificDOLType,\n        }}\n        onChange={handleFieldChange}\n      />\n\n      <Divider />\n\n      {/* Parent PD Array Selector */}\n      <ParentPDArraySelector\n        value={formValues.parentPDArray || ''}\n        onChange={handleFieldChange}\n      />\n    </SectionContainer>\n  );\n};\n\nexport default SetupClassificationSection;\n", "/**\n * Trade Form Strategy Fields Component\n *\n * Displays the strategy fields for the trade form\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\nimport {\n  MODEL_TYPE_OPTIONS,\n  SETUP_OPTIONS,\n  ENTRY_VERSION_OPTIONS,\n  PATTERN_QUALITY_OPTIONS,\n} from '../../hooks';\nimport SelectDropdown from '../SelectDropdown';\nimport SetupClassificationSection from '../setup-classification/SetupClassificationSection';\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst TextArea = styled.textarea`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  min-height: 100px;\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: ${({ theme }) => theme.spacing.md} 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst Divider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.md} 0;\n`;\n\ninterface TradeFormStrategyFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\n  ) => void;\n  validationErrors: ValidationErrors;\n  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;\n}\n\n/**\n * Trade Form Strategy Fields Component\n */\nconst TradeFormStrategyFields: React.FC<TradeFormStrategyFieldsProps> = ({\n  formValues,\n  handleChange,\n  validationErrors,\n  setFormValues,\n}) => {\n  return (\n    <>\n      {/* Basic Strategy Section */}\n      <SectionTitle>Basic Strategy</SectionTitle>\n      <FormRow>\n        <FormGroup>\n          <SelectDropdown\n            id=\"modelType\"\n            name=\"modelType\"\n            label=\"Model Type\"\n            value={formValues.modelType || ''}\n            onChange={handleChange}\n            options={MODEL_TYPE_OPTIONS}\n            placeholder=\"Select Model Type\"\n          />\n        </FormGroup>\n\n        <FormGroup>\n          <SelectDropdown\n            id=\"setup\"\n            name=\"setup\"\n            label=\"Setup\"\n            value={formValues.setup || ''}\n            onChange={handleChange}\n            options={SETUP_OPTIONS}\n            placeholder=\"Select Setup\"\n          />\n        </FormGroup>\n      </FormRow>\n\n      <FormRow>\n        <FormGroup>\n          <SelectDropdown\n            id=\"entryVersion\"\n            name=\"entryVersion\"\n            label=\"Entry Version\"\n            value={formValues.entryVersion || 'First Entry'}\n            onChange={handleChange}\n            options={ENTRY_VERSION_OPTIONS}\n          />\n        </FormGroup>\n\n        <FormGroup>\n          <SelectDropdown\n            id=\"patternQuality\"\n            name=\"patternQuality\"\n            label=\"Pattern Quality (1-10)\"\n            value={formValues.patternQuality || '5'}\n            onChange={handleChange}\n            options={PATTERN_QUALITY_OPTIONS}\n          />\n        </FormGroup>\n      </FormRow>\n\n      <Divider />\n\n      {/* Setup Classification Section */}\n      <SectionTitle>Setup Classification</SectionTitle>\n      <SetupClassificationSection\n        formValues={formValues}\n        onChange={(field, value) => {\n          setFormValues((prev) => ({\n            ...prev,\n            [field]: value,\n          }));\n        }}\n        validationErrors={validationErrors}\n      />\n\n      <Divider />\n\n      {/* Notes Section */}\n      <SectionTitle>Notes</SectionTitle>\n      <FormGroup>\n        <Label htmlFor=\"notes\">Trade Notes</Label>\n        <TextArea id=\"notes\" name=\"notes\" value={formValues.notes} onChange={handleChange} />\n      </FormGroup>\n    </>\n  );\n};\n\nexport default TradeFormStrategyFields;\n", "/**\n * Trade Form Actions Component\n *\n * Displays the action buttons for the trade form\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  justify-content: flex-end;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Button = styled.button`\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n`;\n\nconst CancelButton = styled(Button)`\n  background-color: transparent;\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  color: ${({ theme }) => theme.colors.textSecondary};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst SubmitButton = styled(Button)`\n  background-color: ${({ theme }) => theme.colors.primary};\n  border: none;\n  color: white;\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\ninterface TradeFormActionsProps {\n  isSubmitting: boolean;\n  isLoading: boolean;\n  isNewTrade: boolean;\n}\n\n/**\n * Trade Form Actions Component\n */\nconst TradeFormActions: React.FC<TradeFormActionsProps> = ({\n  isSubmitting,\n  isLoading,\n  isNewTrade,\n}) => {\n  const navigate = useNavigate();\n\n  return (\n    <ButtonGroup>\n      <CancelButton\n        type=\"button\"\n        onClick={() => {\n          console.log('Cancel button clicked, navigating to journal');\n          navigate('/journal');\n        }}\n      >\n        Cancel\n      </CancelButton>\n      <SubmitButton\n        type=\"submit\"\n        disabled={isSubmitting || isLoading}\n        data-testid={isNewTrade ? 'add-trade-button' : 'update-trade-button'}\n      >\n        {isSubmitting ? 'Saving...' : isNewTrade ? 'Add Trade' : 'Update Trade'}\n      </SubmitButton>\n    </ButtonGroup>\n  );\n};\n\nexport default TradeFormActions;\n", "/**\n * Trade Form Messages Component\n *\n * Displays error and success messages for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nconst ErrorMessage = styled.div`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.errorLight || 'rgba(244, 67, 54, 0.1)'};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n`;\n\nconst SuccessMessage = styled.div`\n  color: ${({ theme }) => theme.colors.success};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n`;\n\nconst TabInfo = styled.div`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n`;\n\ninterface TradeFormMessagesProps {\n  error: string | null;\n  success: string | null;\n  showTabInfo?: boolean;\n}\n\n/**\n * Trade Form Messages Component\n */\nconst TradeFormMessages: React.FC<TradeFormMessagesProps> = ({\n  error,\n  success,\n  showTabInfo = true,\n}) => {\n  return (\n    <>\n      {error && <ErrorMessage>{error}</ErrorMessage>}\n      {success && <SuccessMessage>{success}</SuccessMessage>}\n      {showTabInfo && <TabInfo>* Required tab for saving trade</TabInfo>}\n    </>\n  );\n};\n\nexport default TradeFormMessages;\n", "/**\n * Trade Form Loading Component\n *\n * Displays a loading overlay for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nconst LoadingOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.7);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n`;\n\nconst LoadingSpinner = styled.div`\n  border: 4px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top: 4px solid ${({ theme }) => theme.colors.primary};\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\nconst LoadingText = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n`;\n\ninterface TradeFormLoadingProps {\n  isLoading: boolean;\n}\n\n/**\n * Trade Form Loading Component\n */\nconst TradeFormLoading: React.FC<TradeFormLoadingProps> = ({ isLoading }) => {\n  if (!isLoading) return null;\n\n  return (\n    <LoadingOverlay>\n      <LoadingSpinner />\n      <LoadingText>Loading trade data...</LoadingText>\n    </LoadingOverlay>\n  );\n};\n\nexport default TradeFormLoading;\n", "/**\n * Trade Form Page\n *\n * This page displays a form for adding or editing a trade entry.\n * Uses a tabbed interface for progressive disclosure of form fields.\n */\n\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useTradeForm } from './hooks/useTradeForm';\nimport TabPanel from './components/TabPanel';\nimport PatternQualityAssessment from './components/pattern-quality/PatternQualityAssessment';\nimport DOLAnalysis from './components/dol-analysis/DOLAnalysis';\nimport {\n  TradeFormHeader,\n  TradeFormBasicFields,\n  TradeFormTimingFields,\n  TradeFormRiskFields,\n  TradeFormStrategyFields,\n  TradeFormActions,\n  TradeFormMessages,\n  TradeFormLoading,\n} from './components/form';\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst ContentSection = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n  position: relative; /* Required for absolute positioning of loading overlay */\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\n/**\n * TradeForm Component\n *\n * Displays a form for adding or editing trade entries with a tabbed interface\n * for progressive disclosure of form fields.\n */\nconst TradeForm: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n\n  // Enhanced debugging for ID parameter\n  console.log(`TradeForm component mounted with ID parameter: \"${id}\"`);\n  console.log(`Current URL: ${window.location.href}`);\n  console.log(`Is edit mode: ${id && id !== 'new'}`);\n  console.log(`Is new trade: ${id === 'new'}`);\n\n  const {\n    formValues,\n    setFormValues,\n    handleChange,\n    handleSubmit,\n    isSubmitting,\n    isLoading,\n    error,\n    success,\n    validationErrors,\n    isNewTrade,\n    activeTab,\n    handleTabChange,\n    calculateProfitLoss,\n  } = useTradeForm(id);\n\n  return (\n    <PageContainer>\n      <TradeFormHeader isNewTrade={isNewTrade} formValues={formValues} />\n\n      <ContentSection>\n        <TradeFormLoading isLoading={isLoading} />\n\n        <Form onSubmit={handleSubmit}>\n          <TradeFormMessages error={error} success={success} />\n\n          <TabPanel\n            tabs={[\n              {\n                id: 'basic',\n                label: 'Basic Info*',\n                content: (\n                  <TradeFormBasicFields\n                    formValues={formValues}\n                    handleChange={handleChange}\n                    validationErrors={validationErrors}\n                    calculateProfitLoss={calculateProfitLoss}\n                  />\n                ),\n              },\n              {\n                id: 'timing',\n                label: 'Timing',\n                content: (\n                  <TradeFormTimingFields\n                    formValues={formValues}\n                    handleChange={handleChange}\n                    validationErrors={validationErrors}\n                  />\n                ),\n              },\n              {\n                id: 'risk',\n                label: 'Risk Management',\n                content: (\n                  <TradeFormRiskFields\n                    formValues={formValues}\n                    handleChange={handleChange}\n                    validationErrors={validationErrors}\n                  />\n                ),\n              },\n              {\n                id: 'strategy',\n                label: 'Strategy',\n                content: (\n                  <TradeFormStrategyFields\n                    formValues={formValues}\n                    handleChange={handleChange}\n                    validationErrors={validationErrors}\n                    setFormValues={setFormValues}\n                  />\n                ),\n              },\n              {\n                id: 'pattern-quality',\n                label: 'Pattern Quality',\n                content: (\n                  <PatternQualityAssessment\n                    formValues={formValues}\n                    onChange={(field, value) => {\n                      setFormValues((prev) => ({\n                        ...prev,\n                        [field]: value,\n                      }));\n                    }}\n                  />\n                ),\n              },\n              {\n                id: 'dol-analysis',\n                label: 'DOL Analysis',\n                content: (\n                  <DOLAnalysis\n                    formValues={formValues}\n                    onChange={(field, value) => {\n                      setFormValues((prev) => ({\n                        ...prev,\n                        [field]: value,\n                      }));\n                    }}\n                    validationErrors={validationErrors}\n                  />\n                ),\n              },\n            ]}\n            defaultTab=\"basic\"\n            activeTab={activeTab}\n            onTabClick={handleTabChange}\n          />\n\n          <TradeFormMessages error={null} success={null} showTabInfo={true} />\n\n          <TradeFormActions\n            isSubmitting={isSubmitting}\n            isLoading={isLoading}\n            isNewTrade={isNewTrade}\n          />\n        </Form>\n      </ContentSection>\n    </PageContainer>\n  );\n};\n\nexport default TradeForm;\n"], "file": "assets/TradeForm-7631201a.js"}