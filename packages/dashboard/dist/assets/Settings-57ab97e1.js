import{j as o}from"./client-4c27269c.js";import{s as n}from"./styled-components-3ebafa9a.js";import{r as f}from"./react-60374de9.js";import{u as j}from"./main-2920b47d.js";import"./router-e715efa2.js";const y=()=>{const{theme:e,setTheme:i}=j(),[d,t]=f.useState({theme:e.name,refreshInterval:5,showNotifications:!0,enableAdvancedMetrics:!1,autoSaveJournal:!0});return{settings:d,handleChange:(p,m)=>{t(b=>({...b,[p]:m})),p==="theme"&&i(m)},handleSave:()=>{console.log("Settings saved:",d)}}},S=n.div.withConfig({displayName:"PageContainer",componentId:"sc-1g7abjd-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.lg),v=n.div.withConfig({displayName:"PageHeader",componentId:"sc-1g7abjd-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";"],({theme:e})=>e.spacing.lg),w=n.h1.withConfig({displayName:"Title",componentId:"sc-1g7abjd-2"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.xxl,({theme:e})=>e.colors.textPrimary),x=n.div.withConfig({displayName:"ContentSection",componentId:"sc-1g7abjd-3"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.shadows.sm),u=n.h2.withConfig({displayName:"SectionTitle",componentId:"sc-1g7abjd-4"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.md),s=n.div.withConfig({displayName:"SettingGroup",componentId:"sc-1g7abjd-5"})(["display:flex;flex-direction:column;gap:",";padding:"," 0;border-bottom:1px solid ",";&:last-child{border-bottom:none;}"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),r=n.div.withConfig({displayName:"SettingRow",componentId:"sc-1g7abjd-6"})(["display:flex;justify-content:space-between;align-items:center;"]),a=n.div.withConfig({displayName:"SettingLabel",componentId:"sc-1g7abjd-7"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),c=n.div.withConfig({displayName:"SettingDescription",componentId:"sc-1g7abjd-8"})(["font-size:",";color:",";margin-top:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),C=n.select.withConfig({displayName:"Select",componentId:"sc-1g7abjd-9"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),k=n.input.withConfig({displayName:"Input",componentId:"sc-1g7abjd-10"})(["padding:",";background-color:",";border:1px solid ",";border-radius:",";color:",";&:focus{border-color:",";outline:none;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary),l=n.label.withConfig({displayName:"Toggle",componentId:"sc-1g7abjd-11"})(["position:relative;display:inline-block;width:50px;height:24px;"]),g=n.input.withConfig({displayName:"ToggleInput",componentId:"sc-1g7abjd-12"})(["opacity:0;width:0;height:0;&:checked + span{background-color:",";}&:checked + span:before{transform:translateX(26px);}"],({theme:e})=>e.colors.primary),h=n.span.withConfig({displayName:"ToggleSlider",componentId:"sc-1g7abjd-13"})(["position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:",";transition:",';border-radius:34px;&:before{position:absolute;content:"";height:18px;width:18px;left:3px;bottom:3px;background-color:white;transition:',";border-radius:50%;}"],({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal,({theme:e})=>e.transitions.normal),I=n.button.withConfig({displayName:"SaveButton",componentId:"sc-1g7abjd-14"})(["padding:"," ",";background-color:",";border:none;border-radius:",";color:white;font-weight:500;cursor:pointer;transition:background-color ",";align-self:flex-end;margin-top:",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.primary,({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.primaryDark),J=()=>{const{settings:e,handleChange:i,handleSave:d}=y();return o.jsxs(S,{children:[o.jsx(v,{children:o.jsx(w,{children:"Settings"})}),o.jsxs(x,{children:[o.jsx(u,{children:"Appearance"}),o.jsx(s,{children:o.jsxs(r,{children:[o.jsxs("div",{children:[o.jsx(a,{children:"Theme"}),o.jsx(c,{children:"Choose your preferred theme"})]}),o.jsxs(C,{value:e.theme,onChange:t=>i("theme",t.target.value),children:[o.jsx("option",{value:"f1",children:"Formula 1"}),o.jsx("option",{value:"light",children:"Light"})]})]})})]}),o.jsxs(x,{children:[o.jsx(u,{children:"General Settings"}),o.jsx(s,{children:o.jsxs(r,{children:[o.jsxs("div",{children:[o.jsx(a,{children:"Data Refresh Interval"}),o.jsx(c,{children:"How often to refresh dashboard data (minutes)"})]}),o.jsx(k,{type:"number",min:"1",max:"60",value:e.refreshInterval,onChange:t=>i("refreshInterval",parseInt(t.target.value)),style:{width:"80px"}})]})}),o.jsx(s,{children:o.jsxs(r,{children:[o.jsxs("div",{children:[o.jsx(a,{children:"Notifications"}),o.jsx(c,{children:"Enable desktop notifications"})]}),o.jsxs(l,{children:[o.jsx(g,{type:"checkbox",checked:e.showNotifications,onChange:t=>i("showNotifications",t.target.checked)}),o.jsx(h,{})]})]})}),o.jsx(s,{children:o.jsxs(r,{children:[o.jsxs("div",{children:[o.jsx(a,{children:"Advanced Metrics"}),o.jsx(c,{children:"Show additional performance metrics"})]}),o.jsxs(l,{children:[o.jsx(g,{type:"checkbox",checked:e.enableAdvancedMetrics,onChange:t=>i("enableAdvancedMetrics",t.target.checked)}),o.jsx(h,{})]})]})}),o.jsx(s,{children:o.jsxs(r,{children:[o.jsxs("div",{children:[o.jsx(a,{children:"Auto-Save Journal"}),o.jsx(c,{children:"Automatically save trade entries as you type"})]}),o.jsxs(l,{children:[o.jsx(g,{type:"checkbox",checked:e.autoSaveJournal,onChange:t=>i("autoSaveJournal",t.target.checked)}),o.jsx(h,{})]})]})}),o.jsx(I,{onClick:d,children:"Save Settings"})]})]})};export{J as default};
//# sourceMappingURL=Settings-57ab97e1.js.map
