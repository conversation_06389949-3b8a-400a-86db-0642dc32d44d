import{j as t}from"./client-4c27269c.js";import{r as C}from"./react-60374de9.js";import{C as w,s as c,U as De}from"./styled-components-3ebafa9a.js";import{a as ne,C as ie,B as te}from"./Card-a75b9d5e.js";const le={small:w(["height:100px;"]),medium:w(["height:200px;"]),large:w(["height:300px;"]),custom:e=>w(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},Le={default:w(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:w(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:w(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:w(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},ke=De(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Pe=c.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:s,customWidth:r})=>e==="custom"?le.custom({customHeight:s,customWidth:r}):le[e],({variant:e})=>Le[e]),Me=c.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,ke,({theme:e})=>e.spacing.sm),Ne=c.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),Ie=({variant:e="default",size:s="medium",height:r="200px",width:i,text:o="Loading...",showSpinner:f=!0,className:n})=>t.jsxs(Pe,{variant:e,size:s,customHeight:r,customWidth:i,className:n,children:[f&&t.jsx(Me,{}),o&&t.jsx(Ne,{children:o})]}),Ee={small:w(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:w(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:w(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},Fe=e=>w(["",""],({theme:s})=>{let r,i,o;switch(e){case"primary":r=`${s.colors.primary}10`,i=s.colors.primary,o=`${s.colors.primary}30`;break;case"secondary":r=`${s.colors.secondary}10`,i=s.colors.secondary,o=`${s.colors.secondary}30`;break;case"success":r=`${s.colors.success}10`,i=s.colors.success,o=`${s.colors.success}30`;break;case"warning":r=`${s.colors.warning}10`,i=s.colors.warning,o=`${s.colors.warning}30`;break;case"error":r=`${s.colors.error}10`,i=s.colors.error,o=`${s.colors.error}30`;break;case"info":r=`${s.colors.info}10`,i=s.colors.info,o=`${s.colors.info}30`;break;default:r=`${s.colors.textSecondary}10`,i=s.colors.textSecondary,o=`${s.colors.textSecondary}30`}return`
        background-color: ${r};
        color: ${i};
        border: 1px solid ${o};
      `}),$e=c.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>Ee[e],({variant:e})=>Fe(e),({clickable:e})=>e&&w(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:s})=>s.transitions.fast)),Re=c.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:s})=>{const r={small:"12px",medium:"14px",large:"16px"};return`
      width: ${r[e]};
      height: ${r[e]};
      font-size: ${s.fontSizes.xs};
    `}),ae=({children:e,variant:s="default",size:r="medium",removable:i=!1,onRemove:o,className:f,onClick:n})=>{const g=l=>{l.stopPropagation(),o==null||o()};return t.jsxs($e,{variant:s,size:r,clickable:!!n,className:f,onClick:n,children:[e,i&&t.jsx(Re,{size:r,onClick:g,children:"×"})]})},ze=c.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:s})=>{const r={small:s.fontSizes.md,medium:s.fontSizes.lg,large:s.fontSizes.xl};return w(["font-size:",";"],r[e])}),Ae=c.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:s})=>{const r={small:s.fontSizes.sm,medium:s.fontSizes.md,large:s.fontSizes.lg};return w(["font-size:",";"],r[e])}),We={default:w(["background-color:transparent;"]),compact:w(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:w(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},Oe=c.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>We[e],({size:e,theme:s})=>{switch(e){case"small":return w(["padding:",";min-height:120px;"],s.spacing.md);case"large":return w(["padding:",";min-height:300px;"],s.spacing.xl);default:return w(["padding:",";min-height:200px;"],s.spacing.lg)}}),Ve=c.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:s})=>{const r={small:"32px",medium:"48px",large:"64px"};return w(["font-size:",";svg{width:",";height:",";color:",";}"],r[e],r[e],r[e],s.colors.textSecondary)}),Be=c.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),He=c.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),de=({title:e,description:s,icon:r,actionText:i,onAction:o,variant:f="default",size:n="medium",className:g,children:l})=>t.jsxs(Oe,{variant:f,size:n,className:g,children:[r&&t.jsx(Ve,{size:n,children:r}),e&&t.jsx(ze,{size:n,children:e}),s&&t.jsx(Ae,{size:n,children:s}),i&&o&&t.jsx(Be,{children:t.jsx(ne,{variant:"primary",size:n==="small"?"small":"medium",onClick:o,children:i})}),l&&t.jsx(He,{children:l})]}),_e=c.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),M=({title:e,children:s,isLoading:r=!1,hasError:i=!1,errorMessage:o="An error occurred while loading data",showRetry:f=!0,onRetry:n,isEmpty:g=!1,emptyMessage:l="No data available",emptyActionText:x,onEmptyAction:p,actionButton:h,className:y,...T})=>{const m=t.jsx(_e,{children:h});let u;return r?u=t.jsx(Ie,{variant:"card",text:"Loading data..."}):i?u=t.jsx(de,{title:"Error",description:o,variant:"compact",actionText:f?"Retry":void 0,onAction:f?n:void 0}):g?u=t.jsx(de,{title:"No Data",description:l,variant:"compact",actionText:x,onAction:p}):u=s,t.jsx(ie,{title:e,actions:m,className:y,...T,children:u})};function Ue(e,s){const r=()=>{if(typeof window>"u")return s;try{const n=window.localStorage.getItem(e);return n?JSON.parse(n):s}catch(n){return console.warn(`Error reading localStorage key "${e}":`,n),s}},[i,o]=C.useState(r),f=n=>{try{const g=n instanceof Function?n(i):n;o(g),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(g))}catch(g){console.warn(`Error setting localStorage key "${e}":`,g)}};return C.useEffect(()=>{const n=g=>{g.key===e&&g.newValue&&o(JSON.parse(g.newValue))};return window.addEventListener("storage",n),()=>window.removeEventListener("storage",n)},[e]),[i,f]}const qe=async e=>{if(await new Promise(r=>setTimeout(r,800)),Math.random()<.05)throw new Error("Failed to fetch trade analysis data");return Ge(e)},Ge=e=>{const s=Je(e),r=Qe(s),i=K(s,"symbol"),o=K(s,"strategy"),f=K(s,"timeframe"),n=K(s,"session"),g=me(s,"timeOfDay"),l=me(s,"dayOfWeek");return{trades:s,metrics:r,symbolPerformance:i,strategyPerformance:o,timeframePerformance:f,sessionPerformance:n,timeOfDayPerformance:g,dayOfWeekPerformance:l}},Je=e=>{const{dateRange:s}=e,r=new Date(s.startDate),i=new Date(s.endDate),o=Math.floor((i.getTime()-r.getTime())/(1e3*60*60*24)),f=Math.max(1,o)*(1+Math.floor(Math.random()*5)),n=[],g=["AAPL","MSFT","GOOGL","AMZN","TSLA","META","NFLX","NVDA"],l=["Breakout","Reversal","Trend Following","Gap and Go","VWAP Bounce","Support/Resistance"],x=["1m","5m","15m","30m","1h","4h","daily"],p=["pre-market","regular","after-hours"],h=["High Volume","Low Float","Earnings","News","Technical","Momentum","Oversold","Overbought"];for(let y=0;y<f;y++){const T=new Date(r.getTime()+Math.random()*(i.getTime()-r.getTime())),m=9+Math.floor(Math.random()*7),u=Math.floor(Math.random()*60),a=new Date(T);a.setHours(m,u,0,0);const j=5+Math.floor(Math.random()*120),b=new Date(a.getTime()+j*60*1e3),d=g[Math.floor(Math.random()*g.length)],S=Math.random()>.5?"long":"short",v=100+Math.random()*900,B=Math.random()<.6,X=!B&&Math.random()<.1;let P,U,q,Z;if(X)P=v+(Math.random()*.2-.1),Z="breakeven";else if(B){const R=.5+Math.random()*4.5;P=S==="long"?v*(1+R/100):v*(1-R/100),Z="win"}else{const R=.5+Math.random()*2.5;P=S==="long"?v*(1-R/100):v*(1+R/100),Z="loss"}const se=10+Math.floor(Math.random()*90);S==="long"?(U=(P-v)*se,q=(P/v-1)*100):(U=(v-P)*se,q=(v/P-1)*100),U=Math.round(U*100)/100,q=Math.round(q*100)/100;const we=x[Math.floor(Math.random()*x.length)],ve=p[Math.floor(Math.random()*p.length)],Ce=l[Math.floor(Math.random()*l.length)],Te=Math.floor(Math.random()*4),re=[];for(let R=0;R<Te;R++){const ce=h[Math.floor(Math.random()*h.length)];re.includes(ce)||re.push(ce)}const Se={id:`trade-${y}`,symbol:d,direction:S,entryPrice:v,exitPrice:P,quantity:se,entryTime:a.toISOString(),exitTime:b.toISOString(),status:Z,profitLoss:U,profitLossPercent:q,timeframe:we,session:ve,strategy:Ce,tags:re,notes:Math.random()>.7?`Sample note for ${d} ${S} trade`:void 0};n.push(Se)}return n.sort((y,T)=>new Date(T.entryTime).getTime()-new Date(y.entryTime).getTime())},Qe=e=>{const s=e.filter(a=>a.status==="win"),r=e.filter(a=>a.status==="loss"),i=e.filter(a=>a.status==="breakeven"),o=e.reduce((a,j)=>a+j.profitLoss,0),f=s.reduce((a,j)=>a+j.profitLoss,0),n=Math.abs(r.reduce((a,j)=>a+j.profitLoss,0)),g=s.length>0?f/s.length:0,l=r.length>0?n/r.length:0,x=s.length>0?Math.max(...s.map(a=>a.profitLoss)):0,p=r.length>0?Math.min(...r.map(a=>a.profitLoss)):0,h=e.map(a=>{const j=new Date(a.entryTime).getTime();return(new Date(a.exitTime).getTime()-j)/(1e3*60)}),y=h.length>0?h.reduce((a,j)=>a+j,0)/h.length:0,T=n>0?f/n:f>0?1/0:0,m=e.length>0?s.length/e.length:0,u=m*g-(1-m)*l;return{totalTrades:e.length,winningTrades:s.length,losingTrades:r.length,breakeven:i.length,winRate:Math.round(m*1e4)/100,averageWin:Math.round(g*100)/100,averageLoss:Math.round(l*100)/100,profitFactor:Math.round(T*100)/100,totalProfitLoss:Math.round(o*100)/100,largestWin:Math.round(x*100)/100,largestLoss:Math.round(p*100)/100,averageDuration:Math.round(y*100)/100,expectancy:Math.round(u*100)/100}},K=(e,s)=>{const r=new Map;e.forEach(o=>{const f=o[s];r.has(f)||r.set(f,[]),r.get(f).push(o)});const i=[];return r.forEach((o,f)=>{const n=o.filter(p=>p.status==="win"),g=o.reduce((p,h)=>p+h.profitLoss,0),l=o.length>0?n.length/o.length:0,x=o.length>0?g/o.length:0;i.push({category:s,value:f,trades:o.length,winRate:Math.round(l*1e4)/100,profitLoss:Math.round(g*100)/100,averageProfitLoss:Math.round(x*100)/100})}),i.sort((o,f)=>f.profitLoss-o.profitLoss)},me=(e,s)=>{let r;s==="timeOfDay"?r=["9:30-10:30","10:30-11:30","11:30-12:30","12:30-13:30","13:30-14:30","14:30-15:30","15:30-16:00"]:r=["Monday","Tuesday","Wednesday","Thursday","Friday"];const i=r.map(o=>({timeSlot:o,trades:0,winRate:0,profitLoss:0}));return e.forEach(o=>{const f=new Date(o.entryTime);let n;if(s==="timeOfDay"){const p=f.getHours(),h=f.getMinutes(),y=p+h/60;if(y<9.5||y>=16)return;y<10.5?n=0:y<11.5?n=1:y<12.5?n=2:y<13.5?n=3:y<14.5?n=4:y<15.5?n=5:n=6}else{const p=f.getDay();if(p===0||p===6)return;n=p-1}const g=i[n];g.trades++,g.profitLoss+=o.profitLoss;const l=e.filter(p=>{const h=new Date(p.entryTime);if(s==="timeOfDay"){const y=h.getHours(),T=h.getMinutes(),m=y+T/60;return n===0?m>=9.5&&m<10.5:n===1?m>=10.5&&m<11.5:n===2?m>=11.5&&m<12.5:n===3?m>=12.5&&m<13.5:n===4?m>=13.5&&m<14.5:n===5?m>=14.5&&m<15.5:n===6?m>=15.5&&m<16:!1}else return h.getDay()===n+1}),x=l.filter(p=>p.status==="win");g.winRate=l.length>0?x.length/l.length*100:0}),i.filter(o=>o.trades>0).map(o=>({...o,winRate:Math.round(o.winRate*100)/100,profitLoss:Math.round(o.profitLoss*100)/100}))},je=()=>{const e=new Date,s=new Date;return s.setMonth(e.getMonth()-1),{startDate:s.toISOString().split("T")[0],endDate:e.toISOString().split("T")[0]}},fe={data:null,filters:{dateRange:je()},preferences:{defaultDateRange:"month",defaultView:"summary",chartTypes:{performance:"bar",distribution:"pie",timeAnalysis:"bar"},tableColumns:["symbol","direction","entryTime","exitTime","profitLoss","status","strategy"],favoriteStrategies:[],favoriteTags:[]},isLoading:!1,error:null,selectedTradeId:null},Xe=(e,s)=>{switch(s.type){case"FETCH_DATA_START":return{...e,isLoading:!0,error:null};case"FETCH_DATA_SUCCESS":return{...e,data:s.payload,isLoading:!1,error:null};case"FETCH_DATA_ERROR":return{...e,isLoading:!1,error:s.payload};case"UPDATE_FILTERS":return{...e,filters:{...e.filters,...s.payload}};case"UPDATE_PREFERENCES":return{...e,preferences:{...e.preferences,...s.payload}};case"SELECT_TRADE":return{...e,selectedTradeId:s.payload};case"RESET_FILTERS":return{...e,filters:{dateRange:je()}};default:return e}},be=C.createContext(void 0),Ze=({children:e})=>{const[s]=Ue("trade-analysis-preferences",{}),r={...fe,preferences:{...fe.preferences,...s}},[i,o]=C.useReducer(Xe,r);C.useEffect(()=>{localStorage.setItem("trade-analysis-preferences",JSON.stringify(i.preferences))},[i.preferences]);const f=C.useCallback(async()=>{o({type:"FETCH_DATA_START"});try{const h=await qe(i.filters);o({type:"FETCH_DATA_SUCCESS",payload:h})}catch(h){o({type:"FETCH_DATA_ERROR",payload:h instanceof Error?h.message:"An unknown error occurred"})}},[i.filters]),n=C.useCallback(h=>{o({type:"UPDATE_FILTERS",payload:h})},[]),g=C.useCallback(h=>{o({type:"UPDATE_PREFERENCES",payload:h})},[]),l=C.useCallback(h=>{o({type:"SELECT_TRADE",payload:h})},[]),x=C.useCallback(()=>{o({type:"RESET_FILTERS"})},[]);C.useEffect(()=>{f()},[f,i.filters]);const p={...i,fetchData:f,updateFilters:n,updatePreferences:g,selectTrade:l,resetFilters:x};return t.jsx(be.Provider,{value:p,children:e})},V=()=>{const e=C.useContext(be);if(e===void 0)throw new Error("useTradeAnalysis must be used within a TradeAnalysisProvider");return e},Ke=c(ie).withConfig({displayName:"Container",componentId:"sc-wstfmv-0"})(["margin-bottom:",";"],({theme:e})=>e.spacing.md),Ye=c.div.withConfig({displayName:"FilterGrid",componentId:"sc-wstfmv-1"})(["display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),z=c.div.withConfig({displayName:"FilterSection",componentId:"sc-wstfmv-2"})(["margin-bottom:",";"],({theme:e})=>e.spacing.md),A=c.div.withConfig({displayName:"FilterLabel",componentId:"sc-wstfmv-3"})(["font-size:",";font-weight:",";color:",";margin-bottom:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.xs),et=c.div.withConfig({displayName:"DateRangeContainer",componentId:"sc-wstfmv-4"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),pe=c.input.withConfig({displayName:"DateInput",componentId:"sc-wstfmv-5"})(["padding:"," ",";border:1px solid ",";border-radius:",";font-size:",";flex:1;"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.sm),W=c.div.withConfig({displayName:"TagsContainer",componentId:"sc-wstfmv-6"})(["display:flex;flex-wrap:wrap;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs),O=c(ae).withConfig({displayName:"FilterTag",componentId:"sc-wstfmv-7"})(["cursor:pointer;opacity:",";&:hover{opacity:0.8;}"],({selected:e})=>e?1:.6),tt=c.div.withConfig({displayName:"ButtonContainer",componentId:"sc-wstfmv-8"})(["display:flex;justify-content:flex-end;gap:",";margin-top:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),st=({className:e})=>{const{filters:s,updateFilters:r,resetFilters:i,data:o}=V(),[f,n]=C.useState(s),g=o!=null&&o.trades?[...new Set(o.trades.map(d=>d.symbol))]:[],l=o!=null&&o.trades?[...new Set(o.trades.map(d=>d.strategy))]:[],x=o!=null&&o.trades?[...new Set(o.trades.flatMap(d=>d.tags||[]))]:[],p=["long","short"],h=["win","loss","breakeven"],y=["1m","5m","15m","30m","1h","4h","daily"],T=["pre-market","regular","after-hours"],m=(d,S)=>{n(v=>({...v,dateRange:{...v.dateRange,[d]:S}}))},u=(d,S)=>{n(v=>{const B=v[d]||[],X=B.includes(S)?B.filter(P=>P!==S):[...B,S];return{...v,[d]:X.length>0?X:void 0}})},a=()=>{r(f)},j=()=>{i(),n(s)},b=(d,S)=>{const v=f[d];return v?v.includes(S):!1};return t.jsxs(Ke,{className:e,title:"Filters",variant:"default",padding:"medium",children:[t.jsxs(Ye,{children:[t.jsxs(z,{children:[t.jsx(A,{children:"Date Range"}),t.jsxs(et,{children:[t.jsx(pe,{type:"date",value:f.dateRange.startDate,onChange:d=>m("startDate",d.target.value)}),t.jsx(pe,{type:"date",value:f.dateRange.endDate,onChange:d=>m("endDate",d.target.value)})]})]}),t.jsxs(z,{children:[t.jsx(A,{children:"Direction"}),t.jsx(W,{children:p.map(d=>t.jsx(O,{variant:d==="long"?"success":"error",selected:b("directions",d),onClick:()=>u("directions",d),children:d},d))})]}),t.jsxs(z,{children:[t.jsx(A,{children:"Status"}),t.jsx(W,{children:h.map(d=>t.jsx(O,{variant:d==="win"?"success":d==="loss"?"error":"info",selected:b("statuses",d),onClick:()=>u("statuses",d),children:d},d))})]}),g.length>0&&t.jsxs(z,{children:[t.jsx(A,{children:"Symbols"}),t.jsx(W,{children:g.map(d=>t.jsx(O,{variant:"primary",selected:b("symbols",d),onClick:()=>u("symbols",d),children:d},d))})]}),l.length>0&&t.jsxs(z,{children:[t.jsx(A,{children:"Strategies"}),t.jsx(W,{children:l.map(d=>t.jsx(O,{variant:"secondary",selected:b("strategies",d),onClick:()=>u("strategies",d),children:d},d))})]}),t.jsxs(z,{children:[t.jsx(A,{children:"Timeframe"}),t.jsx(W,{children:y.map(d=>t.jsx(O,{variant:"default",selected:b("timeframes",d),onClick:()=>u("timeframes",d),children:d},d))})]}),t.jsxs(z,{children:[t.jsx(A,{children:"Session"}),t.jsx(W,{children:T.map(d=>t.jsx(O,{variant:"default",selected:b("sessions",d),onClick:()=>u("sessions",d),children:d},d))})]}),x.length>0&&t.jsxs(z,{children:[t.jsx(A,{children:"Tags"}),t.jsx(W,{children:x.map(d=>t.jsx(O,{variant:"info",selected:b("tags",d),onClick:()=>u("tags",d),children:d},d))})]})]}),t.jsxs(tt,{children:[t.jsx(ne,{variant:"outline",onClick:j,children:"Reset"}),t.jsx(ne,{onClick:a,children:"Apply Filters"})]})]})},rt=c.div.withConfig({displayName:"Container",componentId:"sc-11ey2j3-0"})(["display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),N=c.div.withConfig({displayName:"MetricCard",componentId:"sc-11ey2j3-1"})(["background-color:",";border-radius:",";padding:",";display:flex;flex-direction:column;"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.md),I=c.div.withConfig({displayName:"MetricLabel",componentId:"sc-11ey2j3-2"})(["font-size:",";color:",";margin-bottom:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),E=c.div.withConfig({displayName:"MetricValue",componentId:"sc-11ey2j3-3"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e,positive:s,negative:r})=>s?e.colors.profit:r?e.colors.loss:e.colors.textPrimary),ge=c.div.withConfig({displayName:"MetricChange",componentId:"sc-11ey2j3-4"})(["font-size:",";margin-top:",";color:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs,({theme:e,positive:s,negative:r})=>s?e.colors.profit:r?e.colors.loss:e.colors.textSecondary),ot=({className:e})=>{const{data:s}=V();if(!s)return null;const{metrics:r}=s,i=f=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(f),o=f=>`${f.toFixed(2)}%`;return t.jsxs(rt,{className:e,children:[t.jsxs(N,{children:[t.jsx(I,{children:"Total P&L"}),t.jsx(E,{positive:r.totalProfitLoss>0,negative:r.totalProfitLoss<0,children:i(r.totalProfitLoss)})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Win Rate"}),t.jsx(E,{positive:r.winRate>50,negative:r.winRate<50,children:o(r.winRate)}),t.jsxs(ge,{children:[r.winningTrades," / ",r.totalTrades," trades"]})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Profit Factor"}),t.jsx(E,{positive:r.profitFactor>1,negative:r.profitFactor<1,children:r.profitFactor.toFixed(2)})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Expectancy"}),t.jsx(E,{positive:r.expectancy>0,negative:r.expectancy<0,children:i(r.expectancy)})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Average Win"}),t.jsx(E,{positive:!0,children:i(r.averageWin)})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Average Loss"}),t.jsx(E,{negative:!0,children:i(-Math.abs(r.averageLoss))})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Largest Win"}),t.jsx(E,{positive:!0,children:i(r.largestWin)})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Largest Loss"}),t.jsx(E,{negative:!0,children:i(r.largestLoss)})]}),t.jsxs(N,{children:[t.jsx(I,{children:"Total Trades"}),t.jsx(E,{children:r.totalTrades}),t.jsxs(ge,{children:["Avg Duration: ",r.averageDuration.toFixed(0)," min"]})]})]})},nt=c.div.withConfig({displayName:"Container",componentId:"sc-ampus7-0"})(["overflow-x:auto;"]),it=c.table.withConfig({displayName:"Table",componentId:"sc-ampus7-1"})(["width:100%;border-collapse:collapse;font-size:",";"],({theme:e})=>e.fontSizes.sm),at=c.thead.withConfig({displayName:"TableHead",componentId:"sc-ampus7-2"})(["background-color:",";position:sticky;top:0;z-index:1;"],({theme:e})=>e.colors.background),ct=c.tbody.withConfig({displayName:"TableBody",componentId:"sc-ampus7-3"})([""]),he=c.tr.withConfig({displayName:"TableRow",componentId:"sc-ampus7-4"})(["border-bottom:1px solid ",";background-color:",";&:hover{background-color:",";}"],({theme:e})=>e.colors.border,({theme:e,isSelected:s})=>s?`${e.colors.primary}10`:"transparent",({theme:e})=>e.colors.background),F=c.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-ampus7-5"})(["padding:",";text-align:left;font-weight:",";color:",";cursor:",";&:hover{","}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontWeights.semibold,({theme:e,active:s})=>s?e.colors.primary:e.colors.textPrimary,({sortable:e})=>e?"pointer":"default",({sortable:e,theme:s})=>e&&`
      color: ${s.colors.primary};
    `),$=c.td.withConfig({displayName:"TableCell",componentId:"sc-ampus7-6"})(["padding:",";vertical-align:middle;"],({theme:e})=>e.spacing.sm),H=c.span.withConfig({displayName:"SortIcon",componentId:"sc-ampus7-7"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":"↓"),lt=c(te).withConfig({displayName:"DirectionBadge",componentId:"sc-ampus7-8"})(["text-transform:capitalize;"]),dt=c(te).withConfig({displayName:"StatusBadge",componentId:"sc-ampus7-9"})(["text-transform:capitalize;"]),mt=c.div.withConfig({displayName:"TagsContainer",componentId:"sc-ampus7-10"})(["display:flex;flex-wrap:wrap;gap:",";"],({theme:e})=>e.spacing.xs),ue=c.span.withConfig({displayName:"ProfitLoss",componentId:"sc-ampus7-11"})(["color:",";font-weight:",";"],({theme:e,value:s})=>s>0?e.colors.profit:s<0?e.colors.loss:e.colors.textSecondary,({theme:e,value:s})=>s!==0?e.fontWeights.medium:e.fontWeights.regular),ft=c.div.withConfig({displayName:"EmptyState",componentId:"sc-ampus7-12"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),pt=({className:e})=>{const{data:s,selectedTradeId:r,selectTrade:i}=V(),[o,f]=C.useState("entryTime"),[n,g]=C.useState("desc"),l=a=>{o===a?g(n==="asc"?"desc":"asc"):(f(a),g("desc"))},x=C.useMemo(()=>s!=null&&s.trades?[...s.trades].sort((a,j)=>{let b=0;switch(o){case"entryTime":b=new Date(a.entryTime).getTime()-new Date(j.entryTime).getTime();break;case"symbol":b=a.symbol.localeCompare(j.symbol);break;case"direction":b=a.direction.localeCompare(j.direction);break;case"profitLoss":b=a.profitLoss-j.profitLoss;break;case"profitLossPercent":b=a.profitLossPercent-j.profitLossPercent;break;case"status":b=a.status.localeCompare(j.status);break;default:b=0}return n==="asc"?b:-b}):[],[s==null?void 0:s.trades,o,n]),p=a=>{const j=new Date(a);return j.toLocaleDateString()+" "+j.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},h=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(a),y=a=>`${a>0?"+":""}${a.toFixed(2)}%`,T=a=>a==="long"?"success":"error",m=a=>{switch(a){case"win":return"success";case"loss":return"error";case"breakeven":return"info";default:return"default"}},u=a=>{i(a===r?null:a)};return!s||!s.trades||s.trades.length===0?t.jsx(ft,{children:"No trades found for the selected filters."}):t.jsx(nt,{className:e,children:t.jsxs(it,{children:[t.jsx(at,{children:t.jsxs(he,{children:[t.jsxs(F,{sortable:!0,active:o==="entryTime",onClick:()=>l("entryTime"),children:["Date/Time",o==="entryTime"&&t.jsx(H,{direction:n})]}),t.jsxs(F,{sortable:!0,active:o==="symbol",onClick:()=>l("symbol"),children:["Symbol",o==="symbol"&&t.jsx(H,{direction:n})]}),t.jsxs(F,{sortable:!0,active:o==="direction",onClick:()=>l("direction"),children:["Direction",o==="direction"&&t.jsx(H,{direction:n})]}),t.jsx(F,{children:"Entry/Exit"}),t.jsxs(F,{sortable:!0,active:o==="profitLoss",onClick:()=>l("profitLoss"),children:["P&L",o==="profitLoss"&&t.jsx(H,{direction:n})]}),t.jsxs(F,{sortable:!0,active:o==="profitLossPercent",onClick:()=>l("profitLossPercent"),children:["P&L %",o==="profitLossPercent"&&t.jsx(H,{direction:n})]}),t.jsxs(F,{sortable:!0,active:o==="status",onClick:()=>l("status"),children:["Status",o==="status"&&t.jsx(H,{direction:n})]}),t.jsx(F,{children:"Strategy"}),t.jsx(F,{children:"Tags"})]})}),t.jsx(ct,{children:x.map(a=>{var j;return t.jsxs(he,{isSelected:a.id===r,onClick:()=>u(a.id),children:[t.jsx($,{children:p(a.entryTime)}),t.jsx($,{children:a.symbol}),t.jsx($,{children:t.jsx(lt,{direction:a.direction,variant:T(a.direction),size:"small",children:a.direction})}),t.jsxs($,{children:[a.entryPrice.toFixed(2)," → ",a.exitPrice.toFixed(2)]}),t.jsx($,{children:t.jsx(ue,{value:a.profitLoss,children:h(a.profitLoss)})}),t.jsx($,{children:t.jsx(ue,{value:a.profitLossPercent,children:y(a.profitLossPercent)})}),t.jsx($,{children:t.jsx(dt,{status:a.status,variant:m(a.status),size:"small",children:a.status})}),t.jsx($,{children:a.strategy}),t.jsx($,{children:t.jsx(mt,{children:(j=a.tags)==null?void 0:j.map((b,d)=>t.jsx(ae,{size:"small",variant:"default",children:b},d))})})]},a.id)})})]})})},gt=c.div.withConfig({displayName:"Container",componentId:"sc-aabl70-0"})(["overflow-x:auto;"]),ht=c.table.withConfig({displayName:"Table",componentId:"sc-aabl70-1"})(["width:100%;border-collapse:collapse;font-size:",";"],({theme:e})=>e.fontSizes.sm),ut=c.thead.withConfig({displayName:"TableHead",componentId:"sc-aabl70-2"})(["background-color:",";"],({theme:e})=>e.colors.background),xt=c.tbody.withConfig({displayName:"TableBody",componentId:"sc-aabl70-3"})([""]),xe=c.tr.withConfig({displayName:"TableRow",componentId:"sc-aabl70-4"})(["border-bottom:1px solid ",";&:hover{background-color:",";}"],({theme:e})=>e.colors.border,({theme:e})=>e.colors.background),G=c.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-aabl70-5"})(["padding:",";text-align:left;font-weight:",";color:",";cursor:",";&:hover{","}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontWeights.semibold,({theme:e,active:s})=>s?e.colors.primary:e.colors.textPrimary,({sortable:e})=>e?"pointer":"default",({sortable:e,theme:s})=>e&&`
      color: ${s.colors.primary};
    `),J=c.td.withConfig({displayName:"TableCell",componentId:"sc-aabl70-6"})(["padding:",";"],({theme:e})=>e.spacing.sm),Q=c.span.withConfig({displayName:"SortIcon",componentId:"sc-aabl70-7"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":"↓"),yt=c.div.withConfig({displayName:"BarContainer",componentId:"sc-aabl70-8"})(["height:8px;background-color:",";border-radius:",";overflow:hidden;margin-top:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.pill,({theme:e})=>e.spacing.xs),jt=c.div.withConfig({displayName:"Bar",componentId:"sc-aabl70-9"})(["height:100%;width:",";background-color:",";"],({width:e})=>`${e}%`,({theme:e,positive:s})=>s?e.colors.profit:e.colors.loss),ye=c.span.withConfig({displayName:"ProfitLoss",componentId:"sc-aabl70-10"})(["color:",";font-weight:",";"],({theme:e,value:s})=>s>0?e.colors.profit:s<0?e.colors.loss:e.colors.textSecondary,({theme:e,value:s})=>s!==0?e.fontWeights.medium:e.fontWeights.regular),bt=c.div.withConfig({displayName:"EmptyState",componentId:"sc-aabl70-11"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),Y=({className:e,category:s,title:r})=>{const{data:i}=V(),[o,f]=C.useState("profitLoss"),[n,g]=C.useState("desc");if(!i)return null;let l=[];switch(s){case"symbol":l=i.symbolPerformance;break;case"strategy":l=i.strategyPerformance;break;case"timeframe":l=i.timeframePerformance;break;case"session":l=i.sessionPerformance;break}if(!l||l.length===0)return t.jsxs(bt,{children:["No ",s," performance data available."]});const x=m=>{o===m?g(n==="asc"?"desc":"asc"):(f(m),g("desc"))},p=[...l].sort((m,u)=>{let a=0;switch(o){case"value":a=m.value.localeCompare(u.value);break;case"trades":a=m.trades-u.trades;break;case"winRate":a=m.winRate-u.winRate;break;case"profitLoss":a=m.profitLoss-u.profitLoss;break;case"averageProfitLoss":a=m.averageProfitLoss-u.averageProfitLoss;break;default:a=0}return n==="asc"?a:-a}),h=Math.max(...l.map(m=>Math.abs(m.profitLoss))),y=m=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(m),T=m=>`${m.toFixed(2)}%`;return t.jsx(gt,{className:e,children:t.jsxs(ht,{children:[t.jsx(ut,{children:t.jsxs(xe,{children:[t.jsxs(G,{sortable:!0,active:o==="value",onClick:()=>x("value"),children:[r,o==="value"&&t.jsx(Q,{direction:n})]}),t.jsxs(G,{sortable:!0,active:o==="trades",onClick:()=>x("trades"),children:["Trades",o==="trades"&&t.jsx(Q,{direction:n})]}),t.jsxs(G,{sortable:!0,active:o==="winRate",onClick:()=>x("winRate"),children:["Win Rate",o==="winRate"&&t.jsx(Q,{direction:n})]}),t.jsxs(G,{sortable:!0,active:o==="profitLoss",onClick:()=>x("profitLoss"),children:["P&L",o==="profitLoss"&&t.jsx(Q,{direction:n})]}),t.jsxs(G,{sortable:!0,active:o==="averageProfitLoss",onClick:()=>x("averageProfitLoss"),children:["Avg P&L",o==="averageProfitLoss"&&t.jsx(Q,{direction:n})]})]})}),t.jsx(xt,{children:p.map((m,u)=>t.jsxs(xe,{children:[t.jsx(J,{children:m.value}),t.jsx(J,{children:m.trades}),t.jsx(J,{children:T(m.winRate)}),t.jsxs(J,{children:[t.jsx(ye,{value:m.profitLoss,children:y(m.profitLoss)}),t.jsx(yt,{children:t.jsx(jt,{width:Math.min(100,Math.abs(m.profitLoss)/h*100),positive:m.profitLoss>=0})})]}),t.jsx(J,{children:t.jsx(ye,{value:m.averageProfitLoss,children:y(m.averageProfitLoss)})})]},u))})]})})},wt=c.div.withConfig({displayName:"Container",componentId:"sc-1khhabm-0"})([""]),vt=c.div.withConfig({displayName:"ChartContainer",componentId:"sc-1khhabm-1"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md),Ct=c.div.withConfig({displayName:"TimeSlot",componentId:"sc-1khhabm-2"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Tt=c.div.withConfig({displayName:"TimeSlotHeader",componentId:"sc-1khhabm-3"})(["display:flex;justify-content:space-between;align-items:center;"]),St=c.div.withConfig({displayName:"TimeSlotLabel",componentId:"sc-1khhabm-4"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Dt=c.div.withConfig({displayName:"TimeSlotMetrics",componentId:"sc-1khhabm-5"})(["display:flex;gap:",";font-size:",";color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textSecondary),oe=c.span.withConfig({displayName:"MetricValue",componentId:"sc-1khhabm-6"})(["color:",";font-weight:",";"],({theme:e,positive:s,negative:r})=>s?e.colors.profit:r?e.colors.loss:e.colors.textSecondary,({theme:e})=>e.fontWeights.medium),Lt=c.div.withConfig({displayName:"BarContainer",componentId:"sc-1khhabm-7"})(["height:24px;background-color:",";border-radius:",";overflow:hidden;position:relative;"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm),kt=c.div.withConfig({displayName:"Bar",componentId:"sc-1khhabm-8"})(["height:100%;width:",";background-color:",";transition:width 0.3s ease;"],({width:e})=>`${e}%`,({theme:e,positive:s})=>s?e.colors.profit:e.colors.loss),Pt=c.div.withConfig({displayName:"BarLabel",componentId:"sc-1khhabm-9"})(["position:absolute;top:0;left:",";height:100%;display:flex;align-items:center;font-size:",";color:",";font-weight:",";text-shadow:0 0 2px rgba(0,0,0,0.5);"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textInverse,({theme:e})=>e.fontWeights.medium),Mt=c.div.withConfig({displayName:"EmptyState",componentId:"sc-1khhabm-10"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),ee=({className:e,timeType:s,title:r})=>{const{data:i}=V();if(!i)return null;const o=s==="timeOfDay"?i.timeOfDayPerformance:i.dayOfWeekPerformance;if(!o||o.length===0)return t.jsxs(Mt,{children:["No ",s," performance data available."]});const f=Math.max(...o.map(l=>Math.abs(l.profitLoss))),n=l=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(l),g=l=>`${l.toFixed(2)}%`;return t.jsx(wt,{className:e,children:t.jsx(vt,{children:o.map((l,x)=>t.jsxs(Ct,{children:[t.jsxs(Tt,{children:[t.jsx(St,{children:l.timeSlot}),t.jsxs(Dt,{children:[t.jsxs("div",{children:["Trades: ",t.jsx(oe,{children:l.trades})]}),t.jsxs("div",{children:["Win Rate: ",t.jsx(oe,{positive:l.winRate>50,negative:l.winRate<50,children:g(l.winRate)})]}),t.jsxs("div",{children:["P&L: ",t.jsx(oe,{positive:l.profitLoss>0,negative:l.profitLoss<0,children:n(l.profitLoss)})]})]})]}),t.jsx(Lt,{children:t.jsx(kt,{width:Math.min(100,Math.abs(l.profitLoss)/f*100),positive:l.profitLoss>=0,children:l.profitLoss!==0&&t.jsx(Pt,{children:n(l.profitLoss)})})})]},x))})})},Nt=c(ie).withConfig({displayName:"Container",componentId:"sc-1an4q20-0"})(["margin-top:",";"],({theme:e})=>e.spacing.md),It=c.div.withConfig({displayName:"DetailGrid",componentId:"sc-1an4q20-1"})(["display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),L=c.div.withConfig({displayName:"DetailSection",componentId:"sc-1an4q20-2"})(["margin-bottom:",";"],({theme:e})=>e.spacing.md),D=c.div.withConfig({displayName:"DetailLabel",componentId:"sc-1an4q20-3"})(["font-size:",";color:",";margin-bottom:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs),k=c.div.withConfig({displayName:"DetailValue",componentId:"sc-1an4q20-4"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Et=c.div.withConfig({displayName:"ProfitLoss",componentId:"sc-1an4q20-5"})(["font-size:",";font-weight:",";color:",";margin-bottom:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e,value:s})=>s>0?e.colors.profit:s<0?e.colors.loss:e.colors.textSecondary,({theme:e})=>e.spacing.sm),Ft=c.div.withConfig({displayName:"TagsContainer",componentId:"sc-1an4q20-6"})(["display:flex;flex-wrap:wrap;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs),$t=c.div.withConfig({displayName:"Notes",componentId:"sc-1an4q20-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";white-space:pre-wrap;"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),Rt=c.div.withConfig({displayName:"EmptyState",componentId:"sc-1an4q20-8"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),zt=({className:e})=>{const{data:s,selectedTradeId:r}=V();if(!s||!r)return null;const i=s.trades.find(p=>p.id===r);if(!i)return t.jsx(Rt,{children:"Trade not found."});const o=p=>{const h=new Date(p);return h.toLocaleDateString()+" "+h.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},f=p=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(p),n=p=>`${p>0?"+":""}${p.toFixed(2)}%`,g=p=>p==="long"?"success":"error",l=p=>{switch(p){case"win":return"success";case"loss":return"error";case"breakeven":return"info";default:return"default"}},x=(p,h)=>{const y=new Date(p).getTime(),m=new Date(h).getTime()-y,u=Math.floor(m/(1e3*60)),a=Math.floor(u/60),j=u%60;return a>0?`${a}h ${j}m`:`${u}m`};return t.jsxs(Nt,{className:e,title:`${i.symbol} Trade Details`,variant:"default",padding:"medium",children:[t.jsxs(Et,{value:i.profitLoss,children:[f(i.profitLoss)," (",n(i.profitLossPercent),")"]}),t.jsxs(It,{children:[t.jsxs(L,{children:[t.jsx(D,{children:"Direction"}),t.jsx(k,{children:t.jsx(te,{variant:g(i.direction),size:"small",children:i.direction})})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Status"}),t.jsx(k,{children:t.jsx(te,{variant:l(i.status),size:"small",children:i.status})})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Entry Time"}),t.jsx(k,{children:o(i.entryTime)})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Exit Time"}),t.jsx(k,{children:o(i.exitTime)})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Duration"}),t.jsx(k,{children:x(i.entryTime,i.exitTime)})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Entry Price"}),t.jsx(k,{children:i.entryPrice.toFixed(2)})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Exit Price"}),t.jsx(k,{children:i.exitPrice.toFixed(2)})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Quantity"}),t.jsx(k,{children:i.quantity})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Timeframe"}),t.jsx(k,{children:i.timeframe})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Session"}),t.jsx(k,{children:i.session})]}),t.jsxs(L,{children:[t.jsx(D,{children:"Strategy"}),t.jsx(k,{children:i.strategy})]})]}),i.tags&&i.tags.length>0&&t.jsxs(L,{children:[t.jsx(D,{children:"Tags"}),t.jsx(Ft,{children:i.tags.map((p,h)=>t.jsx(ae,{variant:"info",size:"small",children:p},h))})]}),i.notes&&t.jsxs($t,{children:[t.jsx(D,{children:"Notes"}),t.jsx(k,{children:i.notes})]})]})},At=c.div.withConfig({displayName:"PageContainer",componentId:"sc-1vvcail-0"})(["display:flex;flex-direction:column;gap:",";max-width:1200px;margin:0 auto;padding:",";"],({theme:e})=>e.spacing.lg,({theme:e})=>e.spacing.lg),Wt=c.div.withConfig({displayName:"PageHeader",componentId:"sc-1vvcail-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";"],({theme:e})=>e.spacing.md),Ot=c.h1.withConfig({displayName:"Title",componentId:"sc-1vvcail-2"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.xxl,({theme:e})=>e.colors.textPrimary),Vt=c.div.withConfig({displayName:"ViewTabs",componentId:"sc-1vvcail-3"})(["display:flex;gap:",";margin-bottom:",";border-bottom:1px solid ",";padding-bottom:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border,({theme:e})=>e.spacing.xs),_=c.button.withConfig({displayName:"ViewTab",componentId:"sc-1vvcail-4"})(["background:none;border:none;padding:",";font-size:",";font-weight:",";color:",";cursor:pointer;border-bottom:2px solid ",";transition:all ",";&:hover{color:",";}"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.md,({theme:e,active:s})=>s?e.fontWeights.semibold:e.fontWeights.regular,({theme:e,active:s})=>s?e.colors.primary:e.colors.textSecondary,({theme:e,active:s})=>s?e.colors.primary:"transparent",({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary),Bt=()=>{const{data:e,isLoading:s,error:r,selectedTradeId:i,preferences:o,updatePreferences:f}=V(),[n,g]=C.useState(o.defaultView||"summary"),l=p=>{g(p),f({defaultView:p})},x=()=>{switch(n){case"summary":return t.jsxs(t.Fragment,{children:[t.jsx(M,{title:"Performance Summary",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.metrics),emptyMessage:"No performance data available for the selected filters.",children:t.jsx(ot,{})}),t.jsx(M,{title:"Performance by Time of Day",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.timeOfDayPerformance)||e.timeOfDayPerformance.length===0,emptyMessage:"No time of day performance data available for the selected filters.",children:t.jsx(ee,{timeType:"timeOfDay",title:"Time of Day"})}),t.jsx(M,{title:"Performance by Day of Week",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.dayOfWeekPerformance)||e.dayOfWeekPerformance.length===0,emptyMessage:"No day of week performance data available for the selected filters.",children:t.jsx(ee,{timeType:"dayOfWeek",title:"Day of Week"})})]});case"trades":return t.jsxs(t.Fragment,{children:[t.jsx(M,{title:"Trades",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.trades)||e.trades.length===0,emptyMessage:"No trades available for the selected filters.",children:t.jsx(pt,{})}),i&&t.jsx(zt,{})]});case"symbols":return t.jsx(M,{title:"Performance by Symbol",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.symbolPerformance)||e.symbolPerformance.length===0,emptyMessage:"No symbol performance data available for the selected filters.",children:t.jsx(Y,{category:"symbol",title:"Symbol"})});case"strategies":return t.jsx(M,{title:"Performance by Strategy",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.strategyPerformance)||e.strategyPerformance.length===0,emptyMessage:"No strategy performance data available for the selected filters.",children:t.jsx(Y,{category:"strategy",title:"Strategy"})});case"timeframes":return t.jsxs(t.Fragment,{children:[t.jsx(M,{title:"Performance by Timeframe",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.timeframePerformance)||e.timeframePerformance.length===0,emptyMessage:"No timeframe performance data available for the selected filters.",children:t.jsx(Y,{category:"timeframe",title:"Timeframe"})}),t.jsx(M,{title:"Performance by Session",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.sessionPerformance)||e.sessionPerformance.length===0,emptyMessage:"No session performance data available for the selected filters.",children:t.jsx(Y,{category:"session",title:"Session"})})]});case"time":return t.jsxs(t.Fragment,{children:[t.jsx(M,{title:"Performance by Time of Day",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.timeOfDayPerformance)||e.timeOfDayPerformance.length===0,emptyMessage:"No time of day performance data available for the selected filters.",children:t.jsx(ee,{timeType:"timeOfDay",title:"Time of Day"})}),t.jsx(M,{title:"Performance by Day of Week",isLoading:s,hasError:!!r,errorMessage:r||"",isEmpty:!(e!=null&&e.dayOfWeekPerformance)||e.dayOfWeekPerformance.length===0,emptyMessage:"No day of week performance data available for the selected filters.",children:t.jsx(ee,{timeType:"dayOfWeek",title:"Day of Week"})})]});default:return null}};return t.jsxs(At,{children:[t.jsx(Wt,{children:t.jsx(Ot,{children:"Trade Analysis"})}),t.jsx(st,{}),t.jsxs(Vt,{children:[t.jsx(_,{active:n==="summary",onClick:()=>l("summary"),children:"Summary"}),t.jsx(_,{active:n==="trades",onClick:()=>l("trades"),children:"Trades"}),t.jsx(_,{active:n==="symbols",onClick:()=>l("symbols"),children:"Symbols"}),t.jsx(_,{active:n==="strategies",onClick:()=>l("strategies"),children:"Strategies"}),t.jsx(_,{active:n==="timeframes",onClick:()=>l("timeframes"),children:"Timeframes"}),t.jsx(_,{active:n==="time",onClick:()=>l("time"),children:"Time Analysis"})]}),x()]})},Gt=()=>t.jsx(Ze,{children:t.jsx(Bt,{})});export{Gt as default};
//# sourceMappingURL=TradeAnalysis-72da3a81.js.map
