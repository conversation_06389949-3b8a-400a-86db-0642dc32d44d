import{j as a}from"./client-4c27269c.js";import{C as s,s as e,U as h}from"./styled-components-3ebafa9a.js";const w={small:s(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:o})=>`${o.spacing.xxs} ${o.spacing.xs}`,({theme:o})=>o.fontSizes.xs,({dot:o})=>o?"8px":"20px"),medium:s(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:o})=>`${o.spacing.xs} ${o.spacing.sm}`,({theme:o})=>o.fontSizes.sm,({dot:o})=>o?"10px":"24px"),large:s(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:o})=>`${o.spacing.sm} ${o.spacing.md}`,({theme:o})=>o.fontSizes.md,({dot:o})=>o?"12px":"32px")},C=(o,n,d=!1)=>s(["",""],({theme:r})=>{let c,i,t;switch(o){case"primary":c=n?r.colors.primary:`${r.colors.primary}20`,i=n?r.colors.textInverse:r.colors.primary,t=r.colors.primary;break;case"secondary":c=n?r.colors.secondary:`${r.colors.secondary}20`,i=n?r.colors.textInverse:r.colors.secondary,t=r.colors.secondary;break;case"success":c=n?r.colors.success:`${r.colors.success}20`,i=n?r.colors.textInverse:r.colors.success,t=r.colors.success;break;case"warning":c=n?r.colors.warning:`${r.colors.warning}20`,i=n?r.colors.textInverse:r.colors.warning,t=r.colors.warning;break;case"error":c=n?r.colors.error:`${r.colors.error}20`,i=n?r.colors.textInverse:r.colors.error,t=r.colors.error;break;case"info":c=n?r.colors.info:`${r.colors.info}20`,i=n?r.colors.textInverse:r.colors.info,t=r.colors.info;break;case"neutral":c=n?r.colors.textSecondary:`${r.colors.textSecondary}10`,i=n?r.colors.textInverse:r.colors.textSecondary,t=r.colors.textSecondary;break;default:c=n?r.colors.textSecondary:`${r.colors.textSecondary}20`,i=n?r.colors.textInverse:r.colors.textSecondary,t=r.colors.textSecondary}return d?`
          background-color: transparent;
          color: ${t};
          border: 1px solid ${t};
        `:`
        background-color: ${c};
        color: ${i};
        border: 1px solid transparent;
      `}),y=e.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),k=e(y).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:o})=>o.spacing.xxs),S=e(y).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:o})=>o.spacing.xxs),I=e.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:o})=>o?"inline-flex":"flex",({theme:o,rounded:n,dot:d})=>d?"50%":n?"9999px":o.borderRadius.sm,({theme:o})=>o.fontWeights.medium,({size:o})=>w[o],({variant:o,solid:n,outlined:d})=>C(o,n,d||!1),({dot:o})=>o&&s(["padding:0;height:8px;width:8px;"]),({counter:o})=>o&&s(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:o})=>o&&s(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:n})=>n.transitions.fast)),J=({children:o,variant:n="default",size:d="medium",solid:r=!1,className:c,onClick:i,rounded:t=!1,dot:l=!1,counter:p=!1,outlined:x=!1,startIcon:g,endIcon:m,max:f,inline:u=!0})=>{let b=o;return p&&typeof o=="number"&&f!==void 0&&o>f&&(b=`${f}+`),a.jsx(I,{variant:n,size:d,solid:r,clickable:!!i,className:c,onClick:i,rounded:t,dot:l,counter:p,outlined:x,inline:u,children:!l&&a.jsxs(a.Fragment,{children:[g&&a.jsx(k,{children:g}),b,m&&a.jsx(S,{children:m})]})})},$=h(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),j=e.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],$,({theme:o})=>o.spacing.xs),z={small:s(["padding:",";font-size:",";min-height:32px;"],({theme:o})=>`${o.spacing.xxs} ${o.spacing.sm}`,({theme:o})=>o.fontSizes.xs),medium:s(["padding:",";font-size:",";min-height:40px;"],({theme:o})=>`${o.spacing.xs} ${o.spacing.md}`,({theme:o})=>o.fontSizes.sm),large:s(["padding:",";font-size:",";min-height:48px;"],({theme:o})=>`${o.spacing.sm} ${o.spacing.lg}`,({theme:o})=>o.fontSizes.md)},N={primary:s(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:o})=>o.colors.primary,({theme:o})=>o.colors.textPrimary||o.colors.textInverse||"#fff",({theme:o})=>o.colors.primaryDark,({theme:o})=>o.colors.primaryDark),secondary:s(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:o})=>o.colors.secondary,({theme:o})=>o.colors.textPrimary||o.colors.textInverse||"#fff",({theme:o})=>o.colors.secondaryDark,({theme:o})=>o.colors.secondaryDark),outline:s(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"],({theme:o})=>o.colors.primary,({theme:o})=>o.colors.primary),text:s(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"],({theme:o})=>o.colors.primary,({theme:o})=>o.spacing.xs,({theme:o})=>o.spacing.xs),success:s(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:o})=>o.colors.success,({theme:o})=>o.colors.textInverse||"#fff",({theme:o})=>o.colors.success),danger:s(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:o})=>o.colors.error,({theme:o})=>o.colors.textInverse||"#fff",({theme:o})=>o.colors.error)},Y=e.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:o})=>o.borderRadius.sm,({theme:o})=>{var n;return((n=o.fontWeights)==null?void 0:n.medium)||500},({theme:o})=>{var n;return((n=o.transitions)==null?void 0:n.fast)||"0.2s ease"},({size:o="medium"})=>z[o],({variant:o="primary"})=>N[o],({fullWidth:o})=>o&&s(["width:100%;"]),({$hasStartIcon:o})=>o&&s(["& > *:first-child{margin-right:",";}"],({theme:n})=>n.spacing.xs),({$hasEndIcon:o})=>o&&s(["& > *:last-child{margin-left:",";}"],({theme:n})=>n.spacing.xs)),B=e.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),K=({children:o,variant:n="primary",disabled:d=!1,loading:r=!1,size:c="medium",fullWidth:i=!1,startIcon:t,endIcon:l,onClick:p,className:x,type:g="button",...m})=>a.jsx(Y,{variant:n,disabled:d||r,size:c,fullWidth:i,onClick:p,className:x,type:g,$hasStartIcon:!!t&&!r,$hasEndIcon:!!l&&!r,...m,children:a.jsxs(B,{children:[r&&a.jsx(j,{}),!r&&t,o,!r&&l]})}),E={none:s(["padding:0;"]),small:s(["padding:",";"],({theme:o})=>o.spacing.sm),medium:s(["padding:",";"],({theme:o})=>o.spacing.md),large:s(["padding:",";"],({theme:o})=>o.spacing.lg)},L={default:s(["background-color:",";"],({theme:o})=>o.colors.surface),primary:s(["background-color:","10;border-color:","30;"],({theme:o})=>o.colors.primary,({theme:o})=>o.colors.primary),secondary:s(["background-color:","10;border-color:","30;"],({theme:o})=>o.colors.secondary,({theme:o})=>o.colors.secondary),outlined:s(["background-color:transparent;border:1px solid ",";"],({theme:o})=>o.colors.border),elevated:s(["background-color:",";box-shadow:",";border:none;"],({theme:o})=>o.colors.surface,({theme:o})=>o.shadows.md)},H=e.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:o})=>o.borderRadius.md,({theme:o})=>o.transitions.fast,({bordered:o,theme:n})=>o&&s(["border:1px solid ",";"],n.colors.border),({padding:o})=>E[o],({variant:o})=>L[o],({clickable:o})=>o&&s(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:n})=>n.shadows.medium)),R=e.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:o})=>o.spacing.md),D=e.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),q=e.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:o})=>o.fontSizes.lg,({theme:o})=>o.fontWeights.semibold,({theme:o})=>o.colors.textPrimary),A=e.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:o})=>o.spacing.xs,({theme:o})=>o.fontSizes.sm,({theme:o})=>o.colors.textSecondary),F=e.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:o})=>o.spacing.sm),P=e.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),W=e.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:o})=>o.spacing.md,({theme:o})=>o.spacing.md,({theme:o})=>o.colors.border),O=e.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:o})=>`${o.colors.background}80`),T=e.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:o})=>o.spacing.md,({theme:o})=>o.colors.error,({theme:o})=>o.borderRadius.sm,({theme:o})=>o.colors.error,({theme:o})=>o.spacing.md),U=e.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:o})=>o.colors.background,({theme:o})=>o.colors.primary),M=({children:o,title:n,subtitle:d,bordered:r=!0,variant:c="default",padding:i="medium",className:t,footer:l,actions:p,isLoading:x=!1,hasError:g=!1,errorMessage:m="An error occurred",clickable:f=!1,onClick:u,...b})=>{const v=n||d||p;return a.jsxs(H,{bordered:r,variant:c,padding:i,clickable:f,className:t,onClick:f?u:void 0,...b,children:[x&&a.jsx(O,{children:a.jsx(U,{})}),v&&a.jsxs(R,{children:[a.jsxs(D,{children:[n&&a.jsx(q,{children:n}),d&&a.jsx(A,{children:d})]}),p&&a.jsx(F,{children:p})]}),g&&a.jsx(T,{children:a.jsx("p",{children:m})}),a.jsx(P,{children:o}),l&&a.jsx(W,{children:l})]})};export{J as B,M as C,K as a};
//# sourceMappingURL=Card-a75b9d5e.js.map
