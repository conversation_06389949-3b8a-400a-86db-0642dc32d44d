import{j as o}from"./client-4c27269c.js";import{r as c}from"./react-60374de9.js";import{s as t}from"./styled-components-3ebafa9a.js";const x=t.div.withConfig({displayName:"Container",componentId:"sc-6zono4-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:",";"],({theme:e})=>e.spacing.md),y=t.div.withConfig({displayName:"MetricCard",componentId:"sc-6zono4-1"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.shadows.sm),C=t.h3.withConfig({displayName:"MetricTitle",componentId:"sc-6zono4-2"})(["font-size:",";color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),z=t.div.withConfig({displayName:"MetricValue",componentId:"sc-6zono4-3"})(["font-size:",";font-weight:600;color:",";"],({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.textPrimary),k=t.div.withConfig({displayName:"LoadingIndicator",componentId:"sc-6zono4-4"})(["color:",";font-style:italic;"],({theme:e})=>e.colors.textSecondary),R=({metrics:e,isLoading:s=!1})=>s?o.jsx(x,{children:[1,2,3,4].map(n=>o.jsxs(y,{children:[o.jsx(C,{children:"Loading..."}),o.jsx(k,{children:"Fetching data..."})]},n))}):o.jsx(x,{children:e.map((n,i)=>o.jsxs(y,{children:[o.jsx(C,{children:n.title}),o.jsx(z,{children:n.value})]},i))}),j=t.div.withConfig({displayName:"ChartContainer",componentId:"sc-141grlc-0"})(["height:300px;position:relative;width:100%;"]),b=t.div.withConfig({displayName:"PlaceholderText",componentId:"sc-141grlc-1"})(["position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:",";"],({theme:e})=>e.colors.textSecondary),L=({data:e})=>!e||e.length===0?o.jsx(j,{children:o.jsx(b,{children:"No chart data available"})}):o.jsx(j,{children:o.jsxs(b,{children:["Chart would render ",e.length," data points from ",e[0].date," to"," ",e[e.length-1].date]})}),H=t.div.withConfig({displayName:"Container",componentId:"sc-1d6jcn4-0"})(["width:100%;"]),A=t.table.withConfig({displayName:"Table",componentId:"sc-1d6jcn4-1"})(["width:100%;border-collapse:collapse;"]),E=t.thead.withConfig({displayName:"TableHead",componentId:"sc-1d6jcn4-2"})(["border-bottom:1px solid ",";"],({theme:e})=>e.colors.border),w=t.tr.withConfig({displayName:"TableRow",componentId:"sc-1d6jcn4-3"})(["&:nth-child(even){background-color:",";}&:hover{background-color:",";}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.hover),l=t.th.withConfig({displayName:"TableHeader",componentId:"sc-1d6jcn4-4"})(["text-align:left;padding:",";font-size:",";font-weight:500;color:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),h=t.td.withConfig({displayName:"TableCell",componentId:"sc-1d6jcn4-5"})(["padding:",";font-size:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontSizes.md),F=t(h).withConfig({displayName:"DirectionCell",componentId:"sc-1d6jcn4-6"})(["color:",";"],({theme:e,direction:s})=>s==="long"?e.colors.success:e.colors.danger),$=t(h).withConfig({displayName:"ResultCell",componentId:"sc-1d6jcn4-7"})(["color:",";"],({theme:e,result:s})=>{switch(s){case"win":return e.colors.success;case"loss":return e.colors.danger;default:return e.colors.textSecondary}}),O=t(h).withConfig({displayName:"ProfitCell",componentId:"sc-1d6jcn4-8"})(["color:",";"],({theme:e,value:s})=>s>0?e.colors.success:s<0?e.colors.danger:e.colors.textSecondary),T=t.div.withConfig({displayName:"LoadingIndicator",componentId:"sc-1d6jcn4-9"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),G=({trades:e,isLoading:s=!1})=>s?o.jsx(T,{children:"Loading recent trades..."}):!e||e.length===0?o.jsx(T,{children:"No recent trades found"}):o.jsx(H,{children:o.jsxs(A,{children:[o.jsx(E,{children:o.jsxs(w,{children:[o.jsx(l,{children:"Date"}),o.jsx(l,{children:"Symbol"}),o.jsx(l,{children:"Direction"}),o.jsx(l,{children:"Result"}),o.jsx(l,{children:"Profit/Loss"})]})}),o.jsx("tbody",{children:e.map(n=>o.jsxs(w,{children:[o.jsx(h,{children:n.date}),o.jsx(h,{children:n.symbol}),o.jsx(F,{direction:n.direction,children:n.direction==="long"?"▲ Long":"▼ Short"}),o.jsx($,{result:n.result,children:n.result.charAt(0).toUpperCase()+n.result.slice(1)}),o.jsxs(O,{value:n.profit,children:["$",n.profit.toFixed(2)]})]},n.id))})]})}),V=()=>{const[e,s]=c.useState([{title:"Win Rate",value:"65%"},{title:"Profit Factor",value:"2.3"},{title:"Net Profit",value:"$12,500"},{title:"Total Trades",value:"120"}]),[n,i]=c.useState([]),[g,S]=c.useState([]),[N,f]=c.useState(!0),[v,u]=c.useState(null),D=()=>{const a=[],p=new Date;for(let d=30;d>=0;d--){const r=new Date(p);r.setDate(r.getDate()-d),a.push({date:r.toISOString().split("T")[0],value:1e4+Math.random()*5e3})}return a},P=()=>{const a=[],p=new Date,d=["AAPL","MSFT","GOOGL","TSLA","AMZN"];for(let r=0;r<5;r++){const m=new Date(p);m.setDate(m.getDate()-r),a.push({id:`trade-${r}`,date:m.toISOString().split("T")[0],symbol:d[Math.floor(Math.random()*d.length)],direction:Math.random()>.5?"long":"short",result:Math.random()>.35?"win":Math.random()>.5?"loss":"breakeven",profit:Math.random()>.35?Math.random()*1e3:-Math.random()*500})}return a},M=c.useCallback(async()=>{f(!0),u(null);try{await new Promise(a=>setTimeout(a,1e3)),i(D()),S(P())}catch(a){console.error("Error fetching dashboard data:",a),u("Failed to load dashboard data")}finally{f(!1)}},[]);return{metrics:e,chartData:n,recentTrades:g,isLoading:N,error:v,fetchDashboardData:M}},U=t.div.withConfig({displayName:"PageContainer",componentId:"sc-1fakc0y-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.lg),W=t.div.withConfig({displayName:"PageHeader",componentId:"sc-1fakc0y-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";"],({theme:e})=>e.spacing.lg),Z=t.h1.withConfig({displayName:"Title",componentId:"sc-1fakc0y-2"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.xxl,({theme:e})=>e.colors.textPrimary),q=t.div.withConfig({displayName:"ChartSection",componentId:"sc-1fakc0y-3"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.shadows.sm),I=t.h2.withConfig({displayName:"ChartTitle",componentId:"sc-1fakc0y-4"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.lg),B=t.div.withConfig({displayName:"ChartPlaceholder",componentId:"sc-1fakc0y-5"})(["height:300px;background-color:",";border-radius:",";display:flex;align-items:center;justify-content:center;color:",";"],({theme:e})=>e.colors.chartGrid,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textSecondary),J=t.div.withConfig({displayName:"RecentTradesSection",componentId:"sc-1fakc0y-6"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.shadows.sm),Y=()=>{const{metrics:e,chartData:s,recentTrades:n,isLoading:i,fetchDashboardData:g}=V();return c.useEffect(()=>{g()},[g]),o.jsxs(U,{children:[o.jsx(W,{children:o.jsx(Z,{children:"Trading Dashboard"})}),o.jsx(R,{metrics:e,isLoading:i}),o.jsxs(q,{children:[o.jsx(I,{children:"Performance"}),i?o.jsx(B,{children:"Loading chart data..."}):o.jsx(L,{data:s})]}),o.jsxs(J,{children:[o.jsx(I,{children:"Recent Trades"}),o.jsx(G,{trades:n,isLoading:i})]})]})};export{Y as default};
//# sourceMappingURL=Dashboard-c9f9e5d6.js.map
