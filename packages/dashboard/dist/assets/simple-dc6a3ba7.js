import{j as e,c as o}from"./client-4c27269c.js";import{R as n}from"./react-60374de9.js";const i=()=>e.jsxs("div",{style:{padding:"20px",background:"red",color:"white",fontFamily:"Arial, sans-serif",height:"100vh",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[e.jsx("h1",{children:"Simple App Working!"}),e.jsx("p",{children:"This is a minimal test component with no dependencies"})]}),r=document.getElementById("root");if(!r){console.error("Root element not found, creating a fallback element");const t=document.createElement("div");t.id="root",document.body.appendChild(t)}const c=o.createRoot(document.getElementById("root"));c.render(e.jsx(n.StrictMode,{children:e.jsx(i,{})}));
//# sourceMappingURL=simple-dc6a3ba7.js.map
