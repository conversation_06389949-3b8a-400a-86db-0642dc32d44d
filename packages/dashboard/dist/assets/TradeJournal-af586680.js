import{j as n}from"./client-4c27269c.js";import{r as j,R as D}from"./react-60374de9.js";import{s as a,U as I}from"./styled-components-3ebafa9a.js";import{t as E}from"./tradeStorage-0955231a.js";import{L as M}from"./router-e715efa2.js";const q=()=>{const[e,i]=j.useState({trades:[],isLoading:!0,error:null});return j.useEffect(()=>{(async()=>{try{i(x=>({...x,isLoading:!0,error:null}));const c=[...await E.getAllTrades()].sort((x,y)=>new Date(y.date).getTime()-new Date(x.date).getTime());i({trades:c,isLoading:!1,error:null})}catch(d){console.error("Error fetching trades:",d),i(c=>({...c,isLoading:!1,error:"Failed to load trades. Please try again."}))}})()},[]),{...e,refreshTrades:async()=>{try{i(c=>({...c,isLoading:!0,error:null}));const d=[...await E.getAllTrades()].sort((c,x)=>new Date(x.date).getTime()-new Date(c.date).getTime());i({trades:d,isLoading:!1,error:null})}catch(p){console.error("Error refreshing trades:",p),i(d=>({...d,isLoading:!1,error:"Failed to refresh trades. Please try again."}))}}}};function R(e){const[i,l]=j.useState({symbol:"",direction:"",setup:"",modelType:"",result:"",dateFrom:"",dateTo:"",primarySetupType:"",secondarySetupType:"",liquidityTaken:"",patternQualityMin:"",patternQualityMax:"",dolType:"",dolEffectivenessMin:"",dolEffectivenessMax:""}),p=r=>{const{name:s,value:b}=r.target;l(A=>({...A,[s]:b}))},d=()=>{l({symbol:"",direction:"",setup:"",modelType:"",result:"",dateFrom:"",dateTo:"",primarySetupType:"",secondarySetupType:"",liquidityTaken:"",patternQualityMin:"",patternQualityMax:"",dolType:"",dolEffectivenessMin:"",dolEffectivenessMax:""})},c=j.useMemo(()=>e?e.filter(r=>{if(i.symbol&&!r.symbol.toLowerCase().includes(i.symbol.toLowerCase())||i.direction&&r.direction!==i.direction||i.setup&&r.setup!==i.setup||i.modelType&&r.modelType!==i.modelType)return!1;if(i.result){const s=r.profitLoss>0;if(i.result==="win"&&!s||i.result==="loss"&&s)return!1}if(i.dateFrom){const s=new Date(r.date),b=new Date(i.dateFrom);if(s<b)return!1}if(i.dateTo){const s=new Date(r.date),b=new Date(i.dateTo);if(b.setHours(23,59,59,999),s>b)return!1}if(i.primarySetupType&&r.primarySetupType!==i.primarySetupType||i.secondarySetupType&&r.secondarySetupType!==i.secondarySetupType||i.liquidityTaken&&r.liquidityTaken!==i.liquidityTaken)return!1;if(i.patternQualityMin&&r.patternQuality){const s=parseInt(i.patternQualityMin);if(!isNaN(s)&&r.patternQuality<s)return!1}if(i.patternQualityMax&&r.patternQuality){const s=parseInt(i.patternQualityMax);if(!isNaN(s)&&r.patternQuality>s)return!1}if(i.dolType&&r.dolAnalysis&&r.dolAnalysis.dolType!==i.dolType)return!1;if(i.dolEffectivenessMin&&r.dolAnalysis){const s=parseInt(i.dolEffectivenessMin);if(!isNaN(s)&&r.dolAnalysis.effectiveness<s)return!1}if(i.dolEffectivenessMax&&r.dolAnalysis){const s=parseInt(i.dolEffectivenessMax);if(!isNaN(s)&&r.dolAnalysis.effectiveness>s)return!1}return!0}):[],[e,i]),x=j.useMemo(()=>{if(!e)return[];const r=e.map(s=>s.setup).filter(s=>!!s);return Array.from(new Set(r))},[e]),y=j.useMemo(()=>{if(!e)return[];const r=e.map(s=>s.modelType).filter(s=>!!s);return Array.from(new Set(r))},[e]),t=j.useMemo(()=>{if(!e)return[];const r=e.map(s=>s.primarySetupType).filter(s=>!!s);return Array.from(new Set(r))},[e]),o=j.useMemo(()=>{if(!e)return[];const r=e.map(s=>s.secondarySetupType).filter(s=>!!s);return Array.from(new Set(r))},[e]),w=j.useMemo(()=>{if(!e)return[];const r=e.map(s=>s.liquidityTaken).filter(s=>!!s);return Array.from(new Set(r))},[e]),S=j.useMemo(()=>{if(!e)return[];const r=e.filter(s=>s.dolAnalysis).map(s=>{var b;return(b=s.dolAnalysis)==null?void 0:b.dolType}).filter(s=>!!s);return Array.from(new Set(r))},[e]);return{filters:i,setFilters:l,handleFilterChange:p,resetFilters:d,filteredTrades:c,uniqueSetups:x,uniqueModelTypes:y,uniquePrimarySetupTypes:t,uniqueSecondarySetupTypes:o,uniqueLiquidityTypes:w,uniqueDOLTypes:S}}const P=a.div.withConfig({displayName:"PageHeader",componentId:"sc-qc0x9k-0"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";"],({theme:e})=>e.spacing.lg),Q=a.h1.withConfig({displayName:"Title",componentId:"sc-qc0x9k-1"})(["font-size:",";font-weight:600;color:",";margin:0;"],({theme:e})=>e.fontSizes.xxl,({theme:e})=>e.colors.textPrimary),z=a.div.withConfig({displayName:"HeaderActions",componentId:"sc-qc0x9k-2"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.md),F=a.button.withConfig({displayName:"ActionButton",componentId:"sc-qc0x9k-3"})(["padding:"," ",";background-color:transparent;border:1px solid ",";border-radius:",";color:",";cursor:pointer;transition:all ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),B=a(F).withConfig({displayName:"RefreshButton",componentId:"sc-qc0x9k-4"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>e.spacing.xs),$=a(M).withConfig({displayName:"AddTradeButton",componentId:"sc-qc0x9k-5"})(["display:inline-flex;align-items:center;justify-content:center;padding:"," ",";background-color:",";color:white;border-radius:",";font-weight:500;text-decoration:none;transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.primary,({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primaryDark),H=({refreshTrades:e,showFilters:i,setShowFilters:l})=>n.jsxs(P,{children:[n.jsx(Q,{children:"Trade Journal"}),n.jsxs(z,{children:[n.jsx(B,{onClick:()=>e&&e(),children:"↻ Refresh"}),n.jsx(F,{onClick:()=>l(!i),children:i?"Hide Filters":"Show Filters"}),n.jsx($,{to:"/trade/new",children:"+ Add Trade"})]})]}),O=a.div.withConfig({displayName:"FilterSection",componentId:"sc-1c8y68u-0"})(["display:flex;flex-wrap:wrap;gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.lg),g=a.div.withConfig({displayName:"FilterGroup",componentId:"sc-1c8y68u-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),f=a.label.withConfig({displayName:"FilterLabel",componentId:"sc-1c8y68u-2"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),T=a.select.withConfig({displayName:"FilterSelect",componentId:"sc-1c8y68u-3"})(["padding:"," ",";border:1px solid ",";border-radius:",";background-color:",";color:",";min-width:150px;"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary),L=a.input.withConfig({displayName:"FilterInput",componentId:"sc-1c8y68u-4"})(["padding:"," ",";border:1px solid ",";border-radius:",";background-color:",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary),J=a.button.withConfig({displayName:"FilterButton",componentId:"sc-1c8y68u-5"})(["padding:"," ",";background-color:transparent;border:1px solid ",";border-radius:",";color:",";cursor:pointer;transition:all ",";align-self:flex-end;&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.background),G=({filters:e,handleFilterChange:i,resetFilters:l,uniqueSetups:p,uniqueModelTypes:d,uniquePrimarySetupTypes:c,uniqueSecondarySetupTypes:x,uniqueLiquidityTypes:y,uniqueDOLTypes:t})=>n.jsxs(O,{children:[n.jsxs(g,{children:[n.jsx(f,{htmlFor:"symbol",children:"Symbol"}),n.jsx(L,{id:"symbol",name:"symbol",value:e.symbol,onChange:i,placeholder:"AAPL, MSFT, etc."})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"direction",children:"Direction"}),n.jsxs(T,{id:"direction",name:"direction",value:e.direction,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),n.jsx("option",{value:"Long",children:"Long"}),n.jsx("option",{value:"Short",children:"Short"})]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"setup",children:"Setup"}),n.jsxs(T,{id:"setup",name:"setup",value:e.setup,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),p.map(o=>n.jsx("option",{value:o,children:o},o))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"modelType",children:"Model Type"}),n.jsxs(T,{id:"modelType",name:"modelType",value:e.modelType,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),d.map(o=>n.jsx("option",{value:o,children:o},o))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"result",children:"Result"}),n.jsxs(T,{id:"result",name:"result",value:e.result,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),n.jsx("option",{value:"win",children:"Wins"}),n.jsx("option",{value:"loss",children:"Losses"})]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"dateFrom",children:"From Date"}),n.jsx(L,{id:"dateFrom",name:"dateFrom",type:"date",value:e.dateFrom,onChange:i})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"dateTo",children:"To Date"}),n.jsx(L,{id:"dateTo",name:"dateTo",type:"date",value:e.dateTo,onChange:i})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"primarySetupType",children:"Primary Setup"}),n.jsxs(T,{id:"primarySetupType",name:"primarySetupType",value:e.primarySetupType,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),c.map(o=>n.jsx("option",{value:o,children:o},o))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"secondarySetupType",children:"Secondary Setup"}),n.jsxs(T,{id:"secondarySetupType",name:"secondarySetupType",value:e.secondarySetupType,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),x.map(o=>n.jsx("option",{value:o,children:o},o))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"liquidityTaken",children:"Liquidity Taken"}),n.jsxs(T,{id:"liquidityTaken",name:"liquidityTaken",value:e.liquidityTaken,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),y.map(o=>n.jsx("option",{value:o,children:o},o))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"patternQualityMin",children:"Pattern Quality Min"}),n.jsxs(T,{id:"patternQualityMin",name:"patternQualityMin",value:e.patternQualityMin,onChange:i,children:[n.jsx("option",{value:"",children:"Any"}),[1,2,3,4,5,6,7,8,9,10].map(o=>n.jsx("option",{value:o,children:o},`min-${o}`))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"patternQualityMax",children:"Pattern Quality Max"}),n.jsxs(T,{id:"patternQualityMax",name:"patternQualityMax",value:e.patternQualityMax,onChange:i,children:[n.jsx("option",{value:"",children:"Any"}),[1,2,3,4,5,6,7,8,9,10].map(o=>n.jsx("option",{value:o,children:o},`max-${o}`))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"dolType",children:"DOL Type"}),n.jsxs(T,{id:"dolType",name:"dolType",value:e.dolType,onChange:i,children:[n.jsx("option",{value:"",children:"All"}),t.map(o=>n.jsx("option",{value:o,children:o},o))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"dolEffectivenessMin",children:"DOL Effectiveness Min"}),n.jsxs(T,{id:"dolEffectivenessMin",name:"dolEffectivenessMin",value:e.dolEffectivenessMin,onChange:i,children:[n.jsx("option",{value:"",children:"Any"}),[1,2,3,4,5,6,7,8,9,10].map(o=>n.jsx("option",{value:o,children:o},`dol-min-${o}`))]})]}),n.jsxs(g,{children:[n.jsx(f,{htmlFor:"dolEffectivenessMax",children:"DOL Effectiveness Max"}),n.jsxs(T,{id:"dolEffectivenessMax",name:"dolEffectivenessMax",value:e.dolEffectivenessMax,onChange:i,children:[n.jsx("option",{value:"",children:"Any"}),[1,2,3,4,5,6,7,8,9,10].map(o=>n.jsx("option",{value:o,children:o},`dol-max-${o}`))]})]}),n.jsx(J,{onClick:l,children:"Reset Filters"})]});function U(e,i=!1){const[l,p]=j.useState({}),d=y=>{i&&p(t=>({...t,[y]:!t[y]}))},c=y=>i&&l[y];return{sortedTrades:j.useMemo(()=>e?[...e].sort((y,t)=>{const o=new Date(y.date).getTime();return new Date(t.date).getTime()-o}):[],[e]),expandedRows:l,toggleRowExpansion:d,isRowExpanded:c}}const V=a.div.withConfig({displayName:"TradeHeader",componentId:"sc-119u3m-0"})(["display:grid;grid-template-columns:var(--grid-template-columns,repeat(auto-fit,minmax(100px,1fr)));align-items:center;font-weight:600;color:",";background-color:transparent;padding:"," ",";position:sticky;top:0;z-index:1;backdrop-filter:blur(8px);"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),W=a.div.withConfig({displayName:"TradeDetail",componentId:"sc-119u3m-1"})(["overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 ",";"],({theme:e})=>e.spacing.xs),Y=({visibleColumns:e})=>n.jsx(V,{children:e.map(i=>n.jsx(W,{children:i.label},i.id))}),_=a.div.withConfig({displayName:"TradeItem",componentId:"sc-fx0jyh-0"})(["display:grid;grid-template-columns:var(--grid-template-columns,repeat(auto-fit,minmax(100px,1fr)));align-items:center;padding:",";background-color:",";border-radius:",";transition:all ",";cursor:",";position:relative;&:hover{background-color:",";}"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.cardBackground,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast,({expanded:e})=>e!==void 0?"pointer":"default",({theme:e})=>e.colors.chartGrid),K=({trade:e,visibleColumns:i,expanded:l,toggleRowExpansion:p})=>n.jsx(_,{expanded:l,onClick:()=>p(e.id),children:i.map(d=>n.jsx(D.Fragment,{children:d.accessor(e)},d.id))}),X=a.div.withConfig({displayName:"ExpandedContent",componentId:"sc-tnetmb-0"})(["padding:",";background-color:",";border-radius:",";margin-top:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.md),k=a.div.withConfig({displayName:"ExpandedSection",componentId:"sc-tnetmb-1"})(["margin-bottom:",";"],({theme:e})=>e.spacing.md),C=a.h3.withConfig({displayName:"SectionTitle",componentId:"sc-tnetmb-2"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm),N=a.div.withConfig({displayName:"DetailGrid",componentId:"sc-tnetmb-3"})(["display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),u=a.div.withConfig({displayName:"DetailItem",componentId:"sc-tnetmb-4"})(["display:flex;flex-direction:column;"]),h=a.span.withConfig({displayName:"DetailLabel",componentId:"sc-tnetmb-5"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),m=a.span.withConfig({displayName:"DetailValue",componentId:"sc-tnetmb-6"})(["font-size:",";color:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary),Z=a.div.withConfig({displayName:"ActionButtons",componentId:"sc-tnetmb-7"})(["display:flex;gap:",";margin-top:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md),ee=a(M).withConfig({displayName:"ActionButton",componentId:"sc-tnetmb-8"})(["padding:"," ",";background-color:",";color:white;border-radius:",";text-decoration:none;font-size:",";font-weight:500;transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.primary,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primaryDark),ne=({trade:e})=>{var i;return n.jsxs(X,{children:[n.jsxs(k,{children:[n.jsx(C,{children:"Trade Details"}),n.jsxs(N,{children:[n.jsxs(u,{children:[n.jsx(h,{children:"Symbol"}),n.jsx(m,{children:e.symbol})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Date"}),n.jsx(m,{children:e.date})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Direction"}),n.jsx(m,{children:e.direction})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Entry Price"}),n.jsxs(m,{children:["$",e.entry.toFixed(2)]})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Exit Price"}),n.jsxs(m,{children:["$",e.exit.toFixed(2)]})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Quantity"}),n.jsx(m,{children:e.size})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Profit/Loss"}),n.jsxs(m,{style:{color:e.profitLoss>0?"green":e.profitLoss<0?"red":"inherit"},children:["$",e.profitLoss.toFixed(2)]})]}),n.jsxs(u,{children:[n.jsx(h,{children:"R-Multiple"}),n.jsx(m,{children:((i=e.rMultiple)==null?void 0:i.toFixed(2))||"N/A"})]})]})]}),e.setup&&n.jsxs(k,{children:[n.jsx(C,{children:"Strategy"}),n.jsxs(N,{children:[n.jsxs(u,{children:[n.jsx(h,{children:"Setup"}),n.jsx(m,{children:e.setup})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Model Type"}),n.jsx(m,{children:e.modelType||"N/A"})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Primary Setup"}),n.jsx(m,{children:e.primarySetupType||"N/A"})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Secondary Setup"}),n.jsx(m,{children:e.secondarySetupType||"N/A"})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Liquidity Taken"}),n.jsx(m,{children:e.liquidityTaken||"N/A"})]}),n.jsxs(u,{children:[n.jsx(h,{children:"Pattern Quality"}),n.jsx(m,{children:e.patternQuality||"N/A"})]})]})]}),e.dolAnalysis&&n.jsxs(k,{children:[n.jsx(C,{children:"DOL Analysis"}),n.jsxs(N,{children:[n.jsxs(u,{children:[n.jsx(h,{children:"DOL Type"}),n.jsx(m,{children:e.dolAnalysis.dolType||"N/A"})]}),n.jsxs(u,{children:[n.jsx(h,{children:"DOL Strength"}),n.jsx(m,{children:e.dolAnalysis.dolStrength||"N/A"})]}),n.jsxs(u,{children:[n.jsx(h,{children:"DOL Reaction"}),n.jsx(m,{children:e.dolAnalysis.dolReaction||"N/A"})]}),n.jsxs(u,{children:[n.jsx(h,{children:"DOL Effectiveness"}),n.jsx(m,{children:e.dolAnalysis.effectiveness||"N/A"})]})]})]}),e.notes&&n.jsxs(k,{children:[n.jsx(C,{children:"Notes"}),n.jsx(u,{children:n.jsx(m,{children:e.notes})})]}),n.jsx(Z,{children:n.jsx(ee,{to:`/trade/edit/${e.id}`,children:"Edit Trade"})})]})},ie=a.div.withConfig({displayName:"EmptyContainer",componentId:"sc-1m14ql-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";background-color:",";border-radius:",";text-align:center;"],({theme:e})=>e.spacing.xl,({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),se=a.h3.withConfig({displayName:"EmptyTitle",componentId:"sc-1m14ql-1"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm),oe=a.p.withConfig({displayName:"EmptyDescription",componentId:"sc-1m14ql-2"})(["font-size:",";color:",";margin:0 0 "," 0;max-width:500px;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.lg),re=a(M).withConfig({displayName:"AddTradeButton",componentId:"sc-1m14ql-3"})(["display:inline-flex;align-items:center;justify-content:center;padding:"," ",";background-color:",";color:white;border-radius:",";font-weight:500;text-decoration:none;transition:background-color ",";&:hover{background-color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.primary,({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primaryDark),te=({filtered:e})=>n.jsxs(ie,{children:[n.jsx(se,{children:e?"No matching trades found":"No trades yet"}),n.jsx(oe,{children:e?"Try adjusting your filters to see more results.":"Start tracking your trades to gain insights into your trading performance."}),!e&&n.jsx(re,{to:"/trade/new",children:"+ Add Your First Trade"})]}),ae=I(["0%{background-position:-1000px 0;}100%{background-position:1000px 0;}"]),le=a.div.withConfig({displayName:"LoadingContainer",componentId:"sc-q8k354-0"})(["display:flex;flex-direction:column;gap:",";padding:"," 0;"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md),de=a.div.withConfig({displayName:"LoadingRow",componentId:"sc-q8k354-1"})(["height:60px;background:linear-gradient( to right,"," 8%,"," 18%,"," 33% );background-size:2000px 100%;animation:"," 1.5s infinite linear;border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.cardBackground,({theme:e})=>e.colors.background,ae,({theme:e})=>e.borderRadius.sm),ce=({rowCount:e=5})=>n.jsx(le,{children:Array.from({length:e}).map((i,l)=>n.jsx(de,{},l))}),pe=a.div.withConfig({displayName:"TradeListContainer",componentId:"sc-1e6ylnq-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.md),v=a.div.withConfig({displayName:"TradeDetail",componentId:"sc-1e6ylnq-1"})(["overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 ",";color:",";font-weight:",";"],({theme:e})=>e.spacing.xs,({theme:e,profit:i,loss:l})=>i?e.colors.success:l?e.colors.danger:e.colors.textPrimary,({profit:e,loss:i})=>e||i?600:"normal"),ue=a.span.withConfig({displayName:"Badge",componentId:"sc-1e6ylnq-2"})(["display:inline-block;padding:",";border-radius:",";font-size:",";font-weight:600;text-transform:uppercase;background-color:",";color:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.xs,({theme:e,type:i})=>i==="long"?e.colors.successLight||"rgba(76, 175, 80, 0.1)":i==="short"?e.colors.dangerLight||"rgba(244, 67, 54, 0.1)":e.colors.background,({theme:e,type:i})=>i==="long"?e.colors.success:i==="short"?e.colors.danger:e.colors.textPrimary),me=({trades:e,isLoading:i=!1,expandable:l=!1})=>{const{sortedTrades:p,toggleRowExpansion:d,isRowExpanded:c}=U(e,l),x=j.useMemo(()=>[{id:"date",label:"Date",accessor:t=>n.jsx(v,{children:t.date})},{id:"symbol",label:"Symbol",accessor:t=>n.jsx(v,{children:t.symbol})},{id:"direction",label:"Direction",accessor:t=>n.jsx(v,{children:n.jsx(ue,{type:t.direction.toLowerCase(),children:t.direction})})},{id:"setup",label:"Setup",accessor:t=>n.jsx(v,{children:t.setup||"N/A"})},{id:"entry",label:"Entry",accessor:t=>n.jsxs(v,{children:["$",t.entry.toFixed(2)]})},{id:"exit",label:"Exit",accessor:t=>n.jsxs(v,{children:["$",t.exit.toFixed(2)]})},{id:"size",label:"Size",accessor:t=>n.jsx(v,{children:t.size})},{id:"profitLoss",label:"P/L",accessor:t=>n.jsxs(v,{profit:t.profitLoss>0,loss:t.profitLoss<0,children:["$",t.profitLoss.toFixed(2)]})}],[]),y=`repeat(${x.length}, 1fr)`;return i?n.jsx(ce,{rowCount:5}):!p||p.length===0?n.jsx(te,{filtered:e&&e.length>0}):n.jsxs(pe,{style:{"--grid-template-columns":y},children:[n.jsx(Y,{visibleColumns:x}),p.map(t=>n.jsxs(D.Fragment,{children:[n.jsx(K,{trade:t,visibleColumns:x,expanded:c(t.id),toggleRowExpansion:d}),c(t.id)&&n.jsx(ne,{trade:t})]},t.id))]})},xe=a.div.withConfig({displayName:"ContentSection",componentId:"sc-1mreyxv-0"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.shadows.sm),ye=a.h2.withConfig({displayName:"SectionTitle",componentId:"sc-1mreyxv-1"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.md),he=a.div.withConfig({displayName:"ErrorMessage",componentId:"sc-1mreyxv-2"})(["color:",";padding:",";background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.danger,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.md),ge=({error:e,showFilters:i,filteredTrades:l,isLoading:p,filters:d,handleFilterChange:c,resetFilters:x,uniqueSetups:y,uniqueModelTypes:t,uniquePrimarySetupTypes:o,uniqueSecondarySetupTypes:w,uniqueLiquidityTypes:S,uniqueDOLTypes:r})=>n.jsxs(n.Fragment,{children:[e&&n.jsx(he,{children:e}),n.jsxs(xe,{children:[n.jsx(ye,{children:"Recent Trades"}),i&&n.jsx(G,{filters:d,handleFilterChange:c,resetFilters:x,uniqueSetups:y,uniqueModelTypes:t,uniquePrimarySetupTypes:o,uniqueSecondarySetupTypes:w,uniqueLiquidityTypes:S,uniqueDOLTypes:r}),n.jsx(me,{trades:l,isLoading:p,expandable:!0})]})]}),fe=a.div.withConfig({displayName:"PageContainer",componentId:"sc-gu4wa5-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.lg),Se=()=>{const{trades:e,isLoading:i,error:l,refreshTrades:p}=q(),[d,c]=j.useState(!1),{filters:x,handleFilterChange:y,resetFilters:t,filteredTrades:o,uniqueSetups:w,uniqueModelTypes:S,uniquePrimarySetupTypes:r,uniqueSecondarySetupTypes:s,uniqueLiquidityTypes:b,uniqueDOLTypes:A}=R(e);return n.jsxs(fe,{children:[n.jsx(H,{refreshTrades:p,showFilters:d,setShowFilters:c}),n.jsx(ge,{error:l,showFilters:d,filteredTrades:o,isLoading:i,filters:x,handleFilterChange:y,resetFilters:t,uniqueSetups:w,uniqueModelTypes:S,uniquePrimarySetupTypes:r,uniqueSecondarySetupTypes:s,uniqueLiquidityTypes:b,uniqueDOLTypes:A})]})};export{Se as default};
//# sourceMappingURL=TradeJournal-af586680.js.map
