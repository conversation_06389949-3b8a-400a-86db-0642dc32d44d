import{j as r,c as $}from"./client-4c27269c.js";import{r as g,R as U}from"./react-60374de9.js";import{u as G,N as H,O as V,R as W,a as h,b as J,B as Y}from"./router-e715efa2.js";import{s as n,W as Q,F as X}from"./styled-components-3ebafa9a.js";const R=n.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),Z=n.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),A=n.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),k=n.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),_=n.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),z=n.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),K=n.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),O=n.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),ee=n.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),re=n(O).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),oe=({error:e,resetError:t,isAppLevel:i,name:a,onSkip:c})=>{const p=()=>{window.location.reload()};return i?r.jsx(R,{isAppLevel:!0,children:r.jsxs(Z,{children:[r.jsx(A,{isAppLevel:!0,children:"Something went wrong"}),r.jsx(k,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),r.jsxs(_,{children:[r.jsx("summary",{children:"Technical Details"}),r.jsx(k,{children:e.message}),e.stack&&r.jsx(z,{children:e.stack})]}),r.jsx(re,{onClick:p,children:"Reload Application"})]})}):r.jsxs(R,{children:[r.jsx(A,{children:a?`Error in ${a}`:"Something went wrong"}),r.jsx(k,{children:a?`We encountered a problem while loading ${a}. You can try again${c?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),r.jsxs(_,{children:[r.jsx("summary",{children:"Technical Details"}),r.jsx(k,{children:e.message}),e.stack&&r.jsx(z,{children:e.stack})]}),r.jsxs(K,{children:[r.jsx(O,{onClick:t,children:"Try Again"}),c&&r.jsx(ee,{onClick:c,children:"Skip This Feature"})]})]})};class te extends g.Component{constructor(t){super(t),this.resetError=()=>{this.setState({hasError:!1,error:null})},this.state={hasError:!1,error:null}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,i){const{name:a}=this.props,c=a?`ErrorBoundary(${a})`:"ErrorBoundary";console.error(`Error caught by ${c}:`,t,i),this.props.onError&&this.props.onError(t,i)}componentDidUpdate(t){this.state.hasError&&this.props.resetOnPropsChange&&t.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:t,error:i}=this.state,{children:a,fallback:c,name:p,isFeatureBoundary:f,onSkip:y}=this.props;return t&&i?typeof c=="function"?c({error:i,resetError:this.resetError}):c||r.jsx(oe,{error:i,resetError:this.resetError,isAppLevel:!f,name:p,onSkip:y}):a}}const ne=({isAppLevel:e,isFeatureBoundary:t,...i})=>{const a=e?"app":t?"feature":"component",c={resetOnPropsChange:a!=="app",resetOnUnmount:a!=="app",isFeatureBoundary:a==="feature"};return r.jsx(te,{...c,...i})},ie=e=>r.jsx(ne,{isAppLevel:!0,...e}),o={f1Red:"#e10600",f1RedDark:"#c10500",f1RedLight:"#ff3b36",f1Blue:"#1e5bc6",f1BlueDark:"#1a4da8",f1BlueLight:"#4a7dd8",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},s={background:"#1a1f2c",surface:"#252a37",cardBackground:"#252a37",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:o.green,warning:o.yellow,error:o.red,info:o.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:o.f1Red,profit:o.green,loss:o.red,neutral:o.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},l={background:"#f5f5f5",surface:"#ffffff",cardBackground:"#ffffff",border:"#e0e0e0",divider:"rgba(0, 0, 0, 0.1)",textPrimary:"#333333",textSecondary:"#666666",textDisabled:"#999999",textInverse:"#ffffff",success:o.green,warning:o.yellow,error:o.red,info:o.blue,chartGrid:"rgba(0, 0, 0, 0.1)",chartLine:o.f1Red,profit:o.green,loss:o.red,neutral:o.gray400,tooltipBackground:"rgba(255, 255, 255, 0.9)",modalBackground:"rgba(255, 255, 255, 0.8)"},v={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},j={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},C={light:300,regular:400,medium:500,semibold:600,bold:700},L={tight:1.25,normal:1.5,relaxed:1.75},B={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},I={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},S={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},E={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},D={fast:"0.1s",normal:"0.3s",slow:"0.5s"},T={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},F={name:"f1",colors:{primary:o.f1Red,primaryDark:o.f1RedDark,primaryLight:o.f1RedLight,secondary:o.f1Blue,secondaryDark:o.f1BlueDark,secondaryLight:o.f1BlueLight,accent:o.purple,accentDark:o.purpleDark,accentLight:o.purpleLight,success:s.success,warning:s.warning,error:s.error,info:s.info,background:s.background,surface:s.surface,cardBackground:s.surface,border:s.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:s.textPrimary,textSecondary:s.textSecondary,textDisabled:s.textDisabled,textInverse:s.textInverse,chartGrid:s.chartGrid,chartLine:s.chartLine,chartAxis:o.gray400,chartTooltip:s.tooltipBackground,profit:s.profit,loss:s.loss,neutral:s.neutral,tabActive:o.f1Red,tabInactive:o.gray600,tooltipBackground:s.tooltipBackground,modalBackground:s.modalBackground,sidebarBackground:o.gray800,headerBackground:"rgba(0, 0, 0, 0.2)"},spacing:v,breakpoints:I,fontSizes:j,fontWeights:C,lineHeights:L,fontFamilies:B,borderRadius:S,shadows:E,transitions:D,zIndex:T},ae={name:"light",colors:{primary:o.f1Red,primaryDark:o.f1RedDark,primaryLight:o.f1RedLight,secondary:o.f1Blue,secondaryDark:o.f1BlueDark,secondaryLight:o.f1BlueLight,accent:o.purple,accentDark:o.purpleDark,accentLight:o.purpleLight,success:l.success,warning:l.warning,error:l.error,info:l.info,background:l.background,surface:l.surface,cardBackground:l.surface,border:l.border,divider:o.blackTransparent10,textPrimary:l.textPrimary,textSecondary:l.textSecondary,textDisabled:l.textDisabled,textInverse:l.textInverse,chartGrid:l.chartGrid,chartLine:l.chartLine,chartAxis:o.gray600,chartTooltip:l.tooltipBackground,profit:l.profit,loss:l.loss,neutral:l.neutral,tabActive:o.f1Red,tabInactive:o.gray400,tooltipBackground:l.tooltipBackground,modalBackground:l.modalBackground,sidebarBackground:o.white,headerBackground:"rgba(0, 0, 0, 0.05)"},spacing:v,breakpoints:I,fontSizes:j,fontWeights:C,lineHeights:L,fontFamilies:B,borderRadius:S,shadows:E,transitions:D,zIndex:T},se={name:"dark",colors:{primary:o.f1Blue,primaryDark:o.f1BlueDark,primaryLight:o.f1BlueLight,secondary:o.f1Blue,secondaryDark:o.f1BlueDark,secondaryLight:o.f1BlueLight,accent:o.purple,accentDark:o.purpleDark,accentLight:o.purpleLight,success:s.success,warning:s.warning,error:s.error,info:s.info,background:o.gray900,surface:o.gray800,cardBackground:o.gray800,border:o.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:o.white,textSecondary:o.gray300,textDisabled:o.gray500,textInverse:o.gray900,chartGrid:s.chartGrid,chartLine:o.f1Blue,chartAxis:o.gray400,chartTooltip:s.tooltipBackground,profit:s.profit,loss:s.loss,neutral:s.neutral,tabActive:o.f1Blue,tabInactive:o.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:o.gray900,headerBackground:"rgba(0, 0, 0, 0.3)"},spacing:v,breakpoints:I,fontSizes:j,fontWeights:C,lineHeights:L,fontFamilies:B,borderRadius:S,shadows:E,transitions:D,zIndex:T},ce=Q(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),le=ce,de={f1:F,light:ae,dark:se},N=F,w=e=>de[e]||N,M=g.createContext({theme:N,setTheme:()=>{}}),tr=()=>g.useContext(M),pe=({initialTheme:e=N,persistTheme:t=!0,storageKey:i="adhd-dashboard-theme",children:a})=>{console.log("ThemeProvider rendering with initialTheme:",e);const[c,p]=g.useState(()=>{if(console.log("ThemeProvider initializing theme state"),t&&typeof window<"u"){console.log("ThemeProvider attempting to load theme from localStorage");const d=window.localStorage.getItem(i);if(console.log("ThemeProvider storedTheme:",d),d)try{console.log("ThemeProvider trying to get theme by name:",d);const m=w(d);if(m)return console.log("ThemeProvider found theme by name:",m.name),m;console.log("ThemeProvider trying to parse theme as JSON");const b=JSON.parse(d);return console.log("ThemeProvider parsed theme:",b),b}catch(m){console.error("Failed to parse stored theme:",m)}}console.log("ThemeProvider falling back to initial theme");const u=typeof e=="string"?w(e):e;return console.log("ThemeProvider resolved theme:",u),u}),f=u=>{const d=typeof u=="string"?w(u):u;p(d),t&&typeof window<"u"&&window.localStorage.setItem(i,d.name||JSON.stringify(d))},y=({children:u})=>{console.log("ThemeWrapper rendering with theme:",c.name);const d={position:"fixed",top:"40px",left:0,padding:"10px",background:c.colors.primary,color:"white",zIndex:9999,fontSize:"16px",fontFamily:"monospace"};return r.jsxs(X,{theme:c,children:[r.jsx(le,{}),r.jsxs("div",{style:d,children:["Theme: ",c.name]}),u]})};return console.log("ThemeProvider returning context with theme:",c.name),r.jsx(M.Provider,{value:{theme:c,setTheme:f},children:r.jsx(y,{children:a})})},ge="modulepreload",me=function(e){return"/"+e},P={},x=function(t,i,a){if(!i||i.length===0)return t();const c=document.getElementsByTagName("link");return Promise.all(i.map(p=>{if(p=me(p),p in P)return;P[p]=!0;const f=p.endsWith(".css"),y=f?'[rel="stylesheet"]':"";if(!!a)for(let m=c.length-1;m>=0;m--){const b=c[m];if(b.href===p&&(!f||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${p}"]${y}`))return;const d=document.createElement("link");if(d.rel=f?"stylesheet":ge,f||(d.as="script",d.crossOrigin=""),d.href=p,document.head.appendChild(d),f)return new Promise((m,b)=>{d.addEventListener("load",m),d.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${p}`)))})})).then(()=>t())},he=n.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-vmq176-0"})(["height:100%;background-color:",";border-right:1px solid ",";display:flex;flex-direction:column;overflow:hidden;transition:width ",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal),fe=n.div.withConfig({displayName:"LogoContainer",componentId:"sc-vmq176-1"})(["height:64px;display:flex;align-items:center;justify-content:",";padding:0 ",";border-bottom:1px solid ",";"],({isOpen:e})=>e?"flex-start":"center",({theme:e,isOpen:t})=>t?e.spacing.md:"0",({theme:e})=>e.colors.border),ue=n.div.withConfig({displayName:"Logo",componentId:"sc-vmq176-2"})(["font-size:",";font-weight:bold;color:",";white-space:nowrap;overflow:hidden;opacity:",";transition:opacity ",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.primary,({isOpen:e})=>e?1:0,({theme:e})=>e.transitions.normal),xe=n.nav.withConfig({displayName:"NavContainer",componentId:"sc-vmq176-3"})(["flex:1;overflow-y:auto;padding:"," 0;"],({theme:e})=>e.spacing.md),be=n(H).withConfig({displayName:"NavItem",componentId:"sc-vmq176-4"})(["display:flex;align-items:center;padding:"," ",";justify-content:",";color:",";text-decoration:none;transition:background-color ",",color ",";&:hover{background-color:rgba(255,255,255,0.05);color:",";}&.active{color:",";background-color:rgba(255,255,255,0.05);border-left:3px solid ",";}"],({theme:e})=>e.spacing.sm,({theme:e,$isOpen:t})=>t?e.spacing.md:"0",({$isOpen:e})=>e?"flex-start":"center",({theme:e})=>e.colors.textSecondary,({theme:e})=>e.transitions.fast,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),ye=n.div.withConfig({displayName:"Icon",componentId:"sc-vmq176-5"})(["display:flex;align-items:center;justify-content:center;width:24px;height:24px;margin-right:",";"],({theme:e})=>e.spacing.sm),ke=n.span.withConfig({displayName:"Label",componentId:"sc-vmq176-6"})(["white-space:nowrap;opacity:",";transition:opacity ",";overflow:hidden;max-width:",";"],({isOpen:e})=>e?1:0,({theme:e})=>e.transitions.normal,({isOpen:e})=>e?"200px":"0"),we=n.div.withConfig({displayName:"Footer",componentId:"sc-vmq176-7"})(["padding:"," ",";border-top:1px solid ",";font-size:",";color:",";white-space:nowrap;opacity:",";transition:opacity ",";text-align:center;"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({isOpen:e})=>e?1:0,({theme:e})=>e.transitions.normal),ve=({isOpen:e})=>{const t=G(),i=[{path:"/",label:"Dashboard",icon:"📊"},{path:"/daily-guide",label:"Daily Guide",icon:"📅"},{path:"/journal",label:"Trade Journal",icon:"📓"},{path:"/analysis",label:"Analysis",icon:"📈"},{path:"/settings",label:"Settings",icon:"⚙️"}];return r.jsxs(he,{isOpen:e,children:[r.jsx(fe,{isOpen:e,children:r.jsx(ue,{isOpen:e,children:"ADHD"})}),r.jsx(xe,{children:i.map(a=>r.jsxs(be,{to:a.path,$isOpen:e,className:t.pathname===a.path?"active":"",children:[r.jsx(ye,{children:a.icon}),r.jsx(ke,{isOpen:e,children:a.label})]},a.path))}),r.jsx(we,{isOpen:e,children:"v1.0.0"})]})},je=n.header.withConfig({displayName:"HeaderContainer",componentId:"sc-7htwke-0"})(["display:flex;align-items:center;justify-content:space-between;height:64px;padding:0 ",";background-color:",";border-bottom:1px solid ",";color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.colors.textPrimary),Ce=n.div.withConfig({displayName:"LeftSection",componentId:"sc-7htwke-1"})(["display:flex;align-items:center;"]),Le=n.button.withConfig({displayName:"MenuButton",componentId:"sc-7htwke-2"})(["display:flex;align-items:center;justify-content:center;width:40px;height:40px;border:none;background:none;color:",";cursor:pointer;transition:color ",";&:hover{color:",";}"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary),Be=n.div.withConfig({displayName:"Logo",componentId:"sc-7htwke-3"})(["display:flex;align-items:center;margin-left:",";font-size:",";font-weight:bold;"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontSizes.lg),Ie=n.div.withConfig({displayName:"RightSection",componentId:"sc-7htwke-4"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>e.spacing.md),Se=n.div.withConfig({displayName:"UserMenu",componentId:"sc-7htwke-5"})(["display:flex;align-items:center;cursor:pointer;padding:"," ",";border-radius:",";transition:background-color ",";&:hover{background-color:rgba(255,255,255,0.1);}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast),Ee=n.div.withConfig({displayName:"Avatar",componentId:"sc-7htwke-6"})(["width:32px;height:32px;border-radius:50%;background-color:",";display:flex;align-items:center;justify-content:center;color:white;font-weight:bold;"],({theme:e})=>e.colors.primary),De=({toggleSidebar:e,sidebarOpen:t})=>r.jsxs(je,{children:[r.jsxs(Ce,{children:[r.jsx(Le,{onClick:e,"aria-label":"Toggle sidebar",children:t?r.jsx("span",{children:"☰"}):r.jsx("span",{children:"☰"})}),r.jsx(Be,{children:"ADHD Trading Dashboard"})]}),r.jsx(Ie,{children:r.jsx(Se,{children:r.jsx(Ee,{children:"JD"})})})]}),Te=n.div.withConfig({displayName:"LayoutContainer",componentId:"sc-1wfskt0-0"})(["display:flex;height:100vh;width:100%;overflow:hidden;background-color:",";"],({theme:e})=>e.colors.background),Ne=n.div.withConfig({displayName:"SidebarContainer",componentId:"sc-1wfskt0-1"})(["width:",";height:100%;transition:width ",";flex-shrink:0;@media (max-width:","){position:fixed;z-index:",";width:",";box-shadow:",";}"],({isOpen:e})=>e?"240px":"64px",({theme:e})=>e.transitions.normal,({theme:e})=>e.breakpoints.md,({theme:e})=>e.zIndex.fixed,({isOpen:e})=>e?"240px":"0",({isOpen:e,theme:t})=>e?t.shadows.lg:"none"),Re=n.div.withConfig({displayName:"ContentContainer",componentId:"sc-1wfskt0-2"})(["flex:1;display:flex;flex-direction:column;overflow:hidden;transition:margin-left ",";@media (max-width:","){margin-left:0;width:100%;}"],({theme:e})=>e.transitions.normal,({theme:e})=>e.breakpoints.md),Ae=n.div.withConfig({displayName:"HeaderContainer",componentId:"sc-1wfskt0-3"})(["height:64px;flex-shrink:0;"]),_e=n.main.withConfig({displayName:"MainContent",componentId:"sc-1wfskt0-4"})(["flex:1;overflow:auto;padding:",";@media (max-width:","){padding:",";}"],({theme:e})=>e.spacing.lg,({theme:e})=>e.breakpoints.sm,({theme:e})=>e.spacing.md),ze=n.div.withConfig({displayName:"Overlay",componentId:"sc-1wfskt0-5"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);z-index:",";opacity:",";visibility:",";transition:opacity ",",visibility ",";@media (min-width:","){display:none;}"],({theme:e})=>e.zIndex.modal-1,({isVisible:e})=>e?1:0,({isVisible:e})=>e?"visible":"hidden",({theme:e})=>e.transitions.normal,({theme:e})=>e.transitions.normal,({theme:e})=>e.breakpoints.md),Pe=()=>{const[e,t]=g.useState(!0),i=()=>{t(!e)},a=()=>{t(!1)};return r.jsxs(Te,{children:[r.jsx(Ne,{isOpen:e,children:r.jsx(ve,{isOpen:e})}),r.jsx(ze,{isVisible:e,onClick:a}),r.jsxs(Re,{sidebarOpen:e,children:[r.jsx(Ae,{children:r.jsx(De,{toggleSidebar:i,sidebarOpen:e})}),r.jsx(_e,{children:r.jsx(V,{})})]})]})},qe=n.div.withConfig({displayName:"LoadingContainer",componentId:"sc-1u55s8z-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:",";"],({theme:e})=>e.spacing.lg),Oe=n.div.withConfig({displayName:"Spinner",componentId:"sc-1u55s8z-1"})(["width:40px;height:40px;border-radius:50%;border:3px solid rgba(255,255,255,0.1);border-top-color:",";animation:spin 1s linear infinite;@keyframes spin{to{transform:rotate(360deg);}}"],({theme:e})=>e.colors.primary),Fe=n.div.withConfig({displayName:"LoadingText",componentId:"sc-1u55s8z-2"})(["margin-top:",";color:",";font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.md),Me=()=>r.jsxs(qe,{children:[r.jsx(Oe,{}),r.jsx(Fe,{children:"Loading..."})]}),$e=g.lazy(()=>x(()=>import("./Dashboard-c9f9e5d6.js"),["assets/Dashboard-c9f9e5d6.js","assets/client-4c27269c.js","assets/react-60374de9.js","assets/styled-components-3ebafa9a.js"])),Ue=g.lazy(()=>x(()=>import("./DailyGuide-6bd79e86.js"),["assets/DailyGuide-6bd79e86.js","assets/client-4c27269c.js","assets/react-60374de9.js","assets/styled-components-3ebafa9a.js","assets/Card-a75b9d5e.js"])),Ge=g.lazy(()=>x(()=>import("./TradeJournal-af586680.js"),["assets/TradeJournal-af586680.js","assets/client-4c27269c.js","assets/react-60374de9.js","assets/styled-components-3ebafa9a.js","assets/tradeStorage-0955231a.js","assets/router-e715efa2.js"])),He=g.lazy(()=>x(()=>import("./TradeAnalysis-72da3a81.js"),["assets/TradeAnalysis-72da3a81.js","assets/client-4c27269c.js","assets/react-60374de9.js","assets/styled-components-3ebafa9a.js","assets/Card-a75b9d5e.js"])),q=g.lazy(()=>x(()=>import("./TradeForm-7631201a.js"),["assets/TradeForm-7631201a.js","assets/client-4c27269c.js","assets/react-60374de9.js","assets/styled-components-3ebafa9a.js","assets/tradeStorage-0955231a.js","assets/router-e715efa2.js"])),Ve=g.lazy(()=>x(()=>import("./Settings-57ab97e1.js"),["assets/Settings-57ab97e1.js","assets/client-4c27269c.js","assets/react-60374de9.js","assets/styled-components-3ebafa9a.js","assets/router-e715efa2.js"])),We=g.lazy(()=>x(()=>import("./NotFound-fd69771b.js"),["assets/NotFound-fd69771b.js","assets/client-4c27269c.js","assets/react-60374de9.js","assets/styled-components-3ebafa9a.js","assets/router-e715efa2.js"])),Je=()=>r.jsx(g.Suspense,{fallback:r.jsx(Me,{}),children:r.jsxs(W,{children:[r.jsxs(h,{path:"/",element:r.jsx(Pe,{}),children:[r.jsx(h,{index:!0,element:r.jsx($e,{})}),r.jsx(h,{path:"daily-guide",element:r.jsx(Ue,{})}),r.jsx(h,{path:"journal",element:r.jsx(Ge,{})}),r.jsx(h,{path:"analysis",element:r.jsx(He,{})}),r.jsx(h,{path:"trade/new",element:r.jsx(q,{})}),r.jsx(h,{path:"trade/:id",element:r.jsx(q,{})}),r.jsx(h,{path:"settings",element:r.jsx(Ve,{})}),r.jsx(h,{path:"*",element:r.jsx(We,{})})]}),r.jsx(h,{path:"/dashboard",element:r.jsx(J,{to:"/",replace:!0})})]})}),Ye=({children:e})=>{const t=i=>{console.error("Application Error:",i)};return r.jsx(ie,{onError:t,name:"Application",children:e})};function Qe(){const[e,t]=g.useState(0);g.useEffect(()=>(console.log("App component mounted"),console.log("Root element:",document.getElementById("root")),console.log("Body children count:",document.body.children.length),t(a=>a+1),()=>{console.log("App component unmounted")}),[]),console.log(`App rendering (count: ${e})`);const i={position:"fixed",top:0,left:0,padding:"10px",background:"red",color:"white",zIndex:9999,fontSize:"16px",fontFamily:"monospace"};return r.jsx(Ye,{children:r.jsx(pe,{initialTheme:"f1",children:r.jsxs(Y,{children:[r.jsxs("div",{style:i,children:["App Rendered: ",e]}),r.jsx(Je,{})]})})})}const Xe=e=>{e&&e instanceof Function&&x(()=>import("./web-vitals-60d3425a.js"),[]).then(({getCLS:t,getFID:i,getFCP:a,getLCP:c,getTTFB:p})=>{t(e),i(e),a(e),c(e),p(e)})};const Ze=()=>{if(console.log("ADHD Trading Dashboard initializing..."),!document.getElementById("root")){console.error("Root element not found, creating a fallback element");const i=document.createElement("div");i.id="root",document.body.appendChild(i)}$.createRoot(document.getElementById("root")).render(r.jsx(U.StrictMode,{children:r.jsx(Qe,{})})),Xe()};window.addEventListener("error",e=>{console.error("Error:",e.error||e.message)});Ze();export{x as _,tr as u};
//# sourceMappingURL=main-2920b47d.js.map
