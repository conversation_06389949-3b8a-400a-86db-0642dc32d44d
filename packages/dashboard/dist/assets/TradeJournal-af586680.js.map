{"version": 3, "file": "TradeJournal-af586680.js", "sources": ["../../src/features/trade-journal/hooks/useTradeJournal.ts", "../../src/features/trade-journal/hooks/useTradeFilters.ts", "../../src/features/trade-journal/components/journal/TradeJournalHeader.tsx", "../../src/features/trade-journal/components/journal/TradeJournalFilters.tsx", "../../src/features/trade-journal/hooks/useTradeList.ts", "../../src/features/trade-journal/components/list/TradeListHeader.tsx", "../../src/features/trade-journal/components/list/TradeListRow.tsx", "../../src/features/trade-journal/components/list/TradeListExpandedRow.tsx", "../../src/features/trade-journal/components/list/TradeListEmpty.tsx", "../../src/features/trade-journal/components/list/TradeListLoading.tsx", "../../src/features/trade-journal/components/TradeList.tsx", "../../src/features/trade-journal/components/journal/TradeJournalContent.tsx", "../../src/features/trade-journal/TradeJournal.tsx"], "sourcesContent": ["/**\n * useTradeJournal Hook\n *\n * Custom hook for managing trade journal data and operations.\n */\n\nimport { useState, useEffect } from 'react';\nimport { Trade, TradeJournalState } from '../types';\nimport { tradeStorage } from '../../../services/tradeStorage';\n\n/**\n * useTradeJournal Hook\n *\n * Manages the state and operations for the trade journal feature.\n */\nexport const useTradeJournal = (): TradeJournalState => {\n  const [state, setState] = useState<TradeJournalState>({\n    trades: [],\n    isLoading: true,\n    error: null,\n  });\n\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        // Set loading state\n        setState((prev) => ({\n          ...prev,\n          isLoading: true,\n          error: null,\n        }));\n\n        // Fetch trades from IndexedDB\n        const trades = await tradeStorage.getAllTrades();\n\n        // Sort trades by date (newest first)\n        const sortedTrades = [...trades].sort(\n          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\n        );\n\n        setState({\n          trades: sortedTrades,\n          isLoading: false,\n          error: null,\n        });\n      } catch (error) {\n        console.error('Error fetching trades:', error);\n        setState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: 'Failed to load trades. Please try again.',\n        }));\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Add function to refresh trades\n  const refreshTrades = async () => {\n    try {\n      setState((prev) => ({\n        ...prev,\n        isLoading: true,\n        error: null,\n      }));\n\n      const trades = await tradeStorage.getAllTrades();\n\n      // Sort trades by date (newest first)\n      const sortedTrades = [...trades].sort(\n        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\n      );\n\n      setState({\n        trades: sortedTrades,\n        isLoading: false,\n        error: null,\n      });\n    } catch (error) {\n      console.error('Error refreshing trades:', error);\n      setState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'Failed to refresh trades. Please try again.',\n      }));\n    }\n  };\n\n  return {\n    ...state,\n    refreshTrades,\n  };\n};\n\nexport default useTradeJournal;\n", "/**\n * useTradeFilters Hook\n *\n * Custom hook for managing trade filters\n */\n\nimport { useState, useMemo } from 'react';\nimport { FilterState, Trade } from '../types';\n\n/**\n * Hook for managing trade filters\n * @param trades The trades to filter\n */\nexport function useTradeFilters(trades: Trade[]) {\n  // Filter state\n  const [filters, setFilters] = useState<FilterState>({\n    symbol: '',\n    direction: '',\n    setup: '',\n    modelType: '',\n    result: '',\n    dateFrom: '',\n    dateTo: '',\n    primarySetupType: '',\n    secondarySetupType: '',\n    liquidityTaken: '',\n    patternQualityMin: '',\n    patternQualityMax: '',\n    dolType: '',\n    dolEffectivenessMin: '',\n    dolEffectivenessMax: '',\n  });\n\n  // Handle filter changes\n  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFilters((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  // Reset filters\n  const resetFilters = () => {\n    setFilters({\n      symbol: '',\n      direction: '',\n      setup: '',\n      modelType: '',\n      result: '',\n      dateFrom: '',\n      dateTo: '',\n      primarySetupType: '',\n      secondarySetupType: '',\n      liquidityTaken: '',\n      patternQualityMin: '',\n      patternQualityMax: '',\n      dolType: '',\n      dolEffectivenessMin: '',\n      dolEffectivenessMax: '',\n    });\n  };\n\n  // Apply filters to trades\n  const filteredTrades = useMemo(() => {\n    if (!trades) return [];\n\n    return trades.filter((trade) => {\n      // Symbol filter\n      if (filters.symbol && !trade.symbol.toLowerCase().includes(filters.symbol.toLowerCase())) {\n        return false;\n      }\n\n      // Direction filter\n      if (filters.direction && trade.direction !== filters.direction) {\n        return false;\n      }\n\n      // Setup filter\n      if (filters.setup && trade.setup !== filters.setup) {\n        return false;\n      }\n\n      // Model type filter\n      if (filters.modelType && trade.modelType !== filters.modelType) {\n        return false;\n      }\n\n      // Result filter (win/loss)\n      if (filters.result) {\n        const isWin = trade.profitLoss > 0;\n        if ((filters.result === 'win' && !isWin) || (filters.result === 'loss' && isWin)) {\n          return false;\n        }\n      }\n\n      // Date range filter\n      if (filters.dateFrom) {\n        const tradeDate = new Date(trade.date);\n        const fromDate = new Date(filters.dateFrom);\n        if (tradeDate < fromDate) {\n          return false;\n        }\n      }\n\n      if (filters.dateTo) {\n        const tradeDate = new Date(trade.date);\n        const toDate = new Date(filters.dateTo);\n        // Set time to end of day\n        toDate.setHours(23, 59, 59, 999);\n        if (tradeDate > toDate) {\n          return false;\n        }\n      }\n\n      // Primary Setup Type filter\n      if (filters.primarySetupType && trade.primarySetupType !== filters.primarySetupType) {\n        return false;\n      }\n\n      // Secondary Setup Type filter\n      if (filters.secondarySetupType && trade.secondarySetupType !== filters.secondarySetupType) {\n        return false;\n      }\n\n      // Liquidity Taken filter\n      if (filters.liquidityTaken && trade.liquidityTaken !== filters.liquidityTaken) {\n        return false;\n      }\n\n      // Pattern Quality Min filter\n      if (filters.patternQualityMin && trade.patternQuality) {\n        const minQuality = parseInt(filters.patternQualityMin);\n        if (!isNaN(minQuality) && trade.patternQuality < minQuality) {\n          return false;\n        }\n      }\n\n      // Pattern Quality Max filter\n      if (filters.patternQualityMax && trade.patternQuality) {\n        const maxQuality = parseInt(filters.patternQualityMax);\n        if (!isNaN(maxQuality) && trade.patternQuality > maxQuality) {\n          return false;\n        }\n      }\n\n      // DOL Type filter\n      if (filters.dolType && trade.dolAnalysis && trade.dolAnalysis.dolType !== filters.dolType) {\n        return false;\n      }\n\n      // DOL Effectiveness Min filter\n      if (filters.dolEffectivenessMin && trade.dolAnalysis) {\n        const minEffectiveness = parseInt(filters.dolEffectivenessMin);\n        if (!isNaN(minEffectiveness) && trade.dolAnalysis.effectiveness < minEffectiveness) {\n          return false;\n        }\n      }\n\n      // DOL Effectiveness Max filter\n      if (filters.dolEffectivenessMax && trade.dolAnalysis) {\n        const maxEffectiveness = parseInt(filters.dolEffectivenessMax);\n        if (!isNaN(maxEffectiveness) && trade.dolAnalysis.effectiveness > maxEffectiveness) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  }, [trades, filters]);\n\n  // Get unique values for filter dropdowns\n  const uniqueSetups = useMemo(() => {\n    if (!trades) return [];\n    const setups = trades.map((trade) => trade.setup).filter((setup): setup is string => !!setup);\n    return Array.from(new Set(setups));\n  }, [trades]);\n\n  const uniqueModelTypes = useMemo(() => {\n    if (!trades) return [];\n    const modelTypes = trades\n      .map((trade) => trade.modelType)\n      .filter((modelType): modelType is string => !!modelType);\n    return Array.from(new Set(modelTypes));\n  }, [trades]);\n\n  const uniquePrimarySetupTypes = useMemo(() => {\n    if (!trades) return [];\n    const setupTypes = trades\n      .map((trade) => trade.primarySetupType)\n      .filter((setupType): setupType is string => !!setupType);\n    return Array.from(new Set(setupTypes));\n  }, [trades]);\n\n  const uniqueSecondarySetupTypes = useMemo(() => {\n    if (!trades) return [];\n    const setupTypes = trades\n      .map((trade) => trade.secondarySetupType)\n      .filter((setupType): setupType is string => !!setupType);\n    return Array.from(new Set(setupTypes));\n  }, [trades]);\n\n  const uniqueLiquidityTypes = useMemo(() => {\n    if (!trades) return [];\n    const liquidityTypes = trades\n      .map((trade) => trade.liquidityTaken)\n      .filter((liquidityType): liquidityType is string => !!liquidityType);\n    return Array.from(new Set(liquidityTypes));\n  }, [trades]);\n\n  const uniqueDOLTypes = useMemo(() => {\n    if (!trades) return [];\n    const dolTypes = trades\n      .filter((trade) => trade.dolAnalysis)\n      .map((trade) => trade.dolAnalysis?.dolType)\n      .filter((dolType): dolType is string => !!dolType);\n    return Array.from(new Set(dolTypes));\n  }, [trades]);\n\n  return {\n    filters,\n    setFilters,\n    handleFilterChange,\n    resetFilters,\n    filteredTrades,\n    uniqueSetups,\n    uniqueModelTypes,\n    uniquePrimarySetupTypes,\n    uniqueSecondarySetupTypes,\n    uniqueLiquidityTypes,\n    uniqueDOLTypes,\n  };\n}\n\nexport type TradeFiltersHook = ReturnType<typeof useTradeFilters>;\n", "/**\n * Trade Journal Header Component\n *\n * Displays the header for the trade journal with title and actions\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xxl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ActionButton = styled.button`\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n  background-color: transparent;\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  cursor: pointer;\n  transition: all ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RefreshButton = styled(ActionButton)`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst AddTradeButton = styled(Link)`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};\n  background-color: ${({ theme }) => theme.colors.primary};\n  color: white;\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  font-weight: 500;\n  text-decoration: none;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\ninterface TradeJournalHeaderProps {\n  refreshTrades?: () => void;\n  showFilters: boolean;\n  setShowFilters: (show: boolean) => void;\n}\n\n/**\n * Trade Journal Header Component\n */\nconst TradeJournalHeader: React.FC<TradeJournalHeaderProps> = ({\n  refreshTrades,\n  showFilters,\n  setShowFilters,\n}) => {\n  return (\n    <PageHeader>\n      <Title>Trade Journal</Title>\n      <HeaderActions>\n        <RefreshButton onClick={() => refreshTrades && refreshTrades()}>↻ Refresh</RefreshButton>\n        <ActionButton onClick={() => setShowFilters(!showFilters)}>\n          {showFilters ? 'Hide Filters' : 'Show Filters'}\n        </ActionButton>\n        <AddTradeButton to=\"/trade/new\">+ Add Trade</AddTradeButton>\n      </HeaderActions>\n    </PageHeader>\n  );\n};\n\nexport default TradeJournalHeader;\n", "/**\n * Trade Journal Filters Component\n *\n * Displays the filters for the trade journal\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { FilterState } from '../../types';\n\nconst FilterSection = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst FilterGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst FilterLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst FilterSelect = styled.select`\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  min-width: 150px;\n`;\n\nconst FilterInput = styled.input`\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst FilterButton = styled.button`\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n  background-color: transparent;\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  cursor: pointer;\n  transition: all ${({ theme }) => theme.transitions.fast};\n  align-self: flex-end;\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\ninterface TradeJournalFiltersProps {\n  filters: FilterState;\n  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n  resetFilters: () => void;\n  uniqueSetups: string[];\n  uniqueModelTypes: string[];\n  uniquePrimarySetupTypes: string[];\n  uniqueSecondarySetupTypes: string[];\n  uniqueLiquidityTypes: string[];\n  uniqueDOLTypes: string[];\n}\n\n/**\n * Trade Journal Filters Component\n */\nconst TradeJournalFilters: React.FC<TradeJournalFiltersProps> = ({\n  filters,\n  handleFilterChange,\n  resetFilters,\n  uniqueSetups,\n  uniqueModelTypes,\n  uniquePrimarySetupTypes,\n  uniqueSecondarySetupTypes,\n  uniqueLiquidityTypes,\n  uniqueDOLTypes,\n}) => {\n  return (\n    <FilterSection>\n      <FilterGroup>\n        <FilterLabel htmlFor=\"symbol\">Symbol</FilterLabel>\n        <FilterInput\n          id=\"symbol\"\n          name=\"symbol\"\n          value={filters.symbol}\n          onChange={handleFilterChange}\n          placeholder=\"AAPL, MSFT, etc.\"\n        />\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"direction\">Direction</FilterLabel>\n        <FilterSelect\n          id=\"direction\"\n          name=\"direction\"\n          value={filters.direction}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          <option value=\"Long\">Long</option>\n          <option value=\"Short\">Short</option>\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"setup\">Setup</FilterLabel>\n        <FilterSelect\n          id=\"setup\"\n          name=\"setup\"\n          value={filters.setup}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          {uniqueSetups.map((setup) => (\n            <option key={setup} value={setup}>\n              {setup}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"modelType\">Model Type</FilterLabel>\n        <FilterSelect\n          id=\"modelType\"\n          name=\"modelType\"\n          value={filters.modelType}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          {uniqueModelTypes.map((modelType) => (\n            <option key={modelType} value={modelType}>\n              {modelType}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"result\">Result</FilterLabel>\n        <FilterSelect\n          id=\"result\"\n          name=\"result\"\n          value={filters.result}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          <option value=\"win\">Wins</option>\n          <option value=\"loss\">Losses</option>\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"dateFrom\">From Date</FilterLabel>\n        <FilterInput\n          id=\"dateFrom\"\n          name=\"dateFrom\"\n          type=\"date\"\n          value={filters.dateFrom}\n          onChange={handleFilterChange}\n        />\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"dateTo\">To Date</FilterLabel>\n        <FilterInput\n          id=\"dateTo\"\n          name=\"dateTo\"\n          type=\"date\"\n          value={filters.dateTo}\n          onChange={handleFilterChange}\n        />\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"primarySetupType\">Primary Setup</FilterLabel>\n        <FilterSelect\n          id=\"primarySetupType\"\n          name=\"primarySetupType\"\n          value={filters.primarySetupType}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          {uniquePrimarySetupTypes.map((setupType) => (\n            <option key={setupType} value={setupType}>\n              {setupType}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"secondarySetupType\">Secondary Setup</FilterLabel>\n        <FilterSelect\n          id=\"secondarySetupType\"\n          name=\"secondarySetupType\"\n          value={filters.secondarySetupType}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          {uniqueSecondarySetupTypes.map((setupType) => (\n            <option key={setupType} value={setupType}>\n              {setupType}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"liquidityTaken\">Liquidity Taken</FilterLabel>\n        <FilterSelect\n          id=\"liquidityTaken\"\n          name=\"liquidityTaken\"\n          value={filters.liquidityTaken}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          {uniqueLiquidityTypes.map((liquidityType) => (\n            <option key={liquidityType} value={liquidityType}>\n              {liquidityType}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"patternQualityMin\">Pattern Quality Min</FilterLabel>\n        <FilterSelect\n          id=\"patternQualityMin\"\n          name=\"patternQualityMin\"\n          value={filters.patternQualityMin}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">Any</option>\n          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (\n            <option key={`min-${rating}`} value={rating}>\n              {rating}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"patternQualityMax\">Pattern Quality Max</FilterLabel>\n        <FilterSelect\n          id=\"patternQualityMax\"\n          name=\"patternQualityMax\"\n          value={filters.patternQualityMax}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">Any</option>\n          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (\n            <option key={`max-${rating}`} value={rating}>\n              {rating}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"dolType\">DOL Type</FilterLabel>\n        <FilterSelect\n          id=\"dolType\"\n          name=\"dolType\"\n          value={filters.dolType}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">All</option>\n          {uniqueDOLTypes.map((dolType) => (\n            <option key={dolType} value={dolType}>\n              {dolType}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"dolEffectivenessMin\">DOL Effectiveness Min</FilterLabel>\n        <FilterSelect\n          id=\"dolEffectivenessMin\"\n          name=\"dolEffectivenessMin\"\n          value={filters.dolEffectivenessMin}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">Any</option>\n          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (\n            <option key={`dol-min-${rating}`} value={rating}>\n              {rating}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterGroup>\n        <FilterLabel htmlFor=\"dolEffectivenessMax\">DOL Effectiveness Max</FilterLabel>\n        <FilterSelect\n          id=\"dolEffectivenessMax\"\n          name=\"dolEffectivenessMax\"\n          value={filters.dolEffectivenessMax}\n          onChange={handleFilterChange}\n        >\n          <option value=\"\">Any</option>\n          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (\n            <option key={`dol-max-${rating}`} value={rating}>\n              {rating}\n            </option>\n          ))}\n        </FilterSelect>\n      </FilterGroup>\n\n      <FilterButton onClick={resetFilters}>Reset Filters</FilterButton>\n    </FilterSection>\n  );\n};\n\nexport default TradeJournalFilters;\n", "/**\n * useTradeList Hook\n *\n * Custom hook for managing trade list state and behavior\n */\n\nimport { useState, useMemo } from 'react';\nimport { Trade } from '../types';\n\n/**\n * Hook for managing trade list state and behavior\n * @param trades The trades to display\n * @param expandable Whether the rows can be expanded\n */\nexport function useTradeList(trades: Trade[], expandable: boolean = false) {\n  // Track expanded rows\n  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});\n\n  // Toggle row expansion\n  const toggleRowExpansion = (tradeId: string) => {\n    if (!expandable) return;\n    \n    setExpandedRows((prev) => ({\n      ...prev,\n      [tradeId]: !prev[tradeId],\n    }));\n  };\n\n  // Check if a row is expanded\n  const isRowExpanded = (tradeId: string) => {\n    return expandable && expandedRows[tradeId];\n  };\n\n  // Sort trades by date (newest first)\n  const sortedTrades = useMemo(() => {\n    if (!trades) return [];\n    \n    return [...trades].sort((a, b) => {\n      const dateA = new Date(a.date).getTime();\n      const dateB = new Date(b.date).getTime();\n      return dateB - dateA; // Newest first\n    });\n  }, [trades]);\n\n  return {\n    sortedTrades,\n    expandedRows,\n    toggleRowExpansion,\n    isRowExpanded,\n  };\n}\n\nexport type TradeListHook = ReturnType<typeof useTradeList>;\n", "/**\n * Trade List Header Component\n *\n * Displays the header for the trade list\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { TradeColumn } from '../TradeList';\n\nconst TradeHeader = styled.div`\n  display: grid;\n  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));\n  align-items: center;\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  background-color: transparent;\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};\n  position: sticky;\n  top: 0;\n  z-index: 1;\n  backdrop-filter: blur(8px);\n`;\n\nconst TradeDetail = styled.div`\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n`;\n\ninterface TradeListHeaderProps {\n  visibleColumns: TradeColumn[];\n}\n\n/**\n * Trade List Header Component\n */\nconst TradeListHeader: React.FC<TradeListHeaderProps> = ({ visibleColumns }) => {\n  return (\n    <TradeHeader>\n      {visibleColumns.map((column) => (\n        <TradeDetail key={column.id}>{column.label}</TradeDetail>\n      ))}\n    </TradeHeader>\n  );\n};\n\nexport default TradeListHeader;\n", "/**\n * Trade List Row Component\n *\n * Displays a row in the trade list\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Trade } from '../../types';\nimport { TradeColumn } from '../TradeList';\n\nconst TradeItem = styled.div<{ expanded?: boolean }>`\n  display: grid;\n  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.cardBackground};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: all ${({ theme }) => theme.transitions.fast};\n  cursor: ${({ expanded }) => (expanded !== undefined ? 'pointer' : 'default')};\n  position: relative;\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.chartGrid};\n  }\n`;\n\ninterface TradeListRowProps {\n  trade: Trade;\n  visibleColumns: TradeColumn[];\n  expanded?: boolean;\n  toggleRowExpansion: (tradeId: string) => void;\n}\n\n/**\n * Trade List Row Component\n */\nconst TradeListRow: React.FC<TradeListRowProps> = ({\n  trade,\n  visibleColumns,\n  expanded,\n  toggleRowExpansion,\n}) => {\n  return (\n    <TradeItem\n      expanded={expanded}\n      onClick={() => toggleRowExpansion(trade.id)}\n    >\n      {visibleColumns.map((column) => (\n        <React.Fragment key={column.id}>{column.accessor(trade)}</React.Fragment>\n      ))}\n    </TradeItem>\n  );\n};\n\nexport default TradeListRow;\n", "/**\n * Trade List Expanded Row Component\n *\n * Displays expanded details for a trade row\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Trade } from '../../types';\n\nconst ExpandedContent = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ExpandedSection = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst DetailGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailItem = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst DetailLabel = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst DetailValue = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ActionButton = styled(Link)`\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.primary};\n  color: white;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  text-decoration: none;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\ninterface TradeListExpandedRowProps {\n  trade: Trade;\n}\n\n/**\n * Trade List Expanded Row Component\n */\nconst TradeListExpandedRow: React.FC<TradeListExpandedRowProps> = ({ trade }) => {\n  return (\n    <ExpandedContent>\n      <ExpandedSection>\n        <SectionTitle>Trade Details</SectionTitle>\n        <DetailGrid>\n          <DetailItem>\n            <DetailLabel>Symbol</DetailLabel>\n            <DetailValue>{trade.symbol}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Date</DetailLabel>\n            <DetailValue>{trade.date}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Direction</DetailLabel>\n            <DetailValue>{trade.direction}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Entry Price</DetailLabel>\n            <DetailValue>${trade.entry.toFixed(2)}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Exit Price</DetailLabel>\n            <DetailValue>${trade.exit.toFixed(2)}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Quantity</DetailLabel>\n            <DetailValue>{trade.size}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Profit/Loss</DetailLabel>\n            <DetailValue\n              style={{\n                color:\n                  trade.profitLoss > 0\n                    ? 'green'\n                    : trade.profitLoss < 0\n                    ? 'red'\n                    : 'inherit',\n              }}\n            >\n              ${trade.profitLoss.toFixed(2)}\n            </DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>R-Multiple</DetailLabel>\n            <DetailValue>{trade.rMultiple?.toFixed(2) || 'N/A'}</DetailValue>\n          </DetailItem>\n        </DetailGrid>\n      </ExpandedSection>\n\n      {trade.setup && (\n        <ExpandedSection>\n          <SectionTitle>Strategy</SectionTitle>\n          <DetailGrid>\n            <DetailItem>\n              <DetailLabel>Setup</DetailLabel>\n              <DetailValue>{trade.setup}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Model Type</DetailLabel>\n              <DetailValue>{trade.modelType || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Primary Setup</DetailLabel>\n              <DetailValue>{trade.primarySetupType || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Secondary Setup</DetailLabel>\n              <DetailValue>{trade.secondarySetupType || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Liquidity Taken</DetailLabel>\n              <DetailValue>{trade.liquidityTaken || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Pattern Quality</DetailLabel>\n              <DetailValue>{trade.patternQuality || 'N/A'}</DetailValue>\n            </DetailItem>\n          </DetailGrid>\n        </ExpandedSection>\n      )}\n\n      {trade.dolAnalysis && (\n        <ExpandedSection>\n          <SectionTitle>DOL Analysis</SectionTitle>\n          <DetailGrid>\n            <DetailItem>\n              <DetailLabel>DOL Type</DetailLabel>\n              <DetailValue>{trade.dolAnalysis.dolType || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>DOL Strength</DetailLabel>\n              <DetailValue>{trade.dolAnalysis.dolStrength || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>DOL Reaction</DetailLabel>\n              <DetailValue>{trade.dolAnalysis.dolReaction || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>DOL Effectiveness</DetailLabel>\n              <DetailValue>{trade.dolAnalysis.effectiveness || 'N/A'}</DetailValue>\n            </DetailItem>\n          </DetailGrid>\n        </ExpandedSection>\n      )}\n\n      {trade.notes && (\n        <ExpandedSection>\n          <SectionTitle>Notes</SectionTitle>\n          <DetailItem>\n            <DetailValue>{trade.notes}</DetailValue>\n          </DetailItem>\n        </ExpandedSection>\n      )}\n\n      <ActionButtons>\n        <ActionButton to={`/trade/edit/${trade.id}`}>Edit Trade</ActionButton>\n      </ActionButtons>\n    </ExpandedContent>\n  );\n};\n\nexport default TradeListExpandedRow;\n", "/**\n * Trade List Empty Component\n *\n * Displays a message when there are no trades to show\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\n\nconst EmptyContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing.xl};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  text-align: center;\n`;\n\nconst EmptyTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst EmptyDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;\n  max-width: 500px;\n`;\n\nconst AddTradeButton = styled(Link)`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};\n  background-color: ${({ theme }) => theme.colors.primary};\n  color: white;\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  font-weight: 500;\n  text-decoration: none;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\ninterface TradeListEmptyProps {\n  filtered?: boolean;\n}\n\n/**\n * Trade List Empty Component\n */\nconst TradeListEmpty: React.FC<TradeListEmptyProps> = ({ filtered }) => {\n  return (\n    <EmptyContainer>\n      <EmptyTitle>{filtered ? 'No matching trades found' : 'No trades yet'}</EmptyTitle>\n      <EmptyDescription>\n        {filtered\n          ? 'Try adjusting your filters to see more results.'\n          : 'Start tracking your trades to gain insights into your trading performance.'}\n      </EmptyDescription>\n      {!filtered && <AddTradeButton to=\"/trade/new\">+ Add Your First Trade</AddTradeButton>}\n    </EmptyContainer>\n  );\n};\n\nexport default TradeListEmpty;\n", "/**\n * Trade List Loading Component\n *\n * Displays a loading state for the trade list\n */\n\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\n\nconst shimmer = keyframes`\n  0% {\n    background-position: -1000px 0;\n  }\n  100% {\n    background-position: 1000px 0;\n  }\n`;\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  padding: ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst LoadingRow = styled.div`\n  height: 60px;\n  background: linear-gradient(\n    to right,\n    ${({ theme }) => theme.colors.background} 8%,\n    ${({ theme }) => theme.colors.cardBackground} 18%,\n    ${({ theme }) => theme.colors.background} 33%\n  );\n  background-size: 2000px 100%;\n  animation: ${shimmer} 1.5s infinite linear;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n`;\n\ninterface TradeListLoadingProps {\n  rowCount?: number;\n}\n\n/**\n * Trade List Loading Component\n */\nconst TradeListLoading: React.FC<TradeListLoadingProps> = ({ rowCount = 5 }) => {\n  return (\n    <LoadingContainer>\n      {Array.from({ length: rowCount }).map((_, index) => (\n        <LoadingRow key={index} />\n      ))}\n    </LoadingContainer>\n  );\n};\n\nexport default TradeListLoading;\n", "/**\n * Trade List Component\n *\n * Displays a list of trades with expandable rows\n */\n\nimport React, { useMemo } from 'react';\nimport styled from 'styled-components';\nimport { Trade } from '../types';\nimport { useTradeList } from '../hooks/useTradeList';\nimport {\n  TradeListHeader,\n  TradeListRow,\n  TradeListExpandedRow,\n  TradeListEmpty,\n  TradeListLoading,\n} from './list';\n\nconst TradeListContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst TradeDetail = styled.div<{ profit?: boolean; loss?: boolean }>`\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n  color: ${({ theme, profit, loss }) =>\n    profit\n      ? theme.colors.success\n      : loss\n      ? theme.colors.danger\n      : theme.colors.textPrimary};\n  font-weight: ${({ profit, loss }) => (profit || loss ? 600 : 'normal')};\n`;\n\nconst Badge = styled.span<{ type?: 'long' | 'short' }>`\n  display: inline-block;\n  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  font-weight: 600;\n  text-transform: uppercase;\n  background-color: ${({ theme, type }) =>\n    type === 'long'\n      ? theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'\n      : type === 'short'\n      ? theme.colors.dangerLight || 'rgba(244, 67, 54, 0.1)'\n      : theme.colors.background};\n  color: ${({ theme, type }) =>\n    type === 'long'\n      ? theme.colors.success\n      : type === 'short'\n      ? theme.colors.danger\n      : theme.colors.textPrimary};\n`;\n\nexport interface TradeColumn {\n  id: string;\n  label: string;\n  accessor: (trade: Trade) => React.ReactNode;\n}\n\ninterface TradeListProps {\n  trades: Trade[];\n  isLoading?: boolean;\n  expandable?: boolean;\n}\n\n/**\n * Trade List Component\n */\nconst TradeList: React.FC<TradeListProps> = ({\n  trades,\n  isLoading = false,\n  expandable = false,\n}) => {\n  const { sortedTrades, toggleRowExpansion, isRowExpanded } = useTradeList(trades, expandable);\n\n  // Define columns for the trade list\n  const columns: TradeColumn[] = useMemo(\n    () => [\n      {\n        id: 'date',\n        label: 'Date',\n        accessor: (trade) => <TradeDetail>{trade.date}</TradeDetail>,\n      },\n      {\n        id: 'symbol',\n        label: 'Symbol',\n        accessor: (trade) => <TradeDetail>{trade.symbol}</TradeDetail>,\n      },\n      {\n        id: 'direction',\n        label: 'Direction',\n        accessor: (trade) => (\n          <TradeDetail>\n            <Badge type={trade.direction.toLowerCase() as 'long' | 'short'}>\n              {trade.direction}\n            </Badge>\n          </TradeDetail>\n        ),\n      },\n      {\n        id: 'setup',\n        label: 'Setup',\n        accessor: (trade) => <TradeDetail>{trade.setup || 'N/A'}</TradeDetail>,\n      },\n      {\n        id: 'entry',\n        label: 'Entry',\n        accessor: (trade) => <TradeDetail>${trade.entry.toFixed(2)}</TradeDetail>,\n      },\n      {\n        id: 'exit',\n        label: 'Exit',\n        accessor: (trade) => <TradeDetail>${trade.exit.toFixed(2)}</TradeDetail>,\n      },\n      {\n        id: 'size',\n        label: 'Size',\n        accessor: (trade) => <TradeDetail>{trade.size}</TradeDetail>,\n      },\n      {\n        id: 'profitLoss',\n        label: 'P/L',\n        accessor: (trade) => (\n          <TradeDetail profit={trade.profitLoss > 0} loss={trade.profitLoss < 0}>\n            ${trade.profitLoss.toFixed(2)}\n          </TradeDetail>\n        ),\n      },\n    ],\n    []\n  );\n\n  // Set grid template columns based on number of columns\n  const gridTemplateColumns = `repeat(${columns.length}, 1fr)`;\n\n  // If loading, show loading state\n  if (isLoading) {\n    return <TradeListLoading rowCount={5} />;\n  }\n\n  // If no trades, show empty state\n  if (!sortedTrades || sortedTrades.length === 0) {\n    return <TradeListEmpty filtered={trades && trades.length > 0} />;\n  }\n\n  return (\n    <TradeListContainer style={{ '--grid-template-columns': gridTemplateColumns } as any}>\n      <TradeListHeader visibleColumns={columns} />\n\n      {sortedTrades.map((trade) => (\n        <React.Fragment key={trade.id}>\n          <TradeListRow\n            trade={trade}\n            visibleColumns={columns}\n            expanded={isRowExpanded(trade.id)}\n            toggleRowExpansion={toggleRowExpansion}\n          />\n          {isRowExpanded(trade.id) && <TradeListExpandedRow trade={trade} />}\n        </React.Fragment>\n      ))}\n    </TradeListContainer>\n  );\n};\n\nexport default TradeList;\n", "/**\n * Trade Journal Content Component\n *\n * Displays the content section of the trade journal\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport TradeList from '../TradeList';\nimport TradeJournalFilters from './TradeJournalFilters';\nimport { Trade } from '../../types';\n\nconst ContentSection = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst ErrorMessage = styled.div`\n  color: ${({ theme }) => theme.colors.danger};\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\ninterface TradeJournalContentProps {\n  error: string | null;\n  showFilters: boolean;\n  filteredTrades: Trade[];\n  isLoading: boolean;\n  filters: any;\n  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n  resetFilters: () => void;\n  uniqueSetups: string[];\n  uniqueModelTypes: string[];\n  uniquePrimarySetupTypes: string[];\n  uniqueSecondarySetupTypes: string[];\n  uniqueLiquidityTypes: string[];\n  uniqueDOLTypes: string[];\n}\n\n/**\n * Trade Journal Content Component\n */\nconst TradeJournalContent: React.FC<TradeJournalContentProps> = ({\n  error,\n  showFilters,\n  filteredTrades,\n  isLoading,\n  filters,\n  handleFilterChange,\n  resetFilters,\n  uniqueSetups,\n  uniqueModelTypes,\n  uniquePrimarySetupTypes,\n  uniqueSecondarySetupTypes,\n  uniqueLiquidityTypes,\n  uniqueDOLTypes,\n}) => {\n  return (\n    <>\n      {error && <ErrorMessage>{error}</ErrorMessage>}\n\n      <ContentSection>\n        <SectionTitle>Recent Trades</SectionTitle>\n\n        {showFilters && (\n          <TradeJournalFilters\n            filters={filters}\n            handleFilterChange={handleFilterChange}\n            resetFilters={resetFilters}\n            uniqueSetups={uniqueSetups}\n            uniqueModelTypes={uniqueModelTypes}\n            uniquePrimarySetupTypes={uniquePrimarySetupTypes}\n            uniqueSecondarySetupTypes={uniqueSecondarySetupTypes}\n            uniqueLiquidityTypes={uniqueLiquidityTypes}\n            uniqueDOLTypes={uniqueDOLTypes}\n          />\n        )}\n\n        <TradeList trades={filteredTrades} isLoading={isLoading} expandable={true} />\n      </ContentSection>\n    </>\n  );\n};\n\nexport default TradeJournalContent;\n", "/**\n * Trade Journal Component\n *\n * This component displays a list of recorded trades with filtering options\n * and allows adding new trades.\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useTradeJournal } from './hooks/useTradeJournal';\nimport { useTradeFilters } from './hooks/useTradeFilters';\nimport { TradeJournalHeader, TradeJournalContent } from './components/journal';\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\n/**\n * TradeJournal Component\n *\n * Main component for the Trade Journal feature that displays trade history\n * with filtering options and provides navigation to add new trades.\n */\nconst TradeJournal: React.FC = () => {\n  const { trades, isLoading, error, refreshTrades } = useTradeJournal();\n  const [showFilters, setShowFilters] = useState(false);\n  \n  const {\n    filters,\n    handleFilterChange,\n    resetFilters,\n    filteredTrades,\n    uniqueSetups,\n    uniqueModelTypes,\n    uniquePrimarySetupTypes,\n    uniqueSecondarySetupTypes,\n    uniqueLiquidityTypes,\n    uniqueDOLTypes,\n  } = useTradeFilters(trades);\n\n  return (\n    <PageContainer>\n      <TradeJournalHeader\n        refreshTrades={refreshTrades}\n        showFilters={showFilters}\n        setShowFilters={setShowFilters}\n      />\n\n      <TradeJournalContent\n        error={error}\n        showFilters={showFilters}\n        filteredTrades={filteredTrades}\n        isLoading={isLoading}\n        filters={filters}\n        handleFilterChange={handleFilterChange}\n        resetFilters={resetFilters}\n        uniqueSetups={uniqueSetups}\n        uniqueModelTypes={uniqueModelTypes}\n        uniquePrimarySetupTypes={uniquePrimarySetupTypes}\n        uniqueSecondarySetupTypes={uniqueSecondarySetupTypes}\n        uniqueLiquidityTypes={uniqueLiquidityTypes}\n        uniqueDOLTypes={uniqueDOLTypes}\n      />\n    </PageContainer>\n  );\n};\n\nexport default TradeJournal;\n"], "names": ["useTradeJournal", "state", "setState", "useState", "trades", "isLoading", "error", "useEffect", "prev", "sortedTrades", "tradeStorage", "getAllTrades", "sort", "a", "b", "Date", "date", "getTime", "refreshTrades", "useTradeFilters", "filters", "setFilters", "symbol", "direction", "setup", "modelType", "result", "dateFrom", "dateTo", "primarySetupType", "secondarySetupType", "liquidityTaken", "patternQualityMin", "patternQualityMax", "dolType", "dolEffectivenessMin", "dolEffectivenessMax", "handleFilterChange", "e", "name", "value", "target", "resetFilters", "filteredTrades", "useMemo", "filter", "trade", "toLowerCase", "includes", "isWin", "profitLoss", "tradeDate", "fromDate", "toDate", "setHours", "patternQuality", "minQuality", "parseInt", "isNaN", "maxQuality", "dolAnalysis", "minEffectiveness", "effectiveness", "maxEffectiveness", "uniqueSetups", "setups", "map", "Array", "from", "Set", "uniqueModelTypes", "modelTypes", "uniquePrimarySetupTypes", "setupTypes", "setupType", "uniqueSecondarySetupTypes", "uniqueLiquidityTypes", "liquidityTypes", "liquidityType", "uniqueDOLTypes", "dolTypes", "<PERSON><PERSON><PERSON><PERSON>", "div", "withConfig", "displayName", "componentId", "theme", "spacing", "lg", "Title", "h1", "fontSizes", "xxl", "colors", "textPrimary", "HeaderActions", "md", "ActionButton", "button", "xs", "sm", "border", "borderRadius", "textSecondary", "transitions", "fast", "background", "RefreshButton", "styled", "AddTradeButton", "Link", "primary", "primaryDark", "TradeJournalHeader", "showFilters", "setShowFilters", "jsx", "FilterSection", "FilterGroup", "Filter<PERSON>abel", "label", "FilterSelect", "select", "FilterInput", "input", "FilterButton", "TradeJournalFilters", "jsxs", "rating", "useTradeList", "expandable", "expandedRows", "setExpandedRows", "toggleRowExpansion", "tradeId", "isRowExpanded", "dateA", "TradeHeader", "TradeDetail", "TradeListHeader", "visibleColumns", "column", "id", "TradeItem", "cardBackground", "expanded", "undefined", "chartGrid", "TradeListRow", "React", "accessor", "ExpandedContent", "ExpandedSection", "SectionTitle", "h3", "DetailGrid", "DetailItem", "DetailLabel", "span", "DetailValue", "ActionButtons", "TradeListExpandedRow", "entry", "toFixed", "exit", "size", "color", "rMultiple", "dol<PERSON><PERSON><PERSON><PERSON>", "dolReaction", "notes", "EmptyContainer", "xl", "EmptyTitle", "EmptyDescription", "p", "TradeListEmpty", "filtered", "shimmer", "keyframes", "LoadingContainer", "LoadingRow", "TradeListLoading", "rowCount", "length", "_", "index", "TradeListContainer", "profit", "loss", "success", "danger", "Badge", "xxs", "type", "successLight", "dangerLight", "TradeList", "columns", "gridTemplateColumns", "ContentSection", "surface", "shadows", "h2", "ErrorMessage", "TradeJournalContent", "<PERSON><PERSON><PERSON><PERSON>", "TradeJournal"], "mappings": "2OAeO,MAAMA,EAAkBA,IAAyB,CACtD,KAAM,CAACC,EAAOC,CAAQ,EAAIC,WAA4B,CACpDC,OAAQ,CAAE,EACVC,UAAW,GACXC,MAAO,IAAA,CACR,EAEDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CAEFL,EAAoBM,IAAA,CAClB,GAAGA,EACHH,UAAW,GACXC,MAAO,IACP,EAAA,EAMIG,MAAAA,EAAe,CAAC,GAHP,MAAMC,EAAaC,cAGH,EAAEC,KAC/B,CAACC,EAAGC,IAAM,IAAIC,KAAKD,EAAEE,IAAI,EAAEC,UAAY,IAAIF,KAAKF,EAAEG,IAAI,EAAEC,QAAAA,CAC1D,EAESf,EAAA,CACPE,OAAQK,EACRJ,UAAW,GACXC,MAAO,IAAA,CACR,QACMA,GACCA,QAAAA,MAAM,yBAA0BA,CAAK,EAC7CJ,EAAoBM,IAAA,CAClB,GAAGA,EACHH,UAAW,GACXC,MAAO,0CACP,EAAA,CACJ,CAAA,IAIJ,EAAG,CAAE,CAAA,EAiCE,CACL,GAAGL,EACHiB,cAhCoB,SAAY,CAC5B,GAAA,CACFhB,EAAoBM,IAAA,CAClB,GAAGA,EACHH,UAAW,GACXC,MAAO,IACP,EAAA,EAKIG,MAAAA,EAAe,CAAC,GAHP,MAAMC,EAAaC,cAGH,EAAEC,KAC/B,CAACC,EAAGC,IAAM,IAAIC,KAAKD,EAAEE,IAAI,EAAEC,UAAY,IAAIF,KAAKF,EAAEG,IAAI,EAAEC,QAAAA,CAC1D,EAESf,EAAA,CACPE,OAAQK,EACRJ,UAAW,GACXC,MAAO,IAAA,CACR,QACMA,GACCA,QAAAA,MAAM,2BAA4BA,CAAK,EAC/CJ,EAAoBM,IAAA,CAClB,GAAGA,EACHH,UAAW,GACXC,MAAO,6CACP,EAAA,CACJ,CAAA,CAKAY,CAEJ,EChFO,SAASC,EAAgBf,EAAiB,CAE/C,KAAM,CAACgB,EAASC,CAAU,EAAIlB,WAAsB,CAClDmB,OAAQ,GACRC,UAAW,GACXC,MAAO,GACPC,UAAW,GACXC,OAAQ,GACRC,SAAU,GACVC,OAAQ,GACRC,iBAAkB,GAClBC,mBAAoB,GACpBC,eAAgB,GAChBC,kBAAmB,GACnBC,kBAAmB,GACnBC,QAAS,GACTC,oBAAqB,GACrBC,oBAAqB,EAAA,CACtB,EAGKC,EAAsBC,GAA+D,CACnF,KAAA,CAAEC,KAAAA,EAAMC,MAAAA,CAAAA,EAAUF,EAAEG,OAC1BpB,EAAsBb,IAAA,CACpB,GAAGA,EACH,CAAC+B,CAAI,EAAGC,CACR,EAAA,CAAA,EAIEE,EAAeA,IAAM,CACdrB,EAAA,CACTC,OAAQ,GACRC,UAAW,GACXC,MAAO,GACPC,UAAW,GACXC,OAAQ,GACRC,SAAU,GACVC,OAAQ,GACRC,iBAAkB,GAClBC,mBAAoB,GACpBC,eAAgB,GAChBC,kBAAmB,GACnBC,kBAAmB,GACnBC,QAAS,GACTC,oBAAqB,GACrBC,oBAAqB,EAAA,CACtB,CAAA,EAIGO,EAAiBC,EAAAA,QAAQ,IACxBxC,EAEEA,EAAOyC,OAAkBC,GAAA,CAiB9B,GAfI1B,EAAQE,QAAU,CAACwB,EAAMxB,OAAOyB,cAAcC,SAAS5B,EAAQE,OAAOyB,YAAa,CAAA,GAKnF3B,EAAQG,WAAauB,EAAMvB,YAAcH,EAAQG,WAKjDH,EAAQI,OAASsB,EAAMtB,QAAUJ,EAAQI,OAKzCJ,EAAQK,WAAaqB,EAAMrB,YAAcL,EAAQK,UAC5C,MAAA,GAIT,GAAIL,EAAQM,OAAQ,CACZuB,MAAAA,EAAQH,EAAMI,WAAa,EAC5B9B,GAAAA,EAAQM,SAAW,OAAS,CAACuB,GAAW7B,EAAQM,SAAW,QAAUuB,EACjE,MAAA,GAKX,GAAI7B,EAAQO,SAAU,CACpB,MAAMwB,EAAY,IAAIpC,KAAK+B,EAAM9B,IAAI,EAC/BoC,EAAW,IAAIrC,KAAKK,EAAQO,QAAQ,EAC1C,GAAIwB,EAAYC,EACP,MAAA,GAIX,GAAIhC,EAAQQ,OAAQ,CAClB,MAAMuB,EAAY,IAAIpC,KAAK+B,EAAM9B,IAAI,EAC/BqC,EAAS,IAAItC,KAAKK,EAAQQ,MAAM,EAGtC,GADAyB,EAAOC,SAAS,GAAI,GAAI,GAAI,GAAG,EAC3BH,EAAYE,EACP,MAAA,GAeX,GAVIjC,EAAQS,kBAAoBiB,EAAMjB,mBAAqBT,EAAQS,kBAK/DT,EAAQU,oBAAsBgB,EAAMhB,qBAAuBV,EAAQU,oBAKnEV,EAAQW,gBAAkBe,EAAMf,iBAAmBX,EAAQW,eACtD,MAAA,GAILX,GAAAA,EAAQY,mBAAqBc,EAAMS,eAAgB,CAC/CC,MAAAA,EAAaC,SAASrC,EAAQY,iBAAiB,EACrD,GAAI,CAAC0B,MAAMF,CAAU,GAAKV,EAAMS,eAAiBC,EACxC,MAAA,GAKPpC,GAAAA,EAAQa,mBAAqBa,EAAMS,eAAgB,CAC/CI,MAAAA,EAAaF,SAASrC,EAAQa,iBAAiB,EACrD,GAAI,CAACyB,MAAMC,CAAU,GAAKb,EAAMS,eAAiBI,EACxC,MAAA,GAKPvC,GAAAA,EAAQc,SAAWY,EAAMc,aAAed,EAAMc,YAAY1B,UAAYd,EAAQc,QACzE,MAAA,GAILd,GAAAA,EAAQe,qBAAuBW,EAAMc,YAAa,CAC9CC,MAAAA,EAAmBJ,SAASrC,EAAQe,mBAAmB,EAC7D,GAAI,CAACuB,MAAMG,CAAgB,GAAKf,EAAMc,YAAYE,cAAgBD,EACzD,MAAA,GAKPzC,GAAAA,EAAQgB,qBAAuBU,EAAMc,YAAa,CAC9CG,MAAAA,EAAmBN,SAASrC,EAAQgB,mBAAmB,EAC7D,GAAI,CAACsB,MAAMK,CAAgB,GAAKjB,EAAMc,YAAYE,cAAgBC,EACzD,MAAA,GAIJ,MAAA,EAAA,CACR,EAvGmB,GAwGnB,CAAC3D,EAAQgB,CAAO,CAAC,EAGd4C,EAAepB,EAAAA,QAAQ,IAAM,CACjC,GAAI,CAACxC,EAAQ,MAAO,GACpB,MAAM6D,EAAS7D,EAAO8D,IAAKpB,GAAUA,EAAMtB,KAAK,EAAEqB,OAAQrB,GAA2B,CAAC,CAACA,CAAK,EAC5F,OAAO2C,MAAMC,KAAK,IAAIC,IAAIJ,CAAM,CAAC,CAAA,EAChC,CAAC7D,CAAM,CAAC,EAELkE,EAAmB1B,EAAAA,QAAQ,IAAM,CACrC,GAAI,CAACxC,EAAQ,MAAO,GACpB,MAAMmE,EAAanE,EAChB8D,IAAKpB,GAAUA,EAAMrB,SAAS,EAC9BoB,OAAQpB,GAAmC,CAAC,CAACA,CAAS,EACzD,OAAO0C,MAAMC,KAAK,IAAIC,IAAIE,CAAU,CAAC,CAAA,EACpC,CAACnE,CAAM,CAAC,EAELoE,EAA0B5B,EAAAA,QAAQ,IAAM,CAC5C,GAAI,CAACxC,EAAQ,MAAO,GACpB,MAAMqE,EAAarE,EAChB8D,IAAKpB,GAAUA,EAAMjB,gBAAgB,EACrCgB,OAAQ6B,GAAmC,CAAC,CAACA,CAAS,EACzD,OAAOP,MAAMC,KAAK,IAAIC,IAAII,CAAU,CAAC,CAAA,EACpC,CAACrE,CAAM,CAAC,EAELuE,EAA4B/B,EAAAA,QAAQ,IAAM,CAC9C,GAAI,CAACxC,EAAQ,MAAO,GACpB,MAAMqE,EAAarE,EAChB8D,IAAKpB,GAAUA,EAAMhB,kBAAkB,EACvCe,OAAQ6B,GAAmC,CAAC,CAACA,CAAS,EACzD,OAAOP,MAAMC,KAAK,IAAIC,IAAII,CAAU,CAAC,CAAA,EACpC,CAACrE,CAAM,CAAC,EAELwE,EAAuBhC,EAAAA,QAAQ,IAAM,CACzC,GAAI,CAACxC,EAAQ,MAAO,GACpB,MAAMyE,EAAiBzE,EACpB8D,IAAKpB,GAAUA,EAAMf,cAAc,EACnCc,OAAQiC,GAA2C,CAAC,CAACA,CAAa,EACrE,OAAOX,MAAMC,KAAK,IAAIC,IAAIQ,CAAc,CAAC,CAAA,EACxC,CAACzE,CAAM,CAAC,EAEL2E,EAAiBnC,EAAAA,QAAQ,IAAM,CACnC,GAAI,CAACxC,EAAQ,MAAO,GACpB,MAAM4E,EAAW5E,EACdyC,UAAkBC,EAAMc,WAAW,EACnCM,IAAKpB,GAAAA,OAAUA,OAAAA,EAAAA,EAAMc,cAANd,YAAAA,EAAmBZ,QAAO,EACzCW,OAAQX,GAA+B,CAAC,CAACA,CAAO,EACnD,OAAOiC,MAAMC,KAAK,IAAIC,IAAIW,CAAQ,CAAC,CAAA,EAClC,CAAC5E,CAAM,CAAC,EAEJ,MAAA,CACLgB,QAAAA,EACAC,WAAAA,EACAgB,mBAAAA,EACAK,aAAAA,EACAC,eAAAA,EACAqB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CAAAA,CAEJ,CC9NA,MAAME,EAAoBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIV,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAG5CC,EAAeC,EAAAA,GAAEP,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACR,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUC,IAEnC,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMO,OAAOC,WAAW,EAI5CC,EAAuBb,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EAEvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAGlCC,EAAsBC,EAAAA,OAAMf,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,kDAAA,kBAAA,UAAA,kCAAA,6BAAA,IAAA,EACrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,GAAM,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,GAEvD,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOQ,OAC/B,CAAC,CAAEf,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOU,cAEnB,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMkB,YAAYC,KAG7B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,UAAU,EAIxDC,EAAgBC,EAAOX,CAAY,EAACd,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGjC,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,EAAE,EAGlCU,EAAiBD,EAAOE,CAAI,EAAC3B,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yEAAA,IAAA,qBAAA,8BAAA,qEAAA,6BAAA,IAAA,EAItB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,GAAM,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GACvD,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMO,OAAOkB,QAE/B,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMgB,aAAaN,GAGpB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMkB,YAAYC,KAG1C,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMO,OAAOmB,WAAW,EAazDC,EAAwDA,CAAC,CAC7D/F,cAAAA,EACAgG,YAAAA,EACAC,eAAAA,CACF,WAEKlC,EACC,CAAA,SAAA,CAAAmC,EAAAA,IAAC3B,GAAM,SAAa,eAAA,CAAA,SACnBM,EACC,CAAA,SAAA,CAAAqB,MAACT,GAAc,QAAS,IAAMzF,GAAiBA,IAAiB,SAAS,YAAA,EACzEkG,EAAAA,IAACnB,EAAa,CAAA,QAAS,IAAMkB,EAAe,CAACD,CAAW,EACrDA,SAAcA,EAAA,eAAiB,cAClC,CAAA,EACCE,EAAA,IAAAP,EAAA,CAAe,GAAG,aAAa,SAAW,cAAA,CAAA,EAC7C,CACF,CAAA,CAAA,EChFEQ,EAAuBnC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,mCAAA,kBAAA,GAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,GACnB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAG5C8B,EAAqBpC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,EAAE,EAGlCoB,EAAqBC,EAAAA,MAAKrC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUS,GACnC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOU,aAAa,EAG9CkB,EAAsBC,EAAAA,OAAMvC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,qBAAA,kBAAA,qBAAA,UAAA,mBAAA,EACrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,GAAM,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,GACvD,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOQ,OAC/B,CAAC,CAAEf,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GAC/B,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,WACvC,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMO,OAAOC,WAAW,EAI5C6B,EAAqBC,EAAAA,MAAKzC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,qBAAA,kBAAA,qBAAA,UAAA,GAAA,EACnB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,GAAM,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,GACvD,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOQ,OAC/B,CAAC,CAAEf,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GAC/B,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,WACvC,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMO,OAAOC,WAAW,EAG5C+B,EAAsB3B,EAAAA,OAAMf,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,kDAAA,kBAAA,UAAA,kCAAA,iDAAA,IAAA,EACrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,GAAM,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,GAEvD,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOQ,OAC/B,CAAC,CAAEf,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOU,cAEnB,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMkB,YAAYC,KAI7B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,UAAU,EAmBxDoB,EAA0DA,CAAC,CAC/D1G,QAAAA,EACAiB,mBAAAA,EACAK,aAAAA,EACAsB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CACF,WAEKsC,EACC,CAAA,SAAA,CAAAU,OAACT,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,SAAS,SAAM,SAAA,EACnCH,EAAAA,IAAAO,EAAA,CACC,GAAG,SACH,KAAK,SACL,MAAOvG,EAAQE,OACf,SAAUe,EACV,YAAY,kBAAkB,CAAA,CAAA,EAElC,SAECiF,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,YAAY,SAAS,YAAA,EAC1CQ,EAAAA,KAACN,EACC,CAAA,GAAG,YACH,KAAK,YACL,MAAOrG,EAAQG,UACf,SAAUc,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnBA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAI,OAAA,EACxBA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,CAAA,EAC7B,CAAA,EACF,SAECE,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,QAAQ,SAAK,QAAA,EAClCQ,EAAAA,KAACN,EACC,CAAA,GAAG,QACH,KAAK,QACL,MAAOrG,EAAQI,MACf,SAAUa,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnBpD,EAAaE,IACZ1C,GAAA4F,EAAA,IAAC,UAAmB,MAAO5F,EACxBA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAEC8F,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,YAAY,SAAU,aAAA,EAC3CQ,EAAAA,KAACN,EACC,CAAA,GAAG,YACH,KAAK,YACL,MAAOrG,EAAQK,UACf,SAAUY,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnB9C,EAAiBJ,IAChBzC,GAAA2F,EAAA,IAAC,UAAuB,MAAO3F,EAC5BA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAEC6F,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,SAAS,SAAM,SAAA,EACpCQ,EAAAA,KAACN,EACC,CAAA,GAAG,SACH,KAAK,SACL,MAAOrG,EAAQM,OACf,SAAUW,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnBA,EAAA,IAAA,SAAA,CAAO,MAAM,MAAM,SAAI,OAAA,EACvBA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAM,SAAA,CAAA,EAC7B,CAAA,EACF,SAECE,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,WAAW,SAAS,YAAA,EACxCH,EAAAA,IAAAO,EAAA,CACC,GAAG,WACH,KAAK,WACL,KAAK,OACL,MAAOvG,EAAQO,SACf,SAAUU,CAAmB,CAAA,CAAA,EAEjC,SAECiF,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,SAAS,SAAO,UAAA,EACpCH,EAAAA,IAAAO,EAAA,CACC,GAAG,SACH,KAAK,SACL,KAAK,OACL,MAAOvG,EAAQQ,OACf,SAAUS,CAAmB,CAAA,CAAA,EAEjC,SAECiF,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,mBAAmB,SAAa,gBAAA,EACrDQ,EAAAA,KAACN,EACC,CAAA,GAAG,mBACH,KAAK,mBACL,MAAOrG,EAAQS,iBACf,SAAUQ,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnB5C,EAAwBN,IACvBQ,GAAA0C,EAAA,IAAC,UAAuB,MAAO1C,EAC5BA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAEC4C,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,qBAAqB,SAAe,kBAAA,EACzDQ,EAAAA,KAACN,EACC,CAAA,GAAG,qBACH,KAAK,qBACL,MAAOrG,EAAQU,mBACf,SAAUO,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnBzC,EAA0BT,IACzBQ,GAAA0C,EAAA,IAAC,UAAuB,MAAO1C,EAC5BA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAEC4C,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,iBAAiB,SAAe,kBAAA,EACrDQ,EAAAA,KAACN,EACC,CAAA,GAAG,iBACH,KAAK,iBACL,MAAOrG,EAAQW,eACf,SAAUM,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnBxC,EAAqBV,IACpBY,GAAAsC,EAAA,IAAC,UAA2B,MAAOtC,EAChCA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAECwC,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,oBAAoB,SAAmB,sBAAA,EAC5DQ,EAAAA,KAACN,EACC,CAAA,GAAG,oBACH,KAAK,oBACL,MAAOrG,EAAQY,kBACf,SAAUK,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,EAAElD,OAC9BkD,MAAA,SAAA,CAA6B,MAAOY,EAClCA,SAAAA,CAAAA,EADU,OAAOA,GAEpB,CACD,CAAA,EACH,CAAA,EACF,SAECV,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,oBAAoB,SAAmB,sBAAA,EAC5DQ,EAAAA,KAACN,EACC,CAAA,GAAG,oBACH,KAAK,oBACL,MAAOrG,EAAQa,kBACf,SAAUI,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,EAAElD,OAC9BkD,MAAA,SAAA,CAA6B,MAAOY,EAClCA,SAAAA,CAAAA,EADU,OAAOA,GAEpB,CACD,CAAA,EACH,CAAA,EACF,SAECV,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,UAAU,SAAQ,WAAA,EACvCQ,EAAAA,KAACN,EACC,CAAA,GAAG,UACH,KAAK,UACL,MAAOrG,EAAQc,QACf,SAAUG,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnBrC,EAAeb,IACdhC,GAAAkF,EAAA,IAAC,UAAqB,MAAOlF,EAC1BA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAECoF,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,sBAAsB,SAAqB,wBAAA,EAChEQ,EAAAA,KAACN,EACC,CAAA,GAAG,sBACH,KAAK,sBACL,MAAOrG,EAAQe,oBACf,SAAUE,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,EAAElD,OAC9BkD,MAAA,SAAA,CAAiC,MAAOY,EACtCA,SAAAA,CAAAA,EADU,WAAWA,GAExB,CACD,CAAA,EACH,CAAA,EACF,SAECV,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CAAY,QAAQ,sBAAsB,SAAqB,wBAAA,EAChEQ,EAAAA,KAACN,EACC,CAAA,GAAG,sBACH,KAAK,sBACL,MAAOrG,EAAQgB,oBACf,SAAUC,EAEV,SAAA,CAAC+E,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAG,MAAA,EACnB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,EAAElD,OAC9BkD,MAAA,SAAA,CAAiC,MAAOY,EACtCA,SAAAA,CAAAA,EADU,WAAWA,GAExB,CACD,CAAA,EACH,CAAA,EACF,EAECZ,EAAA,IAAAS,EAAA,CAAa,QAASnF,EAAc,SAAa,gBAAA,CACpD,CAAA,CAAA,EClTYuF,SAAAA,EAAa7H,EAAiB8H,EAAsB,GAAO,CAEzE,KAAM,CAACC,EAAcC,CAAe,EAAIjI,EAAAA,SAAkC,CAAE,CAAA,EAGtEkI,EAAsBC,GAAoB,CACzCJ,GAELE,EAA2B5H,IAAA,CACzB,GAAGA,EACH,CAAC8H,CAAO,EAAG,CAAC9H,EAAK8H,CAAO,CACxB,EAAA,CAAA,EAIEC,EAAiBD,GACdJ,GAAcC,EAAaG,CAAO,EAcpC,MAAA,CACL7H,aAXmBmC,EAAAA,QAAQ,IACtBxC,EAEE,CAAC,GAAGA,CAAM,EAAEQ,KAAK,CAACC,EAAGC,IAAM,CAChC,MAAM0H,EAAQ,IAAIzH,KAAKF,EAAEG,IAAI,EAAEC,UAE/B,OADc,IAAIF,KAAKD,EAAEE,IAAI,EAAEC,UAChBuH,CAAAA,CAChB,EANmB,GAOnB,CAACpI,CAAM,CAAC,EAIT+H,aAAAA,EACAE,mBAAAA,EACAE,cAAAA,CAAAA,CAEJ,CCxCA,MAAME,EAAqBvD,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+IAAA,yCAAA,IAAA,6DAAA,EAKnB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMO,OAAOU,cAE1B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,GAAM,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAOzE0C,EAAqBxD,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uEAAA,GAAA,EAIf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,EAAE,EAUxCwC,EAAkDA,CAAC,CAAEC,eAAAA,CAAe,IAErExB,EAAAA,IAAAqB,EAAA,CACEG,SAAe1E,EAAAA,IACd2E,GAAAzB,EAAA,IAACsB,EAA6BG,CAAAA,SAAAA,EAAOrB,KAAnBqB,EAAAA,EAAOC,EAAkB,CAC5C,CACH,CAAA,ECjCEC,EAAmB7D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iIAAA,qBAAA,kBAAA,mBAAA,WAAA,+CAAA,IAAA,EAIf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,GACpB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMO,OAAOmD,eAC/B,CAAC,CAAE1D,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GACjC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMkB,YAAYC,KACzC,CAAC,CAAEwC,SAAAA,CAAS,IAAOA,IAAaC,OAAY,UAAY,UAI5C,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,EAAMO,OAAOsD,SAAS,EAcvDC,EAA4CA,CAAC,CACjDtG,MAAAA,EACA8F,eAAAA,EACAK,SAAAA,EACAZ,mBAAAA,CACF,IAEIjB,MAAC2B,GACC,SAAAE,EACA,QAAS,IAAMZ,EAAmBvF,EAAMgG,EAAE,EAEzCF,SAAe1E,EAAAA,OACbkD,EAAAA,IAAAiC,EAAM,SAAN,CAAgCR,SAAOS,EAAAA,SAASxG,CAAK,CAAjC+F,EAAAA,EAAOC,EAA4B,CACzD,CACH,CAAA,ECxCES,EAAyBrE,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,eAAA,kBAAA,GAAA,EACrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,GACpB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,WAC/B,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GACrC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,GAC1B,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAG5CwD,EAAyBtE,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAG5CyD,EAAsBC,EAAAA,GAAEvE,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUK,GAEnC,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMO,OAAOC,YACvB,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,EAAE,EAGzCuD,EAAoBzE,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAGlC4D,EAAoB1E,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAG5B,EAAA,CAAA,qCAAA,CAAA,EAEKwE,EAAqBC,EAAAA,KAAI3E,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUS,GACnC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMO,OAAOU,aAAa,EAG9CwD,EAAqBD,EAAAA,KAAI3E,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUK,GACnC,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMO,OAAOC,WAAW,EAG5CkE,EAAuB9E,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,eAAA,GAAA,EAEvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,GACtB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAGzCC,GAAeW,EAAOE,CAAI,EAAC3B,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,qBAAA,8BAAA,mCAAA,gDAAA,6BAAA,IAAA,EACpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,GAAM,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,GACvD,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMO,OAAOkB,QAE/B,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GAEtC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMK,UAAUS,GAEb,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMkB,YAAYC,KAG1C,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMO,OAAOmB,WAAW,EAWzDiD,GAA4DA,CAAC,CAAEnH,MAAAA,CAAM,IAAM,OAC/E,cACGyG,EACC,CAAA,SAAA,CAAAxB,OAACyB,EACC,CAAA,SAAA,CAAApC,EAAAA,IAACqC,GAAa,SAAa,eAAA,CAAA,SAC1BE,EACC,CAAA,SAAA,CAAA5B,OAAC6B,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAM,QAAA,CAAA,EACnBzC,EAAAA,IAAC2C,EAAajH,CAAAA,SAAAA,EAAMxB,MAAO,CAAA,CAAA,EAC7B,SACCsI,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAI,MAAA,CAAA,EACjBzC,EAAAA,IAAC2C,EAAajH,CAAAA,SAAAA,EAAM9B,IAAK,CAAA,CAAA,EAC3B,SACC4I,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAS,WAAA,CAAA,EACtBzC,EAAAA,IAAC2C,EAAajH,CAAAA,SAAAA,EAAMvB,SAAU,CAAA,CAAA,EAChC,SACCqI,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAW,aAAA,CAAA,SACvBE,EAAY,CAAA,SAAA,CAAA,IAAEjH,EAAMoH,MAAMC,QAAQ,CAAC,CAAA,EAAE,CAAA,EACxC,SACCP,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAU,YAAA,CAAA,SACtBE,EAAY,CAAA,SAAA,CAAA,IAAEjH,EAAMsH,KAAKD,QAAQ,CAAC,CAAA,EAAE,CAAA,EACvC,SACCP,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAQ,UAAA,CAAA,EACrBzC,EAAAA,IAAC2C,EAAajH,CAAAA,SAAAA,EAAMuH,IAAK,CAAA,CAAA,EAC3B,SACCT,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAW,aAAA,CAAA,EACxB9B,OAACgC,GACC,MAAO,CACLO,MACExH,EAAMI,WAAa,EACf,QACAJ,EAAMI,WAAa,EACnB,MACA,SACN,EAAA,SAAA,CAAA,IAEAJ,EAAMI,WAAWiH,QAAQ,CAAC,CAAA,EAC9B,CAAA,EACF,SACCP,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAU,YAAA,CAAA,QACtBE,EAAajH,CAAAA,WAAAA,EAAAA,EAAMyH,YAANzH,YAAAA,EAAiBqH,QAAQ,KAAM,MAAM,CAAA,EACrD,CAAA,EACF,CAAA,EACF,EAECrH,EAAMtB,OACLuG,EAAAA,KAACyB,EACC,CAAA,SAAA,CAAApC,EAAAA,IAACqC,GAAa,SAAQ,UAAA,CAAA,SACrBE,EACC,CAAA,SAAA,CAAA5B,OAAC6B,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAK,OAAA,CAAA,EAClBzC,EAAAA,IAAC2C,EAAajH,CAAAA,SAAAA,EAAMtB,KAAM,CAAA,CAAA,EAC5B,SACCoI,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAU,YAAA,CAAA,EACtBzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMrB,EAAAA,WAAa,MAAM,CAAA,EACzC,SACCmI,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAa,eAAA,CAAA,EACzBzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMjB,EAAAA,kBAAoB,MAAM,CAAA,EAChD,SACC+H,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAe,iBAAA,CAAA,EAC3BzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMhB,EAAAA,oBAAsB,MAAM,CAAA,EAClD,SACC8H,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAe,iBAAA,CAAA,EAC3BzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMf,EAAAA,gBAAkB,MAAM,CAAA,EAC9C,SACC6H,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAe,iBAAA,CAAA,EAC3BzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMS,EAAAA,gBAAkB,MAAM,CAAA,EAC9C,CAAA,EACF,CAAA,EACF,EAGDT,EAAMc,aACLmE,EAAAA,KAACyB,EACC,CAAA,SAAA,CAAApC,EAAAA,IAACqC,GAAa,SAAY,cAAA,CAAA,SACzBE,EACC,CAAA,SAAA,CAAA5B,OAAC6B,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAQ,UAAA,CAAA,EACpBzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMc,EAAAA,YAAY1B,SAAW,MAAM,CAAA,EACnD,SACC0H,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAY,cAAA,CAAA,EACxBzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMc,EAAAA,YAAY4G,aAAe,MAAM,CAAA,EACvD,SACCZ,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAY,cAAA,CAAA,EACxBzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMc,EAAAA,YAAY6G,aAAe,MAAM,CAAA,EACvD,SACCb,EACC,CAAA,SAAA,CAAAxC,EAAAA,IAACyC,GAAY,SAAiB,mBAAA,CAAA,EAC7BzC,EAAA,IAAA2C,EAAA,CAAajH,SAAMc,EAAAA,YAAYE,eAAiB,MAAM,CAAA,EACzD,CAAA,EACF,CAAA,EACF,EAGDhB,EAAM4H,OACL3C,EAAAA,KAACyB,EACC,CAAA,SAAA,CAAApC,EAAAA,IAACqC,GAAa,SAAK,OAAA,CAAA,QAClBG,EACC,CAAA,SAAAxC,MAAC2C,EAAajH,CAAAA,SAAAA,EAAM4H,KAAM,CAAA,EAC5B,CAAA,EACF,EAGFtD,EAAA,IAAC4C,GACC,SAAC5C,EAAAA,IAAAnB,GAAA,CAAa,GAAI,eAAenD,EAAMgG,KAAM,SAAA,YAAU,CAAA,EACzD,CACF,CAAA,CAAA,CAEJ,EC/LM6B,GAAwBzF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,qBAAA,kBAAA,qBAAA,EAKpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQqF,GACpB,CAAC,CAAEtF,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,WAC/B,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMgB,aAAaN,EAAE,EAIjD6E,GAAoBnB,EAAAA,GAAEvE,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUH,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMO,OAAOC,YACvB,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,EAAE,EAGzC0E,GAA0BC,EAAAA,EAAC5F,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,qBAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUK,GACnC,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMO,OAAOU,cACvB,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAIzCqB,GAAiBD,EAAOE,CAAI,EAAC3B,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yEAAA,IAAA,qBAAA,8BAAA,qEAAA,6BAAA,IAAA,EAItB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQa,GAAM,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GACvD,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMO,OAAOkB,QAE/B,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMgB,aAAaN,GAGpB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMkB,YAAYC,KAG1C,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMO,OAAOmB,WAAW,EAWzDgE,GAAgDA,CAAC,CAAEC,SAAAA,CAAS,WAE7DN,GACC,CAAA,SAAA,CAACvD,EAAA,IAAAyD,GAAA,CAAYI,SAAWA,EAAA,2BAA6B,gBAAgB,EACpE7D,EAAA,IAAA0D,GAAA,CACEG,SACGA,EAAA,kDACA,6EACN,EACC,CAACA,GAAY7D,EAAA,IAACP,GAAe,CAAA,GAAG,aAAa,SAAsB,yBAAA,CACtE,CAAA,CAAA,EC5DEqE,GAAUC,EAOf,CAAA,uEAAA,CAAA,EAEKC,GAA0BlG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,YAAA,KAAA,EAG1B,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,GACzB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAGtCqF,GAAoBnG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oDAAA,OAAA,QAAA,gDAAA,uCAAA,GAAA,EAIvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,WAC5B,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMO,OAAOmD,eAC5B,CAAC,CAAE1D,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,WAGnBwE,GACI,CAAC,CAAE5F,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,EAAE,EAUjDkF,GAAoDA,CAAC,CAAEC,SAAAA,EAAW,CAAE,IAEtEnE,EAAA,IAACgE,GACEjH,CAAAA,SAAAA,MAAMC,KAAK,CAAEoH,OAAQD,CAAU,CAAA,EAAErH,IAAI,CAACuH,EAAGC,IACvCtE,EAAAA,IAAAiE,GAAA,GAAgBK,EAClB,CACH,CAAA,ECjCEC,GAA4BzG,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAG5B,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAGlC0C,EAAqBxD,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uEAAA,UAAA,gBAAA,GAAA,EAIf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQY,GACjC,CAAC,CAAEb,MAAAA,EAAOsG,OAAAA,EAAQC,KAAAA,CAAK,IAC9BD,EACItG,EAAMO,OAAOiG,QACbD,EACAvG,EAAMO,OAAOkG,OACbzG,EAAMO,OAAOC,YACJ,CAAC,CAAE8F,OAAAA,EAAQC,KAAAA,CAAK,IAAOD,GAAUC,EAAO,IAAM,QAAS,EAGlEG,GAAelC,EAAAA,KAAI3E,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,kBAAA,cAAA,8DAAA,UAAA,GAAA,EAEZ,CAAC,CAAEC,MAAAA,CAAM,IAAM,GAAGA,EAAMC,QAAQ0G,OAAO3G,EAAMC,QAAQY,KAC/C,CAAC,CAAEb,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GACtC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMK,UAAUQ,GAGxB,CAAC,CAAEb,MAAAA,EAAO4G,KAAAA,CAAK,IACjCA,IAAS,OACL5G,EAAMO,OAAOsG,cAAgB,yBAC7BD,IAAS,QACT5G,EAAMO,OAAOuG,aAAe,yBAC5B9G,EAAMO,OAAOa,WACV,CAAC,CAAEpB,MAAAA,EAAO4G,KAAAA,CAAK,IACtBA,IAAS,OACL5G,EAAMO,OAAOiG,QACbI,IAAS,QACT5G,EAAMO,OAAOkG,OACbzG,EAAMO,OAAOC,WAAW,EAkB1BuG,GAAsCA,CAAC,CAC3CjM,OAAAA,EACAC,UAAAA,EAAY,GACZ6H,WAAAA,EAAa,EACf,IAAM,CACE,KAAA,CAAEzH,aAAAA,EAAc4H,mBAAAA,EAAoBE,cAAAA,CAAAA,EAAkBN,EAAa7H,EAAQ8H,CAAU,EAGrFoE,EAAyB1J,EAC7B,QAAA,IAAM,CACJ,CACEkG,GAAI,OACJtB,MAAO,OACP8B,SAAWxG,GAAWsE,MAAAsB,EAAA,CAAa5F,WAAM9B,KAAK,CAAA,EAEhD,CACE8H,GAAI,SACJtB,MAAO,SACP8B,SAAWxG,GAAWsE,MAAAsB,EAAA,CAAa5F,WAAMxB,OAAO,CAAA,EAElD,CACEwH,GAAI,YACJtB,MAAO,YACP8B,SAAWxG,GACRsE,EAAA,IAAAsB,EAAA,CACC,SAACtB,EAAA,IAAA4E,GAAA,CAAM,KAAMlJ,EAAMvB,UAAUwB,YAC1BD,EAAAA,SAAAA,EAAMvB,SACT,CAAA,EACF,CAAA,EAGJ,CACEuH,GAAI,QACJtB,MAAO,QACP8B,SAAqBxG,GAAAsE,EAAAA,IAACsB,EAAa5F,CAAAA,SAAAA,EAAMtB,OAAS,MAAM,CAAA,EAE1D,CACEsH,GAAI,QACJtB,MAAO,QACP8B,SAAqBxG,GAAAiF,EAAA,KAACW,EAAY,CAAA,SAAA,CAAA,IAAE5F,EAAMoH,MAAMC,QAAQ,CAAC,CAAA,EAAE,CAAA,EAE7D,CACErB,GAAI,OACJtB,MAAO,OACP8B,SAAqBxG,GAAAiF,EAAA,KAACW,EAAY,CAAA,SAAA,CAAA,IAAE5F,EAAMsH,KAAKD,QAAQ,CAAC,CAAA,EAAE,CAAA,EAE5D,CACErB,GAAI,OACJtB,MAAO,OACP8B,SAAWxG,GAAWsE,MAAAsB,EAAA,CAAa5F,WAAMuH,KAAK,CAAA,EAEhD,CACEvB,GAAI,aACJtB,MAAO,MACP8B,SACExG,GAAAiF,EAAA,KAACW,EAAY,CAAA,OAAQ5F,EAAMI,WAAa,EAAG,KAAMJ,EAAMI,WAAa,EAAE,SAAA,CAAA,IAClEJ,EAAMI,WAAWiH,QAAQ,CAAC,CAAA,EAC9B,CAAA,CAEH,EAEH,CACF,CAAA,EAGMoC,EAAsB,UAAUD,EAAQd,eAG9C,OAAInL,EACK+G,EAAA,IAACkE,GAAiB,CAAA,SAAU,CAAK,CAAA,EAItC,CAAC7K,GAAgBA,EAAa+K,SAAW,QACnCR,GAAe,CAAA,SAAU5K,GAAUA,EAAOoL,OAAS,CAAK,CAAA,EAIhEzD,EAAA,KAAC4D,IAAmB,MAAO,CAAE,0BAA2BY,CACtD,EAAA,SAAA,CAACnF,EAAAA,IAAAuB,EAAA,CAAgB,eAAgB2D,CAAQ,CAAA,EAExC7L,EAAayD,IAAKpB,GAChBiF,EAAA,KAAAsB,EAAM,SAAN,CACC,SAAA,CAACjC,EAAAA,IAAAgC,EAAA,CACC,MAAAtG,EACA,eAAgBwJ,EAChB,SAAU/D,EAAczF,EAAMgG,EAAE,EAChC,mBAAAT,CAAuC,CAAA,EAExCE,EAAczF,EAAMgG,EAAE,GAAK1B,EAAA,IAAC6C,IAAqB,MAAAnH,EAAgB,CAAA,GAP/CA,EAAMgG,EAQ3B,CACD,CACH,CAAA,CAAA,CAEJ,EC5JM0D,GAAwBtH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMO,OAAO4G,QAC/B,CAAC,CAAEnH,MAAAA,CAAM,IAAMA,EAAMgB,aAAaN,GACxC,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GAC1B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMoH,QAAQtG,EAAE,EAGzCqD,GAAsBkD,EAAAA,GAAExH,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,UAAUH,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMO,OAAOC,YACvB,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAGzC4G,GAAsB1H,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,YAAA,qBAAA,kBAAA,kBAAA,GAAA,EACpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMO,OAAOkG,OAC1B,CAAC,CAAEzG,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,GACpB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMO,OAAOa,WAC/B,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMgB,aAAaF,GAClC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQS,EAAE,EAsB5C6G,GAA0DA,CAAC,CAC/DvM,MAAAA,EACA4G,YAAAA,EACAvE,eAAAA,EACAtC,UAAAA,EACAe,QAAAA,EACAiB,mBAAAA,EACAK,aAAAA,EACAsB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CACF,IAGOzE,EAAAA,KAAAA,WAAAA,CAAAA,SAAAA,CAASA,GAAA8G,EAAAA,IAACwF,IAActM,SAAMA,CAAA,CAAA,SAE9BkM,GACC,CAAA,SAAA,CAAApF,EAAAA,IAACqC,IAAa,SAAa,eAAA,CAAA,EAE1BvC,GACEE,EAAAA,IAAAU,EAAA,CACC,QAAA1G,EACA,mBAAAiB,EACA,aAAAK,EACA,aAAAsB,EACA,iBAAAM,EACA,wBAAAE,EACA,0BAAAG,EACA,qBAAAC,EACA,eAAAG,CAEH,CAAA,QAEAsH,GAAU,CAAA,OAAQ1J,EAAgB,UAAAtC,EAAsB,WAAY,GAAK,CAAA,EAC5E,CACF,CAAA,CAAA,EC9EEyM,GAAuB5H,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EASlCuH,GAAyBA,IAAM,CAC7B,KAAA,CAAE3M,OAAAA,EAAQC,UAAAA,EAAWC,MAAAA,EAAOY,cAAAA,GAAkBlB,EAAgB,EAC9D,CAACkH,EAAaC,CAAc,EAAIhH,WAAS,EAAK,EAE9C,CACJiB,QAAAA,EACAiB,mBAAAA,EACAK,aAAAA,EACAC,eAAAA,EACAqB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CAAAA,EACE5D,EAAgBf,CAAM,EAE1B,cACG0M,GACC,CAAA,SAAA,CAAC1F,EAAAA,IAAAH,EAAA,CACC,cAAA/F,EACA,YAAAgG,EACA,eAAAC,CAA+B,CAAA,EAGhCC,EAAA,IAAAyF,GAAA,CACC,MAAAvM,EACA,YAAA4G,EACA,eAAAvE,EACA,UAAAtC,EACA,QAAAe,EACA,mBAAAiB,EACA,aAAAK,EACA,aAAAsB,EACA,iBAAAM,EACA,wBAAAE,EACA,0BAAAG,EACA,qBAAAC,EACA,eAAAG,EAA+B,CAEnC,CAAA,CAAA,CAEJ"}