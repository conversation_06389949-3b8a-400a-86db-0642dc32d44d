{"version": 3, "file": "tradeStorage-0955231a.js", "sources": ["../../src/services/tradeStorage.ts"], "sourcesContent": ["/**\n * Trade Storage Service\n *\n * A simple IndexedDB service for storing and retrieving trade data.\n */\n\nimport { Trade } from '../features/trade-journal/types';\n\n// Database configuration\nconst DB_NAME = 'adhd-trading-dashboard';\nconst DB_VERSION = 1;\nconst TRADES_STORE = 'trades';\n\n/**\n * Initialize the IndexedDB database\n */\nconst initDB = (): Promise<IDBDatabase> => {\n  return new Promise((resolve, reject) => {\n    const request = indexedDB.open(DB_NAME, DB_VERSION);\n\n    request.onupgradeneeded = (event) => {\n      const db = (event.target as IDBOpenDBRequest).result;\n\n      // Create trades store if it doesn't exist\n      if (!db.objectStoreNames.contains(TRADES_STORE)) {\n        const store = db.createObjectStore(TRADES_STORE, { keyPath: 'id' });\n\n        // Create indexes for common queries\n        store.createIndex('date', 'date', { unique: false });\n        store.createIndex('symbol', 'symbol', { unique: false });\n        store.createIndex('direction', 'direction', { unique: false });\n      }\n    };\n\n    request.onsuccess = (event) => {\n      resolve((event.target as IDBOpenDBRequest).result);\n    };\n\n    request.onerror = (event) => {\n      console.error('IndexedDB error:', (event.target as IDBOpenDBRequest).error);\n      reject((event.target as IDBOpenDBRequest).error);\n    };\n  });\n};\n\n/**\n * Trade Storage Service\n */\nclass TradeStorageService {\n  private dbPromise: Promise<IDBDatabase>;\n\n  constructor() {\n    this.dbPromise = initDB();\n  }\n\n  /**\n   * Save a trade to IndexedDB\n   * @param trade The trade to save\n   * @returns A promise that resolves with the saved trade\n   */\n  async saveTrade(trade: Omit<Trade, 'id'>): Promise<Trade> {\n    const db = await this.dbPromise;\n\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction(TRADES_STORE, 'readwrite');\n      const store = transaction.objectStore(TRADES_STORE);\n\n      // Generate a unique ID if not provided\n      const newTrade: Trade = {\n        ...trade,\n        id: trade.id || crypto.randomUUID(),\n      } as Trade;\n\n      const request = store.put(newTrade);\n\n      request.onsuccess = () => {\n        resolve(newTrade);\n      };\n\n      request.onerror = () => {\n        console.error('Error saving trade:', request.error);\n        reject(request.error);\n      };\n    });\n  }\n\n  /**\n   * Get all trades from IndexedDB\n   * @returns A promise that resolves with an array of trades\n   */\n  async getAllTrades(): Promise<Trade[]> {\n    const db = await this.dbPromise;\n\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction(TRADES_STORE, 'readonly');\n      const store = transaction.objectStore(TRADES_STORE);\n      const request = store.getAll();\n\n      request.onsuccess = () => {\n        resolve(request.result as Trade[]);\n      };\n\n      request.onerror = () => {\n        console.error('Error getting trades:', request.error);\n        reject(request.error);\n      };\n    });\n  }\n\n  /**\n   * Get a trade by ID\n   * @param id The trade ID\n   * @returns A promise that resolves with the trade or undefined\n   */\n  async getTradeById(id: string): Promise<Trade | undefined> {\n    console.log(`tradeStorage.getTradeById called with ID: \"${id}\"`);\n\n    if (!id) {\n      console.error('getTradeById called with empty ID');\n      return undefined;\n    }\n\n    try {\n      const db = await this.dbPromise;\n      console.log(`IndexedDB connection established for getTradeById`);\n\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction(TRADES_STORE, 'readonly');\n        const store = transaction.objectStore(TRADES_STORE);\n        console.log(`Attempting to get trade with ID: \"${id}\" from IndexedDB`);\n        const request = store.get(id);\n\n        request.onsuccess = () => {\n          console.log(`IndexedDB get request succeeded for ID: \"${id}\"`);\n          console.log(`Result:`, request.result);\n          resolve(request.result as Trade);\n        };\n\n        request.onerror = () => {\n          console.error(`Error getting trade with ID: \"${id}\"`, request.error);\n          reject(request.error);\n        };\n      });\n    } catch (error) {\n      console.error(`Exception in getTradeById for ID: \"${id}\"`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a trade by ID\n   * @param id The trade ID\n   * @returns A promise that resolves when the trade is deleted\n   */\n  async deleteTrade(id: string): Promise<void> {\n    const db = await this.dbPromise;\n\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction(TRADES_STORE, 'readwrite');\n      const store = transaction.objectStore(TRADES_STORE);\n      const request = store.delete(id);\n\n      request.onsuccess = () => {\n        resolve();\n      };\n\n      request.onerror = () => {\n        console.error('Error deleting trade:', request.error);\n        reject(request.error);\n      };\n    });\n  }\n}\n\n// Export a singleton instance\nexport const tradeStorage = new TradeStorageService();\n"], "names": ["DB_NAME", "TRADES_STORE", "initDB", "Promise", "resolve", "reject", "request", "indexedDB", "open", "DB_VERSION", "onupgradeneeded", "event", "db", "target", "result", "objectStoreNames", "contains", "store", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "onsuccess", "onerror", "console", "error", "TradeStorageService", "constructor", "db<PERSON><PERSON><PERSON>", "saveTrade", "trade", "transaction", "objectStore", "newTrade", "id", "crypto", "randomUUID", "put", "getAllTrades", "getAll", "getTradeById", "log", "undefined", "get", "deleteTrade", "delete", "tradeStorage"], "mappings": "AASA,MAAMA,EAAU,yBAEhB,MAAMC,EAAe,SAKfC,EAASA,IACN,IAAIC,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAMC,EAAUC,UAAUC,KAAKR,EAASS,CAAU,EAElDH,EAAQI,gBAA6BC,GAAA,CAC7BC,MAAAA,EAAMD,EAAME,OAA4BC,OAG9C,GAAI,CAACF,EAAGG,iBAAiBC,SAASf,CAAY,EAAG,CACzCgB,MAAAA,EAAQL,EAAGM,kBAAkBjB,EAAc,CAAEkB,QAAS,IAAA,CAAM,EAG5DC,EAAAA,YAAY,OAAQ,OAAQ,CAAEC,OAAQ,EAAA,CAAO,EAC7CD,EAAAA,YAAY,SAAU,SAAU,CAAEC,OAAQ,EAAA,CAAO,EACjDD,EAAAA,YAAY,YAAa,YAAa,CAAEC,OAAQ,EAAA,CAAO,EAC/D,EAGFf,EAAQgB,UAAuBX,GAAA,CACpBA,EAAAA,EAAME,OAA4BC,MAAM,CAAA,EAGnDR,EAAQiB,QAAqBZ,GAAA,CAC3Ba,QAAQC,MAAM,mBAAqBd,EAAME,OAA4BY,KAAK,EAClEd,EAAAA,EAAME,OAA4BY,KAAK,CAAA,CACjD,CACD,EAMH,MAAMC,CAAoB,CAGxBC,aAAc,CACZ,KAAKC,UAAY1B,GACnB,CAOA,MAAM2B,UAAUC,EAA0C,CAClDlB,MAAAA,EAAK,MAAM,KAAKgB,UAEtB,OAAO,IAAIzB,QAAQ,CAACC,EAASC,IAAW,CAEhCY,MAAAA,EADcL,EAAGmB,YAAY9B,EAAc,WAAW,EAClC+B,YAAY/B,CAAY,EAG5CgC,EAAkB,CACtB,GAAGH,EACHI,GAAIJ,EAAMI,IAAMC,OAAOC,WAAW,CAAA,EAG9B9B,EAAUW,EAAMoB,IAAIJ,CAAQ,EAElC3B,EAAQgB,UAAY,IAAM,CACxBlB,EAAQ6B,CAAQ,CAAA,EAGlB3B,EAAQiB,QAAU,IAAM,CACdE,QAAAA,MAAM,sBAAuBnB,EAAQmB,KAAK,EAClDpB,EAAOC,EAAQmB,KAAK,CAAA,CACtB,CACD,CACH,CAMA,MAAMa,cAAiC,CAC/B1B,MAAAA,EAAK,MAAM,KAAKgB,UAEtB,OAAO,IAAIzB,QAAQ,CAACC,EAASC,IAAW,CAGhCC,MAAAA,EAFcM,EAAGmB,YAAY9B,EAAc,UAAU,EACjC+B,YAAY/B,CAAY,EAC5BsC,SAEtBjC,EAAQgB,UAAY,IAAM,CACxBlB,EAAQE,EAAQQ,MAAiB,CAAA,EAGnCR,EAAQiB,QAAU,IAAM,CACdE,QAAAA,MAAM,wBAAyBnB,EAAQmB,KAAK,EACpDpB,EAAOC,EAAQmB,KAAK,CAAA,CACtB,CACD,CACH,CAOA,MAAMe,aAAaN,EAAwC,CAGzD,GAFQO,QAAAA,IAAI,8CAA8CP,IAAK,EAE3D,CAACA,EAAI,CACPV,QAAQC,MAAM,mCAAmC,EAC1CiB,OAGL,GAAA,CACI9B,MAAAA,EAAK,MAAM,KAAKgB,UACtBJ,eAAQiB,IAAI,mDAAmD,EAExD,IAAItC,QAAQ,CAACC,EAASC,IAAW,CAEhCY,MAAAA,EADcL,EAAGmB,YAAY9B,EAAc,UAAU,EACjC+B,YAAY/B,CAAY,EAC1CwC,QAAAA,IAAI,qCAAqCP,mBAAoB,EAC/D5B,MAAAA,EAAUW,EAAM0B,IAAIT,CAAE,EAE5B5B,EAAQgB,UAAY,IAAM,CAChBmB,QAAAA,IAAI,4CAA4CP,IAAK,EACrDO,QAAAA,IAAI,UAAWnC,EAAQQ,MAAM,EACrCV,EAAQE,EAAQQ,MAAe,CAAA,EAGjCR,EAAQiB,QAAU,IAAM,CACtBC,QAAQC,MAAM,iCAAiCS,KAAO5B,EAAQmB,KAAK,EACnEpB,EAAOC,EAAQmB,KAAK,CAAA,CACtB,CACD,QACMA,GACCA,cAAAA,MAAM,sCAAsCS,KAAOT,CAAK,EAC1DA,CACR,CACF,CAOA,MAAMmB,YAAYV,EAA2B,CACrCtB,MAAAA,EAAK,MAAM,KAAKgB,UAEtB,OAAO,IAAIzB,QAAQ,CAACC,EAASC,IAAW,CAGhCC,MAAAA,EAFcM,EAAGmB,YAAY9B,EAAc,WAAW,EAClC+B,YAAY/B,CAAY,EAC5B4C,OAAOX,CAAE,EAE/B5B,EAAQgB,UAAY,IAAM,CAChBlB,GAAA,EAGVE,EAAQiB,QAAU,IAAM,CACdE,QAAAA,MAAM,wBAAyBnB,EAAQmB,KAAK,EACpDpB,EAAOC,EAAQmB,KAAK,CAAA,CACtB,CACD,CACH,CACF,CAGaqB,MAAAA,EAAe,IAAIpB"}