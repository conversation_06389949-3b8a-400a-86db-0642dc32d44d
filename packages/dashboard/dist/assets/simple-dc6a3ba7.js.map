{"version": 3, "file": "simple-dc6a3ba7.js", "sources": ["../../src/SimpleApp.tsx", "../../src/simple-index.tsx"], "sourcesContent": ["import React from 'react';\n\n/**\n * A super simple component with no dependencies\n */\nconst SimpleApp: React.FC = () => {\n  return (\n    <div style={{ \n      padding: '20px', \n      background: 'red', \n      color: 'white',\n      fontFamily: 'Arial, sans-serif',\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }}>\n      <h1>Simple App Working!</h1>\n      <p>This is a minimal test component with no dependencies</p>\n    </div>\n  );\n};\n\nexport default SimpleApp;\n", "// Import DevTools configuration first\nimport './devtools-config';\n\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport SimpleApp from './SimpleApp';\n\n// Get the root element\nconst rootElement = document.getElementById('root');\n\n// Create a fallback root element if needed\nif (!rootElement) {\n  console.error('Root element not found, creating a fallback element');\n  const fallbackRoot = document.createElement('div');\n  fallbackRoot.id = 'root';\n  document.body.appendChild(fallbackRoot);\n}\n\n// Create the React root\nconst root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);\n\n// Render the app\nroot.render(\n  <React.StrictMode>\n    <SimpleApp />\n  </React.StrictMode>\n);\n"], "names": ["SimpleApp", "jsxs", "padding", "background", "color", "fontFamily", "height", "display", "flexDirection", "alignItems", "justifyContent", "jsx", "rootElement", "document", "getElementById", "console", "error", "fallbackRoot", "createElement", "id", "body", "append<PERSON><PERSON><PERSON>", "root", "ReactDOM", "createRoot", "render", "React"], "mappings": "wFAKA,MAAMA,EAAsBA,IAExBC,EAAA,KAAC,OAAI,MAAO,CACVC,QAAS,OACTC,WAAY,MACZC,MAAO,QACPC,WAAY,oBACZC,OAAQ,QACRC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAEhB,EAAA,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAmB,qBAAA,CAAA,EACvBA,EAAAA,IAAC,KAAE,SAAqD,uDAAA,CAAA,CAC1D,CAAA,CAAA,ECZEC,EAAcC,SAASC,eAAe,MAAM,EAGlD,GAAI,CAACF,EAAa,CAChBG,QAAQC,MAAM,qDAAqD,EAC7DC,MAAAA,EAAeJ,SAASK,cAAc,KAAK,EACjDD,EAAaE,GAAK,OACTC,SAAAA,KAAKC,YAAYJ,CAAY,EAIxC,MAAMK,EAAOC,EAASC,WAAWX,SAASC,eAAe,MAAM,CAAgB,EAG/EQ,EAAKG,aACFC,EAAM,WAAN,CACC,SAACf,EAAAA,IAAAX,EAAA,EAAS,EACZ,CACF"}