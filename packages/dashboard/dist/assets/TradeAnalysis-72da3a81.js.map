{"version": 3, "file": "TradeAnalysis-72da3a81.js", "sources": ["../../../shared/src/components/atoms/LoadingPlaceholder.tsx", "../../../shared/src/components/atoms/Tag.tsx", "../../../shared/src/components/molecules/EmptyState.tsx", "../../../shared/src/components/organisms/DataCard.tsx", "../../../shared/src/hooks/useLocalStorage.ts", "../../src/features/trade-analysis/api/tradeAnalysisApi.ts", "../../src/features/trade-analysis/context/TradeAnalysisContext.tsx", "../../src/features/trade-analysis/components/FilterPanel.tsx", "../../src/features/trade-analysis/components/PerformanceSummary.tsx", "../../src/features/trade-analysis/components/TradesTable.tsx", "../../src/features/trade-analysis/components/CategoryPerformanceChart.tsx", "../../src/features/trade-analysis/components/TimePerformanceChart.tsx", "../../src/features/trade-analysis/components/TradeDetail.tsx", "../../src/features/trade-analysis/TradeAnalysis.tsx"], "sourcesContent": ["/**\n * Loading Placeholder Component\n *\n * A component for displaying loading states with customizable appearance.\n */\nimport React from 'react';\nimport styled, { css, keyframes } from 'styled-components';\n\nexport type LoadingPlaceholderVariant = 'default' | 'card' | 'text' | 'list';\nexport type LoadingPlaceholderSize = 'small' | 'medium' | 'large' | 'custom';\n\nexport interface LoadingPlaceholderProps {\n  /** The variant of the loading placeholder */\n  variant?: LoadingPlaceholderVariant;\n  /** The size of the loading placeholder */\n  size?: LoadingPlaceholderSize;\n  /** Custom height (only used when size is 'custom') */\n  height?: string;\n  /** Custom width (only used when size is 'custom') */\n  width?: string;\n  /** Text to display in the loading placeholder */\n  text?: string;\n  /** Whether to show a spinner */\n  showSpinner?: boolean;\n  /** Additional CSS class names */\n  className?: string;\n}\n\n// Size styles\nconst sizeStyles = {\n  small: css`\n    height: 100px;\n  `,\n  medium: css`\n    height: 200px;\n  `,\n  large: css`\n    height: 300px;\n  `,\n  custom: (props: { customHeight: string; customWidth?: string }) => css`\n    height: ${props.customHeight};\n    width: ${props.customWidth || '100%'};\n  `,\n};\n\n// Variant styles\nconst variantStyles = {\n  default: css`\n    background-color: ${({ theme }) => theme.colors.background};\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n  `,\n  card: css`\n    background-color: ${({ theme }) => theme.colors.surface};\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n    box-shadow: ${({ theme }) => theme.shadows.sm};\n  `,\n  text: css`\n    background-color: transparent;\n    height: auto !important;\n    min-height: 1.5em;\n  `,\n  list: css`\n    background-color: ${({ theme }) => theme.colors.background};\n    border-radius: ${({ theme }) => theme.borderRadius.sm};\n    margin-bottom: ${({ theme }) => theme.spacing.sm};\n  `,\n};\n\n// Spinner animation\nconst spin = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n`;\n\nconst Container = styled.div<{\n  variant: LoadingPlaceholderVariant;\n  size: LoadingPlaceholderSize;\n  customHeight: string;\n  customWidth?: string;\n}>`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* Apply size styles */\n  ${({ size, customHeight, customWidth }) => {\n    if (size === 'custom') {\n      return sizeStyles.custom({ customHeight, customWidth });\n    }\n    return sizeStyles[size];\n  }}\n\n  /* Apply variant styles */\n  ${({ variant }) => variantStyles[variant]}\n`;\n\nconst Spinner = styled.div`\n  width: 32px;\n  height: 32px;\n  border: 3px solid ${({ theme }) => theme.colors.background};\n  border-top: 3px solid ${({ theme }) => theme.colors.primary};\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin-bottom: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst Text = styled.div`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n`;\n\n/**\n * Loading Placeholder Component\n *\n * A component for displaying loading states with customizable appearance.\n */\nexport const LoadingPlaceholder: React.FC<LoadingPlaceholderProps> = ({\n  variant = 'default',\n  size = 'medium',\n  height = '200px',\n  width,\n  text = 'Loading...',\n  showSpinner = true,\n  className,\n}) => {\n  return (\n    <Container\n      variant={variant}\n      size={size}\n      customHeight={height}\n      customWidth={width}\n      className={className}\n    >\n      {showSpinner && <Spinner />}\n      {text && <Text>{text}</Text>}\n    </Container>\n  );\n};\n", "/**\n * Tag Component\n *\n * A customizable tag component for categorizing content.\n */\nimport React from 'react';\nimport styled, { css } from 'styled-components';\n\nexport type TagVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';\nexport type TagSize = 'small' | 'medium' | 'large';\n\nexport interface TagProps {\n  /** The content to display inside the tag */\n  children: React.ReactNode;\n  /** The variant of the tag */\n  variant?: TagVariant;\n  /** The size of the tag */\n  size?: TagSize;\n  /** Whether the tag is removable */\n  removable?: boolean;\n  /** Function called when the remove button is clicked */\n  onRemove?: () => void;\n  /** Additional CSS class names */\n  className?: string;\n  /** Optional click handler */\n  onClick?: () => void;\n}\n\n// Size styles\nconst sizeStyles = {\n  small: css`\n    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};\n    font-size: ${({ theme }) => theme.fontSizes.xs};\n  `,\n  medium: css`\n    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};\n    font-size: ${({ theme }) => theme.fontSizes.sm};\n  `,\n  large: css`\n    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};\n    font-size: ${({ theme }) => theme.fontSizes.md};\n  `,\n};\n\n// Variant styles\nconst getVariantStyles = (variant: TagVariant) => {\n  return css`\n    ${({ theme }) => {\n      // Get the appropriate colors based on the variant\n      let bgColor, textColor, borderColor;\n      \n      switch (variant) {\n        case 'primary':\n          bgColor = `${theme.colors.primary}10`;\n          textColor = theme.colors.primary;\n          borderColor = `${theme.colors.primary}30`;\n          break;\n        case 'secondary':\n          bgColor = `${theme.colors.secondary}10`;\n          textColor = theme.colors.secondary;\n          borderColor = `${theme.colors.secondary}30`;\n          break;\n        case 'success':\n          bgColor = `${theme.colors.success}10`;\n          textColor = theme.colors.success;\n          borderColor = `${theme.colors.success}30`;\n          break;\n        case 'warning':\n          bgColor = `${theme.colors.warning}10`;\n          textColor = theme.colors.warning;\n          borderColor = `${theme.colors.warning}30`;\n          break;\n        case 'error':\n          bgColor = `${theme.colors.error}10`;\n          textColor = theme.colors.error;\n          borderColor = `${theme.colors.error}30`;\n          break;\n        case 'info':\n          bgColor = `${theme.colors.info}10`;\n          textColor = theme.colors.info;\n          borderColor = `${theme.colors.info}30`;\n          break;\n        default: // 'default'\n          bgColor = `${theme.colors.textSecondary}10`;\n          textColor = theme.colors.textSecondary;\n          borderColor = `${theme.colors.textSecondary}30`;\n      }\n      \n      return `\n        background-color: ${bgColor};\n        color: ${textColor};\n        border: 1px solid ${borderColor};\n      `;\n    }}\n  `;\n};\n\nconst StyledTag = styled.span<{\n  variant: TagVariant;\n  size: TagSize;\n  clickable: boolean;\n}>`\n  display: inline-flex;\n  align-items: center;\n  border-radius: ${({ theme }) => theme.borderRadius.pill};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  \n  /* Apply size styles */\n  ${({ size }) => sizeStyles[size]}\n  \n  /* Apply variant styles */\n  ${({ variant }) => getVariantStyles(variant)}\n  \n  /* Clickable styles */\n  ${({ clickable }) => clickable && css`\n    cursor: pointer;\n    transition: opacity ${({ theme }) => theme.transitions.fast};\n    \n    &:hover {\n      opacity: 0.8;\n    }\n    \n    &:active {\n      opacity: 0.6;\n    }\n  `}\n`;\n\nconst RemoveButton = styled.button<{ size: TagSize }>`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  opacity: 0.7;\n  margin-left: ${({ theme }) => theme.spacing.xs};\n  padding: 0;\n  \n  /* Size-specific styles */\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: '12px',\n      medium: '14px',\n      large: '16px',\n    };\n    \n    return `\n      width: ${sizeMap[size]};\n      height: ${sizeMap[size]};\n      font-size: ${theme.fontSizes.xs};\n    `;\n  }}\n  \n  &:hover {\n    opacity: 1;\n  }\n`;\n\n/**\n * Tag Component\n *\n * A customizable tag component for categorizing content.\n */\nexport const Tag: React.FC<TagProps> = ({\n  children,\n  variant = 'default',\n  size = 'medium',\n  removable = false,\n  onRemove,\n  className,\n  onClick,\n}) => {\n  const handleRemoveClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    onRemove?.();\n  };\n  \n  return (\n    <StyledTag\n      variant={variant}\n      size={size}\n      clickable={!!onClick}\n      className={className}\n      onClick={onClick}\n    >\n      {children}\n      {removable && (\n        <RemoveButton size={size} onClick={handleRemoveClick}>\n          ×\n        </RemoveButton>\n      )}\n    </StyledTag>\n  );\n};\n", "/**\n * Empty State Component\n *\n * A component for displaying empty states with customizable appearance.\n */\nimport React from 'react';\nimport styled, { css } from 'styled-components';\nimport { Button } from '../atoms/Button';\n\nexport type EmptyStateVariant = 'default' | 'compact' | 'card';\nexport type EmptyStateSize = 'small' | 'medium' | 'large';\n\nexport interface EmptyStateProps {\n  /** The title of the empty state */\n  title?: string;\n  /** The description of the empty state */\n  description?: string;\n  /** The icon to display (as a component) */\n  icon?: React.ReactNode;\n  /** The action button text */\n  actionText?: string;\n  /** Function called when the action button is clicked */\n  onAction?: () => void;\n  /** The variant of the empty state */\n  variant?: EmptyStateVariant;\n  /** The size of the empty state */\n  size?: EmptyStateSize;\n  /** Additional CSS class names */\n  className?: string;\n  /** Additional content to display */\n  children?: React.ReactNode;\n}\n\n// Define styled components first to avoid reference errors\nconst Title = styled.h3<{ size: EmptyStateSize }>`\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: theme.fontSizes.md,\n      medium: theme.fontSizes.lg,\n      large: theme.fontSizes.xl,\n    };\n\n    return css`\n      font-size: ${sizeMap[size]};\n    `;\n  }}\n`;\n\nconst Description = styled.p<{ size: EmptyStateSize }>`\n  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;\n  color: ${({ theme }) => theme.colors.textSecondary};\n\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: theme.fontSizes.sm,\n      medium: theme.fontSizes.md,\n      large: theme.fontSizes.lg,\n    };\n\n    return css`\n      font-size: ${sizeMap[size]};\n    `;\n  }}\n`;\n\n// Size styles - removed nested component references to avoid circular dependencies\n\n// Variant styles\nconst variantStyles = {\n  default: css`\n    background-color: transparent;\n  `,\n  compact: css`\n    background-color: transparent;\n    text-align: left;\n    align-items: flex-start;\n  `,\n  card: css`\n    background-color: ${({ theme }) => theme.colors.surface};\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n    box-shadow: ${({ theme }) => theme.shadows.sm};\n  `,\n};\n\nconst Container = styled.div<{\n  variant: EmptyStateVariant;\n  size: EmptyStateSize;\n}>`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  width: 100%;\n\n  /* Apply variant styles */\n  ${({ variant }) => variantStyles[variant]}\n\n  /* Apply size styles - using a function to avoid styled-components warning about circular references */\n  ${({ size, theme }) => {\n    switch (size) {\n      case 'small':\n        return css`\n          padding: ${theme.spacing.md};\n          min-height: 120px;\n        `;\n      case 'large':\n        return css`\n          padding: ${theme.spacing.xl};\n          min-height: 300px;\n        `;\n      default: // 'medium'\n        return css`\n          padding: ${theme.spacing.lg};\n          min-height: 200px;\n        `;\n    }\n  }}\n`;\n\nconst IconContainer = styled.div<{ size: EmptyStateSize }>`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: '32px',\n      medium: '48px',\n      large: '64px',\n    };\n\n    return css`\n      font-size: ${sizeMap[size]};\n\n      svg {\n        width: ${sizeMap[size]};\n        height: ${sizeMap[size]};\n        color: ${theme.colors.textSecondary};\n      }\n    `;\n  }}\n`;\n\n// Title and Description components are defined at the top of the file\n\nconst ActionContainer = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ChildrenContainer = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.lg};\n  width: 100%;\n`;\n\n/**\n * Empty State Component\n *\n * A component for displaying empty states with customizable appearance.\n */\nexport const EmptyState: React.FC<EmptyStateProps> = ({\n  title,\n  description,\n  icon,\n  actionText,\n  onAction,\n  variant = 'default',\n  size = 'medium',\n  className,\n  children,\n}) => {\n  return (\n    <Container variant={variant} size={size} className={className}>\n      {icon && <IconContainer size={size}>{icon}</IconContainer>}\n\n      {title && <Title size={size}>{title}</Title>}\n      {description && <Description size={size}>{description}</Description>}\n\n      {actionText && onAction && (\n        <ActionContainer>\n          <Button variant=\"primary\" size={size === 'small' ? 'small' : 'medium'} onClick={onAction}>\n            {actionText}\n          </Button>\n        </ActionContainer>\n      )}\n\n      {children && <ChildrenContainer>{children}</ChildrenContainer>}\n    </Container>\n  );\n};\n", "/**\n * DataCard Component\n *\n * A specialized card component for displaying data sections with loading and error states.\n */\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Card, CardProps } from '../molecules/Card';\n// Button import removed as it's not used\nimport { LoadingPlaceholder } from '../atoms/LoadingPlaceholder';\nimport { EmptyState } from '../molecules/EmptyState';\n\nexport interface DataCardProps extends Omit<CardProps, 'isLoading' | 'hasError' | 'errorMessage'> {\n  /** The title of the data card */\n  title: string;\n  /** The content to display inside the data card */\n  children: React.ReactNode;\n  /** Whether the data is loading */\n  isLoading?: boolean;\n  /** Whether there was an error loading the data */\n  hasError?: boolean;\n  /** Error message to display */\n  errorMessage?: string;\n  /** Whether to show a retry button when there's an error */\n  showRetry?: boolean;\n  /** Function called when the retry button is clicked */\n  onRetry?: () => void;\n  /** Whether the data is empty */\n  isEmpty?: boolean;\n  /** Empty state message */\n  emptyMessage?: string;\n  /** Empty state action text */\n  emptyActionText?: string;\n  /** Function called when the empty state action button is clicked */\n  onEmptyAction?: () => void;\n  /** Action button to display in the header */\n  actionButton?: React.ReactNode;\n  /** Additional CSS class names */\n  className?: string;\n}\n\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\n/**\n * DataCard Component\n *\n * A specialized card component for displaying data sections with loading and error states.\n */\nexport const DataCard: React.FC<DataCardProps> = ({\n  title,\n  children,\n  isLoading = false,\n  hasError = false,\n  errorMessage = 'An error occurred while loading data',\n  showRetry = true,\n  onRetry,\n  isEmpty = false,\n  emptyMessage = 'No data available',\n  emptyActionText,\n  onEmptyAction,\n  actionButton,\n  className,\n  ...cardProps\n}) => {\n  // Create actions for the card header\n  const headerActions = <HeaderActions>{actionButton}</HeaderActions>;\n\n  // Determine what content to show based on state\n  let content;\n\n  if (isLoading) {\n    content = <LoadingPlaceholder variant=\"card\" text=\"Loading data...\" />;\n  } else if (hasError) {\n    content = (\n      <EmptyState\n        title=\"Error\"\n        description={errorMessage}\n        variant=\"compact\"\n        actionText={showRetry ? 'Retry' : undefined}\n        onAction={showRetry ? onRetry : undefined}\n      />\n    );\n  } else if (isEmpty) {\n    content = (\n      <EmptyState\n        title=\"No Data\"\n        description={emptyMessage}\n        variant=\"compact\"\n        actionText={emptyActionText}\n        onAction={onEmptyAction}\n      />\n    );\n  } else {\n    content = children;\n  }\n\n  return (\n    <Card title={title} actions={headerActions} className={className} {...cardProps}>\n      {content}\n    </Card>\n  );\n};\n", "/**\n * useLocalStorage Hook\n *\n * Custom hook for managing localStorage.\n */\n\nimport { useState, useEffect } from \"react\";\n\n/**\n * Custom hook for managing localStorage\n * @param key - The localStorage key\n * @param initialValue - The initial value\n * @returns [storedValue, setValue] - The stored value and a function to update it\n */\nexport function useLocalStorage<T>(\n  key: string,\n  initialValue: T\n): [T, (value: T | ((val: T) => T)) => void] {\n  // Get from localStorage then\n  // parse stored json or return initialValue\n  const readValue = (): T => {\n    // Prevent build error \"window is undefined\" but keep working\n    if (typeof window === \"undefined\") {\n      return initialValue;\n    }\n\n    try {\n      const item = window.localStorage.getItem(key);\n      return item ? (JSON.parse(item) as T) : initialValue;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \"${key}\":`, error);\n      return initialValue;\n    }\n  };\n\n  // State to store our value\n  // Pass initial state function to useState so logic is only executed once\n  const [storedValue, setStoredValue] = useState<T>(readValue);\n\n  // Return a wrapped version of useState's setter function that ...\n  // ... persists the new value to localStorage.\n  const setValue = (value: T | ((val: T) => T)) => {\n    try {\n      // Allow value to be a function so we have the same API as useState\n      const valueToStore =\n        value instanceof Function ? value(storedValue) : value;\n\n      // Save to state\n      setStoredValue(valueToStore);\n\n      // Save to localStorage\n      if (typeof window !== \"undefined\") {\n        window.localStorage.setItem(key, JSON.stringify(valueToStore));\n      }\n    } catch (error) {\n      console.warn(`Error setting localStorage key \"${key}\":`, error);\n    }\n  };\n\n  // Listen to changes in localStorage\n  useEffect(() => {\n    const handleStorageChange = (event: StorageEvent) => {\n      if (event.key === key && event.newValue) {\n        setStoredValue(JSON.parse(event.newValue) as T);\n      }\n    };\n\n    // Listen for changes to this localStorage key\n    window.addEventListener(\"storage\", handleStorageChange);\n\n    // Remove event listener on cleanup\n    return () => window.removeEventListener(\"storage\", handleStorageChange);\n  }, [key]);\n\n  return [storedValue, setValue];\n}\n", "/**\n * Trade Analysis API\n * \n * API functions for the trade analysis feature\n */\n\nimport {\n  TradeAnalysisData,\n  TradeFilters,\n  Trade,\n  TradeDirection,\n  TradeStatus,\n  TradeTimeframe,\n  TradingSession,\n  PerformanceMetrics,\n  CategoryPerformance,\n  TimePerformance,\n} from '../types';\n\n/**\n * Fetch trade analysis data from the API\n * \n * In a real application, this would make an actual API call.\n * For now, we're generating mock data.\n */\nexport const fetchTradeAnalysisData = async (\n  filters: TradeFilters\n): Promise<TradeAnalysisData> => {\n  // Simulate API call delay\n  await new Promise(resolve => setTimeout(resolve, 800));\n  \n  // Randomly decide if we should throw an error (for testing error handling)\n  const shouldError = Math.random() < 0.05; // 5% chance of error\n  if (shouldError) {\n    throw new Error('Failed to fetch trade analysis data');\n  }\n  \n  // Generate mock data based on filters\n  return generateMockData(filters);\n};\n\n/**\n * Generate mock data for development and testing\n */\nconst generateMockData = (filters: TradeFilters): TradeAnalysisData => {\n  // Generate trades\n  const trades = generateMockTrades(filters);\n  \n  // Calculate metrics\n  const metrics = calculateMetrics(trades);\n  \n  // Generate performance by category\n  const symbolPerformance = calculateCategoryPerformance(trades, 'symbol');\n  const strategyPerformance = calculateCategoryPerformance(trades, 'strategy');\n  const timeframePerformance = calculateCategoryPerformance(trades, 'timeframe');\n  const sessionPerformance = calculateCategoryPerformance(trades, 'session');\n  \n  // Generate performance by time\n  const timeOfDayPerformance = calculateTimePerformance(trades, 'timeOfDay');\n  const dayOfWeekPerformance = calculateTimePerformance(trades, 'dayOfWeek');\n  \n  return {\n    trades,\n    metrics,\n    symbolPerformance,\n    strategyPerformance,\n    timeframePerformance,\n    sessionPerformance,\n    timeOfDayPerformance,\n    dayOfWeekPerformance,\n  };\n};\n\n/**\n * Generate mock trades\n */\nconst generateMockTrades = (filters: TradeFilters): Trade[] => {\n  const { dateRange } = filters;\n  const startDate = new Date(dateRange.startDate);\n  const endDate = new Date(dateRange.endDate);\n  \n  // Calculate number of days in the date range\n  const daysDiff = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));\n  \n  // Generate between 1-5 trades per day\n  const numTrades = Math.max(1, daysDiff) * (1 + Math.floor(Math.random() * 5));\n  \n  const trades: Trade[] = [];\n  const symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NFLX', 'NVDA'];\n  const strategies = ['Breakout', 'Reversal', 'Trend Following', 'Gap and Go', 'VWAP Bounce', 'Support/Resistance'];\n  const timeframes: TradeTimeframe[] = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];\n  const sessions: TradingSession[] = ['pre-market', 'regular', 'after-hours'];\n  const tags = ['High Volume', 'Low Float', 'Earnings', 'News', 'Technical', 'Momentum', 'Oversold', 'Overbought'];\n  \n  for (let i = 0; i < numTrades; i++) {\n    // Generate random trade date within the range\n    const tradeDate = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));\n    \n    // Generate entry time (market hours)\n    const entryHour = 9 + Math.floor(Math.random() * 7); // 9 AM to 4 PM\n    const entryMinute = Math.floor(Math.random() * 60);\n    const entryTime = new Date(tradeDate);\n    entryTime.setHours(entryHour, entryMinute, 0, 0);\n    \n    // Generate exit time (after entry time)\n    const durationMinutes = 5 + Math.floor(Math.random() * 120); // 5 to 120 minutes\n    const exitTime = new Date(entryTime.getTime() + durationMinutes * 60 * 1000);\n    \n    // Generate random trade details\n    const symbol = symbols[Math.floor(Math.random() * symbols.length)];\n    const direction: TradeDirection = Math.random() > 0.5 ? 'long' : 'short';\n    const entryPrice = 100 + Math.random() * 900; // $100 to $1000\n    \n    // Determine if win or loss (60% win rate)\n    const isWin = Math.random() < 0.6;\n    const isBreakeven = !isWin && Math.random() < 0.1; // 10% of losses are breakeven\n    \n    // Calculate exit price based on win/loss\n    let exitPrice, profitLoss, profitLossPercent, status: TradeStatus;\n    \n    if (isBreakeven) {\n      exitPrice = entryPrice + (Math.random() * 0.2 - 0.1); // Small fluctuation around entry\n      status = 'breakeven';\n    } else if (isWin) {\n      const winPercent = 0.5 + Math.random() * 4.5; // 0.5% to 5% win\n      exitPrice = direction === 'long' \n        ? entryPrice * (1 + winPercent / 100)\n        : entryPrice * (1 - winPercent / 100);\n      status = 'win';\n    } else {\n      const lossPercent = 0.5 + Math.random() * 2.5; // 0.5% to 3% loss\n      exitPrice = direction === 'long'\n        ? entryPrice * (1 - lossPercent / 100)\n        : entryPrice * (1 + lossPercent / 100);\n      status = 'loss';\n    }\n    \n    // Calculate P&L\n    const quantity = 10 + Math.floor(Math.random() * 90); // 10 to 100 shares\n    \n    if (direction === 'long') {\n      profitLoss = (exitPrice - entryPrice) * quantity;\n      profitLossPercent = ((exitPrice / entryPrice) - 1) * 100;\n    } else {\n      profitLoss = (entryPrice - exitPrice) * quantity;\n      profitLossPercent = ((entryPrice / exitPrice) - 1) * 100;\n    }\n    \n    // Round to 2 decimal places\n    profitLoss = Math.round(profitLoss * 100) / 100;\n    profitLossPercent = Math.round(profitLossPercent * 100) / 100;\n    \n    // Generate random trade metadata\n    const timeframe = timeframes[Math.floor(Math.random() * timeframes.length)];\n    const session = sessions[Math.floor(Math.random() * sessions.length)];\n    const strategy = strategies[Math.floor(Math.random() * strategies.length)];\n    \n    // Generate random tags (0-3 tags)\n    const numTags = Math.floor(Math.random() * 4);\n    const tradeTags: string[] = [];\n    for (let j = 0; j < numTags; j++) {\n      const tag = tags[Math.floor(Math.random() * tags.length)];\n      if (!tradeTags.includes(tag)) {\n        tradeTags.push(tag);\n      }\n    }\n    \n    // Create trade object\n    const trade: Trade = {\n      id: `trade-${i}`,\n      symbol,\n      direction,\n      entryPrice,\n      exitPrice,\n      quantity,\n      entryTime: entryTime.toISOString(),\n      exitTime: exitTime.toISOString(),\n      status,\n      profitLoss,\n      profitLossPercent,\n      timeframe,\n      session,\n      strategy,\n      tags: tradeTags,\n      notes: Math.random() > 0.7 ? `Sample note for ${symbol} ${direction} trade` : undefined,\n    };\n    \n    trades.push(trade);\n  }\n  \n  // Sort trades by date (newest first)\n  return trades.sort((a, b) => new Date(b.entryTime).getTime() - new Date(a.entryTime).getTime());\n};\n\n/**\n * Calculate performance metrics from trades\n */\nconst calculateMetrics = (trades: Trade[]): PerformanceMetrics => {\n  const winningTrades = trades.filter(trade => trade.status === 'win');\n  const losingTrades = trades.filter(trade => trade.status === 'loss');\n  const breakeven = trades.filter(trade => trade.status === 'breakeven');\n  \n  const totalProfitLoss = trades.reduce((sum, trade) => sum + trade.profitLoss, 0);\n  const totalWinAmount = winningTrades.reduce((sum, trade) => sum + trade.profitLoss, 0);\n  const totalLossAmount = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profitLoss, 0));\n  \n  const averageWin = winningTrades.length > 0 \n    ? totalWinAmount / winningTrades.length \n    : 0;\n    \n  const averageLoss = losingTrades.length > 0 \n    ? totalLossAmount / losingTrades.length \n    : 0;\n  \n  const largestWin = winningTrades.length > 0 \n    ? Math.max(...winningTrades.map(trade => trade.profitLoss)) \n    : 0;\n    \n  const largestLoss = losingTrades.length > 0 \n    ? Math.min(...losingTrades.map(trade => trade.profitLoss)) \n    : 0;\n  \n  // Calculate average duration in minutes\n  const durations = trades.map(trade => {\n    const entryTime = new Date(trade.entryTime).getTime();\n    const exitTime = new Date(trade.exitTime).getTime();\n    return (exitTime - entryTime) / (1000 * 60); // Convert to minutes\n  });\n  \n  const averageDuration = durations.length > 0 \n    ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length \n    : 0;\n  \n  // Calculate profit factor and expectancy\n  const profitFactor = totalLossAmount > 0 \n    ? totalWinAmount / totalLossAmount \n    : totalWinAmount > 0 ? Infinity : 0;\n    \n  const winRate = trades.length > 0 \n    ? winningTrades.length / trades.length \n    : 0;\n    \n  const expectancy = (winRate * averageWin) - ((1 - winRate) * averageLoss);\n  \n  return {\n    totalTrades: trades.length,\n    winningTrades: winningTrades.length,\n    losingTrades: losingTrades.length,\n    breakeven: breakeven.length,\n    winRate: Math.round(winRate * 10000) / 100, // Convert to percentage with 2 decimal places\n    averageWin: Math.round(averageWin * 100) / 100,\n    averageLoss: Math.round(averageLoss * 100) / 100,\n    profitFactor: Math.round(profitFactor * 100) / 100,\n    totalProfitLoss: Math.round(totalProfitLoss * 100) / 100,\n    largestWin: Math.round(largestWin * 100) / 100,\n    largestLoss: Math.round(largestLoss * 100) / 100,\n    averageDuration: Math.round(averageDuration * 100) / 100,\n    expectancy: Math.round(expectancy * 100) / 100,\n  };\n};\n\n/**\n * Calculate performance by category\n */\nconst calculateCategoryPerformance = (\n  trades: Trade[],\n  category: 'symbol' | 'strategy' | 'timeframe' | 'session'\n): CategoryPerformance[] => {\n  // Group trades by category\n  const categories = new Map<string, Trade[]>();\n  \n  trades.forEach(trade => {\n    const categoryValue = trade[category] as string;\n    if (!categories.has(categoryValue)) {\n      categories.set(categoryValue, []);\n    }\n    categories.get(categoryValue)!.push(trade);\n  });\n  \n  // Calculate performance for each category\n  const performance: CategoryPerformance[] = [];\n  \n  categories.forEach((categoryTrades, categoryValue) => {\n    const winningTrades = categoryTrades.filter(trade => trade.status === 'win');\n    const totalProfitLoss = categoryTrades.reduce((sum, trade) => sum + trade.profitLoss, 0);\n    const winRate = categoryTrades.length > 0 \n      ? winningTrades.length / categoryTrades.length \n      : 0;\n    const averageProfitLoss = categoryTrades.length > 0 \n      ? totalProfitLoss / categoryTrades.length \n      : 0;\n    \n    performance.push({\n      category,\n      value: categoryValue,\n      trades: categoryTrades.length,\n      winRate: Math.round(winRate * 10000) / 100, // Convert to percentage with 2 decimal places\n      profitLoss: Math.round(totalProfitLoss * 100) / 100,\n      averageProfitLoss: Math.round(averageProfitLoss * 100) / 100,\n    });\n  });\n  \n  // Sort by profit/loss (descending)\n  return performance.sort((a, b) => b.profitLoss - a.profitLoss);\n};\n\n/**\n * Calculate performance by time\n */\nconst calculateTimePerformance = (\n  trades: Trade[],\n  timeType: 'timeOfDay' | 'dayOfWeek'\n): TimePerformance[] => {\n  // Define time slots\n  let timeSlots: string[];\n  \n  if (timeType === 'timeOfDay') {\n    timeSlots = [\n      '9:30-10:30', '10:30-11:30', '11:30-12:30',\n      '12:30-13:30', '13:30-14:30', '14:30-15:30', '15:30-16:00'\n    ];\n  } else {\n    timeSlots = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\n  }\n  \n  // Group trades by time slot\n  const timePerformance: TimePerformance[] = timeSlots.map(timeSlot => ({\n    timeSlot,\n    trades: 0,\n    winRate: 0,\n    profitLoss: 0,\n  }));\n  \n  trades.forEach(trade => {\n    const entryTime = new Date(trade.entryTime);\n    let slotIndex: number;\n    \n    if (timeType === 'timeOfDay') {\n      // Get hour and determine slot\n      const hour = entryTime.getHours();\n      const minute = entryTime.getMinutes();\n      const timeValue = hour + minute / 60;\n      \n      if (timeValue < 9.5 || timeValue >= 16) {\n        return; // Outside regular market hours\n      }\n      \n      if (timeValue < 10.5) slotIndex = 0;\n      else if (timeValue < 11.5) slotIndex = 1;\n      else if (timeValue < 12.5) slotIndex = 2;\n      else if (timeValue < 13.5) slotIndex = 3;\n      else if (timeValue < 14.5) slotIndex = 4;\n      else if (timeValue < 15.5) slotIndex = 5;\n      else slotIndex = 6;\n    } else {\n      // Get day of week (0 = Sunday, 1 = Monday, etc.)\n      const dayOfWeek = entryTime.getDay();\n      if (dayOfWeek === 0 || dayOfWeek === 6) {\n        return; // Weekend\n      }\n      slotIndex = dayOfWeek - 1;\n    }\n    \n    // Update time slot data\n    const slot = timePerformance[slotIndex];\n    slot.trades++;\n    slot.profitLoss += trade.profitLoss;\n    \n    // Recalculate win rate\n    const slotTrades = trades.filter(t => {\n      const tEntryTime = new Date(t.entryTime);\n      if (timeType === 'timeOfDay') {\n        const tHour = tEntryTime.getHours();\n        const tMinute = tEntryTime.getMinutes();\n        const tTimeValue = tHour + tMinute / 60;\n        \n        if (slotIndex === 0) return tTimeValue >= 9.5 && tTimeValue < 10.5;\n        if (slotIndex === 1) return tTimeValue >= 10.5 && tTimeValue < 11.5;\n        if (slotIndex === 2) return tTimeValue >= 11.5 && tTimeValue < 12.5;\n        if (slotIndex === 3) return tTimeValue >= 12.5 && tTimeValue < 13.5;\n        if (slotIndex === 4) return tTimeValue >= 13.5 && tTimeValue < 14.5;\n        if (slotIndex === 5) return tTimeValue >= 14.5 && tTimeValue < 15.5;\n        if (slotIndex === 6) return tTimeValue >= 15.5 && tTimeValue < 16;\n        return false;\n      } else {\n        return tEntryTime.getDay() === slotIndex + 1;\n      }\n    });\n    \n    const winningSlotTrades = slotTrades.filter(t => t.status === 'win');\n    slot.winRate = slotTrades.length > 0 \n      ? (winningSlotTrades.length / slotTrades.length) * 100 \n      : 0;\n  });\n  \n  // Round numbers and filter out empty slots\n  return timePerformance\n    .filter(slot => slot.trades > 0)\n    .map(slot => ({\n      ...slot,\n      winRate: Math.round(slot.winRate * 100) / 100,\n      profitLoss: Math.round(slot.profitLoss * 100) / 100,\n    }));\n};\n", "/**\n * Trade Analysis Context\n * \n * Context for managing trade analysis state\n */\n\nimport React, { createContext, useContext, useReducer, ReactNode, useCallback, useEffect } from 'react';\nimport { useLocalStorage } from '@adhd-trading-dashboard/shared';\nimport { \n  TradeAnalysisState, \n  TradeAnalysisAction,\n  TradeAnalysisData,\n  TradeFilters,\n  UserPreferences\n} from '../types';\nimport { fetchTradeAnalysisData } from '../api/tradeAnalysisApi';\n\n// Get default date range\nconst getDefaultDateRange = () => {\n  const today = new Date();\n  const startDate = new Date();\n  startDate.setMonth(today.getMonth() - 1); // Default to last month\n  \n  return {\n    startDate: startDate.toISOString().split('T')[0],\n    endDate: today.toISOString().split('T')[0],\n  };\n};\n\n// Initial state\nconst initialState: TradeAnalysisState = {\n  data: null,\n  filters: {\n    dateRange: getDefaultDateRange(),\n  },\n  preferences: {\n    defaultDateRange: 'month',\n    defaultView: 'summary',\n    chartTypes: {\n      performance: 'bar',\n      distribution: 'pie',\n      timeAnalysis: 'bar',\n    },\n    tableColumns: [\n      'symbol',\n      'direction',\n      'entryTime',\n      'exitTime',\n      'profitLoss',\n      'status',\n      'strategy',\n    ],\n    favoriteStrategies: [],\n    favoriteTags: [],\n  },\n  isLoading: false,\n  error: null,\n  selectedTradeId: null,\n};\n\n// Reducer function\nconst tradeAnalysisReducer = (\n  state: TradeAnalysisState,\n  action: TradeAnalysisAction\n): TradeAnalysisState => {\n  switch (action.type) {\n    case 'FETCH_DATA_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'FETCH_DATA_SUCCESS':\n      return {\n        ...state,\n        data: action.payload,\n        isLoading: false,\n        error: null,\n      };\n    case 'FETCH_DATA_ERROR':\n      return {\n        ...state,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'UPDATE_FILTERS':\n      return {\n        ...state,\n        filters: {\n          ...state.filters,\n          ...action.payload,\n        },\n      };\n    case 'UPDATE_PREFERENCES':\n      return {\n        ...state,\n        preferences: {\n          ...state.preferences,\n          ...action.payload,\n        },\n      };\n    case 'SELECT_TRADE':\n      return {\n        ...state,\n        selectedTradeId: action.payload,\n      };\n    case 'RESET_FILTERS':\n      return {\n        ...state,\n        filters: {\n          dateRange: getDefaultDateRange(),\n        },\n      };\n    default:\n      return state;\n  }\n};\n\n// Context\ninterface TradeAnalysisContextType extends TradeAnalysisState {\n  fetchData: () => Promise<void>;\n  updateFilters: (filters: Partial<TradeFilters>) => void;\n  updatePreferences: (preferences: Partial<UserPreferences>) => void;\n  selectTrade: (tradeId: string | null) => void;\n  resetFilters: () => void;\n}\n\nconst TradeAnalysisContext = createContext<TradeAnalysisContextType | undefined>(undefined);\n\n// Provider component\ninterface TradeAnalysisProviderProps {\n  children: ReactNode;\n}\n\nexport const TradeAnalysisProvider: React.FC<TradeAnalysisProviderProps> = ({ children }) => {\n  // Load saved preferences from localStorage\n  const [savedPreferences] = useLocalStorage<Partial<UserPreferences>>(\n    'trade-analysis-preferences',\n    {}\n  );\n  \n  // Merge saved preferences with initial state\n  const mergedInitialState = {\n    ...initialState,\n    preferences: {\n      ...initialState.preferences,\n      ...savedPreferences,\n    },\n  };\n  \n  const [state, dispatch] = useReducer(tradeAnalysisReducer, mergedInitialState);\n  \n  // Save preferences to localStorage when they change\n  useEffect(() => {\n    localStorage.setItem(\n      'trade-analysis-preferences',\n      JSON.stringify(state.preferences)\n    );\n  }, [state.preferences]);\n  \n  const fetchData = useCallback(async () => {\n    dispatch({ type: 'FETCH_DATA_START' });\n    try {\n      const data = await fetchTradeAnalysisData(state.filters);\n      dispatch({ type: 'FETCH_DATA_SUCCESS', payload: data });\n    } catch (error) {\n      dispatch({\n        type: 'FETCH_DATA_ERROR',\n        payload: error instanceof Error ? error.message : 'An unknown error occurred',\n      });\n    }\n  }, [state.filters]);\n  \n  const updateFilters = useCallback((filters: Partial<TradeFilters>) => {\n    dispatch({ type: 'UPDATE_FILTERS', payload: filters });\n  }, []);\n  \n  const updatePreferences = useCallback((preferences: Partial<UserPreferences>) => {\n    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });\n  }, []);\n  \n  const selectTrade = useCallback((tradeId: string | null) => {\n    dispatch({ type: 'SELECT_TRADE', payload: tradeId });\n  }, []);\n  \n  const resetFilters = useCallback(() => {\n    dispatch({ type: 'RESET_FILTERS' });\n  }, []);\n  \n  // Fetch data when filters change\n  useEffect(() => {\n    fetchData();\n  }, [fetchData, state.filters]);\n  \n  const value = {\n    ...state,\n    fetchData,\n    updateFilters,\n    updatePreferences,\n    selectTrade,\n    resetFilters,\n  };\n  \n  return (\n    <TradeAnalysisContext.Provider value={value}>\n      {children}\n    </TradeAnalysisContext.Provider>\n  );\n};\n\n// Custom hook for using the context\nexport const useTradeAnalysis = (): TradeAnalysisContextType => {\n  const context = useContext(TradeAnalysisContext);\n  if (context === undefined) {\n    throw new Error('useTradeAnalysis must be used within a TradeAnalysisProvider');\n  }\n  return context;\n};\n", "/**\n * Filter Panel Component\n * \n * Provides filtering options for trade analysis\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { \n  TradeFilters, \n  TradeDirection, \n  TradeStatus, \n  TradeTimeframe, \n  TradingSession \n} from '../types';\nimport { useTradeAnalysis } from '../context/TradeAnalysisContext';\nimport { Button, Card, Tag } from '@adhd-trading-dashboard/shared';\n\ninterface FilterPanelProps {\n  className?: string;\n}\n\nconst Container = styled(Card)`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FilterGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FilterSection = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FilterLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst DateRangeContainer = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst DateInput = styled.input`\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  flex: 1;\n`;\n\nconst TagsContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst FilterTag = styled(Tag)<{ selected: boolean }>`\n  cursor: pointer;\n  opacity: ${({ selected }) => (selected ? 1 : 0.6)};\n  \n  &:hover {\n    opacity: 0.8;\n  }\n`;\n\nconst ButtonContainer = styled.div`\n  display: flex;\n  justify-content: flex-end;\n  gap: ${({ theme }) => theme.spacing.sm};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nexport const FilterPanel: React.FC<FilterPanelProps> = ({ className }) => {\n  const { filters, updateFilters, resetFilters, data } = useTradeAnalysis();\n  \n  // Local state for filter values\n  const [localFilters, setLocalFilters] = useState<TradeFilters>(filters);\n  \n  // Available options from data\n  const availableSymbols = data?.trades \n    ? [...new Set(data.trades.map(trade => trade.symbol))]\n    : [];\n    \n  const availableStrategies = data?.trades \n    ? [...new Set(data.trades.map(trade => trade.strategy))]\n    : [];\n    \n  const availableTags = data?.trades \n    ? [...new Set(data.trades.flatMap(trade => trade.tags || []))]\n    : [];\n  \n  // Direction options\n  const directionOptions: TradeDirection[] = ['long', 'short'];\n  \n  // Status options\n  const statusOptions: TradeStatus[] = ['win', 'loss', 'breakeven'];\n  \n  // Timeframe options\n  const timeframeOptions: TradeTimeframe[] = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];\n  \n  // Session options\n  const sessionOptions: TradingSession[] = ['pre-market', 'regular', 'after-hours'];\n  \n  // Handle date range change\n  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {\n    setLocalFilters(prev => ({\n      ...prev,\n      dateRange: {\n        ...prev.dateRange,\n        [field]: value,\n      },\n    }));\n  };\n  \n  // Handle array filter toggle\n  const handleToggleFilter = <T extends string>(\n    field: keyof Pick<TradeFilters, 'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'>,\n    value: T\n  ) => {\n    setLocalFilters(prev => {\n      const currentValues = prev[field] as T[] || [];\n      const newValues = currentValues.includes(value)\n        ? currentValues.filter(v => v !== value)\n        : [...currentValues, value];\n      \n      return {\n        ...prev,\n        [field]: newValues.length > 0 ? newValues : undefined,\n      };\n    });\n  };\n  \n  // Apply filters\n  const applyFilters = () => {\n    updateFilters(localFilters);\n  };\n  \n  // Reset filters\n  const handleResetFilters = () => {\n    resetFilters();\n    setLocalFilters(filters);\n  };\n  \n  // Check if a filter value is selected\n  const isSelected = <T extends string>(\n    field: keyof Pick<TradeFilters, 'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'>,\n    value: T\n  ): boolean => {\n    const values = localFilters[field] as T[] | undefined;\n    return values ? values.includes(value) : false;\n  };\n  \n  return (\n    <Container \n      className={className}\n      title=\"Filters\"\n      variant=\"default\"\n      padding=\"medium\"\n    >\n      <FilterGrid>\n        <FilterSection>\n          <FilterLabel>Date Range</FilterLabel>\n          <DateRangeContainer>\n            <DateInput\n              type=\"date\"\n              value={localFilters.dateRange.startDate}\n              onChange={(e) => handleDateChange('startDate', e.target.value)}\n            />\n            <DateInput\n              type=\"date\"\n              value={localFilters.dateRange.endDate}\n              onChange={(e) => handleDateChange('endDate', e.target.value)}\n            />\n          </DateRangeContainer>\n        </FilterSection>\n        \n        <FilterSection>\n          <FilterLabel>Direction</FilterLabel>\n          <TagsContainer>\n            {directionOptions.map((direction) => (\n              <FilterTag\n                key={direction}\n                variant={direction === 'long' ? 'success' : 'error'}\n                selected={isSelected('directions', direction)}\n                onClick={() => handleToggleFilter('directions', direction)}\n              >\n                {direction}\n              </FilterTag>\n            ))}\n          </TagsContainer>\n        </FilterSection>\n        \n        <FilterSection>\n          <FilterLabel>Status</FilterLabel>\n          <TagsContainer>\n            {statusOptions.map((status) => (\n              <FilterTag\n                key={status}\n                variant={\n                  status === 'win' ? 'success' : \n                  status === 'loss' ? 'error' : \n                  'info'\n                }\n                selected={isSelected('statuses', status)}\n                onClick={() => handleToggleFilter('statuses', status)}\n              >\n                {status}\n              </FilterTag>\n            ))}\n          </TagsContainer>\n        </FilterSection>\n        \n        {availableSymbols.length > 0 && (\n          <FilterSection>\n            <FilterLabel>Symbols</FilterLabel>\n            <TagsContainer>\n              {availableSymbols.map((symbol) => (\n                <FilterTag\n                  key={symbol}\n                  variant=\"primary\"\n                  selected={isSelected('symbols', symbol)}\n                  onClick={() => handleToggleFilter('symbols', symbol)}\n                >\n                  {symbol}\n                </FilterTag>\n              ))}\n            </TagsContainer>\n          </FilterSection>\n        )}\n        \n        {availableStrategies.length > 0 && (\n          <FilterSection>\n            <FilterLabel>Strategies</FilterLabel>\n            <TagsContainer>\n              {availableStrategies.map((strategy) => (\n                <FilterTag\n                  key={strategy}\n                  variant=\"secondary\"\n                  selected={isSelected('strategies', strategy)}\n                  onClick={() => handleToggleFilter('strategies', strategy)}\n                >\n                  {strategy}\n                </FilterTag>\n              ))}\n            </TagsContainer>\n          </FilterSection>\n        )}\n        \n        <FilterSection>\n          <FilterLabel>Timeframe</FilterLabel>\n          <TagsContainer>\n            {timeframeOptions.map((timeframe) => (\n              <FilterTag\n                key={timeframe}\n                variant=\"default\"\n                selected={isSelected('timeframes', timeframe)}\n                onClick={() => handleToggleFilter('timeframes', timeframe)}\n              >\n                {timeframe}\n              </FilterTag>\n            ))}\n          </TagsContainer>\n        </FilterSection>\n        \n        <FilterSection>\n          <FilterLabel>Session</FilterLabel>\n          <TagsContainer>\n            {sessionOptions.map((session) => (\n              <FilterTag\n                key={session}\n                variant=\"default\"\n                selected={isSelected('sessions', session)}\n                onClick={() => handleToggleFilter('sessions', session)}\n              >\n                {session}\n              </FilterTag>\n            ))}\n          </TagsContainer>\n        </FilterSection>\n        \n        {availableTags.length > 0 && (\n          <FilterSection>\n            <FilterLabel>Tags</FilterLabel>\n            <TagsContainer>\n              {availableTags.map((tag) => (\n                <FilterTag\n                  key={tag}\n                  variant=\"info\"\n                  selected={isSelected('tags', tag)}\n                  onClick={() => handleToggleFilter('tags', tag)}\n                >\n                  {tag}\n                </FilterTag>\n              ))}\n            </TagsContainer>\n          </FilterSection>\n        )}\n      </FilterGrid>\n      \n      <ButtonContainer>\n        <Button variant=\"outline\" onClick={handleResetFilters}>\n          Reset\n        </Button>\n        <Button onClick={applyFilters}>\n          Apply Filters\n        </Button>\n      </ButtonContainer>\n    </Container>\n  );\n};\n", "/**\n * Performance Summary Component\n * \n * Displays a summary of trading performance metrics\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { PerformanceMetrics } from '../types';\nimport { useTradeAnalysis } from '../context/TradeAnalysisContext';\n\ninterface PerformanceSummaryProps {\n  className?: string;\n}\n\nconst Container = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst MetricCard = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  padding: ${({ theme }) => theme.spacing.md};\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MetricLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst MetricValue = styled.div<{ positive?: boolean; negative?: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme, positive, negative }) => {\n    if (positive) return theme.colors.profit;\n    if (negative) return theme.colors.loss;\n    return theme.colors.textPrimary;\n  }};\n`;\n\nconst MetricChange = styled.div<{ positive?: boolean; negative?: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  color: ${({ theme, positive, negative }) => {\n    if (positive) return theme.colors.profit;\n    if (negative) return theme.colors.loss;\n    return theme.colors.textSecondary;\n  }};\n`;\n\nexport const PerformanceSummary: React.FC<PerformanceSummaryProps> = ({ className }) => {\n  const { data } = useTradeAnalysis();\n  \n  if (!data) {\n    return null;\n  }\n  \n  const { metrics } = data;\n  \n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n  \n  const formatPercent = (value: number): string => {\n    return `${value.toFixed(2)}%`;\n  };\n  \n  return (\n    <Container className={className}>\n      <MetricCard>\n        <MetricLabel>Total P&L</MetricLabel>\n        <MetricValue positive={metrics.totalProfitLoss > 0} negative={metrics.totalProfitLoss < 0}>\n          {formatCurrency(metrics.totalProfitLoss)}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Win Rate</MetricLabel>\n        <MetricValue positive={metrics.winRate > 50} negative={metrics.winRate < 50}>\n          {formatPercent(metrics.winRate)}\n        </MetricValue>\n        <MetricChange>\n          {metrics.winningTrades} / {metrics.totalTrades} trades\n        </MetricChange>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Profit Factor</MetricLabel>\n        <MetricValue positive={metrics.profitFactor > 1} negative={metrics.profitFactor < 1}>\n          {metrics.profitFactor.toFixed(2)}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Expectancy</MetricLabel>\n        <MetricValue positive={metrics.expectancy > 0} negative={metrics.expectancy < 0}>\n          {formatCurrency(metrics.expectancy)}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Average Win</MetricLabel>\n        <MetricValue positive={true}>\n          {formatCurrency(metrics.averageWin)}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Average Loss</MetricLabel>\n        <MetricValue negative={true}>\n          {formatCurrency(-Math.abs(metrics.averageLoss))}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Largest Win</MetricLabel>\n        <MetricValue positive={true}>\n          {formatCurrency(metrics.largestWin)}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Largest Loss</MetricLabel>\n        <MetricValue negative={true}>\n          {formatCurrency(metrics.largestLoss)}\n        </MetricValue>\n      </MetricCard>\n      \n      <MetricCard>\n        <MetricLabel>Total Trades</MetricLabel>\n        <MetricValue>\n          {metrics.totalTrades}\n        </MetricValue>\n        <MetricChange>\n          Avg Duration: {metrics.averageDuration.toFixed(0)} min\n        </MetricChange>\n      </MetricCard>\n    </Container>\n  );\n};\n", "/**\n * Trades Table Component\n * \n * Displays a table of trades with sorting and filtering\n */\n\nimport React, { useState, useMemo } from 'react';\nimport styled from 'styled-components';\nimport { Trade, TradeDirection, TradeStatus } from '../types';\nimport { useTradeAnalysis } from '../context/TradeAnalysisContext';\nimport { Badge, Tag } from '@adhd-trading-dashboard/shared';\n\ninterface TradesTableProps {\n  className?: string;\n}\n\ntype SortField = 'entryTime' | 'symbol' | 'direction' | 'profitLoss' | 'profitLossPercent' | 'status';\ntype SortDirection = 'asc' | 'desc';\n\nconst Container = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n`;\n\nconst TableHead = styled.thead`\n  background-color: ${({ theme }) => theme.colors.background};\n  position: sticky;\n  top: 0;\n  z-index: 1;\n`;\n\nconst TableBody = styled.tbody``;\n\nconst TableRow = styled.tr<{ isSelected?: boolean }>`\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n  background-color: ${({ theme, isSelected }) => \n    isSelected ? `${theme.colors.primary}10` : 'transparent'};\n  \n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst TableHeaderCell = styled.th<{ sortable?: boolean; active?: boolean }>`\n  padding: ${({ theme }) => theme.spacing.sm};\n  text-align: left;\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme, active }) => active ? theme.colors.primary : theme.colors.textPrimary};\n  cursor: ${({ sortable }) => sortable ? 'pointer' : 'default'};\n  \n  &:hover {\n    ${({ sortable, theme }) => sortable && `\n      color: ${theme.colors.primary};\n    `}\n  }\n`;\n\nconst TableCell = styled.td`\n  padding: ${({ theme }) => theme.spacing.sm};\n  vertical-align: middle;\n`;\n\nconst SortIcon = styled.span<{ direction: SortDirection }>`\n  display: inline-block;\n  margin-left: ${({ theme }) => theme.spacing.xs};\n  \n  &::after {\n    content: '${({ direction }) => direction === 'asc' ? '↑' : '↓'}';\n  }\n`;\n\nconst DirectionBadge = styled(Badge)<{ direction: TradeDirection }>`\n  text-transform: capitalize;\n`;\n\nconst StatusBadge = styled(Badge)<{ status: TradeStatus }>`\n  text-transform: capitalize;\n`;\n\nconst TagsContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst ProfitLoss = styled.span<{ value: number }>`\n  color: ${({ theme, value }) => \n    value > 0 ? theme.colors.profit : \n    value < 0 ? theme.colors.loss : \n    theme.colors.textSecondary};\n  font-weight: ${({ theme, value }) => \n    value !== 0 ? theme.fontWeights.medium : theme.fontWeights.regular};\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const TradesTable: React.FC<TradesTableProps> = ({ className }) => {\n  const { data, selectedTradeId, selectTrade } = useTradeAnalysis();\n  const [sortField, setSortField] = useState<SortField>('entryTime');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n  \n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      // Toggle direction if same field\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      // Set new field and default direction\n      setSortField(field);\n      setSortDirection('desc');\n    }\n  };\n  \n  const sortedTrades = useMemo(() => {\n    if (!data?.trades) return [];\n    \n    return [...data.trades].sort((a, b) => {\n      let comparison = 0;\n      \n      switch (sortField) {\n        case 'entryTime':\n          comparison = new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime();\n          break;\n        case 'symbol':\n          comparison = a.symbol.localeCompare(b.symbol);\n          break;\n        case 'direction':\n          comparison = a.direction.localeCompare(b.direction);\n          break;\n        case 'profitLoss':\n          comparison = a.profitLoss - b.profitLoss;\n          break;\n        case 'profitLossPercent':\n          comparison = a.profitLossPercent - b.profitLossPercent;\n          break;\n        case 'status':\n          comparison = a.status.localeCompare(b.status);\n          break;\n        default:\n          comparison = 0;\n      }\n      \n      return sortDirection === 'asc' ? comparison : -comparison;\n    });\n  }, [data?.trades, sortField, sortDirection]);\n  \n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n  \n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n  \n  const formatPercent = (value: number): string => {\n    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;\n  };\n  \n  const getDirectionVariant = (direction: TradeDirection): string => {\n    return direction === 'long' ? 'success' : 'error';\n  };\n  \n  const getStatusVariant = (status: TradeStatus): string => {\n    switch (status) {\n      case 'win': return 'success';\n      case 'loss': return 'error';\n      case 'breakeven': return 'info';\n      default: return 'default';\n    }\n  };\n  \n  const handleRowClick = (tradeId: string) => {\n    selectTrade(tradeId === selectedTradeId ? null : tradeId);\n  };\n  \n  if (!data || !data.trades || data.trades.length === 0) {\n    return <EmptyState>No trades found for the selected filters.</EmptyState>;\n  }\n  \n  return (\n    <Container className={className}>\n      <Table>\n        <TableHead>\n          <TableRow>\n            <TableHeaderCell \n              sortable \n              active={sortField === 'entryTime'} \n              onClick={() => handleSort('entryTime')}\n            >\n              Date/Time\n              {sortField === 'entryTime' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'symbol'} \n              onClick={() => handleSort('symbol')}\n            >\n              Symbol\n              {sortField === 'symbol' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'direction'} \n              onClick={() => handleSort('direction')}\n            >\n              Direction\n              {sortField === 'direction' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell>\n              Entry/Exit\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'profitLoss'} \n              onClick={() => handleSort('profitLoss')}\n            >\n              P&L\n              {sortField === 'profitLoss' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'profitLossPercent'} \n              onClick={() => handleSort('profitLossPercent')}\n            >\n              P&L %\n              {sortField === 'profitLossPercent' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'status'} \n              onClick={() => handleSort('status')}\n            >\n              Status\n              {sortField === 'status' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell>\n              Strategy\n            </TableHeaderCell>\n            \n            <TableHeaderCell>\n              Tags\n            </TableHeaderCell>\n          </TableRow>\n        </TableHead>\n        \n        <TableBody>\n          {sortedTrades.map((trade) => (\n            <TableRow \n              key={trade.id} \n              isSelected={trade.id === selectedTradeId}\n              onClick={() => handleRowClick(trade.id)}\n            >\n              <TableCell>{formatDate(trade.entryTime)}</TableCell>\n              <TableCell>{trade.symbol}</TableCell>\n              <TableCell>\n                <DirectionBadge \n                  direction={trade.direction}\n                  variant={getDirectionVariant(trade.direction) as any}\n                  size=\"small\"\n                >\n                  {trade.direction}\n                </DirectionBadge>\n              </TableCell>\n              <TableCell>\n                {trade.entryPrice.toFixed(2)} → {trade.exitPrice.toFixed(2)}\n              </TableCell>\n              <TableCell>\n                <ProfitLoss value={trade.profitLoss}>\n                  {formatCurrency(trade.profitLoss)}\n                </ProfitLoss>\n              </TableCell>\n              <TableCell>\n                <ProfitLoss value={trade.profitLossPercent}>\n                  {formatPercent(trade.profitLossPercent)}\n                </ProfitLoss>\n              </TableCell>\n              <TableCell>\n                <StatusBadge \n                  status={trade.status}\n                  variant={getStatusVariant(trade.status) as any}\n                  size=\"small\"\n                >\n                  {trade.status}\n                </StatusBadge>\n              </TableCell>\n              <TableCell>{trade.strategy}</TableCell>\n              <TableCell>\n                <TagsContainer>\n                  {trade.tags?.map((tag, index) => (\n                    <Tag key={index} size=\"small\" variant=\"default\">\n                      {tag}\n                    </Tag>\n                  ))}\n                </TagsContainer>\n              </TableCell>\n            </TableRow>\n          ))}\n        </TableBody>\n      </Table>\n    </Container>\n  );\n};\n", "/**\n * Category Performance Chart Component\n * \n * Displays performance metrics by category (symbol, strategy, timeframe, session)\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { CategoryPerformance } from '../types';\nimport { useTradeAnalysis } from '../context/TradeAnalysisContext';\n\ninterface CategoryPerformanceChartProps {\n  className?: string;\n  category: 'symbol' | 'strategy' | 'timeframe' | 'session';\n  title: string;\n}\n\ntype SortField = 'value' | 'trades' | 'winRate' | 'profitLoss' | 'averageProfitLoss';\ntype SortDirection = 'asc' | 'desc';\n\nconst Container = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n`;\n\nconst TableHead = styled.thead`\n  background-color: ${({ theme }) => theme.colors.background};\n`;\n\nconst TableBody = styled.tbody``;\n\nconst TableRow = styled.tr`\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n  \n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst TableHeaderCell = styled.th<{ sortable?: boolean; active?: boolean }>`\n  padding: ${({ theme }) => theme.spacing.sm};\n  text-align: left;\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme, active }) => active ? theme.colors.primary : theme.colors.textPrimary};\n  cursor: ${({ sortable }) => sortable ? 'pointer' : 'default'};\n  \n  &:hover {\n    ${({ sortable, theme }) => sortable && `\n      color: ${theme.colors.primary};\n    `}\n  }\n`;\n\nconst TableCell = styled.td`\n  padding: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst SortIcon = styled.span<{ direction: SortDirection }>`\n  display: inline-block;\n  margin-left: ${({ theme }) => theme.spacing.xs};\n  \n  &::after {\n    content: '${({ direction }) => direction === 'asc' ? '↑' : '↓'}';\n  }\n`;\n\nconst BarContainer = styled.div`\n  height: 8px;\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.pill};\n  overflow: hidden;\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Bar = styled.div<{ width: number; positive: boolean }>`\n  height: 100%;\n  width: ${({ width }) => `${width}%`};\n  background-color: ${({ theme, positive }) => positive ? theme.colors.profit : theme.colors.loss};\n`;\n\nconst ProfitLoss = styled.span<{ value: number }>`\n  color: ${({ theme, value }) => \n    value > 0 ? theme.colors.profit : \n    value < 0 ? theme.colors.loss : \n    theme.colors.textSecondary};\n  font-weight: ${({ theme, value }) => \n    value !== 0 ? theme.fontWeights.medium : theme.fontWeights.regular};\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const CategoryPerformanceChart: React.FC<CategoryPerformanceChartProps> = ({ \n  className,\n  category,\n  title,\n}) => {\n  const { data } = useTradeAnalysis();\n  const [sortField, setSortField] = useState<SortField>('profitLoss');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n  \n  if (!data) {\n    return null;\n  }\n  \n  let performanceData: CategoryPerformance[] = [];\n  \n  switch (category) {\n    case 'symbol':\n      performanceData = data.symbolPerformance;\n      break;\n    case 'strategy':\n      performanceData = data.strategyPerformance;\n      break;\n    case 'timeframe':\n      performanceData = data.timeframePerformance;\n      break;\n    case 'session':\n      performanceData = data.sessionPerformance;\n      break;\n  }\n  \n  if (!performanceData || performanceData.length === 0) {\n    return <EmptyState>No {category} performance data available.</EmptyState>;\n  }\n  \n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      // Toggle direction if same field\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      // Set new field and default direction\n      setSortField(field);\n      setSortDirection('desc');\n    }\n  };\n  \n  // Sort data\n  const sortedData = [...performanceData].sort((a, b) => {\n    let comparison = 0;\n    \n    switch (sortField) {\n      case 'value':\n        comparison = a.value.localeCompare(b.value);\n        break;\n      case 'trades':\n        comparison = a.trades - b.trades;\n        break;\n      case 'winRate':\n        comparison = a.winRate - b.winRate;\n        break;\n      case 'profitLoss':\n        comparison = a.profitLoss - b.profitLoss;\n        break;\n      case 'averageProfitLoss':\n        comparison = a.averageProfitLoss - b.averageProfitLoss;\n        break;\n      default:\n        comparison = 0;\n    }\n    \n    return sortDirection === 'asc' ? comparison : -comparison;\n  });\n  \n  // Find max profit/loss for bar scaling\n  const maxProfitLoss = Math.max(\n    ...performanceData.map(item => Math.abs(item.profitLoss))\n  );\n  \n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n  \n  const formatPercent = (value: number): string => {\n    return `${value.toFixed(2)}%`;\n  };\n  \n  return (\n    <Container className={className}>\n      <Table>\n        <TableHead>\n          <TableRow>\n            <TableHeaderCell \n              sortable \n              active={sortField === 'value'} \n              onClick={() => handleSort('value')}\n            >\n              {title}\n              {sortField === 'value' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'trades'} \n              onClick={() => handleSort('trades')}\n            >\n              Trades\n              {sortField === 'trades' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'winRate'} \n              onClick={() => handleSort('winRate')}\n            >\n              Win Rate\n              {sortField === 'winRate' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'profitLoss'} \n              onClick={() => handleSort('profitLoss')}\n            >\n              P&L\n              {sortField === 'profitLoss' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n            \n            <TableHeaderCell \n              sortable \n              active={sortField === 'averageProfitLoss'} \n              onClick={() => handleSort('averageProfitLoss')}\n            >\n              Avg P&L\n              {sortField === 'averageProfitLoss' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n          </TableRow>\n        </TableHead>\n        \n        <TableBody>\n          {sortedData.map((item, index) => (\n            <TableRow key={index}>\n              <TableCell>{item.value}</TableCell>\n              <TableCell>{item.trades}</TableCell>\n              <TableCell>{formatPercent(item.winRate)}</TableCell>\n              <TableCell>\n                <ProfitLoss value={item.profitLoss}>\n                  {formatCurrency(item.profitLoss)}\n                </ProfitLoss>\n                <BarContainer>\n                  <Bar \n                    width={Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100)} \n                    positive={item.profitLoss >= 0}\n                  />\n                </BarContainer>\n              </TableCell>\n              <TableCell>\n                <ProfitLoss value={item.averageProfitLoss}>\n                  {formatCurrency(item.averageProfitLoss)}\n                </ProfitLoss>\n              </TableCell>\n            </TableRow>\n          ))}\n        </TableBody>\n      </Table>\n    </Container>\n  );\n};\n", "/**\n * Time Performance Chart Component\n * \n * Displays performance metrics by time (time of day, day of week)\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { TimePerformance } from '../types';\nimport { useTradeAnalysis } from '../context/TradeAnalysisContext';\n\ninterface TimePerformanceChartProps {\n  className?: string;\n  timeType: 'timeOfDay' | 'dayOfWeek';\n  title: string;\n}\n\nconst Container = styled.div``;\n\nconst ChartContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst TimeSlot = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst TimeSlotHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst TimeSlotLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst TimeSlotMetrics = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.md};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst MetricValue = styled.span<{ positive?: boolean; negative?: boolean }>`\n  color: ${({ theme, positive, negative }) => {\n    if (positive) return theme.colors.profit;\n    if (negative) return theme.colors.loss;\n    return theme.colors.textSecondary;\n  }};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n`;\n\nconst BarContainer = styled.div`\n  height: 24px;\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  overflow: hidden;\n  position: relative;\n`;\n\nconst Bar = styled.div<{ width: number; positive: boolean }>`\n  height: 100%;\n  width: ${({ width }) => `${width}%`};\n  background-color: ${({ theme, positive }) => positive ? theme.colors.profit : theme.colors.loss};\n  transition: width 0.3s ease;\n`;\n\nconst BarLabel = styled.div`\n  position: absolute;\n  top: 0;\n  left: ${({ theme }) => theme.spacing.sm};\n  height: 100%;\n  display: flex;\n  align-items: center;\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textInverse};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const TimePerformanceChart: React.FC<TimePerformanceChartProps> = ({ \n  className,\n  timeType,\n  title,\n}) => {\n  const { data } = useTradeAnalysis();\n  \n  if (!data) {\n    return null;\n  }\n  \n  const performanceData = timeType === 'timeOfDay' \n    ? data.timeOfDayPerformance \n    : data.dayOfWeekPerformance;\n  \n  if (!performanceData || performanceData.length === 0) {\n    return <EmptyState>No {timeType} performance data available.</EmptyState>;\n  }\n  \n  // Find max profit/loss for bar scaling\n  const maxProfitLoss = Math.max(\n    ...performanceData.map(item => Math.abs(item.profitLoss))\n  );\n  \n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n  \n  const formatPercent = (value: number): string => {\n    return `${value.toFixed(2)}%`;\n  };\n  \n  return (\n    <Container className={className}>\n      <ChartContainer>\n        {performanceData.map((item, index) => (\n          <TimeSlot key={index}>\n            <TimeSlotHeader>\n              <TimeSlotLabel>{item.timeSlot}</TimeSlotLabel>\n              <TimeSlotMetrics>\n                <div>\n                  Trades: <MetricValue>{item.trades}</MetricValue>\n                </div>\n                <div>\n                  Win Rate: <MetricValue positive={item.winRate > 50} negative={item.winRate < 50}>\n                    {formatPercent(item.winRate)}\n                  </MetricValue>\n                </div>\n                <div>\n                  P&L: <MetricValue positive={item.profitLoss > 0} negative={item.profitLoss < 0}>\n                    {formatCurrency(item.profitLoss)}\n                  </MetricValue>\n                </div>\n              </TimeSlotMetrics>\n            </TimeSlotHeader>\n            <BarContainer>\n              <Bar \n                width={Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100)} \n                positive={item.profitLoss >= 0}\n              >\n                {item.profitLoss !== 0 && (\n                  <BarLabel>\n                    {formatCurrency(item.profitLoss)}\n                  </BarLabel>\n                )}\n              </Bar>\n            </BarContainer>\n          </TimeSlot>\n        ))}\n      </ChartContainer>\n    </Container>\n  );\n};\n", "/**\n * Trade Detail Component\n * \n * Displays detailed information about a selected trade\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Trade } from '../types';\nimport { useTradeAnalysis } from '../context/TradeAnalysisContext';\nimport { Badge, Card, Tag } from '@adhd-trading-dashboard/shared';\n\ninterface TradeDetailProps {\n  className?: string;\n}\n\nconst Container = styled(Card)`\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailSection = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xxs};\n`;\n\nconst DetailValue = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst ProfitLoss = styled.div<{ value: number }>`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme, value }) => \n    value > 0 ? theme.colors.profit : \n    value < 0 ? theme.colors.loss : \n    theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst TagsContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Notes = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n  padding-top: ${({ theme }) => theme.spacing.md};\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  white-space: pre-wrap;\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const TradeDetail: React.FC<TradeDetailProps> = ({ className }) => {\n  const { data, selectedTradeId } = useTradeAnalysis();\n  \n  if (!data || !selectedTradeId) {\n    return null;\n  }\n  \n  const selectedTrade = data.trades.find(trade => trade.id === selectedTradeId);\n  \n  if (!selectedTrade) {\n    return <EmptyState>Trade not found.</EmptyState>;\n  }\n  \n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n  \n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n  \n  const formatPercent = (value: number): string => {\n    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;\n  };\n  \n  const getDirectionVariant = (direction: string): string => {\n    return direction === 'long' ? 'success' : 'error';\n  };\n  \n  const getStatusVariant = (status: string): string => {\n    switch (status) {\n      case 'win': return 'success';\n      case 'loss': return 'error';\n      case 'breakeven': return 'info';\n      default: return 'default';\n    }\n  };\n  \n  const calculateDuration = (entryTime: string, exitTime: string): string => {\n    const entry = new Date(entryTime).getTime();\n    const exit = new Date(exitTime).getTime();\n    const durationMs = exit - entry;\n    \n    const minutes = Math.floor(durationMs / (1000 * 60));\n    const hours = Math.floor(minutes / 60);\n    const remainingMinutes = minutes % 60;\n    \n    if (hours > 0) {\n      return `${hours}h ${remainingMinutes}m`;\n    }\n    return `${minutes}m`;\n  };\n  \n  return (\n    <Container \n      className={className}\n      title={`${selectedTrade.symbol} Trade Details`}\n      variant=\"default\"\n      padding=\"medium\"\n    >\n      <ProfitLoss value={selectedTrade.profitLoss}>\n        {formatCurrency(selectedTrade.profitLoss)} ({formatPercent(selectedTrade.profitLossPercent)})\n      </ProfitLoss>\n      \n      <DetailGrid>\n        <DetailSection>\n          <DetailLabel>Direction</DetailLabel>\n          <DetailValue>\n            <Badge \n              variant={getDirectionVariant(selectedTrade.direction) as any}\n              size=\"small\"\n            >\n              {selectedTrade.direction}\n            </Badge>\n          </DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Status</DetailLabel>\n          <DetailValue>\n            <Badge \n              variant={getStatusVariant(selectedTrade.status) as any}\n              size=\"small\"\n            >\n              {selectedTrade.status}\n            </Badge>\n          </DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Entry Time</DetailLabel>\n          <DetailValue>{formatDate(selectedTrade.entryTime)}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Exit Time</DetailLabel>\n          <DetailValue>{formatDate(selectedTrade.exitTime)}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Duration</DetailLabel>\n          <DetailValue>{calculateDuration(selectedTrade.entryTime, selectedTrade.exitTime)}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Entry Price</DetailLabel>\n          <DetailValue>{selectedTrade.entryPrice.toFixed(2)}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Exit Price</DetailLabel>\n          <DetailValue>{selectedTrade.exitPrice.toFixed(2)}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Quantity</DetailLabel>\n          <DetailValue>{selectedTrade.quantity}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Timeframe</DetailLabel>\n          <DetailValue>{selectedTrade.timeframe}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Session</DetailLabel>\n          <DetailValue>{selectedTrade.session}</DetailValue>\n        </DetailSection>\n        \n        <DetailSection>\n          <DetailLabel>Strategy</DetailLabel>\n          <DetailValue>{selectedTrade.strategy}</DetailValue>\n        </DetailSection>\n      </DetailGrid>\n      \n      {selectedTrade.tags && selectedTrade.tags.length > 0 && (\n        <DetailSection>\n          <DetailLabel>Tags</DetailLabel>\n          <TagsContainer>\n            {selectedTrade.tags.map((tag, index) => (\n              <Tag key={index} variant=\"info\" size=\"small\">\n                {tag}\n              </Tag>\n            ))}\n          </TagsContainer>\n        </DetailSection>\n      )}\n      \n      {selectedTrade.notes && (\n        <Notes>\n          <DetailLabel>Notes</DetailLabel>\n          <DetailValue>{selectedTrade.notes}</DetailValue>\n        </Notes>\n      )}\n    </Container>\n  );\n};\n", "/**\n * Trade Analysis Page\n *\n * This page displays trade analysis and performance metrics.\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { DataCard } from '@adhd-trading-dashboard/shared';\nimport { TradeAnalysisProvider, useTradeAnalysis } from './context/TradeAnalysisContext';\nimport { FilterPanel } from './components/FilterPanel';\nimport { PerformanceSummary } from './components/PerformanceSummary';\nimport { TradesTable } from './components/TradesTable';\nimport { CategoryPerformanceChart } from './components/CategoryPerformanceChart';\nimport { TimePerformanceChart } from './components/TimePerformanceChart';\nimport { TradeDetail } from './components/TradeDetail';\n\ntype ViewType = 'summary' | 'trades' | 'symbols' | 'strategies' | 'timeframes' | 'time';\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xxl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst ViewTabs = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n  padding-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst ViewTab = styled.button<{ active: boolean }>`\n  background: none;\n  border: none;\n  padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: ${({ theme, active }) =>\n    active ? theme.fontWeights.semibold : theme.fontWeights.regular};\n  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textSecondary)};\n  cursor: pointer;\n  border-bottom: 2px solid ${({ theme, active }) => (active ? theme.colors.primary : 'transparent')};\n  transition: all ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\nconst TradeAnalysisContent: React.FC = () => {\n  const { data, isLoading, error, selectedTradeId, preferences, updatePreferences } =\n    useTradeAnalysis();\n  const [activeView, setActiveView] = useState<ViewType>(\n    (preferences.defaultView as ViewType) || 'summary'\n  );\n\n  const handleViewChange = (view: ViewType) => {\n    setActiveView(view);\n    updatePreferences({ defaultView: view });\n  };\n\n  const renderContent = () => {\n    switch (activeView) {\n      case 'summary':\n        return (\n          <>\n            <DataCard\n              title=\"Performance Summary\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.metrics}\n              emptyMessage=\"No performance data available for the selected filters.\"\n            >\n              <PerformanceSummary />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Time of Day\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}\n              emptyMessage=\"No time of day performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"timeOfDay\" title=\"Time of Day\" />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Day of Week\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}\n              emptyMessage=\"No day of week performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"dayOfWeek\" title=\"Day of Week\" />\n            </DataCard>\n          </>\n        );\n\n      case 'trades':\n        return (\n          <>\n            <DataCard\n              title=\"Trades\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.trades || data.trades.length === 0}\n              emptyMessage=\"No trades available for the selected filters.\"\n            >\n              <TradesTable />\n            </DataCard>\n\n            {selectedTradeId && <TradeDetail />}\n          </>\n        );\n\n      case 'symbols':\n        return (\n          <DataCard\n            title=\"Performance by Symbol\"\n            isLoading={isLoading}\n            hasError={!!error}\n            errorMessage={error || ''}\n            isEmpty={!data?.symbolPerformance || data.symbolPerformance.length === 0}\n            emptyMessage=\"No symbol performance data available for the selected filters.\"\n          >\n            <CategoryPerformanceChart category=\"symbol\" title=\"Symbol\" />\n          </DataCard>\n        );\n\n      case 'strategies':\n        return (\n          <DataCard\n            title=\"Performance by Strategy\"\n            isLoading={isLoading}\n            hasError={!!error}\n            errorMessage={error || ''}\n            isEmpty={!data?.strategyPerformance || data.strategyPerformance.length === 0}\n            emptyMessage=\"No strategy performance data available for the selected filters.\"\n          >\n            <CategoryPerformanceChart category=\"strategy\" title=\"Strategy\" />\n          </DataCard>\n        );\n\n      case 'timeframes':\n        return (\n          <>\n            <DataCard\n              title=\"Performance by Timeframe\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.timeframePerformance || data.timeframePerformance.length === 0}\n              emptyMessage=\"No timeframe performance data available for the selected filters.\"\n            >\n              <CategoryPerformanceChart category=\"timeframe\" title=\"Timeframe\" />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Session\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.sessionPerformance || data.sessionPerformance.length === 0}\n              emptyMessage=\"No session performance data available for the selected filters.\"\n            >\n              <CategoryPerformanceChart category=\"session\" title=\"Session\" />\n            </DataCard>\n          </>\n        );\n\n      case 'time':\n        return (\n          <>\n            <DataCard\n              title=\"Performance by Time of Day\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}\n              emptyMessage=\"No time of day performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"timeOfDay\" title=\"Time of Day\" />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Day of Week\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}\n              emptyMessage=\"No day of week performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"dayOfWeek\" title=\"Day of Week\" />\n            </DataCard>\n          </>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <PageContainer>\n      <PageHeader>\n        <Title>Trade Analysis</Title>\n      </PageHeader>\n\n      <FilterPanel />\n\n      <ViewTabs>\n        <ViewTab active={activeView === 'summary'} onClick={() => handleViewChange('summary')}>\n          Summary\n        </ViewTab>\n        <ViewTab active={activeView === 'trades'} onClick={() => handleViewChange('trades')}>\n          Trades\n        </ViewTab>\n        <ViewTab active={activeView === 'symbols'} onClick={() => handleViewChange('symbols')}>\n          Symbols\n        </ViewTab>\n        <ViewTab\n          active={activeView === 'strategies'}\n          onClick={() => handleViewChange('strategies')}\n        >\n          Strategies\n        </ViewTab>\n        <ViewTab\n          active={activeView === 'timeframes'}\n          onClick={() => handleViewChange('timeframes')}\n        >\n          Timeframes\n        </ViewTab>\n        <ViewTab active={activeView === 'time'} onClick={() => handleViewChange('time')}>\n          Time Analysis\n        </ViewTab>\n      </ViewTabs>\n\n      {renderContent()}\n    </PageContainer>\n  );\n};\n\nconst TradeAnalysis: React.FC = () => {\n  return (\n    <TradeAnalysisProvider>\n      <TradeAnalysisContent />\n    </TradeAnalysisProvider>\n  );\n};\n\nexport default TradeAnalysis;\n"], "names": ["sizeStyles", "small", "css", "medium", "large", "custom", "props", "customHeight", "customWidth", "variantStyles", "default", "theme", "colors", "background", "borderRadius", "md", "card", "surface", "shadows", "sm", "text", "list", "spacing", "spin", "keyframes", "Container", "div", "withConfig", "displayName", "componentId", "size", "variant", "Spinner", "primary", "Text", "textSecondary", "fontSizes", "LoadingPlaceholder", "height", "width", "showSpinner", "className", "jsxs", "jsx", "xxs", "xs", "getVariantStyles", "bgColor", "textColor", "borderColor", "secondary", "success", "warning", "error", "info", "StyledTag", "span", "pill", "fontWeights", "clickable", "transitions", "fast", "RemoveButton", "button", "sizeMap", "Tag", "children", "removable", "onRemove", "onClick", "handleRemoveClick", "e", "stopPropagation", "Title", "h3", "textPrimary", "semibold", "lg", "xl", "Description", "p", "compact", "IconContainer", "ActionContainer", "ChildrenC<PERSON>r", "EmptyState", "title", "description", "icon", "actionText", "onAction", "<PERSON><PERSON>", "HeaderActions", "DataCard", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "showRetry", "onRetry", "isEmpty", "emptyMessage", "emptyActionText", "onEmptyAction", "actionButton", "cardProps", "headerActions", "content", "undefined", "Card", "useLocalStorage", "key", "initialValue", "readValue", "window", "item", "localStorage", "getItem", "JSON", "parse", "warn", "storedValue", "setStoredValue", "useState", "setValue", "value", "valueToStore", "Function", "setItem", "stringify", "useEffect", "handleStorageChange", "event", "newValue", "addEventListener", "removeEventListener", "fetchTradeAnalysisData", "filters", "Promise", "resolve", "setTimeout", "Math", "random", "Error", "generateMockData", "trades", "generateMockTrades", "metrics", "calculateMetrics", "symbolPerformance", "calculateCategoryPerformance", "strategyPerformance", "timeframePerformance", "sessionPerformance", "timeOfDayPerformance", "calculateTimePerformance", "dayOfWeekPerformance", "date<PERSON><PERSON><PERSON>", "startDate", "Date", "endDate", "daysDiff", "floor", "getTime", "numTrades", "max", "symbols", "strategies", "timeframes", "sessions", "tags", "i", "tradeDate", "entryHour", "entryMinute", "entryTime", "setHours", "durationMinutes", "exitTime", "symbol", "length", "direction", "entryPrice", "isWin", "isBreakeven", "exitPrice", "profitLoss", "profitLossPercent", "status", "winPercent", "lossPercent", "quantity", "round", "timeframe", "session", "strategy", "numTags", "tradeTags", "j", "tag", "includes", "push", "trade", "id", "toISOString", "notes", "sort", "a", "b", "winningTrades", "filter", "losingTrades", "breakeven", "totalProfitLoss", "reduce", "sum", "totalWinAmount", "totalLossAmount", "abs", "averageWin", "averageLoss", "largestWin", "map", "largestLoss", "min", "durations", "averageDuration", "duration", "profitFactor", "Infinity", "winRate", "expectancy", "totalTrades", "category", "categories", "Map", "for<PERSON>ach", "categoryValue", "has", "set", "get", "performance", "categoryTrades", "averageProfitLoss", "timeType", "timeSlots", "timePerformance", "timeSlot", "slotIndex", "hour", "getHours", "minute", "getMinutes", "timeValue", "dayOfWeek", "getDay", "slot", "slotTrades", "t", "tEntryTime", "tHour", "tMinute", "tTimeValue", "winningSlotTrades", "getDefaultDateRange", "today", "setMonth", "getMonth", "split", "initialState", "data", "preferences", "defaultDateRange", "defaultView", "chartTypes", "distribution", "timeAnalysis", "tableColumns", "favoriteStrategies", "favoriteTags", "selectedTradeId", "tradeAnalysisReducer", "state", "action", "type", "payload", "TradeAnalysisContext", "createContext", "TradeAnalysisProvider", "savedPreferences", "mergedInitialState", "dispatch", "useReducer", "fetchData", "useCallback", "message", "updateFilters", "updatePreferences", "selectTrade", "tradeId", "resetFilters", "useTradeAnalysis", "context", "useContext", "styled", "<PERSON>lter<PERSON><PERSON>", "FilterSection", "Filter<PERSON>abel", "DateRangeContainer", "DateInput", "input", "border", "TagsContainer", "FilterTag", "selected", "ButtonContainer", "FilterPanel", "localFilters", "setLocalFilters", "availableSymbols", "Set", "availableStrategies", "availableTags", "flatMap", "directionOptions", "statusOptions", "timeframeOptions", "sessionOptions", "handleDateChange", "field", "prev", "handleToggleFilter", "currentV<PERSON>ues", "newValues", "v", "applyFilters", "handleResetFilters", "isSelected", "values", "target", "MetricCard", "MetricLabel", "MetricValue", "positive", "negative", "profit", "loss", "MetricChange", "PerformanceSummary", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatPercent", "toFixed", "Table", "table", "TableHead", "thead", "TableBody", "tbody", "TableRow", "tr", "TableHeaderCell", "th", "active", "sortable", "TableCell", "td", "SortIcon", "DirectionBadge", "Badge", "StatusBadge", "ProfitLoss", "regular", "TradesTable", "sortField", "setSortField", "sortDirection", "setSortDirection", "handleSort", "sortedTrades", "useMemo", "comparison", "localeCompare", "formatDate", "dateString", "date", "toLocaleDateString", "toLocaleTimeString", "getDirectionVariant", "getStatusVariant", "handleRowClick", "index", "BarC<PERSON>r", "Bar", "CategoryPerformanceChart", "performanceData", "sortedData", "maxProfitLoss", "ChartContainer", "TimeSlot", "TimeSlotHeader", "TimeSlotLabel", "TimeSlotMetrics", "BarLabel", "textInverse", "TimePerformanceChart", "DetailGrid", "DetailSection", "DetailLabel", "DetailValue", "Notes", "TradeDetail", "selected<PERSON><PERSON>", "find", "calculateDuration", "entry", "durationMs", "minutes", "hours", "remainingMinutes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "h1", "xxl", "ViewTabs", "ViewTab", "TradeAnalysisContent", "activeView", "setActiveView", "handleViewChange", "view", "renderContent", "Fragment", "TradeAnalysis"], "mappings": "4MA6BA,MAAMA,GAAa,CACjBC,MAAOC,EAEN,CAAA,eAAA,CAAA,EACDC,OAAQD,EAEP,CAAA,eAAA,CAAA,EACDE,MAAOF,EAEN,CAAA,eAAA,CAAA,EACDG,OAASC,GAA0DJ,EAAG,CAAA,UAAA,UAAA,GAAA,EAC1DI,EAAMC,aACPD,EAAME,aAAe,MAAM,CAExC,EAGMC,GAAgB,CACpBC,QAASR,EAAG,CAAA,oBAAA,kBAAA,GAAA,EACU,CAAC,CAAES,MAAAA,CAAAA,IAAYA,EAAMC,OAAOC,WAC/B,CAAC,CAAEF,MAAAA,CAAAA,IAAYA,EAAMG,aAAaC,EAAE,EAEvDC,KAAMd,EAAG,CAAA,oBAAA,kBAAA,eAAA,GAAA,EACa,CAAC,CAAES,MAAAA,CAAAA,IAAYA,EAAMC,OAAOK,QAC/B,CAAC,CAAEN,MAAAA,CAAAA,IAAYA,EAAMG,aAAaC,GACrC,CAAC,CAAEJ,MAAAA,CAAAA,IAAYA,EAAMO,QAAQC,EAAE,EAE/CC,KAAMlB,EAIL,CAAA,uEAAA,CAAA,EACDmB,KAAMnB,EAAG,CAAA,oBAAA,kBAAA,kBAAA,GAAA,EACa,CAAC,CAAES,MAAAA,CAAAA,IAAYA,EAAMC,OAAOC,WAC/B,CAAC,CAAEF,MAAAA,CAAAA,IAAYA,EAAMG,aAAaK,GAClC,CAAC,CAAER,MAAAA,CAAAA,IAAYA,EAAMW,QAAQH,EAAE,CAEpD,EAGMI,GAAOC,GAOZ,CAAA,4DAAA,CAAA,EAEKC,GAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gFAAA,IAAA,EAAA,EAYxB,CAAC,CAAEC,KAAAA,EAAMvB,aAAAA,EAAcC,YAAAA,CAAY,IAC/BsB,IAAS,SACJ9B,GAAWK,OAAO,CAAEE,aAAAA,EAAcC,YAAAA,CAAAA,CAAa,EAEjDR,GAAW8B,CAAI,EAItB,CAAC,CAAEC,QAAAA,CAAQ,IAAMtB,GAAcsB,CAAO,CAAC,EAGrCC,GAAiBN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2CAAA,yBAAA,gCAAA,qCAAA,GAAA,EAGJ,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,WACxB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,OAAOqB,QAEvCV,GACI,CAAC,CAAEZ,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,EAAE,EAG5Ce,GAAcR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,OAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,cAAA,GAAA,EACZ,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,cACxB,CAAC,CAAExB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,EAAE,EAQnCkB,GAAwDA,CAAC,CACpEN,QAAAA,EAAU,UACVD,KAAAA,EAAO,SACPQ,OAAAA,EAAS,QACTC,MAAAA,EACAnB,KAAAA,EAAO,aACPoB,YAAAA,EAAc,GACdC,UAAAA,CACF,IAEIC,OAACjB,IACC,QAAAM,EACA,KAAAD,EACA,aAAcQ,EACd,YAAaC,EACb,UAAAE,EAECD,SAAAA,CAAAA,SAAgBR,GAAU,EAAA,EAC1BZ,GAASuB,EAAAA,IAAAT,GAAA,CAAMd,SAAKA,CAAA,CAAA,CACvB,CAAA,CAAA,EC/GEpB,GAAa,CACjBC,MAAOC,EAAG,CAAA,WAAA,cAAA,GAAA,EACG,CAAC,CAAES,MAAAA,CAAAA,IAAY,GAAGA,EAAMW,QAAQsB,OAAOjC,EAAMW,QAAQuB,KACnD,CAAC,CAAElC,MAAAA,CAAAA,IAAYA,EAAMyB,UAAUS,EAAE,EAEhD1C,OAAQD,EAAG,CAAA,WAAA,cAAA,GAAA,EACE,CAAC,CAAES,MAAAA,CAAAA,IAAY,GAAGA,EAAMW,QAAQuB,MAAMlC,EAAMW,QAAQH,KAClD,CAAC,CAAER,MAAAA,CAAAA,IAAYA,EAAMyB,UAAUjB,EAAE,EAEhDf,MAAOF,EAAG,CAAA,WAAA,cAAA,GAAA,EACG,CAAC,CAAES,MAAAA,CAAAA,IAAY,GAAGA,EAAMW,QAAQH,MAAMR,EAAMW,QAAQP,KAClD,CAAC,CAAEJ,MAAAA,CAAAA,IAAYA,EAAMyB,UAAUrB,EAAE,CAElD,EAGM+B,GAAoBf,GACjB7B,UACH,CAAC,CAAES,MAAAA,CAAAA,IAAY,CAEf,IAAIoC,EAASC,EAAWC,EAExB,OAAQlB,EAAO,CACb,IAAK,UACOgB,EAAA,GAAGpC,EAAMC,OAAOqB,YAC1Be,EAAYrC,EAAMC,OAAOqB,QACXgB,EAAA,GAAGtC,EAAMC,OAAOqB,YAC9B,MACF,IAAK,YACOc,EAAA,GAAGpC,EAAMC,OAAOsC,cAC1BF,EAAYrC,EAAMC,OAAOsC,UACXD,EAAA,GAAGtC,EAAMC,OAAOsC,cAC9B,MACF,IAAK,UACOH,EAAA,GAAGpC,EAAMC,OAAOuC,YAC1BH,EAAYrC,EAAMC,OAAOuC,QACXF,EAAA,GAAGtC,EAAMC,OAAOuC,YAC9B,MACF,IAAK,UACOJ,EAAA,GAAGpC,EAAMC,OAAOwC,YAC1BJ,EAAYrC,EAAMC,OAAOwC,QACXH,EAAA,GAAGtC,EAAMC,OAAOwC,YAC9B,MACF,IAAK,QACOL,EAAA,GAAGpC,EAAMC,OAAOyC,UAC1BL,EAAYrC,EAAMC,OAAOyC,MACXJ,EAAA,GAAGtC,EAAMC,OAAOyC,UAC9B,MACF,IAAK,OACON,EAAA,GAAGpC,EAAMC,OAAO0C,SAC1BN,EAAYrC,EAAMC,OAAO0C,KACXL,EAAA,GAAGtC,EAAMC,OAAO0C,SAC9B,MACF,QACYP,EAAA,GAAGpC,EAAMC,OAAOuB,kBAC1Ba,EAAYrC,EAAMC,OAAOuB,cACXc,EAAA,GAAGtC,EAAMC,OAAOuB,iBAClC,CAEO,MAAA;AAAA,4BACeY;AAAAA,iBACXC;AAAAA,4BACWC;AAAAA,OAAAA,CAEvB,EAICM,GAAmBC,EAAAA,KAAI7B,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wDAAA,gBAAA,IAAA,IAAA,IAAA,EAAA,EAOV,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMG,aAAa2C,KACpC,CAAC,CAAE9C,MAAAA,CAAM,IAAMA,EAAM+C,YAAYvD,OAG9C,CAAC,CAAE2B,KAAAA,CAAK,IAAM9B,GAAW8B,CAAI,EAG7B,CAAC,CAAEC,QAAAA,CAAQ,IAAMe,GAAiBf,CAAO,EAGzC,CAAC,CAAE4B,UAAAA,CAAU,IAAMA,GAAazD,wFAEV,CAAC,CAAES,MAAAA,CAAM,IAAMA,EAAMiD,YAAYC,IAAI,CAS5D,EAGGC,GAAsBC,EAAAA,OAAMpC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,kJAAA,cAAA,sBAAA,EASjB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GAI1C,CAAC,CAAEf,KAAAA,EAAMnB,MAAAA,CAAM,IAAM,CACrB,MAAMqD,EAAU,CACd/D,MAAO,OACPE,OAAQ,OACRC,MAAO,MAAA,EAGF,MAAA;AAAA,eACI4D,EAAQlC,CAAI;AAAA,gBACXkC,EAAQlC,CAAI;AAAA,mBACTnB,EAAMyB,UAAUS;AAAAA,KAEjC,CAAC,EAYUoB,GAA0BA,CAAC,CACtCC,SAAAA,EACAnC,QAAAA,EAAU,UACVD,KAAAA,EAAO,SACPqC,UAAAA,EAAY,GACZC,SAAAA,EACA3B,UAAAA,EACA4B,QAAAA,CACF,IAAM,CACEC,MAAAA,EAAqBC,GAAwB,CACjDA,EAAEC,gBAAgB,EACPJ,GAAA,MAAAA,GAAA,EAIX,OAAA1B,OAACa,IACC,QAAAxB,EACA,KAAAD,EACA,UAAW,CAAC,CAACuC,EACb,UAAA5B,EACA,QAAA4B,EAECH,SAAAA,CAAAA,EACAC,GACExB,EAAAA,IAAAmB,GAAA,CAAa,KAAAhC,EAAY,QAASwC,EAAkB,SAErD,IAAA,CAEJ,CAAA,CAAA,CAEJ,ECjKMG,GAAeC,EAAAA,GAAE/C,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,YAAA,gBAAA,IAAA,EAAA,EACP,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,GAClC,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMC,OAAO+D,YACtB,CAAC,CAAEhE,MAAAA,CAAM,IAAMA,EAAM+C,YAAYkB,SAE9C,CAAC,CAAE9C,KAAAA,EAAMnB,MAAAA,CAAM,IAAM,CACrB,MAAMqD,EAAU,CACd/D,MAAOU,EAAMyB,UAAUrB,GACvBZ,OAAQQ,EAAMyB,UAAUyC,GACxBzE,MAAOO,EAAMyB,UAAU0C,EAAAA,EAGzB,OAAO5E,EAAG,CAAA,aAAA,GAAA,EACK8D,EAAQlC,CAAI,CAAC,CAE9B,CAAC,EAGGiD,GAAqBC,EAAAA,EAACrD,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,YAAA,IAAA,EAAA,EACZ,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,GAClC,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,cAEnC,CAAC,CAAEL,KAAAA,EAAMnB,MAAAA,CAAM,IAAM,CACrB,MAAMqD,EAAU,CACd/D,MAAOU,EAAMyB,UAAUjB,GACvBhB,OAAQQ,EAAMyB,UAAUrB,GACxBX,MAAOO,EAAMyB,UAAUyC,EAAAA,EAGzB,OAAO3E,EAAG,CAAA,aAAA,GAAA,EACK8D,EAAQlC,CAAI,CAAC,CAE9B,CAAC,EAMGrB,GAAgB,CACpBC,QAASR,EAER,CAAA,+BAAA,CAAA,EACD+E,QAAS/E,EAIR,CAAA,sEAAA,CAAA,EACDc,KAAMd,EAAG,CAAA,oBAAA,kBAAA,eAAA,GAAA,EACa,CAAC,CAAES,MAAAA,CAAAA,IAAYA,EAAMC,OAAOK,QAC/B,CAAC,CAAEN,MAAAA,CAAAA,IAAYA,EAAMG,aAAaC,GACrC,CAAC,CAAEJ,MAAAA,CAAAA,IAAYA,EAAMO,QAAQC,EAAE,CAEjD,EAEMM,GAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6GAAA,IAAA,EAAA,EAYxB,CAAC,CAAEE,QAAAA,CAAQ,IAAMtB,GAAcsB,CAAO,EAGtC,CAAC,CAAED,KAAAA,EAAMnB,MAAAA,CAAM,IAAM,CACrB,OAAQmB,EAAI,CACV,IAAK,QACH,OAAO5B,EACMS,CAAAA,WAAAA,oBAAAA,EAAAA,EAAMW,QAAQP,EAAE,EAG/B,IAAK,QACH,OAAOb,EACMS,CAAAA,WAAAA,oBAAAA,EAAAA,EAAMW,QAAQwD,EAAE,EAG/B,QACE,OAAO5E,EACMS,CAAAA,WAAAA,oBAAAA,EAAAA,EAAMW,QAAQuD,EAAE,CAGjC,CACF,CAAC,EAGGK,GAAuBxD,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iBAAA,IAAA,EAAA,EACb,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,GAE5C,CAAC,CAAEe,KAAAA,EAAMnB,MAAAA,CAAM,IAAM,CACrB,MAAMqD,EAAU,CACd/D,MAAO,OACPE,OAAQ,OACRC,MAAO,MAAA,EAGT,OAAOF,yDACQ8D,EAAQlC,CAAI,EAGdkC,EAAQlC,CAAI,EACXkC,EAAQlC,CAAI,EACbnB,EAAMC,OAAOuB,aAAa,CAGzC,CAAC,EAKGgD,GAAyBzD,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EAClB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAGzCqE,GAA2B1D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,cAAA,EACpB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,EAAE,EASlCQ,GAAwCA,CAAC,CACpDC,MAAAA,EACAC,YAAAA,EACAC,KAAAA,EACAC,WAAAA,EACAC,SAAAA,EACA3D,QAAAA,EAAU,UACVD,KAAAA,EAAO,SACPW,UAAAA,EACAyB,SAAAA,CACF,IAEKxB,EAAAA,KAAAjB,GAAA,CAAU,QAAAM,EAAkB,KAAAD,EAAY,UAAAW,EACtC+C,SAAAA,CAAQA,GAAA7C,EAAA,IAACuC,GAAc,CAAA,KAAApD,EAAa0D,SAAKA,EAAA,EAEzCF,GAAS3C,EAAA,IAAC8B,GAAM,CAAA,KAAA3C,EAAawD,SAAMA,EAAA,EACnCC,GAAe5C,EAAA,IAACoC,GAAY,CAAA,KAAAjD,EAAayD,SAAYA,EAAA,EAErDE,GAAcC,GACb/C,EAAA,IAACwC,GACC,CAAA,SAAAxC,MAACgD,IAAO,QAAQ,UAAU,KAAM7D,IAAS,QAAU,QAAU,SAAU,QAAS4D,EAC7ED,UACH,CAAA,EACF,EAGDvB,GAAavB,EAAA,IAAAyC,GAAA,CAAmBlB,SAAAA,CAAS,CAAA,CAC5C,CAAA,CAAA,ECpJE0B,GAAuBlE,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EAEvB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,EAAE,EAQ3B0E,EAAoCA,CAAC,CAChDP,MAAAA,EACApB,SAAAA,EACA4B,UAAAA,EAAY,GACZC,SAAAA,EAAW,GACXC,aAAAA,EAAe,uCACfC,UAAAA,EAAY,GACZC,QAAAA,EACAC,QAAAA,EAAU,GACVC,aAAAA,EAAe,oBACfC,gBAAAA,EACAC,cAAAA,EACAC,aAAAA,EACA9D,UAAAA,EACA,GAAG+D,CACL,IAAM,CAEEC,MAAAA,EAAiB9D,EAAAA,IAAAiD,GAAA,CAAeW,SAAaA,CAAA,CAAA,EAG/CG,IAAAA,EAEJ,OAAIZ,EACFY,EAAW/D,EAAAA,IAAAN,GAAA,CAAmB,QAAQ,OAAO,KAAK,iBAAoB,CAAA,EAC7D0D,EACTW,EACG/D,EAAAA,IAAA0C,GAAA,CACC,MAAM,QACN,YAAaW,EACb,QAAQ,UACR,WAAYC,EAAY,QAAUU,OAClC,SAAUV,EAAYC,EAAUS,MAEnC,CAAA,EACQR,EAEPO,EAAA/D,EAAA,IAAC0C,GACC,CAAA,MAAM,UACN,YAAae,EACb,QAAQ,UACR,WAAYC,EACZ,SAAUC,CAEb,CAAA,EAESpC,EAAAA,EAIVvB,MAACiE,IAAK,MAAAtB,EAAc,QAASmB,EAAe,UAAAhE,EAA0B+D,GAAAA,EACnEE,SACHA,CAAA,CAAA,CAEJ,EC1FgBG,SAAAA,GACdC,EACAC,EAC2C,CAG3C,MAAMC,EAAYA,IAAS,CAErB,GAAA,OAAOC,OAAW,IACbF,OAAAA,EAGL,GAAA,CACF,MAAMG,EAAOD,OAAOE,aAAaC,QAAQN,CAAG,EAC5C,OAAOI,EAAQG,KAAKC,MAAMJ,CAAI,EAAUH,QACjC1D,GACCkE,eAAAA,KAAK,mCAAmCT,MAASzD,CAAK,EACvD0D,CACT,CAAA,EAKI,CAACS,EAAaC,CAAc,EAAIC,WAAYV,CAAS,EAIrDW,EAAYC,GAA+B,CAC3C,GAAA,CAEF,MAAMC,EACJD,aAAiBE,SAAWF,EAAMJ,CAAW,EAAII,EAGnDH,EAAeI,CAAY,EAGvB,OAAOZ,OAAW,KACpBA,OAAOE,aAAaY,QAAQjB,EAAKO,KAAKW,UAAUH,CAAY,CAAC,QAExDxE,GACCkE,QAAAA,KAAK,mCAAmCT,MAASzD,CAAK,CAChE,CAAA,EAIF4E,OAAAA,EAAAA,UAAU,IAAM,CACRC,MAAAA,EAAuBC,GAAwB,CAC/CA,EAAMrB,MAAQA,GAAOqB,EAAMC,UAC7BX,EAAeJ,KAAKC,MAAMa,EAAMC,QAAQ,CAAM,CAChD,EAIKC,cAAAA,iBAAiB,UAAWH,CAAmB,EAG/C,IAAMjB,OAAOqB,oBAAoB,UAAWJ,CAAmB,CAAA,EACrE,CAACpB,CAAG,CAAC,EAED,CAACU,EAAaG,CAAQ,CAC/B,CClDaY,MAAAA,GAAyB,MACpCC,GAC+B,CAM/B,GAJA,MAAM,IAAIC,QAAQC,GAAWC,WAAWD,EAAS,GAAG,CAAC,EAGjCE,KAAKC,OAAAA,EAAW,IAE5B,MAAA,IAAIC,MAAM,qCAAqC,EAIvD,OAAOC,GAAiBP,CAAO,CACjC,EAKMO,GAAoBP,GAA6C,CAE/DQ,MAAAA,EAASC,GAAmBT,CAAO,EAGnCU,EAAUC,GAAiBH,CAAM,EAGjCI,EAAoBC,EAA6BL,EAAQ,QAAQ,EACjEM,EAAsBD,EAA6BL,EAAQ,UAAU,EACrEO,EAAuBF,EAA6BL,EAAQ,WAAW,EACvEQ,EAAqBH,EAA6BL,EAAQ,SAAS,EAGnES,EAAuBC,GAAyBV,EAAQ,WAAW,EACnEW,EAAuBD,GAAyBV,EAAQ,WAAW,EAElE,MAAA,CACLA,OAAAA,EACAE,QAAAA,EACAE,kBAAAA,EACAE,oBAAAA,EACAC,qBAAAA,EACAC,mBAAAA,EACAC,qBAAAA,EACAE,qBAAAA,CAAAA,CAEJ,EAKMV,GAAsBT,GAAmC,CACvD,KAAA,CAAEoB,UAAAA,CAAcpB,EAAAA,EAChBqB,EAAY,IAAIC,KAAKF,EAAUC,SAAS,EACxCE,EAAU,IAAID,KAAKF,EAAUG,OAAO,EAGpCC,EAAWpB,KAAKqB,OAAOF,EAAQG,QAAQ,EAAIL,EAAUK,QAAAA,IAAc,IAAO,GAAK,GAAK,GAAG,EAGvFC,EAAYvB,KAAKwB,IAAI,EAAGJ,CAAQ,GAAK,EAAIpB,KAAKqB,MAAMrB,KAAKC,OAAO,EAAI,CAAC,GAErEG,EAAkB,CAAA,EAClBqB,EAAU,CAAC,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,MAAM,EAC1EC,EAAa,CAAC,WAAY,WAAY,kBAAmB,aAAc,cAAe,oBAAoB,EAC1GC,EAA+B,CAAC,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,OAAO,EAC7EC,EAA6B,CAAC,aAAc,UAAW,aAAa,EACpEC,EAAO,CAAC,cAAe,YAAa,WAAY,OAAQ,YAAa,WAAY,WAAY,YAAY,EAE/G,QAASC,EAAI,EAAGA,EAAIP,EAAWO,IAAK,CAElC,MAAMC,EAAY,IAAIb,KAAKD,EAAUK,QAAYtB,EAAAA,KAAKC,OAAO,GAAKkB,EAAQG,QAAYL,EAAAA,EAAUK,UAAU,EAGpGU,EAAY,EAAIhC,KAAKqB,MAAMrB,KAAKC,OAAAA,EAAW,CAAC,EAC5CgC,EAAcjC,KAAKqB,MAAMrB,KAAKC,OAAAA,EAAW,EAAE,EAC3CiC,EAAY,IAAIhB,KAAKa,CAAS,EACpCG,EAAUC,SAASH,EAAWC,EAAa,EAAG,CAAC,EAG/C,MAAMG,EAAkB,EAAIpC,KAAKqB,MAAMrB,KAAKC,OAAAA,EAAW,GAAG,EACpDoC,EAAW,IAAInB,KAAKgB,EAAUZ,UAAYc,EAAkB,GAAK,GAAI,EAGrEE,EAASb,EAAQzB,KAAKqB,MAAMrB,KAAKC,SAAWwB,EAAQc,MAAM,CAAC,EAC3DC,EAA4BxC,KAAKC,OAAO,EAAI,GAAM,OAAS,QAC3DwC,EAAa,IAAMzC,KAAKC,OAAAA,EAAW,IAGnCyC,EAAQ1C,KAAKC,OAAAA,EAAW,GACxB0C,EAAc,CAACD,GAAS1C,KAAKC,SAAW,GAG1C2C,IAAAA,EAAWC,EAAYC,EAAmBC,EAE9C,GAAIJ,EACFC,EAAYH,GAAczC,KAAKC,OAAO,EAAI,GAAM,IACvC8C,EAAA,oBACAL,EAAO,CAChB,MAAMM,EAAa,GAAMhD,KAAKC,OAAAA,EAAW,IAC7BuC,EAAAA,IAAc,OACtBC,GAAc,EAAIO,EAAa,KAC/BP,GAAc,EAAIO,EAAa,KAC1BD,EAAA,UACJ,CACL,MAAME,EAAc,GAAMjD,KAAKC,OAAAA,EAAW,IAC9BuC,EAAAA,IAAc,OACtBC,GAAc,EAAIQ,EAAc,KAChCR,GAAc,EAAIQ,EAAc,KAC3BF,EAAA,OAIX,MAAMG,GAAW,GAAKlD,KAAKqB,MAAMrB,KAAKC,OAAAA,EAAW,EAAE,EAE/CuC,IAAc,QAChBK,GAAcD,EAAYH,GAAcS,GAClBN,GAAAA,EAAYH,EAAc,GAAK,MAErDI,GAAcJ,EAAaG,GAAaM,GAClBT,GAAAA,EAAaG,EAAa,GAAK,KAIvDC,EAAa7C,KAAKmD,MAAMN,EAAa,GAAG,EAAI,IAC5CC,EAAoB9C,KAAKmD,MAAML,EAAoB,GAAG,EAAI,IAGpDM,MAAAA,GAAYzB,EAAW3B,KAAKqB,MAAMrB,KAAKC,SAAW0B,EAAWY,MAAM,CAAC,EACpEc,GAAUzB,EAAS5B,KAAKqB,MAAMrB,KAAKC,SAAW2B,EAASW,MAAM,CAAC,EAC9De,GAAW5B,EAAW1B,KAAKqB,MAAMrB,KAAKC,SAAWyB,EAAWa,MAAM,CAAC,EAGnEgB,GAAUvD,KAAKqB,MAAMrB,KAAKC,OAAAA,EAAW,CAAC,EACtCuD,GAAsB,CAAA,EAC5B,QAASC,EAAI,EAAGA,EAAIF,GAASE,IAAK,CAC1BC,MAAAA,GAAM7B,EAAK7B,KAAKqB,MAAMrB,KAAKC,SAAW4B,EAAKU,MAAM,CAAC,EACnDiB,GAAUG,SAASD,EAAG,GACzBF,GAAUI,KAAKF,EAAG,EAKtB,MAAMG,GAAe,CACnBC,GAAI,SAAShC,IACbQ,OAAAA,EACAE,UAAAA,EACAC,WAAAA,EACAG,UAAAA,EACAM,SAAAA,GACAhB,UAAWA,EAAU6B,YAAY,EACjC1B,SAAUA,EAAS0B,YAAY,EAC/BhB,OAAAA,EACAF,WAAAA,EACAC,kBAAAA,EACAM,UAAAA,GACAC,QAAAA,GACAC,SAAAA,GACAzB,KAAM2B,GACNQ,MAAOhE,KAAKC,SAAW,GAAM,mBAAmBqC,KAAUE,UAAoBzE,MAAAA,EAGhFqC,EAAOwD,KAAKC,EAAK,EAInB,OAAOzD,EAAO6D,KAAK,CAACC,EAAGC,IAAM,IAAIjD,KAAKiD,EAAEjC,SAAS,EAAEZ,QAAAA,EAAY,IAAIJ,KAAKgD,EAAEhC,SAAS,EAAEZ,SAAS,CAChG,EAKMf,GAAoBH,GAAwC,CAChE,MAAMgE,EAAgBhE,EAAOiE,OAAgBR,GAAAA,EAAMd,SAAW,KAAK,EAC7DuB,EAAelE,EAAOiE,OAAgBR,GAAAA,EAAMd,SAAW,MAAM,EAC7DwB,EAAYnE,EAAOiE,OAAgBR,GAAAA,EAAMd,SAAW,WAAW,EAE/DyB,EAAkBpE,EAAOqE,OAAO,CAACC,EAAKb,IAAUa,EAAMb,EAAMhB,WAAY,CAAC,EACzE8B,EAAiBP,EAAcK,OAAO,CAACC,EAAKb,IAAUa,EAAMb,EAAMhB,WAAY,CAAC,EAC/E+B,EAAkB5E,KAAK6E,IAAIP,EAAaG,OAAO,CAACC,EAAKb,IAAUa,EAAMb,EAAMhB,WAAY,CAAC,CAAC,EAEzFiC,EAAaV,EAAc7B,OAAS,EACtCoC,EAAiBP,EAAc7B,OAC/B,EAEEwC,EAAcT,EAAa/B,OAAS,EACtCqC,EAAkBN,EAAa/B,OAC/B,EAEEyC,EAAaZ,EAAc7B,OAAS,EACtCvC,KAAKwB,IAAI,GAAG4C,EAAca,IAAIpB,GAASA,EAAMhB,UAAU,CAAC,EACxD,EAEEqC,EAAcZ,EAAa/B,OAAS,EACtCvC,KAAKmF,IAAI,GAAGb,EAAaW,IAAIpB,GAASA,EAAMhB,UAAU,CAAC,EACvD,EAGEuC,EAAYhF,EAAO6E,IAAapB,GAAA,CACpC,MAAM3B,EAAY,IAAIhB,KAAK2C,EAAM3B,SAAS,EAAEZ,UAEpCe,OADS,IAAInB,KAAK2C,EAAMxB,QAAQ,EAAEf,UACvBY,IAAc,IAAO,GAAA,CACzC,EAEKmD,EAAkBD,EAAU7C,OAAS,EACvC6C,EAAUX,OAAO,CAACC,EAAKY,IAAaZ,EAAMY,EAAU,CAAC,EAAIF,EAAU7C,OACnE,EAGEgD,EAAeX,EAAkB,EACnCD,EAAiBC,EACjBD,EAAiB,EAAIa,IAAW,EAE9BC,EAAUrF,EAAOmC,OAAS,EAC5B6B,EAAc7B,OAASnC,EAAOmC,OAC9B,EAEEmD,EAAcD,EAAUX,GAAgB,EAAIW,GAAWV,EAEtD,MAAA,CACLY,YAAavF,EAAOmC,OACpB6B,cAAeA,EAAc7B,OAC7B+B,aAAcA,EAAa/B,OAC3BgC,UAAWA,EAAUhC,OACrBkD,QAASzF,KAAKmD,MAAMsC,EAAU,GAAK,EAAI,IACvCX,WAAY9E,KAAKmD,MAAM2B,EAAa,GAAG,EAAI,IAC3CC,YAAa/E,KAAKmD,MAAM4B,EAAc,GAAG,EAAI,IAC7CQ,aAAcvF,KAAKmD,MAAMoC,EAAe,GAAG,EAAI,IAC/Cf,gBAAiBxE,KAAKmD,MAAMqB,EAAkB,GAAG,EAAI,IACrDQ,WAAYhF,KAAKmD,MAAM6B,EAAa,GAAG,EAAI,IAC3CE,YAAalF,KAAKmD,MAAM+B,EAAc,GAAG,EAAI,IAC7CG,gBAAiBrF,KAAKmD,MAAMkC,EAAkB,GAAG,EAAI,IACrDK,WAAY1F,KAAKmD,MAAMuC,EAAa,GAAG,EAAI,GAAA,CAE/C,EAKMjF,EAA+BA,CACnCL,EACAwF,IAC0B,CAEpBC,MAAAA,MAAiBC,IAEvB1F,EAAO2F,QAAiBlC,GAAA,CAChBmC,MAAAA,EAAgBnC,EAAM+B,CAAQ,EAC/BC,EAAWI,IAAID,CAAa,GACpBE,EAAAA,IAAIF,EAAe,CAAA,CAAE,EAElCH,EAAWM,IAAIH,CAAa,EAAGpC,KAAKC,CAAK,CAAA,CAC1C,EAGD,MAAMuC,EAAqC,CAAA,EAEhCL,OAAAA,EAAAA,QAAQ,CAACM,EAAgBL,IAAkB,CACpD,MAAM5B,EAAgBiC,EAAehC,OAAgBR,GAAAA,EAAMd,SAAW,KAAK,EACrEyB,EAAkB6B,EAAe5B,OAAO,CAACC,EAAKb,IAAUa,EAAMb,EAAMhB,WAAY,CAAC,EACjF4C,EAAUY,EAAe9D,OAAS,EACpC6B,EAAc7B,OAAS8D,EAAe9D,OACtC,EACE+D,EAAoBD,EAAe9D,OAAS,EAC9CiC,EAAkB6B,EAAe9D,OACjC,EAEJ6D,EAAYxC,KAAK,CACfgC,SAAAA,EACA5G,MAAOgH,EACP5F,OAAQiG,EAAe9D,OACvBkD,QAASzF,KAAKmD,MAAMsC,EAAU,GAAK,EAAI,IACvC5C,WAAY7C,KAAKmD,MAAMqB,EAAkB,GAAG,EAAI,IAChD8B,kBAAmBtG,KAAKmD,MAAMmD,EAAoB,GAAG,EAAI,GAAA,CAC1D,CAAA,CACF,EAGMF,EAAYnC,KAAK,CAACC,EAAGC,IAAMA,EAAEtB,WAAaqB,EAAErB,UAAU,CAC/D,EAKM/B,GAA2BA,CAC/BV,EACAmG,IACsB,CAElBC,IAAAA,EAEAD,IAAa,YACfC,EAAY,CACV,aAAc,cAAe,cAC7B,cAAe,cAAe,cAAe,aAAa,EAG5DA,EAAY,CAAC,SAAU,UAAW,YAAa,WAAY,QAAQ,EAI/DC,MAAAA,EAAqCD,EAAUvB,IAAiByB,IAAA,CACpEA,SAAAA,EACAtG,OAAQ,EACRqF,QAAS,EACT5C,WAAY,CACZ,EAAA,EAEFzC,OAAAA,EAAO2F,QAAiBlC,GAAA,CACtB,MAAM3B,EAAY,IAAIhB,KAAK2C,EAAM3B,SAAS,EACtCyE,IAAAA,EAEJ,GAAIJ,IAAa,YAAa,CAEtBK,MAAAA,EAAO1E,EAAU2E,WACjBC,EAAS5E,EAAU6E,aACnBC,EAAYJ,EAAOE,EAAS,GAE9BE,GAAAA,EAAY,KAAOA,GAAa,GAClC,OAGEA,EAAY,KAAkBL,EAAA,EACzBK,EAAY,KAAkBL,EAAA,EAC9BK,EAAY,KAAkBL,EAAA,EAC9BK,EAAY,KAAkBL,EAAA,EAC9BK,EAAY,KAAkBL,EAAA,EAC9BK,EAAY,KAAkBL,EAAA,EACtBA,EAAA,MACZ,CAECM,MAAAA,EAAY/E,EAAUgF,SACxBD,GAAAA,IAAc,GAAKA,IAAc,EACnC,OAEFN,EAAYM,EAAY,EAIpBE,MAAAA,EAAOV,EAAgBE,CAAS,EACjCvG,EAAAA,SACL+G,EAAKtE,YAAcgB,EAAMhB,WAGnBuE,MAAAA,EAAahH,EAAOiE,OAAYgD,GAAA,CACpC,MAAMC,EAAa,IAAIpG,KAAKmG,EAAEnF,SAAS,EACvC,GAAIqE,IAAa,YAAa,CACtBgB,MAAAA,EAAQD,EAAWT,WACnBW,EAAUF,EAAWP,aACrBU,EAAaF,EAAQC,EAAU,GAErC,OAAIb,IAAc,EAAUc,GAAc,KAAOA,EAAa,KAC1Dd,IAAc,EAAUc,GAAc,MAAQA,EAAa,KAC3Dd,IAAc,EAAUc,GAAc,MAAQA,EAAa,KAC3Dd,IAAc,EAAUc,GAAc,MAAQA,EAAa,KAC3Dd,IAAc,EAAUc,GAAc,MAAQA,EAAa,KAC3Dd,IAAc,EAAUc,GAAc,MAAQA,EAAa,KAC3Dd,IAAc,EAAUc,GAAc,MAAQA,EAAa,GACxD,OAEAH,QAAAA,EAAWJ,WAAaP,EAAY,CAC7C,CACD,EAEKe,EAAoBN,EAAW/C,OAAYgD,GAAAA,EAAEtE,SAAW,KAAK,EAC9D0C,EAAAA,QAAU2B,EAAW7E,OAAS,EAC9BmF,EAAkBnF,OAAS6E,EAAW7E,OAAU,IACjD,CAAA,CACL,EAGMkE,EACJpC,OAAe8C,GAAAA,EAAK/G,OAAS,CAAC,EAC9B6E,IAAakC,IAAA,CACZ,GAAGA,EACH1B,QAASzF,KAAKmD,MAAMgE,EAAK1B,QAAU,GAAG,EAAI,IAC1C5C,WAAY7C,KAAKmD,MAAMgE,EAAKtE,WAAa,GAAG,EAAI,GAChD,EAAA,CACN,ECjYM8E,GAAsBA,IAAM,CAC1BC,MAAAA,MAAY1G,KACZD,MAAgBC,KACtBD,OAAAA,EAAU4G,SAASD,EAAME,SAAS,EAAI,CAAC,EAEhC,CACL7G,UAAWA,EAAU8C,YAAAA,EAAcgE,MAAM,GAAG,EAAE,CAAC,EAC/C5G,QAASyG,EAAM7D,YAAAA,EAAcgE,MAAM,GAAG,EAAE,CAAC,CAAA,CAE7C,EAGMC,GAAmC,CACvCC,KAAM,KACNrI,QAAS,CACPoB,UAAW2G,GAAoB,CACjC,EACAO,YAAa,CACXC,iBAAkB,QAClBC,YAAa,UACbC,WAAY,CACVjC,YAAa,MACbkC,aAAc,MACdC,aAAc,KAChB,EACAC,aAAc,CACZ,SACA,YACA,YACA,WACA,aACA,SACA,UAAU,EAEZC,mBAAoB,CAAE,EACtBC,aAAc,CAAA,CAChB,EACAxL,UAAW,GACXzC,MAAO,KACPkO,gBAAiB,IACnB,EAGMC,GAAuBA,CAC3BC,EACAC,IACuB,CACvB,OAAQA,EAAOC,KAAI,CACjB,IAAK,mBACI,MAAA,CACL,GAAGF,EACH3L,UAAW,GACXzC,MAAO,IAAA,EAEX,IAAK,qBACI,MAAA,CACL,GAAGoO,EACHZ,KAAMa,EAAOE,QACb9L,UAAW,GACXzC,MAAO,IAAA,EAEX,IAAK,mBACI,MAAA,CACL,GAAGoO,EACH3L,UAAW,GACXzC,MAAOqO,EAAOE,OAAAA,EAElB,IAAK,iBACI,MAAA,CACL,GAAGH,EACHjJ,QAAS,CACP,GAAGiJ,EAAMjJ,QACT,GAAGkJ,EAAOE,OACZ,CAAA,EAEJ,IAAK,qBACI,MAAA,CACL,GAAGH,EACHX,YAAa,CACX,GAAGW,EAAMX,YACT,GAAGY,EAAOE,OACZ,CAAA,EAEJ,IAAK,eACI,MAAA,CACL,GAAGH,EACHF,gBAAiBG,EAAOE,OAAAA,EAE5B,IAAK,gBACI,MAAA,CACL,GAAGH,EACHjJ,QAAS,CACPoB,UAAW2G,GAAoB,CACjC,CAAA,EAEJ,QACSkB,OAAAA,CACX,CACF,EAWMI,GAAuBC,EAAAA,cAAoDnL,MAAS,EAO7EoL,GAA8DA,CAAC,CAAE7N,SAAAA,CAAS,IAAM,CAE3F,KAAM,CAAC8N,CAAgB,EAAInL,GACzB,6BACA,CACF,CAAA,EAGMoL,EAAqB,CACzB,GAAGrB,GACHE,YAAa,CACX,GAAGF,GAAaE,YAChB,GAAGkB,CACL,CAAA,EAGI,CAACP,EAAOS,CAAQ,EAAIC,EAAAA,WAAWX,GAAsBS,CAAkB,EAG7EhK,EAAAA,UAAU,IAAM,CACdd,aAAaY,QACX,6BACAV,KAAKW,UAAUyJ,EAAMX,WAAW,CAClC,CAAA,EACC,CAACW,EAAMX,WAAW,CAAC,EAEhBsB,MAAAA,EAAYC,EAAAA,YAAY,SAAY,CAC/BH,EAAA,CAAEP,KAAM,kBAAA,CAAoB,EACjC,GAAA,CACF,MAAMd,EAAO,MAAMtI,GAAuBkJ,EAAMjJ,OAAO,EAC9C0J,EAAA,CAAEP,KAAM,qBAAsBC,QAASf,CAAAA,CAAM,QAC/CxN,GACE6O,EAAA,CACPP,KAAM,mBACNC,QAASvO,aAAiByF,MAAQzF,EAAMiP,QAAU,2BAAA,CACnD,CACH,CAAA,EACC,CAACb,EAAMjJ,OAAO,CAAC,EAEZ+J,EAAgBF,cAAa7J,GAAmC,CAC3D0J,EAAA,CAAEP,KAAM,iBAAkBC,QAASpJ,CAAAA,CAAS,CACvD,EAAG,CAAE,CAAA,EAECgK,EAAoBH,cAAavB,GAA0C,CACtEoB,EAAA,CAAEP,KAAM,qBAAsBC,QAASd,CAAAA,CAAa,CAC/D,EAAG,CAAE,CAAA,EAEC2B,EAAcJ,cAAaK,GAA2B,CACjDR,EAAA,CAAEP,KAAM,eAAgBC,QAASc,CAAAA,CAAS,CACrD,EAAG,CAAE,CAAA,EAECC,EAAeN,EAAAA,YAAY,IAAM,CAC5BH,EAAA,CAAEP,KAAM,eAAA,CAAiB,CACpC,EAAG,CAAE,CAAA,EAGL1J,EAAAA,UAAU,IAAM,CACJmK,GACT,EAAA,CAACA,EAAWX,EAAMjJ,OAAO,CAAC,EAE7B,MAAMZ,EAAQ,CACZ,GAAG6J,EACHW,UAAAA,EACAG,cAAAA,EACAC,kBAAAA,EACAC,YAAAA,EACAE,aAAAA,CAAAA,EAGF,OACGhQ,EAAAA,IAAAkP,GAAqB,SAArB,CAA8B,MAAAjK,EAC5B1D,SAAAA,CACH,CAAA,CAEJ,EAGa0O,EAAmBA,IAAgC,CACxDC,MAAAA,EAAUC,aAAWjB,EAAoB,EAC/C,GAAIgB,IAAYlM,OACR,MAAA,IAAImC,MAAM,8DAA8D,EAEzE+J,OAAAA,CACT,ECnMMpR,GAAYsR,EAAOnM,EAAI,EAACjF,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACX,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAG5CiS,GAAoBtR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGpB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAGlCkS,EAAuBvR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACb,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAG5CmS,EAAqBxR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,kBAAA,GAAA,EACf,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,GAC7B,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAM+C,YAAYvD,OACvC,CAAC,CAAEQ,MAAAA,CAAM,IAAMA,EAAMC,OAAO+D,YACpB,CAAC,CAAEhE,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAG5CsQ,GAA4BzR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EAE5B,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,EAAE,EAGlCiS,GAAmBC,EAAAA,MAAK1R,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,qBAAA,kBAAA,cAAA,UAAA,EACjB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GAAM,CAAC,CAAElC,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,GACvD,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMC,OAAO0S,OAC/B,CAAC,CAAE3S,MAAAA,CAAM,IAAMA,EAAMG,aAAaK,GACtC,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,EAAE,EAI1CoS,EAAuB7R,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,mCAAA,eAAA,GAAA,EAGvB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GACtB,CAAC,CAAElC,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAGzC2Q,EAAYT,EAAO9O,EAAG,EAACtC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0BAAA,wBAAA,EAEhB,CAAC,CAAE4R,SAAAA,CAAS,IAAOA,EAAW,EAAI,EAAI,EAO7CC,GAAyBhS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,6CAAA,eAAA,GAAA,EAGzB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,GACtB,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAGlC4S,GAA0CA,CAAC,CAAElR,UAAAA,CAAU,IAAM,CAClE,KAAA,CAAE+F,QAAAA,EAAS+J,cAAAA,EAAeI,aAAAA,EAAc9B,KAAAA,GAAS+B,EAAiB,EAGlE,CAACgB,EAAcC,CAAe,EAAInM,WAAuBc,CAAO,EAGhEsL,EAAmBjD,GAAAA,MAAAA,EAAM7H,OAC3B,CAAC,GAAG,IAAI+K,IAAIlD,EAAK7H,OAAO6E,OAAapB,EAAMvB,MAAM,CAAC,CAAC,EACnD,GAEE8I,EAAsBnD,GAAAA,MAAAA,EAAM7H,OAC9B,CAAC,GAAG,IAAI+K,IAAIlD,EAAK7H,OAAO6E,OAAapB,EAAMP,QAAQ,CAAC,CAAC,EACrD,GAEE+H,EAAgBpD,GAAAA,MAAAA,EAAM7H,OACxB,CAAC,GAAG,IAAI+K,IAAIlD,EAAK7H,OAAOkL,QAAQzH,GAASA,EAAMhC,MAAQ,CAAA,CAAE,CAAC,CAAC,EAC3D,GAGE0J,EAAqC,CAAC,OAAQ,OAAO,EAGrDC,EAA+B,CAAC,MAAO,OAAQ,WAAW,EAG1DC,EAAqC,CAAC,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,OAAO,EAGnFC,EAAmC,CAAC,aAAc,UAAW,aAAa,EAG1EC,EAAmBA,CAACC,EAAgC5M,IAAkB,CAC1EiM,EAAyBY,IAAA,CACvB,GAAGA,EACH7K,UAAW,CACT,GAAG6K,EAAK7K,UACR,CAAC4K,CAAK,EAAG5M,CACX,CACA,EAAA,CAAA,EAIE8M,EAAqB,CACzBF,EACA5M,IACG,CACHiM,EAAwBY,GAAA,CACtB,MAAME,EAAgBF,EAAKD,CAAK,GAAY,CAAA,EACtCI,EAAYD,EAAcpI,SAAS3E,CAAK,EAC1C+M,EAAc1H,OAAY4H,GAAAA,IAAMjN,CAAK,EACrC,CAAC,GAAG+M,EAAe/M,CAAK,EAErB,MAAA,CACL,GAAG6M,EACH,CAACD,CAAK,EAAGI,EAAUzJ,OAAS,EAAIyJ,EAAYjO,MAAAA,CAC9C,CACD,CAAA,EAIGmO,EAAeA,IAAM,CACzBvC,EAAcqB,CAAY,CAAA,EAItBmB,EAAqBA,IAAM,CAClBpC,IACbkB,EAAgBrL,CAAO,CAAA,EAInBwM,EAAa,CACjBR,EACA5M,IACY,CACNqN,MAAAA,EAASrB,EAAaY,CAAK,EACjC,OAAOS,EAASA,EAAO1I,SAAS3E,CAAK,EAAI,EAAA,EAIzC,OAAAlF,OAACjB,IACC,UAAAgB,EACA,MAAM,UACN,QAAQ,UACR,QAAQ,SAER,SAAA,CAAAC,OAACsQ,GACC,CAAA,SAAA,CAAAtQ,OAACuQ,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAU,YAAA,CAAA,SACtBC,GACC,CAAA,SAAA,CAAAxQ,EAAA,IAACyQ,GACC,CAAA,KAAK,OACL,MAAOQ,EAAahK,UAAUC,UAC9B,SAAWtF,GAAMgQ,EAAiB,YAAahQ,EAAE2Q,OAAOtN,KAAK,EAAE,EAEhEjF,EAAA,IAAAyQ,GAAA,CACC,KAAK,OACL,MAAOQ,EAAahK,UAAUG,QAC9B,YAAiBwK,EAAiB,UAAWhQ,EAAE2Q,OAAOtN,KAAK,EAAE,CAAA,EAEjE,CAAA,EACF,SAECqL,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAS,WAAA,CAAA,EACtBvQ,EAAAA,IAAC4Q,EACEY,CAAAA,SAAAA,EAAiBtG,IAAKzC,GACpBzI,EAAA,IAAA6Q,EAAA,CAEC,QAASpI,IAAc,OAAS,UAAY,QAC5C,SAAU4J,EAAW,aAAc5J,CAAS,EAC5C,QAAS,IAAMsJ,EAAmB,aAActJ,CAAS,EAExDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,SAEC6H,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAM,QAAA,CAAA,EAClBvQ,EAAA,IAAA4Q,EAAA,CACEa,SAAcvG,EAAAA,IACblC,GAAAhJ,EAAA,IAAC6Q,EAEC,CAAA,QACE7H,IAAW,MAAQ,UACnBA,IAAW,OAAS,QACpB,OAEF,SAAUqJ,EAAW,WAAYrJ,CAAM,EACvC,QAAS,IAAM+I,EAAmB,WAAY/I,CAAM,EAEnDA,SATIA,CAAAA,EAAAA,CAUP,CACD,EACH,CAAA,EACF,EAECmI,EAAiB3I,OAAS,GACzBzI,EAAA,KAACuQ,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAO,SAAA,CAAA,EACpBvQ,EAAAA,IAAC4Q,GACEO,SAAiBjG,EAAAA,OACflL,EAAA,IAAA6Q,EAAA,CAEC,QAAQ,UACR,SAAUwB,EAAW,UAAW9J,CAAM,EACtC,QAAS,IAAMwJ,EAAmB,UAAWxJ,CAAM,EAElDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,EAGD8I,EAAoB7I,OAAS,GAC5BzI,EAAA,KAACuQ,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAU,YAAA,CAAA,EACvBvQ,EAAAA,IAAC4Q,GACES,SAAoBnG,EAAAA,OAClBlL,EAAA,IAAA6Q,EAAA,CAEC,QAAQ,YACR,SAAUwB,EAAW,aAAc9I,CAAQ,EAC3C,QAAS,IAAMwI,EAAmB,aAAcxI,CAAQ,EAEvDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,SAGD+G,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAS,WAAA,CAAA,EACtBvQ,EAAAA,IAAC4Q,GACEc,SAAiBxG,EAAAA,OACflL,EAAA,IAAA6Q,EAAA,CAEC,QAAQ,UACR,SAAUwB,EAAW,aAAchJ,CAAS,EAC5C,QAAS,IAAM0I,EAAmB,aAAc1I,CAAS,EAExDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,SAECiH,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAO,SAAA,CAAA,EACpBvQ,EAAAA,IAAC4Q,GACEe,SAAezG,EAAAA,OACblL,EAAA,IAAA6Q,EAAA,CAEC,QAAQ,UACR,SAAUwB,EAAW,WAAY/I,CAAO,EACxC,QAAS,IAAMyI,EAAmB,WAAYzI,CAAO,EAEpDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,EAECgI,EAAc9I,OAAS,GACtBzI,EAAA,KAACuQ,EACC,CAAA,SAAA,CAAAtQ,EAAAA,IAACuQ,GAAY,SAAI,MAAA,CAAA,EACjBvQ,EAAAA,IAAC4Q,GACEU,SAAcpG,EAAAA,OACZlL,EAAA,IAAA6Q,EAAA,CAEC,QAAQ,OACR,SAAUwB,EAAW,OAAQ1I,CAAG,EAChC,QAAS,IAAMoI,EAAmB,OAAQpI,CAAG,EAE5CA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,CAAA,EAEJ,SAECoH,GACC,CAAA,SAAA,CAAA/Q,MAACgD,GAAO,CAAA,QAAQ,UAAU,QAASoP,EAAmB,SAEtD,QAAA,EACCpS,EAAA,IAAAgD,GAAA,CAAO,QAASmP,EAAa,SAE9B,gBAAA,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EC7SMrT,GAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGnB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAGlCoU,EAAoBzT,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,sCAAA,EACP,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,WAC/B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,aAAaK,GACxC,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAKtCqU,EAAqB1T,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,GAAA,EACf,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,GACnC,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,cACpB,CAAC,CAAExB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAG5CwS,EAAqB3T,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACf,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUyC,GAC7B,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAM+C,YAAYkB,SACvC,CAAC,CAAEjE,MAAAA,EAAO2U,SAAAA,EAAUC,SAAAA,CAAS,IAChCD,EAAiB3U,EAAMC,OAAO4U,OAC9BD,EAAiB5U,EAAMC,OAAO6U,KAC3B9U,EAAMC,OAAO+D,WACrB,EAGG+Q,GAAsBhU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,eAAA,UAAA,GAAA,EAChB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUS,GAC9B,CAAC,CAAElC,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GAClC,CAAC,CAAElC,MAAAA,EAAO2U,SAAAA,EAAUC,SAAAA,CAAS,IAChCD,EAAiB3U,EAAMC,OAAO4U,OAC9BD,EAAiB5U,EAAMC,OAAO6U,KAC3B9U,EAAMC,OAAOuB,aACrB,EAGUwT,GAAwDA,CAAC,CAAElT,UAAAA,CAAU,IAAM,CAChF,KAAA,CAAEoO,KAAAA,GAAS+B,EAAiB,EAElC,GAAI,CAAC/B,EACI,OAAA,KAGH,KAAA,CAAE3H,QAAAA,CAAY2H,EAAAA,EAEd+E,EAAkBhO,GACf,IAAIiO,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAOvO,CAAK,EAGXwO,EAAiBxO,GACd,GAAGA,EAAMyO,QAAQ,CAAC,KAIzB,OAAA3T,OAACjB,IAAU,UAAAgB,EACT,SAAA,CAAAC,OAACyS,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAS,WAAA,CAAA,EACrBzS,EAAA,IAAA0S,EAAA,CAAY,SAAUnM,EAAQkE,gBAAkB,EAAG,SAAUlE,EAAQkE,gBAAkB,EACrFwI,SAAe1M,EAAAA,EAAQkE,eAAe,EACzC,CAAA,EACF,SAEC+H,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAQ,UAAA,CAAA,EACpBzS,EAAA,IAAA0S,EAAA,CAAY,SAAUnM,EAAQmF,QAAU,GAAI,SAAUnF,EAAQmF,QAAU,GACtE+H,SAAclN,EAAAA,EAAQmF,OAAO,EAChC,SACCqH,GACExM,CAAAA,SAAAA,CAAQ8D,EAAAA,cAAc,MAAI9D,EAAQqF,YAAY,SAAA,EACjD,CAAA,EACF,SAEC4G,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAa,eAAA,CAAA,EACzBzS,EAAA,IAAA0S,EAAA,CAAY,SAAUnM,EAAQiF,aAAe,EAAG,SAAUjF,EAAQiF,aAAe,EAC/EjF,SAAAA,EAAQiF,aAAakI,QAAQ,CAAC,EACjC,CAAA,EACF,SAEClB,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAU,YAAA,CAAA,EACtBzS,EAAA,IAAA0S,EAAA,CAAY,SAAUnM,EAAQoF,WAAa,EAAG,SAAUpF,EAAQoF,WAAa,EAC3EsH,SAAe1M,EAAAA,EAAQoF,UAAU,EACpC,CAAA,EACF,SAEC6G,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAW,aAAA,CAAA,QACvBC,EAAY,CAAA,SAAU,GACpBO,SAAe1M,EAAAA,EAAQwE,UAAU,EACpC,CAAA,EACF,SAECyH,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAY,cAAA,CAAA,EACzBzS,EAAAA,IAAC0S,EAAY,CAAA,SAAU,GACpBO,SAAAA,EAAe,CAAChN,KAAK6E,IAAIvE,EAAQyE,WAAW,CAAC,CAChD,CAAA,CAAA,EACF,SAECwH,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAW,aAAA,CAAA,QACvBC,EAAY,CAAA,SAAU,GACpBO,SAAe1M,EAAAA,EAAQ0E,UAAU,EACpC,CAAA,EACF,SAECuH,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAY,cAAA,CAAA,QACxBC,EAAY,CAAA,SAAU,GACpBO,SAAe1M,EAAAA,EAAQ4E,WAAW,EACrC,CAAA,EACF,SAECqH,EACC,CAAA,SAAA,CAAAxS,EAAAA,IAACyS,GAAY,SAAY,cAAA,CAAA,EACzBzS,EAAAA,IAAC0S,EACEnM,CAAAA,SAAAA,EAAQqF,WACX,CAAA,SACCmH,GAAY,CAAA,SAAA,CAAA,iBACIxM,EAAQ+E,gBAAgBoI,QAAQ,CAAC,EAAE,MAAA,EACpD,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EClIM5U,GAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAE3B,EAAA,CAAA,kBAAA,CAAA,EAEKyU,GAAeC,EAAAA,MAAK5U,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iDAAA,GAAA,EAGX,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,EAAE,EAG1CqV,GAAmBC,EAAAA,MAAK9U,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,mCAAA,EACR,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,UAAU,EAMtD6V,GAAmBC,EAAAA,MAAKhV,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAE,EAAA,CAAA,EAAA,CAAA,EAE1B+U,GAAkBC,EAAAA,GAAElV,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2BAAA,qBAAA,6BAAA,IAAA,EACG,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAO0S,OACnC,CAAC,CAAE3S,MAAAA,EAAOqU,WAAAA,CAAW,IACvCA,EAAa,GAAGrU,EAAMC,OAAOqB,YAAc,cAGvB,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,UAAU,EAIxDiW,EAAyBC,EAAAA,GAAEpV,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,gCAAA,UAAA,WAAA,YAAA,GAAA,EACpB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,GAEzB,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAM+C,YAAYkB,SACvC,CAAC,CAAEjE,MAAAA,EAAOqW,OAAAA,CAAO,IAAMA,EAASrW,EAAMC,OAAOqB,QAAUtB,EAAMC,OAAO+D,YACnE,CAAC,CAAEsS,SAAAA,CAAS,IAAMA,EAAW,UAAY,UAG/C,CAAC,CAAEA,SAAAA,EAAUtW,MAAAA,CAAM,IAAMsW,GAAY;AAAA,eAC5BtW,EAAMC,OAAOqB;AAAAA,KACvB,EAICiV,EAAmBC,EAAAA,GAAExV,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,yBAAA,EACd,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,EAAE,EAItCiW,EAAkB5T,EAAAA,KAAI7B,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oCAAA,sBAAA,KAAA,EAEX,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GAG9B,CAAC,CAAEuI,UAAAA,CAAU,IAAMA,IAAc,MAAQ,IAAM,GAAG,EAI5DiM,GAAiBtE,EAAOuE,EAAK,EAAC3V,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAEnC,EAAA,CAAA,4BAAA,CAAA,EAEK0V,GAAcxE,EAAOuE,EAAK,EAAC3V,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAEhC,EAAA,CAAA,4BAAA,CAAA,EAEK0R,GAAuB7R,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,mCAAA,GAAA,EAGvB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAGlC2U,GAAoBhU,EAAAA,KAAI7B,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,gBAAA,GAAA,EACnB,CAAC,CAAElB,MAAAA,EAAOiH,MAAAA,CAAM,IACvBA,EAAQ,EAAIjH,EAAMC,OAAO4U,OACzB5N,EAAQ,EAAIjH,EAAMC,OAAO6U,KACzB9U,EAAMC,OAAOuB,cACA,CAAC,CAAExB,MAAAA,EAAOiH,MAAAA,CAAM,IAC7BA,IAAU,EAAIjH,EAAM+C,YAAYvD,OAASQ,EAAM+C,YAAY+T,OAAO,EAGhEpS,GAAoB3D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,GAE/B,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,aAAa,EAIvCuV,GAA0CA,CAAC,CAAEjV,UAAAA,CAAU,IAAM,CAClE,KAAA,CAAEoO,KAAAA,EAAMU,gBAAAA,EAAiBkB,YAAAA,GAAgBG,EAAiB,EAC1D,CAAC+E,EAAWC,CAAY,EAAIlQ,WAAoB,WAAW,EAC3D,CAACmQ,EAAeC,CAAgB,EAAIpQ,WAAwB,MAAM,EAElEqQ,EAAcvD,GAAqB,CACnCmD,IAAcnD,EAECqD,EAAAA,IAAkB,MAAQ,OAAS,KAAK,GAGzDD,EAAapD,CAAK,EAClBsD,EAAiB,MAAM,EACzB,EAGIE,EAAeC,EAAAA,QAAQ,IACtBpH,GAAAA,MAAAA,EAAM7H,OAEJ,CAAC,GAAG6H,EAAK7H,MAAM,EAAE6D,KAAK,CAACC,EAAGC,IAAM,CACrC,IAAImL,EAAa,EAEjB,OAAQP,EAAS,CACf,IAAK,YACHO,EAAa,IAAIpO,KAAKgD,EAAEhC,SAAS,EAAEZ,UAAY,IAAIJ,KAAKiD,EAAEjC,SAAS,EAAEZ,QAAQ,EAC7E,MACF,IAAK,SACHgO,EAAapL,EAAE5B,OAAOiN,cAAcpL,EAAE7B,MAAM,EAC5C,MACF,IAAK,YACHgN,EAAapL,EAAE1B,UAAU+M,cAAcpL,EAAE3B,SAAS,EAClD,MACF,IAAK,aACU0B,EAAAA,EAAErB,WAAasB,EAAEtB,WAC9B,MACF,IAAK,oBACUqB,EAAAA,EAAEpB,kBAAoBqB,EAAErB,kBACrC,MACF,IAAK,SACHwM,EAAapL,EAAEnB,OAAOwM,cAAcpL,EAAEpB,MAAM,EAC5C,MACF,QACeuM,EAAA,CACjB,CAEOL,OAAAA,IAAkB,MAAQK,EAAa,CAACA,CAAAA,CAChD,EA7ByB,GA8BzB,CAACrH,GAAAA,YAAAA,EAAM7H,OAAQ2O,EAAWE,CAAa,CAAC,EAErCO,EAAcC,GAA+B,CAC3CC,MAAAA,EAAO,IAAIxO,KAAKuO,CAAU,EAChC,OAAOC,EAAKC,mBAAmB,EAAI,IAAMD,EAAKE,mBAAmB,GAAI,CAAEhJ,KAAM,UAAWE,OAAQ,SAAA,CAAW,CAAA,EAGvGkG,EAAkBhO,GACf,IAAIiO,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAOvO,CAAK,EAGXwO,EAAiBxO,GACd,GAAGA,EAAQ,EAAI,IAAM,KAAKA,EAAMyO,QAAQ,CAAC,KAG5CoC,EAAuBrN,GACpBA,IAAc,OAAS,UAAY,QAGtCsN,EAAoB/M,GAAgC,CACxD,OAAQA,EAAM,CACZ,IAAK,MAAc,MAAA,UACnB,IAAK,OAAe,MAAA,QACpB,IAAK,YAAoB,MAAA,OACzB,QAAgB,MAAA,SAClB,CAAA,EAGIgN,EAAkBjG,GAAoB,CAC9BA,EAAAA,IAAYnB,EAAkB,KAAOmB,CAAO,CAAA,EAGtD,MAAA,CAAC7B,GAAQ,CAACA,EAAK7H,QAAU6H,EAAK7H,OAAOmC,SAAW,EAC3CxI,EAAA,IAAC0C,IAAW,SAAyC,2CAAA,CAAA,EAI3D1C,EAAA,IAAAlB,GAAA,CAAU,UAAAgB,EACT,SAAAC,OAAC4T,GACC,CAAA,SAAA,CAAC3T,EAAA,IAAA6T,GAAA,CACC,gBAACI,GACC,CAAA,SAAA,CAAClU,EAAAA,KAAAoU,EAAA,CACC,SAAQ,GACR,OAAQa,IAAc,YACtB,QAAS,IAAMI,EAAW,WAAW,EAAE,SAAA,CAAA,YAGtCJ,IAAc,aAAgBhV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACtE,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,SACtB,QAAS,IAAMI,EAAW,QAAQ,EAAE,SAAA,CAAA,SAGnCJ,IAAc,UAAahV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACnE,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,YACtB,QAAS,IAAMI,EAAW,WAAW,EAAE,SAAA,CAAA,YAGtCJ,IAAc,aAAgBhV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACtE,EAEAlV,EAAAA,IAACmU,GAAe,SAEhB,YAAA,CAAA,EAEApU,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,aACtB,QAAS,IAAMI,EAAW,YAAY,EAAE,SAAA,CAAA,MAGvCJ,IAAc,cAAiBhV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACvE,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,oBACtB,QAAS,IAAMI,EAAW,mBAAmB,EAAE,SAAA,CAAA,QAG9CJ,IAAc,qBAAwBhV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EAC9E,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,SACtB,QAAS,IAAMI,EAAW,QAAQ,EAAE,SAAA,CAAA,SAGnCJ,IAAc,UAAahV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACnE,EAEAlV,EAAAA,IAACmU,GAAe,SAEhB,UAAA,CAAA,EAEAnU,EAAAA,IAACmU,GAAe,SAEhB,MAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAECJ,GACEsB,CAAAA,SAAAA,EAAanK,IAAKpB,wBAChBmK,GAEC,CAAA,WAAYnK,EAAMC,KAAO6E,EACzB,QAAS,IAAMoH,EAAelM,EAAMC,EAAE,EAEtC,SAAA,CAAA/J,EAAA,IAACuU,EAAWkB,CAAAA,SAAAA,EAAW3L,EAAM3B,SAAS,EAAE,EACxCnI,EAAAA,IAACuU,EAAWzK,CAAAA,SAAAA,EAAMvB,MAAO,CAAA,QACxBgM,EACC,CAAA,SAAAvU,EAAAA,IAAC0U,GACC,CAAA,UAAW5K,EAAMrB,UACjB,QAASqN,EAAoBhM,EAAMrB,SAAS,EAC5C,KAAK,QAEJqB,SAAAA,EAAMrB,SACT,CAAA,EACF,SACC8L,EACEzK,CAAAA,SAAAA,CAAMpB,EAAAA,WAAWgL,QAAQ,CAAC,EAAE,MAAI5J,EAAMjB,UAAU6K,QAAQ,CAAC,CAAA,EAC5D,EACA1T,EAAA,IAACuU,EACC,CAAA,SAAAvU,EAAAA,IAAC6U,GAAW,CAAA,MAAO/K,EAAMhB,WACtBmK,SAAenJ,EAAAA,EAAMhB,UAAU,CAClC,CAAA,EACF,EACA9I,EAAA,IAACuU,EACC,CAAA,SAAAvU,EAAAA,IAAC6U,GAAW,CAAA,MAAO/K,EAAMf,kBACtB0K,SAAc3J,EAAAA,EAAMf,iBAAiB,CACxC,CAAA,EACF,QACCwL,EACC,CAAA,SAAAvU,EAAAA,IAAC4U,GACC,CAAA,OAAQ9K,EAAMd,OACd,QAAS+M,EAAiBjM,EAAMd,MAAM,EACtC,KAAK,QAEJc,SAAAA,EAAMd,MACT,CAAA,EACF,EACAhJ,EAAAA,IAACuU,EAAWzK,CAAAA,SAAAA,EAAMP,QAAS,CAAA,EAC3BvJ,EAAAA,IAACuU,GACC,SAACvU,EAAA,IAAA4Q,GAAA,CACE9G,cAAMhC,qBAAMoD,IAAI,CAACvB,EAAKsM,UACpB3U,GAAgB,CAAA,KAAK,QAAQ,QAAQ,UACnCqI,YADOsM,CAEV,GAEJ,CACF,CAAA,CA9CKnM,CAAAA,EAAAA,EAAMC,EA+Cb,EACD,EACH,CAAA,CACF,CAAA,CACF,CAAA,CAEJ,EC/SMjL,GAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAE3B,EAAA,CAAA,kBAAA,CAAA,EAEKyU,GAAeC,EAAAA,MAAK5U,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iDAAA,GAAA,EAGX,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,EAAE,EAG1CqV,GAAmBC,EAAAA,MAAK9U,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EACR,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,UAAU,EAGtD6V,GAAmBC,EAAAA,MAAKhV,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAE,EAAA,CAAA,EAAA,CAAA,EAE1B+U,GAAkBC,EAAAA,GAAElV,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2BAAA,6BAAA,IAAA,EACG,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAO0S,OAGjC,CAAC,CAAE3S,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,UAAU,EAIxDiW,EAAyBC,EAAAA,GAAEpV,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,gCAAA,UAAA,WAAA,YAAA,GAAA,EACpB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,GAEzB,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAM+C,YAAYkB,SACvC,CAAC,CAAEjE,MAAAA,EAAOqW,OAAAA,CAAO,IAAMA,EAASrW,EAAMC,OAAOqB,QAAUtB,EAAMC,OAAO+D,YACnE,CAAC,CAAEsS,SAAAA,CAAS,IAAMA,EAAW,UAAY,UAG/C,CAAC,CAAEA,SAAAA,EAAUtW,MAAAA,CAAM,IAAMsW,GAAY;AAAA,eAC5BtW,EAAMC,OAAOqB;AAAAA,KACvB,EAICiV,EAAmBC,EAAAA,GAAExV,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,GAAA,EACd,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,EAAE,EAGtCiW,EAAkB5T,EAAAA,KAAI7B,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oCAAA,sBAAA,KAAA,EAEX,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GAG9B,CAAC,CAAEuI,UAAAA,CAAU,IAAMA,IAAc,MAAQ,IAAM,GAAG,EAI5DyN,GAAsBnX,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+BAAA,kBAAA,+BAAA,GAAA,EAET,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,WAC/B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,aAAa2C,KAErC,CAAC,CAAE9C,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAGzCiW,GAAapX,EAAAA,IAAGC,WAAA,CAAAC,YAAA,MAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,qBAAA,qBAAA,GAAA,EAEX,CAAC,CAAEU,MAAAA,CAAM,IAAM,GAAGA,KACP,CAAC,CAAE5B,MAAAA,EAAO2U,SAAAA,CAAS,IAAMA,EAAW3U,EAAMC,OAAO4U,OAAS7U,EAAMC,OAAO6U,IAAI,EAG3F+B,GAAoBhU,EAAAA,KAAI7B,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,gBAAA,GAAA,EACnB,CAAC,CAAElB,MAAAA,EAAOiH,MAAAA,CAAM,IACvBA,EAAQ,EAAIjH,EAAMC,OAAO4U,OACzB5N,EAAQ,EAAIjH,EAAMC,OAAO6U,KACzB9U,EAAMC,OAAOuB,cACA,CAAC,CAAExB,MAAAA,EAAOiH,MAAAA,CAAM,IAC7BA,IAAU,EAAIjH,EAAM+C,YAAYvD,OAASQ,EAAM+C,YAAY+T,OAAO,EAGhEpS,GAAoB3D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,GAE/B,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,aAAa,EAIvC4W,EAAoEA,CAAC,CAChFtW,UAAAA,EACA+L,SAAAA,EACAlJ,MAAAA,CACF,IAAM,CACE,KAAA,CAAEuL,KAAAA,GAAS+B,EAAiB,EAC5B,CAAC+E,EAAWC,CAAY,EAAIlQ,WAAoB,YAAY,EAC5D,CAACmQ,EAAeC,CAAgB,EAAIpQ,WAAwB,MAAM,EAExE,GAAI,CAACmJ,EACI,OAAA,KAGT,IAAImI,EAAyC,CAAA,EAE7C,OAAQxK,EAAQ,CACd,IAAK,SACHwK,EAAkBnI,EAAKzH,kBACvB,MACF,IAAK,WACH4P,EAAkBnI,EAAKvH,oBACvB,MACF,IAAK,YACH0P,EAAkBnI,EAAKtH,qBACvB,MACF,IAAK,UACHyP,EAAkBnI,EAAKrH,mBACvB,KACJ,CAEA,GAAI,CAACwP,GAAmBA,EAAgB7N,SAAW,EACjD,cAAQ9F,GAAW,CAAA,SAAA,CAAA,MAAImJ,EAAS,8BAA4B,CAAA,CAAA,EAGxDuJ,MAAAA,EAAcvD,GAAqB,CACnCmD,IAAcnD,EAECqD,EAAAA,IAAkB,MAAQ,OAAS,KAAK,GAGzDD,EAAapD,CAAK,EAClBsD,EAAiB,MAAM,EACzB,EAIImB,EAAa,CAAC,GAAGD,CAAe,EAAEnM,KAAK,CAACC,EAAGC,IAAM,CACrD,IAAImL,EAAa,EAEjB,OAAQP,EAAS,CACf,IAAK,QACHO,EAAapL,EAAElF,MAAMuQ,cAAcpL,EAAEnF,KAAK,EAC1C,MACF,IAAK,SACUkF,EAAAA,EAAE9D,OAAS+D,EAAE/D,OAC1B,MACF,IAAK,UACU8D,EAAAA,EAAEuB,QAAUtB,EAAEsB,QAC3B,MACF,IAAK,aACUvB,EAAAA,EAAErB,WAAasB,EAAEtB,WAC9B,MACF,IAAK,oBACUqB,EAAAA,EAAEoC,kBAAoBnC,EAAEmC,kBACrC,MACF,QACegJ,EAAA,CACjB,CAEOL,OAAAA,IAAkB,MAAQK,EAAa,CAACA,CAAAA,CAChD,EAGKgB,EAAgBtQ,KAAKwB,IACzB,GAAG4O,EAAgBnL,IAAYjF,GAAAA,KAAK6E,IAAIvG,EAAKuE,UAAU,CAAC,CAC1D,EAEMmK,EAAkBhO,GACf,IAAIiO,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAOvO,CAAK,EAGXwO,EAAiBxO,GACd,GAAGA,EAAMyO,QAAQ,CAAC,KAG3B,OACG1T,EAAA,IAAAlB,GAAA,CAAU,UAAAgB,EACT,SAAAC,OAAC4T,GACC,CAAA,SAAA,CAAC3T,EAAA,IAAA6T,GAAA,CACC,gBAACI,GACC,CAAA,SAAA,CAAClU,EAAAA,KAAAoU,EAAA,CACC,SAAQ,GACR,OAAQa,IAAc,QACtB,QAAS,IAAMI,EAAW,OAAO,EAEhCzS,SAAAA,CAAAA,EACAqS,IAAc,SAAYhV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EAClE,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,SACtB,QAAS,IAAMI,EAAW,QAAQ,EAAE,SAAA,CAAA,SAGnCJ,IAAc,UAAahV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACnE,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,UACtB,QAAS,IAAMI,EAAW,SAAS,EAAE,SAAA,CAAA,WAGpCJ,IAAc,WAAchV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACpE,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,aACtB,QAAS,IAAMI,EAAW,YAAY,EAAE,SAAA,CAAA,MAGvCJ,IAAc,cAAiBhV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EACvE,EAEAnV,EAAAA,KAACoU,EACC,CAAA,SAAQ,GACR,OAAQa,IAAc,oBACtB,QAAS,IAAMI,EAAW,mBAAmB,EAAE,SAAA,CAAA,UAG9CJ,IAAc,qBAAwBhV,EAAA,IAAAyU,EAAA,CAAS,UAAWS,EAAiB,CAAA,EAC9E,CAAA,CAAA,CACF,CACF,CAAA,EAEAlV,EAAAA,IAAC+T,IACEuC,SAAWpL,EAAAA,IAAI,CAAC3G,EAAM0R,WACpBhC,GACC,CAAA,SAAA,CAACjU,EAAAA,IAAAuU,EAAA,CAAWhQ,WAAKU,KAAM,CAAA,EACvBjF,EAAAA,IAACuU,EAAWhQ,CAAAA,SAAAA,EAAK8B,MAAO,CAAA,EACvBrG,EAAA,IAAAuU,EAAA,CAAWd,SAAclP,EAAAA,EAAKmH,OAAO,EAAE,SACvC6I,EACC,CAAA,SAAA,CAAAvU,EAAAA,IAAC6U,IAAW,MAAOtQ,EAAKuE,WACrBmK,SAAe1O,EAAAA,EAAKuE,UAAU,EACjC,EACA9I,EAAAA,IAACkW,IACC,SAAClW,MAAAmW,GAAA,CACC,MAAOlQ,KAAKmF,IAAI,IAAMnF,KAAK6E,IAAIvG,EAAKuE,UAAU,EAAIyN,EAAiB,GAAG,EACtE,SAAUhS,EAAKuE,YAAc,CAAE,CAAA,EAEnC,CAAA,EACF,EACA9I,EAAA,IAACuU,EACC,CAAA,SAAAvU,EAAAA,IAAC6U,GAAW,CAAA,MAAOtQ,EAAKgI,kBACrB0G,SAAe1O,EAAAA,EAAKgI,iBAAiB,CACxC,CAAA,EACF,CAAA,GAnBa0J,CAoBf,CACD,EACH,CAAA,CACF,CAAA,CACF,CAAA,CAEJ,EC9PMnX,GAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAE,EAAA,CAAA,EAAA,CAAA,EAExBsX,GAAwBzX,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAGxB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,GACtB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAGzCqY,GAAkB1X,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGlB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAGlCwW,GAAwB3X,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAIhC,EAAA,CAAA,gEAAA,CAAA,EAEKyX,GAAuB5X,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACjB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,GAC7B,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAM+C,YAAYvD,OACvC,CAAC,CAAEQ,MAAAA,CAAM,IAAMA,EAAMC,OAAO+D,WAAW,EAG5C4U,GAAyB7X,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,cAAA,UAAA,GAAA,EAEzB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,GACvB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMyB,UAAUS,GACnC,CAAC,CAAElC,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,aAAa,EAG9CkT,GAAqB7R,EAAAA,KAAI7B,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,gBAAA,GAAA,EACpB,CAAC,CAAElB,MAAAA,EAAO2U,SAAAA,EAAUC,SAAAA,CAAS,IAChCD,EAAiB3U,EAAMC,OAAO4U,OAC9BD,EAAiB5U,EAAMC,OAAO6U,KAC3B9U,EAAMC,OAAOuB,cAEP,CAAC,CAAExB,MAAAA,CAAM,IAAMA,EAAM+C,YAAYvD,MAAM,EAGlD0Y,GAAsBnX,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,kBAAA,qCAAA,EAET,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,WAC/B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,aAAaK,EAAE,EAKjD2X,GAAapX,EAAAA,IAAGC,WAAA,CAAAC,YAAA,MAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,qBAAA,qBAAA,8BAAA,EAEX,CAAC,CAAEU,MAAAA,CAAM,IAAM,GAAGA,KACP,CAAC,CAAE5B,MAAAA,EAAO2U,SAAAA,CAAS,IAAMA,EAAW3U,EAAMC,OAAO4U,OAAS7U,EAAMC,OAAO6U,IAAI,EAI3F+D,GAAkB9X,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,0DAAA,UAAA,gBAAA,uCAAA,EAGjB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,GAIxB,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMyB,UAAUS,GACnC,CAAC,CAAElC,MAAAA,CAAM,IAAMA,EAAMC,OAAO6Y,YACtB,CAAC,CAAE9Y,MAAAA,CAAM,IAAMA,EAAM+C,YAAYvD,MAAM,EAIlDkF,GAAoB3D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,GAE/B,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,aAAa,EAIvCuX,GAA4DA,CAAC,CACxEjX,UAAAA,EACA0M,SAAAA,EACA7J,MAAAA,CACF,IAAM,CACE,KAAA,CAAEuL,KAAAA,GAAS+B,EAAiB,EAElC,GAAI,CAAC/B,EACI,OAAA,KAGT,MAAMmI,EAAkB7J,IAAa,YACjC0B,EAAKpH,qBACLoH,EAAKlH,qBAET,GAAI,CAACqP,GAAmBA,EAAgB7N,SAAW,EACjD,cAAQ9F,GAAW,CAAA,SAAA,CAAA,MAAI8J,EAAS,8BAA4B,CAAA,CAAA,EAI9D,MAAM+J,EAAgBtQ,KAAKwB,IACzB,GAAG4O,EAAgBnL,IAAYjF,GAAAA,KAAK6E,IAAIvG,EAAKuE,UAAU,CAAC,CAC1D,EAEMmK,EAAkBhO,GACf,IAAIiO,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAOvO,CAAK,EAGXwO,EAAiBxO,GACd,GAAGA,EAAMyO,QAAQ,CAAC,KAIzB,OAAA1T,EAAAA,IAAClB,GAAU,CAAA,UAAAgB,EACT,SAACE,EAAAA,IAAAwW,GAAA,CACEH,SAAgBnL,EAAAA,IAAI,CAAC3G,EAAM0R,IAC1BlW,EAAAA,KAAC0W,GACC,CAAA,SAAA,CAAA1W,OAAC2W,GACC,CAAA,SAAA,CAAC1W,EAAAA,IAAA2W,GAAA,CAAepS,WAAKoI,QAAS,CAAA,SAC7BiK,GACC,CAAA,SAAA,CAAA7W,OAAC,MAAG,CAAA,SAAA,CAAA,WACMC,EAAAA,IAAC0S,GAAanO,CAAAA,SAAAA,EAAK8B,MAAO,CAAA,CAAA,EACpC,SACC,MAAG,CAAA,SAAA,CAAA,aACSrG,EAAA,IAAA0S,GAAA,CAAY,SAAUnO,EAAKmH,QAAU,GAAI,SAAUnH,EAAKmH,QAAU,GAC1E+H,SAAclP,EAAAA,EAAKmH,OAAO,EAC7B,CAAA,EACF,SACC,MAAG,CAAA,SAAA,CAAA,QACI1L,EAAA,IAAA0S,GAAA,CAAY,SAAUnO,EAAKuE,WAAa,EAAG,SAAUvE,EAAKuE,WAAa,EAC1EmK,SAAe1O,EAAAA,EAAKuE,UAAU,EACjC,CAAA,EACF,CAAA,EACF,CAAA,EACF,EACC9I,EAAA,IAAAkW,GAAA,CACC,SAAClW,EAAA,IAAAmW,GAAA,CACC,MAAOlQ,KAAKmF,IAAI,IAAMnF,KAAK6E,IAAIvG,EAAKuE,UAAU,EAAIyN,EAAiB,GAAG,EACtE,SAAUhS,EAAKuE,YAAc,EAE5BvE,SAAAA,EAAKuE,aAAe,GAClB9I,EAAA,IAAA6W,GAAA,CACE5D,SAAe1O,EAAAA,EAAKuE,UAAU,CACjC,CAAA,CAEJ,CAAA,EACF,CA9BamN,CAAAA,EAAAA,CA+Bf,CACD,CACH,CAAA,CACF,CAAA,CAEJ,EC5JMnX,GAAYsR,EAAOnM,EAAI,EAACjF,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EACd,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAGzC4Y,GAAoBjY,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGpB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAGlC6Y,EAAuBlY,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACb,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAG5C8Y,EAAqBnY,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,GAAA,EACf,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUjB,GACnC,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,cACpB,CAAC,CAAExB,MAAAA,CAAM,IAAMA,EAAMW,QAAQsB,GAAG,EAG7CkX,EAAqBpY,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACf,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUrB,GAC7B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAM+C,YAAYvD,OACvC,CAAC,CAAEQ,MAAAA,CAAM,IAAMA,EAAMC,OAAO+D,WAAW,EAG5C6S,GAAoB9V,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,kBAAA,GAAA,EACd,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUyC,GAC7B,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAM+C,YAAYkB,SACvC,CAAC,CAAEjE,MAAAA,EAAOiH,MAAAA,CAAM,IACvBA,EAAQ,EAAIjH,EAAMC,OAAO4U,OACzB5N,EAAQ,EAAIjH,EAAMC,OAAO6U,KACzB9U,EAAMC,OAAOuB,cACE,CAAC,CAAExB,MAAAA,CAAM,IAAMA,EAAMW,QAAQH,EAAE,EAG5CoS,GAAuB7R,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,mCAAA,eAAA,GAAA,EAGvB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GACtB,CAAC,CAAElC,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAGzCkX,GAAerY,EAAAA,IAAGC,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,gBAAA,yBAAA,wBAAA,EACR,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,GAC5B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,GACpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMC,OAAO0S,MAAM,EAItDjO,GAAoB3D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,GAE/B,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAMC,OAAOuB,aAAa,EAIvC6X,GAA0CA,CAAC,CAAEvX,UAAAA,CAAU,IAAM,CAClE,KAAA,CAAEoO,KAAAA,EAAMU,gBAAAA,GAAoBqB,EAAiB,EAE/C,GAAA,CAAC/B,GAAQ,CAACU,EACL,OAAA,KAGT,MAAM0I,EAAgBpJ,EAAK7H,OAAOkR,KAAczN,GAAAA,EAAMC,KAAO6E,CAAe,EAE5E,GAAI,CAAC0I,EACI,OAAAtX,EAAA,IAAC0C,IAAW,SAAgB,kBAAA,CAAA,EAG/B+S,MAAAA,EAAcC,GAA+B,CAC3CC,MAAAA,EAAO,IAAIxO,KAAKuO,CAAU,EAChC,OAAOC,EAAKC,mBAAmB,EAAI,IAAMD,EAAKE,mBAAmB,GAAI,CAAEhJ,KAAM,UAAWE,OAAQ,SAAA,CAAW,CAAA,EAGvGkG,EAAkBhO,GACf,IAAIiO,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAOvO,CAAK,EAGXwO,EAAiBxO,GACd,GAAGA,EAAQ,EAAI,IAAM,KAAKA,EAAMyO,QAAQ,CAAC,KAG5CoC,EAAuBrN,GACpBA,IAAc,OAAS,UAAY,QAGtCsN,EAAoB/M,GAA2B,CACnD,OAAQA,EAAM,CACZ,IAAK,MAAc,MAAA,UACnB,IAAK,OAAe,MAAA,QACpB,IAAK,YAAoB,MAAA,OACzB,QAAgB,MAAA,SAClB,CAAA,EAGIwO,EAAoBA,CAACrP,EAAmBG,IAA6B,CACzE,MAAMmP,EAAQ,IAAItQ,KAAKgB,CAAS,EAAEZ,QAAQ,EAEpCmQ,EADO,IAAIvQ,KAAKmB,CAAQ,EAAEf,QAAQ,EACdkQ,EAEpBE,EAAU1R,KAAKqB,MAAMoQ,GAAc,IAAO,GAAG,EAC7CE,EAAQ3R,KAAKqB,MAAMqQ,EAAU,EAAE,EAC/BE,EAAmBF,EAAU,GAEnC,OAAIC,EAAQ,EACH,GAAGA,MAAUC,KAEf,GAAGF,IAAAA,EAIV,OAAA5X,EAAAA,KAACjB,GACC,CAAA,UAAAgB,EACA,MAAO,GAAGwX,EAAc/O,uBACxB,QAAQ,UACR,QAAQ,SAER,SAAA,CAACxI,EAAA,KAAA8U,GAAA,CAAW,MAAOyC,EAAcxO,WAC9BmK,SAAAA,CAAAA,EAAeqE,EAAcxO,UAAU,EAAE,KAAG2K,EAAc6D,EAAcvO,iBAAiB,EAAE,GAAA,EAC9F,SAECiO,GACC,CAAA,SAAA,CAAAjX,OAACkX,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAS,WAAA,CAAA,EACrBlX,EAAA,IAAAmX,EAAA,CACC,SAACnX,EAAA,IAAA2U,GAAA,CACC,QAASmB,EAAoBwB,EAAc7O,SAAS,EACpD,KAAK,QAEJ6O,SAAAA,EAAc7O,SACjB,CAAA,EACF,CAAA,EACF,SAECwO,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAM,QAAA,CAAA,EAClBlX,EAAA,IAAAmX,EAAA,CACC,SAACnX,EAAA,IAAA2U,GAAA,CACC,QAASoB,EAAiBuB,EAActO,MAAM,EAC9C,KAAK,QAEJsO,SAAAA,EAActO,MACjB,CAAA,EACF,CAAA,EACF,SAECiO,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAU,YAAA,CAAA,EACtBlX,EAAA,IAAAmX,EAAA,CAAa1B,SAAW6B,EAAAA,EAAcnP,SAAS,EAAE,CAAA,EACpD,SAEC8O,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAS,WAAA,CAAA,EACrBlX,EAAA,IAAAmX,EAAA,CAAa1B,SAAW6B,EAAAA,EAAchP,QAAQ,EAAE,CAAA,EACnD,SAEC2O,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAQ,UAAA,CAAA,QACpBC,EAAaK,CAAAA,SAAAA,EAAkBF,EAAcnP,UAAWmP,EAAchP,QAAQ,EAAE,CAAA,EACnF,SAEC2O,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAW,aAAA,CAAA,QACvBC,EAAaG,CAAAA,SAAAA,EAAc5O,WAAWgL,QAAQ,CAAC,EAAE,CAAA,EACpD,SAECuD,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAU,YAAA,CAAA,QACtBC,EAAaG,CAAAA,SAAAA,EAAczO,UAAU6K,QAAQ,CAAC,EAAE,CAAA,EACnD,SAECuD,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAQ,UAAA,CAAA,EACrBlX,EAAAA,IAACmX,EAAaG,CAAAA,SAAAA,EAAcnO,QAAS,CAAA,CAAA,EACvC,SAEC8N,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAS,WAAA,CAAA,EACtBlX,EAAAA,IAACmX,EAAaG,CAAAA,SAAAA,EAAcjO,SAAU,CAAA,CAAA,EACxC,SAEC4N,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAO,SAAA,CAAA,EACpBlX,EAAAA,IAACmX,EAAaG,CAAAA,SAAAA,EAAchO,OAAQ,CAAA,CAAA,EACtC,SAEC2N,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAQ,UAAA,CAAA,EACrBlX,EAAAA,IAACmX,EAAaG,CAAAA,SAAAA,EAAc/N,QAAS,CAAA,CAAA,EACvC,CAAA,EACF,EAEC+N,EAAcxP,MAAQwP,EAAcxP,KAAKU,OAAS,UAChDyO,EACC,CAAA,SAAA,CAAAjX,EAAAA,IAACkX,GAAY,SAAI,MAAA,CAAA,QAChBtG,GACE0G,CAAAA,SAAAA,EAAcxP,KAAKoD,IAAI,CAACvB,EAAKsM,IAC3BjW,EAAA,IAAAsB,GAAA,CAAgB,QAAQ,OAAO,KAAK,QAClCqI,SADOsM,CAAAA,EAAAA,CAEV,CACD,EACH,CAAA,EACF,EAGDqB,EAAcrN,OACblK,EAAAA,KAACqX,GACC,CAAA,SAAA,CAAApX,EAAAA,IAACkX,GAAY,SAAK,OAAA,CAAA,EAClBlX,EAAAA,IAACmX,EAAaG,CAAAA,SAAAA,EAAcrN,KAAM,CAAA,CAAA,EACpC,CAEJ,CAAA,CAAA,CAEJ,ECxNM6N,GAAuB/Y,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,2CAAA,GAAA,EAGvB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,GAGzB,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAMW,QAAQuD,EAAE,EAGtC6V,GAAoBhZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIV,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,EAAE,EAG5C0D,GAAekW,EAAAA,GAAEhZ,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACR,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMyB,UAAUwY,IAEnC,CAAC,CAAEja,MAAAA,CAAM,IAAMA,EAAMC,OAAO+D,WAAW,EAI5CkW,GAAkBnZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,4BAAA,mBAAA,GAAA,EAElB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,GACnB,CAAC,CAAElC,MAAAA,CAAM,IAAMA,EAAMW,QAAQP,GACnB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMC,OAAO0S,OACrC,CAAC,CAAE3S,MAAAA,CAAM,IAAMA,EAAMW,QAAQuB,EAAE,EAG7CiY,EAAiB/W,EAAAA,OAAMpC,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,cAAA,gBAAA,UAAA,2CAAA,mBAAA,kBAAA,IAAA,EAGhB,CAAC,CAAElB,MAAAA,CAAM,IAAM,GAAGA,EAAMW,QAAQuB,MAAMlC,EAAMW,QAAQH,KAClD,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMyB,UAAUrB,GAC7B,CAAC,CAAEJ,MAAAA,EAAOqW,OAAAA,CAAO,IAC9BA,EAASrW,EAAM+C,YAAYkB,SAAWjE,EAAM+C,YAAY+T,QACjD,CAAC,CAAE9W,MAAAA,EAAOqW,OAAAA,CAAO,IAAOA,EAASrW,EAAMC,OAAOqB,QAAUtB,EAAMC,OAAOuB,cAEnD,CAAC,CAAExB,MAAAA,EAAOqW,OAAAA,CAAO,IAAOA,EAASrW,EAAMC,OAAOqB,QAAU,cACjE,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMiD,YAAYC,KAGxC,CAAC,CAAElD,MAAAA,CAAM,IAAMA,EAAMC,OAAOqB,OAAO,EAI1C8Y,GAAiCA,IAAM,CACrC,KAAA,CAAElK,KAAAA,EAAM/K,UAAAA,EAAWzC,MAAAA,EAAOkO,gBAAAA,EAAiBT,YAAAA,EAAa0B,kBAAAA,GAC5DI,EAAiB,EACb,CAACoI,EAAYC,CAAa,EAAIvT,EACjCoJ,SAAAA,EAAYE,aAA4B,SAC3C,EAEMkK,EAAoBC,GAAmB,CAC3CF,EAAcE,CAAI,EACA3I,EAAA,CAAExB,YAAamK,CAAAA,CAAM,CAAA,EAGnCC,EAAgBA,IAAM,CAC1B,OAAQJ,EAAU,CAChB,IAAK,UACH,OAEItY,EAAA,KAAA2Y,WAAA,CAAA,SAAA,CAAA1Y,MAACkD,GACC,MAAM,sBACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAM3H,SAChB,aAAa,0DAEb,SAAAvG,MAACgT,IAAkB,CAAA,EACrB,EAEChT,EAAAA,IAAAkD,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMpH,uBAAwBoH,EAAKpH,qBAAqB0B,SAAW,EAC7E,aAAa,sEAEb,SAACxI,EAAAA,IAAA+W,GAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,EAEC/W,EAAAA,IAAAkD,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMlH,uBAAwBkH,EAAKlH,qBAAqBwB,SAAW,EAC7E,aAAa,sEAEb,SAACxI,EAAAA,IAAA+W,GAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,CACF,CAAA,CAAA,EAGJ,IAAK,SACH,OAEIhX,EAAA,KAAA2Y,WAAA,CAAA,SAAA,CAAC1Y,EAAAA,IAAAkD,EAAA,CACC,MAAM,SACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAM7H,SAAU6H,EAAK7H,OAAOmC,SAAW,EACjD,aAAa,gDAEb,SAACxI,EAAA,IAAA+U,GAAA,CAAA,CAAW,CACd,CAAA,EAECnG,SAAoByI,GAAc,EAAA,CACrC,CAAA,CAAA,EAGJ,IAAK,UACH,OACGrX,EAAAA,IAAAkD,EAAA,CACC,MAAM,wBACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMzH,oBAAqByH,EAAKzH,kBAAkB+B,SAAW,EACvE,aAAa,iEAEb,SAACxI,EAAAA,IAAAoW,EAAA,CAAyB,SAAS,SAAS,MAAM,QAAA,CAAQ,CAC5D,CAAA,EAGJ,IAAK,aACH,OACGpW,EAAAA,IAAAkD,EAAA,CACC,MAAM,0BACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMvH,sBAAuBuH,EAAKvH,oBAAoB6B,SAAW,EAC3E,aAAa,mEAEb,SAACxI,EAAAA,IAAAoW,EAAA,CAAyB,SAAS,WAAW,MAAM,UAAA,CAAU,CAChE,CAAA,EAGJ,IAAK,aACH,OAEIrW,EAAA,KAAA2Y,WAAA,CAAA,SAAA,CAAC1Y,EAAAA,IAAAkD,EAAA,CACC,MAAM,2BACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMtH,uBAAwBsH,EAAKtH,qBAAqB4B,SAAW,EAC7E,aAAa,oEAEb,SAACxI,EAAAA,IAAAoW,EAAA,CAAyB,SAAS,YAAY,MAAM,WAAA,CAAW,CAClE,CAAA,EAECpW,EAAAA,IAAAkD,EAAA,CACC,MAAM,yBACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMrH,qBAAsBqH,EAAKrH,mBAAmB2B,SAAW,EACzE,aAAa,kEAEb,SAACxI,EAAAA,IAAAoW,EAAA,CAAyB,SAAS,UAAU,MAAM,SAAA,CAAS,CAC9D,CAAA,CACF,CAAA,CAAA,EAGJ,IAAK,OACH,OAEIrW,EAAA,KAAA2Y,WAAA,CAAA,SAAA,CAAC1Y,EAAAA,IAAAkD,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMpH,uBAAwBoH,EAAKpH,qBAAqB0B,SAAW,EAC7E,aAAa,sEAEb,SAACxI,EAAAA,IAAA+W,GAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,EAEC/W,EAAAA,IAAAkD,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAACzC,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACwN,GAAAA,MAAAA,EAAMlH,uBAAwBkH,EAAKlH,qBAAqBwB,SAAW,EAC7E,aAAa,sEAEb,SAACxI,EAAAA,IAAA+W,GAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,CACF,CAAA,CAAA,EAGJ,QACS,OAAA,IACX,CAAA,EAGF,cACGe,GACC,CAAA,SAAA,CAAA9X,MAAC+X,GACC,CAAA,SAAA/X,EAAA,IAAC8B,GAAM,CAAA,SAAA,gBAAc,CAAA,EACvB,QAECkP,GAAW,EAAA,SAEXkH,GACC,CAAA,SAAA,CAAClY,EAAAA,IAAAmY,EAAA,CAAQ,OAAQE,IAAe,UAAW,QAAS,IAAME,EAAiB,SAAS,EAAE,SAEtF,SAAA,CAAA,EACAvY,EAAAA,IAACmY,EAAQ,CAAA,OAAQE,IAAe,SAAU,QAAS,IAAME,EAAiB,QAAQ,EAAE,SAEpF,QAAA,CAAA,EACAvY,EAAAA,IAACmY,EAAQ,CAAA,OAAQE,IAAe,UAAW,QAAS,IAAME,EAAiB,SAAS,EAAE,SAEtF,SAAA,CAAA,EACAvY,EAAAA,IAACmY,EACC,CAAA,OAAQE,IAAe,aACvB,QAAS,IAAME,EAAiB,YAAY,EAAE,SAGhD,YAAA,CAAA,EACAvY,EAAAA,IAACmY,EACC,CAAA,OAAQE,IAAe,aACvB,QAAS,IAAME,EAAiB,YAAY,EAAE,SAGhD,YAAA,CAAA,EACAvY,EAAAA,IAACmY,EAAQ,CAAA,OAAQE,IAAe,OAAQ,QAAS,IAAME,EAAiB,MAAM,EAAE,SAEhF,eAAA,CAAA,CAAA,EACF,EAECE,EAAc,CACjB,CAAA,CAAA,CAEJ,EAEME,GAA0BA,IAE3B3Y,EAAA,IAAAoP,GAAA,CACC,SAACpP,EAAA,IAAAoY,GAAA,CAAA,CAAoB,CACvB,CAAA"}