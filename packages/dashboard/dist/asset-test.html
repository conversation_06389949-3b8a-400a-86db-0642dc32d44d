<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Asset Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .asset-container {
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 5px;
      }
      .asset-title {
        font-weight: bold;
        margin-bottom: 10px;
      }
      .asset-image {
        max-width: 100%;
        height: auto;
        display: block;
        margin-bottom: 10px;
      }
      .status {
        padding: 5px 10px;
        border-radius: 3px;
        display: inline-block;
      }
      .success {
        background-color: #d4edda;
        color: #155724;
      }
      .error {
        background-color: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <h1>ADHD Trading Dashboard Asset Test</h1>
    <p>This page tests if the assets are loading correctly.</p>

    <div class="asset-container">
      <div class="asset-title">Favicon (favicon.ico)</div>
      <img src="/favicon.ico" alt="Favicon" class="asset-image" id="favicon" />
      <div id="favicon-status"></div>
    </div>

    <div class="asset-container">
      <div class="asset-title">Logo 192px (logo192.png)</div>
      <img src="/logo192.png" alt="Logo 192px" class="asset-image" id="logo192" />
      <div id="logo192-status"></div>
    </div>

    <div class="asset-container">
      <div class="asset-title">Logo 512px (logo512.png)</div>
      <img src="/logo512.png" alt="Logo 512px" class="asset-image" id="logo512" />
      <div id="logo512-status"></div>
    </div>

    <div class="asset-container">
      <div class="asset-title">Manifest (manifest.json)</div>
      <pre id="manifest-content"></pre>
      <div id="manifest-status"></div>
    </div>

    <div class="asset-container">
      <div class="asset-title">Web Manifest (manifest.webmanifest)</div>
      <pre id="webmanifest-content"></pre>
      <div id="webmanifest-status"></div>
    </div>

    <div class="asset-container">
      <div class="asset-title">Base URL Information</div>
      <div id="base-url-info"></div>
    </div>

    <script>
      // Check if images loaded successfully
      function checkImage(id) {
        const img = document.getElementById(id);
        const statusEl = document.getElementById(`${id}-status`);

        img.onload = function () {
          const fullUrl = new URL(img.src, window.location.href).href;
          statusEl.innerHTML = `✅ Loaded successfully<br>Full URL: <code>${fullUrl}</code>`;
          statusEl.className = 'status success';
          console.log(`Asset loaded successfully: ${id}`, fullUrl);
        };

        img.onerror = function () {
          const fullUrl = new URL(img.src, window.location.href).href;
          statusEl.innerHTML = `❌ Failed to load<br>Full URL: <code>${fullUrl}</code>`;
          statusEl.className = 'status error';
          console.error(`Asset failed to load: ${id}`, fullUrl);

          // Try to fetch the asset directly to get more error details
          fetch(img.src)
            .then((response) => {
              if (!response.ok) {
                statusEl.innerHTML += `<br>HTTP Status: ${response.status} ${response.statusText}`;
                console.error(`HTTP error for ${id}:`, response.status, response.statusText);
              }
            })
            .catch((error) => {
              statusEl.innerHTML += `<br>Fetch error: ${error.message}`;
              console.error(`Fetch error for ${id}:`, error);
            });
        };
      }

      // Function to check manifest files
      function checkManifest(url, contentId, statusId) {
        const fullUrl = new URL(url, window.location.href).href;
        const statusEl = document.getElementById(statusId);

        console.log(`Checking manifest: ${fullUrl}`);

        fetch(url)
          .then((response) => {
            if (response.ok) {
              statusEl.innerHTML = `✅ Loaded successfully<br>Full URL: <code>${fullUrl}</code>`;
              statusEl.className = 'status success';
              console.log(`Manifest loaded successfully: ${url}`, fullUrl);
              return response.json();
            } else {
              statusEl.innerHTML = `❌ Failed to load: ${response.status} ${response.statusText}<br>Full URL: <code>${fullUrl}</code>`;
              statusEl.className = 'status error';
              console.error(`Manifest HTTP error: ${url}`, response.status, response.statusText);
              throw new Error(`Failed to load: ${response.status}`);
            }
          })
          .then((data) => {
            document.getElementById(contentId).textContent = JSON.stringify(data, null, 2);

            // Check if icons are defined and log their paths
            if (data.icons && Array.isArray(data.icons)) {
              console.log(`Manifest icons defined in ${url}:`, data.icons);
              statusEl.innerHTML += `<br>Icons defined: ${data.icons.length}`;

              // Add icon paths to the display
              const iconPaths = data.icons
                .map((icon) => `<li>${icon.src} (${icon.sizes})</li>`)
                .join('');
              statusEl.innerHTML += `<br>Icon paths:<ul>${iconPaths}</ul>`;
            } else {
              console.warn(`No icons defined in ${url}`);
              statusEl.innerHTML += '<br>Warning: No icons defined in manifest';
            }
          })
          .catch((error) => {
            console.error(`Error loading ${url}:`, error);
          });
      }

      // Check manifest.json
      checkManifest('/manifest.json', 'manifest-content', 'manifest-status');

      // Check manifest.webmanifest
      checkManifest('/manifest.webmanifest', 'webmanifest-content', 'webmanifest-status');

      // Display base URL information
      const baseUrlInfo = document.getElementById('base-url-info');
      const locationInfo = {
        href: window.location.href,
        origin: window.location.origin,
        protocol: window.location.protocol,
        host: window.location.host,
        hostname: window.location.hostname,
        port: window.location.port,
        pathname: window.location.pathname,
        hash: window.location.hash,
        search: window.location.search
      };

      let baseUrlHtml = '<ul>';
      for (const [key, value] of Object.entries(locationInfo)) {
        baseUrlHtml += `<li><strong>${key}:</strong> ${value}</li>`;
      }
      baseUrlHtml += '</ul>';

      // Add document base URL if it exists
      const baseElement = document.querySelector('base');
      if (baseElement) {
        baseUrlHtml += `<p><strong>Document base href:</strong> ${baseElement.getAttribute('href')}</p>`;
      } else {
        baseUrlHtml += '<p><strong>Document base href:</strong> Not defined</p>';
      }

      // Add Vite base URL if available
      if (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.BASE_URL) {
        baseUrlHtml += `<p><strong>Vite BASE_URL:</strong> ${import.meta.env.BASE_URL}</p>`;
      } else {
        baseUrlHtml += '<p><strong>Vite BASE_URL:</strong> Not available</p>';
      }

      baseUrlInfo.innerHTML = baseUrlHtml;

      // Check all images
      checkImage('favicon');
      checkImage('logo192');
      checkImage('logo512');
    </script>
  </body>
</html>
