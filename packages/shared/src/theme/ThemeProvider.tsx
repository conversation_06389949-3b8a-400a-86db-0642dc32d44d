/**
 * Theme Provider Component
 *
 * This component provides the theme context to the application.
 */

import React, { useState, createContext, useContext } from 'react';
import { ThemeProvider as StyledThemeProvider, DefaultTheme } from 'styled-components';
import { Theme } from './types';
import { f1Theme } from './f1Theme';
import { lightTheme } from './lightTheme';
import { darkTheme } from './darkTheme';
import GlobalStyles from './GlobalStyles';

// Map of available themes
const themes: Record<string, Theme> = {
  f1: f1Theme,
  light: lightTheme,
  dark: darkTheme,
};

// Default theme
const defaultTheme = f1Theme;

// Helper to get a theme by name
const getTheme = (themeName: string): Theme => {
  return themes[themeName] || defaultTheme;
};

// Create theme context
export const ThemeContext = createContext<{
  theme: Theme;
  setTheme: (theme: Theme | string) => void;
}>({
  theme: defaultTheme,
  setTheme: () => {},
});

// Hook to use the theme
export const useTheme = () => useContext(ThemeContext);

interface ThemeProviderProps {
  /** The initial theme to use */
  initialTheme?: string | Theme;
  /** Whether to store the theme in local storage */
  persistTheme?: boolean;
  /** The key to use for storing the theme in local storage */
  storageKey?: string;
  /** The child components */
  children: React.ReactNode;
}

/**
 * Theme Provider Component
 *
 * Provides theme context to the application and handles theme switching.
 */
export const ThemeProvider = ({
  initialTheme = defaultTheme,
  persistTheme = true,
  storageKey = 'adhd-dashboard-theme',
  children,
}: ThemeProviderProps) => {
  // console.log('ThemeProvider rendering with initialTheme:', initialTheme);

  // Initial theme setup
  const [theme, setThemeState] = useState<Theme>(() => {
    // Try to load from localStorage
    if (persistTheme && typeof window !== 'undefined') {
      const storedTheme = window.localStorage.getItem(storageKey);

      if (storedTheme) {
        try {
          // Try to get the theme by name first
          const themeByName = getTheme(storedTheme);
          if (themeByName) {
            return themeByName;
          }

          // Otherwise, try to parse as JSON
          const parsedTheme = JSON.parse(storedTheme) as Theme;
          return parsedTheme;
        } catch (error) {
          console.error('Failed to parse stored theme:', error);
        }
      }
    }

    // Fall back to initial theme
    const resolvedTheme = typeof initialTheme === 'string' ? getTheme(initialTheme) : initialTheme;
    return resolvedTheme;
  });

  // Theme change handler
  const setTheme = (newTheme: Theme | string) => {
    const themeObject = typeof newTheme === 'string' ? getTheme(newTheme) : newTheme;
    setThemeState(themeObject);

    // Save to localStorage if enabled
    if (persistTheme && typeof window !== 'undefined') {
      window.localStorage.setItem(storageKey, themeObject.name || JSON.stringify(themeObject));
    }
  };

  // Create a wrapper component to avoid TypeScript issues
  const ThemeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
      <StyledThemeProvider theme={theme as DefaultTheme}>
        <GlobalStyles />
        {children}
      </StyledThemeProvider>
    );
  };

  // Provide the theme context
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      <ThemeWrapper>{children}</ThemeWrapper>
    </ThemeContext.Provider>
  );
};
