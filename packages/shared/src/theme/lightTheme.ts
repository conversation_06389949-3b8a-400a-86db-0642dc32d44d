/**
 * Light Theme
 *
 * This file contains the light theme for the ADHD Trading Dashboard.
 */

import { Theme } from './types';
import {
  baseColors,
  lightModeColors,
  spacing,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  breakpoints,
  borderRadius,
  shadows,
  transitions,
  zIndex,
} from './tokens';

/**
 * Light Theme
 *
 * A light theme with red accents.
 */
export const lightTheme: Theme = {
  name: 'light',
  colors: {
    // Primary colors
    primary: baseColors.f1Red,
    primaryDark: baseColors.f1RedDark,
    primaryLight: baseColors.f1RedLight,

    // Secondary colors
    secondary: baseColors.f1Blue,
    secondaryDark: baseColors.f1BlueDark,
    secondaryLight: baseColors.f1BlueLight,

    // Accent colors
    accent: baseColors.purple,
    accentDark: baseColors.purpleDark,
    accentLight: baseColors.purpleLight,

    // Status colors
    success: lightModeColors.success,
    warning: lightModeColors.warning,
    error: lightModeColors.error,
    info: lightModeColors.info,

    // Neutral colors
    background: lightModeColors.background,
    surface: lightModeColors.surface,
    cardBackground: lightModeColors.surface,
    border: lightModeColors.border,
    divider: baseColors.blackTransparent10,

    // Text colors
    textPrimary: lightModeColors.textPrimary,
    textSecondary: lightModeColors.textSecondary,
    textDisabled: lightModeColors.textDisabled,
    textInverse: lightModeColors.textInverse,

    // Chart colors
    chartGrid: lightModeColors.chartGrid,
    chartLine: lightModeColors.chartLine,
    chartAxis: baseColors.gray600,
    chartTooltip: lightModeColors.tooltipBackground,

    // Trading specific colors
    profit: lightModeColors.profit,
    loss: lightModeColors.loss,
    neutral: lightModeColors.neutral,

    // Tab colors
    tabActive: baseColors.f1Red,
    tabInactive: baseColors.gray400,

    // Component specific colors
    tooltipBackground: lightModeColors.tooltipBackground,
    modalBackground: lightModeColors.modalBackground,
    sidebarBackground: baseColors.white,
    headerBackground: 'rgba(0, 0, 0, 0.05)',
  },
  spacing,
  breakpoints,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};
