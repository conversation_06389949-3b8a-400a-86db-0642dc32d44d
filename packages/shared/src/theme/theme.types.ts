/**
 * Theme Type Definitions
 *
 * This file contains TypeScript type definitions for the theme system.
 */

export interface ColorPalette {
  // Primary colors
  primary: string;
  primaryDark: string;
  primaryLight: string;

  // Secondary colors
  secondary: string;
  secondaryDark: string;
  secondaryLight: string;

  // Accent colors
  accent: string;
  accentDark: string;
  accentLight: string;

  // Status colors
  success: string;
  warning: string;
  error: string;
  info: string;

  // Neutral colors
  background: string;
  surface: string;
  cardBackground: string;
  border: string;
  divider: string;

  // Text colors
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  textInverse: string;

  // Chart colors
  chartGrid: string;
  chartLine: string;
  chartAxis: string;
  chartTooltip: string;

  // Trading specific colors
  profit: string;
  loss: string;
  neutral: string;

  // Tab colors
  tabActive: string;
  tabInactive: string;

  // Component specific colors
  tooltipBackground: string;
  modalBackground: string;
  sidebarBackground: string;
  headerBackground: string;
}

export interface Spacing {
  xxs: string;
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
}

export interface Breakpoints {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export interface FontSizes {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
  h1: string;
  h2: string;
  h3: string;
  h4: string;
  h5: string;
  h6: string;
}

export interface BorderRadius {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  pill: string;
  circle: string;
}

export interface Shadows {
  sm: string;
  md: string;
  lg: string;
}

export interface Transitions {
  fast: string;
  normal: string;
  slow: string;
}

export interface ZIndex {
  base: number;
  overlay: number;
  modal: number;
  popover: number;
  tooltip: number;
  fixed: number;
}

export interface FontWeights {
  light: number;
  regular: number;
  medium: number;
  semibold: number;
  bold: number;
}

export interface LineHeights {
  tight: number;
  normal: number;
  loose: number;
}

export interface FontFamilies {
  sans: string;
  mono: string;
}

export interface Theme {
  name: string;
  colors: ColorPalette;
  spacing: Spacing;
  breakpoints: Breakpoints;
  fontSizes: FontSizes;
  fontWeights: FontWeights;
  lineHeights: LineHeights;
  fontFamilies: FontFamilies;
  borderRadius: BorderRadius;
  shadows: Shadows;
  transitions: Transitions;
  zIndex: ZIndex;
}
