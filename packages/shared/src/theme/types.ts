/**
 * Theme Types
 *
 * Comprehensive type definitions for the theme system
 */

export interface ThemeColors {
  // Primary colors
  primary: string;
  primaryDark: string;
  primaryLight: string;

  // Secondary colors
  secondary: string;
  secondaryDark: string;
  secondaryLight: string;

  // Accent colors
  accent: string;
  accentDark: string;
  accentLight: string;

  // Status colors
  success: string;
  warning: string;
  error: string;
  info: string;

  // Neutral colors
  background: string;
  surface: string;
  cardBackground: string;
  border: string;
  divider: string;

  // Text colors
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  textInverse: string;

  // Chart colors
  chartGrid: string;
  chartLine: string;
  chartAxis: string;
  chartTooltip: string;

  // Trading specific colors
  profit: string;
  loss: string;
  neutral: string;

  // Tab colors
  tabActive: string;
  tabInactive: string;

  // Component specific colors
  tooltipBackground: string;
  modalBackground: string;
  sidebarBackground: string;
  headerBackground: string;
}

export interface ThemeSpacing {
  xxs: string;
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
}

export interface ThemeBreakpoints {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export interface ThemeFontSizes {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
  h1: string;
  h2: string;
  h3: string;
  h4: string;
  h5: string;
  h6: string;
}

export interface ThemeFontWeights {
  light: number;
  regular: number;
  medium: number;
  semibold: number;
  bold: number;
}

export interface ThemeLineHeights {
  tight: number;
  normal: number;
  relaxed: number;
}

export interface ThemeFontFamilies {
  body: string;
  heading: string;
  mono: string;
}

export interface ThemeBorderRadius {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  pill: string;
  circle: string;
}

export interface ThemeShadows {
  sm: string;
  md: string;
  lg: string;
}

export interface ThemeTransitions {
  fast: string;
  normal: string;
  slow: string;
}

export interface ThemeZIndex {
  base: number;
  overlay: number;
  modal: number;
  popover: number;
  tooltip: number;
  fixed: number;
}

export interface Theme {
  name: string;
  colors: ThemeColors;
  spacing: ThemeSpacing;
  breakpoints: ThemeBreakpoints;
  fontSizes: ThemeFontSizes;
  fontWeights: ThemeFontWeights;
  lineHeights: ThemeLineHeights;
  fontFamilies: ThemeFontFamilies;
  borderRadius: ThemeBorderRadius;
  shadows: ThemeShadows;
  transitions: ThemeTransitions;
  zIndex: ThemeZIndex;
}
