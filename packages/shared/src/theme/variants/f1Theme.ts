/**
 * Formula 1 Theme
 *
 * This file contains the Formula 1 inspired theme for the ADHD Trading Dashboard.
 */

import { Theme } from "../types";
import {
  baseColors,
  darkModeColors,
  spacing,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  breakpoints,
  borderRadius,
  shadows,
  transitions,
  zIndex,
} from "../tokens";

/**
 * F1 Theme
 *
 * A dark theme inspired by Formula 1 racing with red accents.
 */
export const f1Theme: Theme = {
  name: "f1",
  colors: {
    // Primary colors
    primary: baseColors.f1Red,
    primaryDark: baseColors.f1RedDark,
    primaryLight: baseColors.f1RedLight,

    // Secondary colors
    secondary: baseColors.f1Blue,
    secondaryDark: baseColors.f1BlueDark,
    secondaryLight: baseColors.f1BlueLight,

    // Accent colors
    accent: baseColors.purple,
    accentDark: baseColors.purpleDark,
    accentLight: baseColors.purpleLight,

    // Status colors
    success: darkModeColors.success,
    warning: darkModeColors.warning,
    error: darkModeColors.error,
    info: darkModeColors.info,

    // Neutral colors
    background: darkModeColors.background,
    surface: darkModeColors.surface,
    cardBackground: darkModeColors.surface,
    border: darkModeColors.border,
    divider: "rgba(255, 255, 255, 0.1)",

    // Text colors
    textPrimary: darkModeColors.textPrimary,
    textSecondary: darkModeColors.textSecondary,
    textDisabled: darkModeColors.textDisabled,
    textInverse: darkModeColors.textInverse,

    // Chart colors
    chartGrid: darkModeColors.chartGrid,
    chartLine: darkModeColors.chartLine,
    chartAxis: baseColors.gray400,
    chartTooltip: darkModeColors.tooltipBackground,

    // Trading specific colors
    profit: darkModeColors.profit,
    loss: darkModeColors.loss,
    neutral: darkModeColors.neutral,

    // Tab colors
    tabActive: baseColors.f1Red,
    tabInactive: baseColors.gray600,

    // Component specific colors
    tooltipBackground: darkModeColors.tooltipBackground,
    modalBackground: darkModeColors.modalBackground,
    sidebarBackground: baseColors.gray800,
    headerBackground: "rgba(0, 0, 0, 0.2)",
  },
  spacing,
  breakpoints,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};
