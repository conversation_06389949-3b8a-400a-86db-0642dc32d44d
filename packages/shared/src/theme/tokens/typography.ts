/**
 * Typography Tokens
 *
 * This file defines the typography tokens used throughout the application.
 */

/**
 * Font Sizes
 * 
 * These are the font size values used as the foundation for the theme.
 */
export const fontSizes = {
  xs: "0.75rem",    // 12px
  sm: "0.875rem",   // 14px
  md: "1rem",       // 16px
  lg: "1.125rem",   // 18px
  xl: "1.25rem",    // 20px
  xxl: "1.5rem",    // 24px
  h1: "2.5rem",     // 40px
  h2: "2rem",       // 32px
  h3: "1.75rem",    // 28px
  h4: "1.5rem",     // 24px
  h5: "1.25rem",    // 20px
  h6: "1rem",       // 16px
};

/**
 * Font Weights
 * 
 * These are the font weight values used as the foundation for the theme.
 */
export const fontWeights = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
};

/**
 * Line Heights
 * 
 * These are the line height values used as the foundation for the theme.
 */
export const lineHeights = {
  tight: 1.2,
  normal: 1.5,
  loose: 1.8,
};

/**
 * Font Families
 * 
 * These are the font family values used as the foundation for the theme.
 */
export const fontFamilies = {
  sans: '"Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
  mono: '"Roboto Mono", SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
};
