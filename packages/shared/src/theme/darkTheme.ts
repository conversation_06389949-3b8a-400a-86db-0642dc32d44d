/**
 * Dark Theme
 *
 * This file contains the dark theme for the ADHD Trading Dashboard.
 */

import { Theme } from './types';
import {
  baseColors,
  darkModeColors,
  spacing,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  breakpoints,
  borderRadius,
  shadows,
  transitions,
  zIndex,
} from './tokens';

/**
 * Dark Theme
 *
 * A dark theme with blue accents, distinct from the F1 theme.
 */
export const darkTheme: Theme = {
  name: 'dark',
  colors: {
    // Primary colors (using blue as primary instead of red to differentiate from F1 theme)
    primary: baseColors.f1Blue,
    primaryDark: baseColors.f1BlueDark,
    primaryLight: baseColors.f1BlueLight,

    // Secondary colors
    secondary: baseColors.f1Blue,
    secondaryDark: baseColors.f1BlueDark,
    secondaryLight: baseColors.f1BlueLight,

    // Accent colors
    accent: baseColors.purple,
    accentDark: baseColors.purpleDark,
    accentLight: baseColors.purpleLight,

    // Status colors
    success: darkModeColors.success,
    warning: darkModeColors.warning,
    error: darkModeColors.error,
    info: darkModeColors.info,

    // Neutral colors
    background: baseColors.gray900, // Slightly different from F1 theme
    surface: baseColors.gray800,
    cardBackground: baseColors.gray800,
    border: baseColors.gray700,
    divider: 'rgba(255, 255, 255, 0.1)',

    // Text colors
    textPrimary: baseColors.white,
    textSecondary: baseColors.gray300,
    textDisabled: baseColors.gray500,
    textInverse: baseColors.gray900,

    // Chart colors
    chartGrid: darkModeColors.chartGrid,
    chartLine: baseColors.f1Blue, // Using blue instead of red
    chartAxis: baseColors.gray400,
    chartTooltip: darkModeColors.tooltipBackground,

    // Trading specific colors
    profit: darkModeColors.profit,
    loss: darkModeColors.loss,
    neutral: darkModeColors.neutral,

    // Tab colors
    tabActive: baseColors.f1Blue, // Using blue instead of red
    tabInactive: baseColors.gray600,

    // Component specific colors
    tooltipBackground: 'rgba(26, 32, 44, 0.9)', // Slightly different from F1 theme
    modalBackground: 'rgba(26, 32, 44, 0.8)',
    sidebarBackground: baseColors.gray900,
    headerBackground: 'rgba(0, 0, 0, 0.3)',
  },
  spacing,
  breakpoints,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};
