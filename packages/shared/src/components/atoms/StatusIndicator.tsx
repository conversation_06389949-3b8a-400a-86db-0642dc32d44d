/**
 * Status Indicator Component
 *
 * A component for displaying status (success, error, warning, info).
 */
import React from 'react';
import styled, { css } from 'styled-components';

export type StatusType = 'success' | 'error' | 'warning' | 'info' | 'neutral';
export type StatusSize = 'small' | 'medium' | 'large';

export interface StatusIndicatorProps {
  /** The type of status to display */
  status: StatusType;
  /** The size of the indicator */
  size?: StatusSize;
  /** Whether to pulse the indicator */
  pulse?: boolean;
  /** Whether to show a label next to the indicator */
  showLabel?: boolean;
  /** Custom label text (defaults to capitalized status type) */
  label?: string;
  /** Additional CSS class names */
  className?: string;
}

// Size styles
const sizeMap = {
  small: '8px',
  medium: '12px',
  large: '16px',
};

const labelSizeMap = {
  small: css`
    font-size: ${({ theme }) => theme.fontSizes.xs};
    margin-left: ${({ theme }) => theme.spacing.xs};
  `,
  medium: css`
    font-size: ${({ theme }) => theme.fontSizes.sm};
    margin-left: ${({ theme }) => theme.spacing.sm};
  `,
  large: css`
    font-size: ${({ theme }) => theme.fontSizes.md};
    margin-left: ${({ theme }) => theme.spacing.md};
  `,
};

// Pulse animation
const pulseAnimation = css`
  @keyframes pulse {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--pulse-color), 0.7);
    }
    
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 6px rgba(var(--pulse-color), 0);
    }
    
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(var(--pulse-color), 0);
    }
  }
  
  animation: pulse 2s infinite;
`;

const Container = styled.div`
  display: inline-flex;
  align-items: center;
`;

const Indicator = styled.div<{
  status: StatusType;
  size: StatusSize;
  pulse: boolean;
}>`
  border-radius: 50%;
  width: ${({ size }) => sizeMap[size]};
  height: ${({ size }) => sizeMap[size]};
  
  ${({ status, theme, pulse }) => {
    let color;
    let pulseColorRGB;
    
    switch (status) {
      case 'success':
        color = theme.colors.success;
        pulseColorRGB = '76, 175, 80';
        break;
      case 'error':
        color = theme.colors.error;
        pulseColorRGB = '244, 67, 54';
        break;
      case 'warning':
        color = theme.colors.warning;
        pulseColorRGB = '255, 152, 0';
        break;
      case 'info':
        color = theme.colors.info;
        pulseColorRGB = '33, 150, 243';
        break;
      default: // 'neutral'
        color = theme.colors.textSecondary;
        pulseColorRGB = '158, 158, 158';
    }
    
    return css`
      background-color: ${color};
      ${pulse && css`
        --pulse-color: ${pulseColorRGB};
        ${pulseAnimation}
      `}
    `;
  }}
`;

const Label = styled.span<{
  status: StatusType;
  size: StatusSize;
}>`
  ${({ size }) => labelSizeMap[size]}
  
  ${({ status, theme }) => {
    let color;
    
    switch (status) {
      case 'success':
        color = theme.colors.success;
        break;
      case 'error':
        color = theme.colors.error;
        break;
      case 'warning':
        color = theme.colors.warning;
        break;
      case 'info':
        color = theme.colors.info;
        break;
      default: // 'neutral'
        color = theme.colors.textSecondary;
    }
    
    return css`
      color: ${color};
      font-weight: ${theme.fontWeights.medium};
    `;
  }}
`;

/**
 * Status Indicator Component
 *
 * A component for displaying status (success, error, warning, info).
 */
export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'medium',
  pulse = false,
  showLabel = false,
  label,
  className,
}) => {
  const statusLabel = label || status.charAt(0).toUpperCase() + status.slice(1);
  
  return (
    <Container className={className}>
      <Indicator status={status} size={size} pulse={pulse} />
      {showLabel && (
        <Label status={status} size={size}>
          {statusLabel}
        </Label>
      )}
    </Container>
  );
};
