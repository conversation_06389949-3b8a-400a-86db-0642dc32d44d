/**
 * Tag Component
 *
 * A customizable tag component for categorizing content.
 */
import React from 'react';
import styled, { css } from 'styled-components';

export type TagVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
export type TagSize = 'small' | 'medium' | 'large';

export interface TagProps {
  /** The content to display inside the tag */
  children: React.ReactNode;
  /** The variant of the tag */
  variant?: TagVariant;
  /** The size of the tag */
  size?: TagSize;
  /** Whether the tag is removable */
  removable?: boolean;
  /** Function called when the remove button is clicked */
  onRemove?: () => void;
  /** Additional CSS class names */
  className?: string;
  /** Optional click handler */
  onClick?: () => void;
}

// Size styles
const sizeStyles = {
  small: css`
    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};
    font-size: ${({ theme }) => theme.fontSizes.xs};
  `,
  medium: css`
    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};
    font-size: ${({ theme }) => theme.fontSizes.sm};
  `,
  large: css`
    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};
    font-size: ${({ theme }) => theme.fontSizes.md};
  `,
};

// Variant styles
const getVariantStyles = (variant: TagVariant) => {
  return css`
    ${({ theme }) => {
      // Get the appropriate colors based on the variant
      let bgColor, textColor, borderColor;
      
      switch (variant) {
        case 'primary':
          bgColor = `${theme.colors.primary}10`;
          textColor = theme.colors.primary;
          borderColor = `${theme.colors.primary}30`;
          break;
        case 'secondary':
          bgColor = `${theme.colors.secondary}10`;
          textColor = theme.colors.secondary;
          borderColor = `${theme.colors.secondary}30`;
          break;
        case 'success':
          bgColor = `${theme.colors.success}10`;
          textColor = theme.colors.success;
          borderColor = `${theme.colors.success}30`;
          break;
        case 'warning':
          bgColor = `${theme.colors.warning}10`;
          textColor = theme.colors.warning;
          borderColor = `${theme.colors.warning}30`;
          break;
        case 'error':
          bgColor = `${theme.colors.error}10`;
          textColor = theme.colors.error;
          borderColor = `${theme.colors.error}30`;
          break;
        case 'info':
          bgColor = `${theme.colors.info}10`;
          textColor = theme.colors.info;
          borderColor = `${theme.colors.info}30`;
          break;
        default: // 'default'
          bgColor = `${theme.colors.textSecondary}10`;
          textColor = theme.colors.textSecondary;
          borderColor = `${theme.colors.textSecondary}30`;
      }
      
      return `
        background-color: ${bgColor};
        color: ${textColor};
        border: 1px solid ${borderColor};
      `;
    }}
  `;
};

const StyledTag = styled.span<{
  variant: TagVariant;
  size: TagSize;
  clickable: boolean;
}>`
  display: inline-flex;
  align-items: center;
  border-radius: ${({ theme }) => theme.borderRadius.pill};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  
  /* Apply size styles */
  ${({ size }) => sizeStyles[size]}
  
  /* Apply variant styles */
  ${({ variant }) => getVariantStyles(variant)}
  
  /* Clickable styles */
  ${({ clickable }) => clickable && css`
    cursor: pointer;
    transition: opacity ${({ theme }) => theme.transitions.fast};
    
    &:hover {
      opacity: 0.8;
    }
    
    &:active {
      opacity: 0.6;
    }
  `}
`;

const RemoveButton = styled.button<{ size: TagSize }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  margin-left: ${({ theme }) => theme.spacing.xs};
  padding: 0;
  
  /* Size-specific styles */
  ${({ size, theme }) => {
    const sizeMap = {
      small: '12px',
      medium: '14px',
      large: '16px',
    };
    
    return `
      width: ${sizeMap[size]};
      height: ${sizeMap[size]};
      font-size: ${theme.fontSizes.xs};
    `;
  }}
  
  &:hover {
    opacity: 1;
  }
`;

/**
 * Tag Component
 *
 * A customizable tag component for categorizing content.
 */
export const Tag: React.FC<TagProps> = ({
  children,
  variant = 'default',
  size = 'medium',
  removable = false,
  onRemove,
  className,
  onClick,
}) => {
  const handleRemoveClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRemove?.();
  };
  
  return (
    <StyledTag
      variant={variant}
      size={size}
      clickable={!!onClick}
      className={className}
      onClick={onClick}
    >
      {children}
      {removable && (
        <RemoveButton size={size} onClick={handleRemoveClick}>
          ×
        </RemoveButton>
      )}
    </StyledTag>
  );
};
