import { <PERSON>a, StoryObj } from '@storybook/react';
import { Badge } from './Badge';
import { ThemeProvider } from '../../theme/ThemeProvider';

const meta: Meta<typeof Badge> = {
  title: 'Atoms/Badge',
  component: Badge,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <Story />
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: [
        'default',
        'primary',
        'secondary',
        'success',
        'warning',
        'error',
        'info',
        'neutral',
      ],
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    solid: {
      control: 'boolean',
    },
    rounded: {
      control: 'boolean',
    },
    dot: {
      control: 'boolean',
    },
    counter: {
      control: 'boolean',
    },
    outlined: {
      control: 'boolean',
    },
    inline: {
      control: 'boolean',
    },
    onClick: { action: 'clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof Badge>;

export const Default: Story = {
  args: {
    children: 'Default Badge',
    variant: 'default',
    size: 'medium',
    solid: false,
  },
};

export const Primary: Story = {
  args: {
    children: 'Primary Badge',
    variant: 'primary',
    size: 'medium',
    solid: false,
  },
};

export const Success: Story = {
  args: {
    children: 'Success Badge',
    variant: 'success',
    size: 'medium',
    solid: false,
  },
};

export const Warning: Story = {
  args: {
    children: 'Warning Badge',
    variant: 'warning',
    size: 'medium',
    solid: false,
  },
};

export const Error: Story = {
  args: {
    children: 'Error Badge',
    variant: 'error',
    size: 'medium',
    solid: false,
  },
};

export const Solid: Story = {
  args: {
    children: 'Solid Badge',
    variant: 'primary',
    size: 'medium',
    solid: true,
  },
};

export const Outlined: Story = {
  args: {
    children: 'Outlined Badge',
    variant: 'primary',
    size: 'medium',
    outlined: true,
  },
};

export const Rounded: Story = {
  args: {
    children: 'Rounded Badge',
    variant: 'primary',
    size: 'medium',
    rounded: true,
  },
};

export const Dot: Story = {
  args: {
    children: '',
    variant: 'error',
    dot: true,
  },
};

export const Counter: Story = {
  args: {
    children: 42,
    variant: 'primary',
    counter: true,
    solid: true,
    rounded: true,
  },
};

export const CounterWithMax: Story = {
  args: {
    children: 999,
    variant: 'primary',
    counter: true,
    solid: true,
    rounded: true,
    max: 99,
  },
};

export const WithIcons: Story = {
  args: {
    children: 'Badge with Icons',
    variant: 'primary',
    startIcon: <span>🔔</span>,
    endIcon: <span>✓</span>,
  },
};

export const Small: Story = {
  args: {
    children: 'Small Badge',
    variant: 'primary',
    size: 'small',
    solid: false,
  },
};

export const Large: Story = {
  args: {
    children: 'Large Badge',
    variant: 'primary',
    size: 'large',
    solid: false,
  },
};

export const Clickable: Story = {
  args: {
    children: 'Clickable Badge',
    variant: 'primary',
    size: 'medium',
    solid: false,
    onClick: () => alert('Badge clicked!'),
  },
};

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
      <Badge variant="default">Default</Badge>
      <Badge variant="primary">Primary</Badge>
      <Badge variant="secondary">Secondary</Badge>
      <Badge variant="success">Success</Badge>
      <Badge variant="warning">Warning</Badge>
      <Badge variant="error">Error</Badge>
      <Badge variant="info">Info</Badge>
      <Badge variant="neutral">Neutral</Badge>
    </div>
  ),
};

export const AllStyles: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }}>
        <Badge variant="primary">Default</Badge>
        <Badge variant="primary" solid>
          Solid
        </Badge>
        <Badge variant="primary" outlined>
          Outlined
        </Badge>
        <Badge variant="primary" rounded>
          Rounded
        </Badge>
        <Badge variant="primary" dot>
          {''}
        </Badge>
        <Badge variant="primary" counter solid rounded>
          42
        </Badge>
      </div>

      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }}>
        <Badge variant="primary" size="small">
          Small
        </Badge>
        <Badge variant="primary" size="medium">
          Medium
        </Badge>
        <Badge variant="primary" size="large">
          Large
        </Badge>
      </div>

      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }}>
        <Badge variant="primary" startIcon={<span>🔔</span>}>
          With Start Icon
        </Badge>
        <Badge variant="primary" endIcon={<span>✓</span>}>
          With End Icon
        </Badge>
        <Badge variant="primary" startIcon={<span>🔔</span>} endIcon={<span>✓</span>}>
          Both Icons
        </Badge>
      </div>
    </div>
  ),
};
