// React import removed as it's not needed
import { Meta, StoryObj } from '@storybook/react';
import { Tag } from './Tag';
import { ThemeProvider } from '../../theme/ThemeProvider';

const meta: Meta<typeof Tag> = {
  title: 'Atoms/Tag',
  component: Tag,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <Story />
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'primary', 'secondary', 'success', 'warning', 'error', 'info'],
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    removable: {
      control: 'boolean',
    },
    onClick: { action: 'clicked' },
    onRemove: { action: 'removed' },
  },
};

export default meta;
type Story = StoryObj<typeof Tag>;

export const Default: Story = {
  args: {
    children: 'Default Tag',
    variant: 'default',
    size: 'medium',
    removable: false,
  },
};

export const Primary: Story = {
  args: {
    children: 'Primary Tag',
    variant: 'primary',
    size: 'medium',
    removable: false,
  },
};

export const Success: Story = {
  args: {
    children: 'Success Tag',
    variant: 'success',
    size: 'medium',
    removable: false,
  },
};

export const Warning: Story = {
  args: {
    children: 'Warning Tag',
    variant: 'warning',
    size: 'medium',
    removable: false,
  },
};

export const Error: Story = {
  args: {
    children: 'Error Tag',
    variant: 'error',
    size: 'medium',
    removable: false,
  },
};

export const Removable: Story = {
  args: {
    children: 'Removable Tag',
    variant: 'primary',
    size: 'medium',
    removable: true,
    onRemove: () => console.log('Tag removed'),
  },
};

export const Small: Story = {
  args: {
    children: 'Small Tag',
    variant: 'primary',
    size: 'small',
    removable: false,
  },
};

export const Large: Story = {
  args: {
    children: 'Large Tag',
    variant: 'primary',
    size: 'large',
    removable: false,
  },
};

export const Clickable: Story = {
  args: {
    children: 'Clickable Tag',
    variant: 'primary',
    size: 'medium',
    removable: false,
    onClick: () => alert('Tag clicked!'),
  },
};

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
      <Tag variant="default">Default</Tag>
      <Tag variant="primary">Primary</Tag>
      <Tag variant="secondary">Secondary</Tag>
      <Tag variant="success">Success</Tag>
      <Tag variant="warning">Warning</Tag>
      <Tag variant="error">Error</Tag>
      <Tag variant="info">Info</Tag>
    </div>
  ),
};

export const RemovableTags: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
      <Tag variant="default" removable onRemove={() => console.log('Default removed')}>
        Default
      </Tag>
      <Tag variant="primary" removable onRemove={() => console.log('Primary removed')}>
        Primary
      </Tag>
      <Tag variant="secondary" removable onRemove={() => console.log('Secondary removed')}>
        Secondary
      </Tag>
      <Tag variant="success" removable onRemove={() => console.log('Success removed')}>
        Success
      </Tag>
      <Tag variant="warning" removable onRemove={() => console.log('Warning removed')}>
        Warning
      </Tag>
      <Tag variant="error" removable onRemove={() => console.log('Error removed')}>
        Error
      </Tag>
      <Tag variant="info" removable onRemove={() => console.log('Info removed')}>
        Info
      </Tag>
    </div>
  ),
};
