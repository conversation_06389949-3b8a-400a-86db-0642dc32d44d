import { useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { Input } from './Input';
import { ThemeProvider } from '../../theme/ThemeProvider';

const meta: Meta<typeof Input> = {
  title: 'Atoms/Input',
  component: Input,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div style={{ padding: '1rem', maxWidth: '400px' }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'password', 'email', 'number', 'tel', 'url'],
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    disabled: {
      control: 'boolean',
    },
    required: {
      control: 'boolean',
    },
    clearable: {
      control: 'boolean',
    },
    success: {
      control: 'boolean',
    },
    loading: {
      control: 'boolean',
    },
    fullWidth: {
      control: 'boolean',
    },
    showCharCount: {
      control: 'boolean',
    },
    onChange: { action: 'changed' },
    onClear: { action: 'cleared' },
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

// Controlled component wrapper for interactive stories
const ControlledInput = (args: any) => {
  const [value, setValue] = useState(args.value || '');
  return <Input {...args} value={value} onChange={setValue} />;
};

export const Default: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    placeholder: 'Enter text',
    value: '',
  },
};

export const WithLabel: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Username',
    placeholder: 'Enter username',
    value: '',
  },
};

export const WithHelperText: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Password',
    placeholder: 'Enter password',
    type: 'password',
    helperText: 'Password must be at least 8 characters',
    value: '',
  },
};

export const WithError: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    type: 'email',
    error: 'Please enter a valid email address',
    value: 'invalid-email',
  },
};

export const WithSuccess: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    type: 'email',
    success: true,
    value: '<EMAIL>',
  },
};

export const Disabled: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Disabled Input',
    placeholder: 'This input is disabled',
    disabled: true,
    value: '',
  },
};

export const Required: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Required Field',
    placeholder: 'This field is required',
    required: true,
    value: '',
  },
};

export const WithIcons: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Search',
    placeholder: 'Search...',
    startIcon: <span>🔍</span>,
    endIcon: <span>⌘K</span>,
    value: '',
  },
};

export const Clearable: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Clearable Input',
    placeholder: 'Type something...',
    clearable: true,
    value: 'This can be cleared',
  },
};

export const WithCharCount: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Tweet',
    placeholder: "What's happening?",
    maxLength: 280,
    showCharCount: true,
    value: 'This is a sample tweet with character count.',
  },
};

export const Small: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Small Input',
    placeholder: 'Small size',
    size: 'small',
    value: '',
  },
};

export const Large: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Large Input',
    placeholder: 'Large size',
    size: 'large',
    value: '',
  },
};

export const FullWidth: Story = {
  render: (args) => <ControlledInput {...args} />,
  args: {
    label: 'Full Width Input',
    placeholder: 'This input takes full width',
    fullWidth: true,
    value: '',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Input label="Default Input" placeholder="Default input" value="" onChange={() => {}} />
      <Input
        label="With Helper Text"
        placeholder="With helper text"
        helperText="This is a helper text"
        value=""
        onChange={() => {}}
      />
      <Input
        label="With Error"
        placeholder="With error"
        error="This field has an error"
        value="Invalid value"
        onChange={() => {}}
      />
      <Input
        label="With Success"
        placeholder="With success"
        success
        value="Valid value"
        onChange={() => {}}
      />
      <Input
        label="With Icons"
        placeholder="With icons"
        startIcon={<span>🔍</span>}
        endIcon={<span>⌘K</span>}
        value=""
        onChange={() => {}}
      />
    </div>
  ),
};
