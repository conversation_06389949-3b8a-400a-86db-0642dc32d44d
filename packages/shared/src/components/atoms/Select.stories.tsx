import { useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { Select } from './Select';
import { ThemeProvider } from '../../theme/ThemeProvider';

const meta: Meta<typeof Select> = {
  title: 'Atoms/Select',
  component: Select,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div style={{ padding: '1rem', maxWidth: '400px' }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    disabled: {
      control: 'boolean',
    },
    required: {
      control: 'boolean',
    },
    success: {
      control: 'boolean',
    },
    loading: {
      control: 'boolean',
    },
    fullWidth: {
      control: 'boolean',
    },
    onChange: { action: 'changed' },
  },
};

export default meta;
type Story = StoryObj<typeof Select>;

// Sample options
const simpleOptions = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
  { value: 'option4', label: 'Option 4' },
];

const groupedOptions = [
  { value: 'apple', label: 'Apple', group: 'Fruits' },
  { value: 'banana', label: 'Banana', group: 'Fruits' },
  { value: 'orange', label: 'Orange', group: 'Fruits' },
  { value: 'carrot', label: 'Carrot', group: 'Vegetables' },
  { value: 'potato', label: 'Potato', group: 'Vegetables' },
  { value: 'broccoli', label: 'Broccoli', group: 'Vegetables' },
  { value: 'chicken', label: 'Chicken', group: 'Meat' },
  { value: 'beef', label: 'Beef', group: 'Meat' },
  { value: 'pork', label: 'Pork', group: 'Meat' },
];

const optionsWithDisabled = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2', disabled: true },
  { value: 'option3', label: 'Option 3' },
  { value: 'option4', label: 'Option 4', disabled: true },
];

// Controlled component wrapper for interactive stories
const ControlledSelect = (args: any) => {
  const [value, setValue] = useState(args.value || '');
  return <Select {...args} value={value} onChange={setValue} />;
};

export const Default: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    options: simpleOptions,
    placeholder: 'Select an option',
    value: '',
  },
};

export const WithLabel: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Favorite Option',
    options: simpleOptions,
    placeholder: 'Select an option',
    value: '',
  },
};

export const WithHelperText: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Favorite Option',
    options: simpleOptions,
    placeholder: 'Select an option',
    helperText: 'Choose your favorite option',
    value: '',
  },
};

export const WithError: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Favorite Option',
    options: simpleOptions,
    placeholder: 'Select an option',
    error: 'Please select a valid option',
    value: '',
  },
};

export const WithSuccess: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Favorite Option',
    options: simpleOptions,
    success: true,
    value: 'option1',
  },
};

export const Disabled: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Disabled Select',
    options: simpleOptions,
    placeholder: 'This select is disabled',
    disabled: true,
    value: '',
  },
};

export const Required: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Required Field',
    options: simpleOptions,
    placeholder: 'This field is required',
    required: true,
    value: '',
  },
};

export const WithIcon: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Category',
    options: simpleOptions,
    placeholder: 'Select a category',
    startIcon: <span>🔍</span>,
    value: '',
  },
};

export const GroupedOptions: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Food Category',
    options: groupedOptions,
    placeholder: 'Select a food item',
    value: '',
  },
};

export const WithDisabledOptions: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Options',
    options: optionsWithDisabled,
    placeholder: 'Some options are disabled',
    value: '',
  },
};

export const Small: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Small Select',
    options: simpleOptions,
    placeholder: 'Small size',
    size: 'small',
    value: '',
  },
};

export const Large: Story = {
  render: (args) => <ControlledSelect {...args} />,
  args: {
    label: 'Large Select',
    options: simpleOptions,
    placeholder: 'Large size',
    size: 'large',
    value: '',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Select
        label="Default Select"
        options={simpleOptions}
        placeholder="Default select"
        value=""
        onChange={() => {}}
      />
      <Select
        label="With Helper Text"
        options={simpleOptions}
        placeholder="With helper text"
        helperText="This is a helper text"
        value=""
        onChange={() => {}}
      />
      <Select
        label="With Error"
        options={simpleOptions}
        placeholder="With error"
        error="This field has an error"
        value=""
        onChange={() => {}}
      />
      <Select
        label="With Success"
        options={simpleOptions}
        placeholder="With success"
        success
        value="option1"
        onChange={() => {}}
      />
      <Select
        label="With Icon"
        options={simpleOptions}
        placeholder="With icon"
        startIcon={<span>🔍</span>}
        value=""
        onChange={() => {}}
      />
    </div>
  ),
};
