// React import removed as it's not needed
import { Meta, StoryObj } from '@storybook/react';
import { DataCard } from './DataCard';
import { Button } from '../atoms/Button';
import { ThemeProvider } from '../../theme/ThemeProvider';

const meta: Meta<typeof DataCard> = {
  title: 'Organisms/DataCard',
  component: DataCard,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div style={{ padding: '1rem', maxWidth: '600px' }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    isLoading: {
      control: 'boolean',
    },
    hasError: {
      control: 'boolean',
    },
    isEmpty: {
      control: 'boolean',
    },
    showRetry: {
      control: 'boolean',
    },
    onRetry: { action: 'retry clicked' },
    onEmptyAction: { action: 'empty action clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof DataCard>;

export const Default: Story = {
  args: {
    title: 'Market Data',
    children: (
      <div>
        <p>This is a data card with sample content.</p>
        <ul>
          <li>S&P 500: 4,587.64 (+0.37%)</li>
          <li>Nasdaq: 14,346.02 (+0.54%)</li>
          <li>Dow Jones: 36,142.22 (+0.15%)</li>
        </ul>
      </div>
    ),
  },
};

export const WithActionButton: Story = {
  args: {
    title: 'Market Data',
    actionButton: <Button size="small">Refresh</Button>,
    children: (
      <div>
        <p>This data card has an action button in the header.</p>
        <ul>
          <li>S&P 500: 4,587.64 (+0.37%)</li>
          <li>Nasdaq: 14,346.02 (+0.54%)</li>
          <li>Dow Jones: 36,142.22 (+0.15%)</li>
        </ul>
      </div>
    ),
  },
};

export const Loading: Story = {
  args: {
    title: 'Market Data',
    isLoading: true,
    children: (
      <div>
        <p>This content will be hidden while loading.</p>
      </div>
    ),
  },
};

export const Error: Story = {
  args: {
    title: 'Market Data',
    hasError: true,
    errorMessage: 'Failed to fetch market data. Please try again.',
    showRetry: true,
    onRetry: () => console.log('Retry clicked'),
    children: (
      <div>
        <p>This content will be hidden when there's an error.</p>
      </div>
    ),
  },
};

export const Empty: Story = {
  args: {
    title: 'Market Data',
    isEmpty: true,
    emptyMessage: 'No market data available for the selected period.',
    emptyActionText: 'Change Filters',
    onEmptyAction: () => console.log('Empty action clicked'),
    children: (
      <div>
        <p>This content will be hidden when the data is empty.</p>
      </div>
    ),
  },
};

export const ErrorWithoutRetry: Story = {
  args: {
    title: 'Market Data',
    hasError: true,
    errorMessage: 'Failed to fetch market data.',
    showRetry: false,
    children: (
      <div>
        <p>This content will be hidden when there's an error.</p>
      </div>
    ),
  },
};

export const AllStates: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <DataCard title="Normal State">
        <p>This is the normal state of a data card.</p>
      </DataCard>

      <DataCard title="Loading State" isLoading>
        <p>This content is hidden while loading.</p>
      </DataCard>

      <DataCard
        title="Error State"
        hasError
        errorMessage="An error occurred while fetching data."
        showRetry
        onRetry={() => console.log('Retry clicked')}
      >
        <p>This content is hidden when there's an error.</p>
      </DataCard>

      <DataCard
        title="Empty State"
        isEmpty
        emptyMessage="No data available for the selected filters."
        emptyActionText="Clear Filters"
        onEmptyAction={() => console.log('Empty action clicked')}
      >
        <p>This content is hidden when the data is empty.</p>
      </DataCard>
    </div>
  ),
};
