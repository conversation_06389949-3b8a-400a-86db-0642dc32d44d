/**
 * DataCard Component
 *
 * A specialized card component for displaying data sections with loading and error states.
 */
import React from 'react';
import styled from 'styled-components';
import { Card, CardProps } from '../molecules/Card';
// Button import removed as it's not used
import { LoadingPlaceholder } from '../atoms/LoadingPlaceholder';
import { EmptyState } from '../molecules/EmptyState';

export interface DataCardProps extends Omit<CardProps, 'isLoading' | 'hasError' | 'errorMessage'> {
  /** The title of the data card */
  title: string;
  /** The content to display inside the data card */
  children: React.ReactNode;
  /** Whether the data is loading */
  isLoading?: boolean;
  /** Whether there was an error loading the data */
  hasError?: boolean;
  /** Error message to display */
  errorMessage?: string;
  /** Whether to show a retry button when there's an error */
  showRetry?: boolean;
  /** Function called when the retry button is clicked */
  onRetry?: () => void;
  /** Whether the data is empty */
  isEmpty?: boolean;
  /** Empty state message */
  emptyMessage?: string;
  /** Empty state action text */
  emptyActionText?: string;
  /** Function called when the empty state action button is clicked */
  onEmptyAction?: () => void;
  /** Action button to display in the header */
  actionButton?: React.ReactNode;
  /** Additional CSS class names */
  className?: string;
}

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;

/**
 * DataCard Component
 *
 * A specialized card component for displaying data sections with loading and error states.
 */
export const DataCard: React.FC<DataCardProps> = ({
  title,
  children,
  isLoading = false,
  hasError = false,
  errorMessage = 'An error occurred while loading data',
  showRetry = true,
  onRetry,
  isEmpty = false,
  emptyMessage = 'No data available',
  emptyActionText,
  onEmptyAction,
  actionButton,
  className,
  ...cardProps
}) => {
  // Create actions for the card header
  const headerActions = <HeaderActions>{actionButton}</HeaderActions>;

  // Determine what content to show based on state
  let content;

  if (isLoading) {
    content = <LoadingPlaceholder variant="card" text="Loading data..." />;
  } else if (hasError) {
    content = (
      <EmptyState
        title="Error"
        description={errorMessage}
        variant="compact"
        actionText={showRetry ? 'Retry' : undefined}
        onAction={showRetry ? onRetry : undefined}
      />
    );
  } else if (isEmpty) {
    content = (
      <EmptyState
        title="No Data"
        description={emptyMessage}
        variant="compact"
        actionText={emptyActionText}
        onAction={onEmptyAction}
      />
    );
  } else {
    content = children;
  }

  return (
    <Card title={title} actions={headerActions} className={className} {...cardProps}>
      {content}
    </Card>
  );
};
