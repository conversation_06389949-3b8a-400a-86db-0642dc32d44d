/**
 * Dashboard Template Stories
 */
// React import removed as it's not needed
import { Meta, StoryObj } from '@storybook/react';
import { DashboardTemplate } from './DashboardTemplate';
import { Card } from '../molecules/Card';
import { Button } from '../atoms/Button';

const meta: Meta<typeof DashboardTemplate> = {
  title: 'Templates/DashboardTemplate',
  component: DashboardTemplate,
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof DashboardTemplate>;

// Mock header component
const Header = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
    }}
  >
    <div style={{ fontWeight: 'bold', fontSize: '1.5rem' }}>ADHD Trading Dashboard</div>
    <div>
      <Button variant="text" size="small">
        Settings
      </Button>
      <Button variant="text" size="small">
        Profile
      </Button>
      <Button variant="text" size="small">
        Logout
      </Button>
    </div>
  </div>
);

// Mock sidebar component
const Sidebar = ({ collapsed }: { collapsed?: boolean }) => (
  <div style={{ padding: collapsed ? '8px' : '16px' }}>
    {!collapsed && <h3 style={{ marginBottom: '16px' }}>Navigation</h3>}
    <ul>
      <li style={{ marginBottom: '8px' }}>{collapsed ? '📊' : 'Dashboard'}</li>
      <li style={{ marginBottom: '8px' }}>{collapsed ? '📈' : 'Trading'}</li>
      <li style={{ marginBottom: '8px' }}>{collapsed ? '📝' : 'Journal'}</li>
      <li style={{ marginBottom: '8px' }}>{collapsed ? '⚙️' : 'Settings'}</li>
    </ul>
  </div>
);

// Mock content
const Content = () => (
  <div>
    <h1 style={{ marginBottom: '24px' }}>Dashboard</h1>
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '16px',
      }}
    >
      <Card title="Performance">
        <p>Your performance metrics will appear here.</p>
      </Card>
      <Card title="Recent Trades">
        <p>Your recent trades will appear here.</p>
      </Card>
      <Card title="Market Overview">
        <p>Market overview will appear here.</p>
      </Card>
      <Card title="Trading Plan">
        <p>Your trading plan will appear here.</p>
      </Card>
    </div>
  </div>
);

export const Default: Story = {
  args: {
    header: <Header />,
    sidebar: <Sidebar />,
    children: <Content />,
    sidebarCollapsed: false,
  },
};

export const CollapsedSidebar: Story = {
  args: {
    header: <Header />,
    sidebar: <Sidebar collapsed />,
    children: <Content />,
    sidebarCollapsed: true,
  },
};
