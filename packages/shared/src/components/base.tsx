/**
 * Base Components
 *
 * This file exports atomic components that can be used across the application.
 */
import React from "react";

// Button
export interface ButtonProps {
  /** The content to display inside the button */
  children: React.ReactNode;
  /** The variant of the button */
  variant?: "primary" | "secondary" | "outline" | "text";
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Whether the button is in a loading state */
  loading?: boolean;
  /** The size of the button */
  size?: "small" | "medium" | "large";
  /** Function called when the button is clicked */
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = "primary",
  disabled = false,
  loading = false,
  size = "medium",
  onClick,
}) => {
  // Implementation will come later
  return (
    <button
      className={`f1-button f1-button--${variant} f1-button--${size}`}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading ? "Loading..." : children}
    </button>
  );
};

// Card
export interface CardProps {
  /** The content to display inside the card */
  children: React.ReactNode;
  /** The title of the card */
  title?: string;
  /** Whether the card has a border */
  bordered?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  title,
  bordered = true,
}) => {
  // Implementation will come later
  return (
    <div className={`f1-card ${bordered ? "f1-card--bordered" : ""}`}>
      {title && <div className="f1-card__header">{title}</div>}
      <div className="f1-card__content">{children}</div>
    </div>
  );
};

// Input
export interface InputProps {
  /** The value of the input */
  value: string;
  /** Function called when the input value changes */
  onChange: (value: string) => void;
  /** The placeholder text */
  placeholder?: string;
  /** The label text */
  label?: string;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** The error message */
  error?: string;
}

export const Input: React.FC<InputProps> = ({
  value,
  onChange,
  placeholder,
  label,
  disabled = false,
  error,
}) => {
  // Implementation will come later
  return (
    <div className="f1-input-container">
      {label && <label className="f1-input__label">{label}</label>}
      <input
        className={`f1-input ${error ? "f1-input--error" : ""}`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
      />
      {error && <div className="f1-input__error">{error}</div>}
    </div>
  );
};

// Select
export interface SelectOption {
  /** The value of the option */
  value: string;
  /** The label to display for the option */
  label: string;
}

export interface SelectProps {
  /** The options to display */
  options: SelectOption[];
  /** The selected value */
  value: string;
  /** Function called when the selection changes */
  onChange: (value: string) => void;
  /** The label text */
  label?: string;
  /** Whether the select is disabled */
  disabled?: boolean;
  /** The error message */
  error?: string;
}

export const Select: React.FC<SelectProps> = ({
  options,
  value,
  onChange,
  label,
  disabled = false,
  error,
}) => {
  // Implementation will come later
  return (
    <div className="f1-select-container">
      {label && <label className="f1-select__label">{label}</label>}
      <select
        className={`f1-select ${error ? "f1-select--error" : ""}`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && <div className="f1-select__error">{error}</div>}
    </div>
  );
};
