/**
 * FormField Component
 *
 * A component that combines a label with an input, select, or other form control.
 */
import React from "react";
import styled from "styled-components";

export interface FormFieldProps {
  /** The form control to render (input, select, etc.) */
  children: React.ReactNode;
  /** The label text */
  label: string;
  /** Optional helper text */
  helperText?: string;
  /** Whether the field is required */
  required?: boolean;
  /** The error message */
  error?: string;
  /** Additional CSS class names */
  className?: string;
  /** The id of the form control */
  id?: string;
}

type FieldContainerProps = {
  children?: React.ReactNode;
};

const FieldContainer = styled.div<FieldContainerProps>`
  display: flex;
  flex-direction: column;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

// Define the props that Label will accept
type LabelProps = {
  hasError?: boolean;
  children?: React.ReactNode;
};

const Label = styled.label<LabelProps>`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
  color: ${({ theme, hasError }) =>
    hasError ? theme.colors.error : theme.colors.textPrimary};

  .required-indicator {
    color: ${({ theme }) => theme.colors.error};
    margin-left: ${({ theme }) => theme.spacing.xxs};
  }
`;

// Define the props that HelperText will accept
type HelperTextProps = {
  hasError?: boolean;
  children?: React.ReactNode;
};

const HelperText = styled.div<HelperTextProps>`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme, hasError }) =>
    hasError ? theme.colors.error : theme.colors.textSecondary};
  margin-top: ${({ theme }) => theme.spacing.xxs};
`;

/**
 * FormField Component
 *
 * A component that combines a label with an input, select, or other form control.
 */
export const FormField: React.FC<FormFieldProps> = ({
  children,
  label,
  helperText,
  required = false,
  error,
  className,
  id,
  ...rest
}) => {
  // Generate a unique ID if none is provided
  const fieldId = id || `field-${Math.random().toString(36).substr(2, 9)}`;

  // Clone the child element to pass the id
  const childElement = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        id: fieldId,
        required,
        error,
        ...child.props,
      });
    }
    return child;
  });

  return (
    <FieldContainer className={className} {...rest}>
      <Label htmlFor={fieldId} hasError={!!error}>
        {label}
        {required && <span className="required-indicator">*</span>}
      </Label>

      {childElement}

      {(helperText || error) && (
        <HelperText hasError={!!error}>{error || helperText}</HelperText>
      )}
    </FieldContainer>
  );
};
