/**
 * Unified Error Boundary
 *
 * A unified error boundary component that can be used at both the application level
 * and feature level, replacing the previous three-layer architecture.
 */
import React from 'react';
import { ErrorBoundary, ErrorBoundaryProps } from './ErrorBoundary';

export interface UnifiedErrorBoundaryProps extends Omit<ErrorBoundaryProps, 'isFeatureBoundary'> {
  /** Whether this is an application-level boundary */
  isAppLevel?: boolean;
  /** Whether this is a feature-level boundary */
  isFeatureBoundary?: boolean;
}

/**
 * Unified Error Boundary
 *
 * A wrapper around the base ErrorBoundary component that provides a simpler API
 * for common use cases.
 */
export const UnifiedErrorBoundary: React.FC<UnifiedErrorBoundaryProps> = ({
  isAppLevel,
  isFeatureBoundary,
  ...props
}) => {
  // Determine the boundary type based on props
  const boundaryType = isAppLevel ? 'app' : isFeatureBoundary ? 'feature' : 'component';

  // Set appropriate defaults based on boundary type
  const defaultProps: Partial<ErrorBoundaryProps> = {
    resetOnPropsChange: boundaryType !== 'app', // App-level boundaries should not reset on props change
    resetOnUnmount: boundaryType !== 'app', // App-level boundaries should not reset on unmount
    isFeatureBoundary: boundaryType === 'feature',
  };

  return <ErrorBoundary {...defaultProps} {...props} />;
};

/**
 * App Error Boundary
 *
 * A specialized error boundary for the application level.
 */
export const AppErrorBoundary: React.FC<Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'>> = (
  props
) => {
  return <UnifiedErrorBoundary isAppLevel {...props} />;
};

/**
 * Feature Error Boundary
 *
 * A specialized error boundary for feature modules.
 */
export const FeatureErrorBoundary: React.FC<
  Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'> & { featureName: string }
> = ({ featureName, ...props }) => {
  return <UnifiedErrorBoundary isFeatureBoundary name={featureName} {...props} />;
};

export default UnifiedErrorBoundary;
