# Error Handling Architecture

This directory contains the error handling components for the ADHD Trading Dashboard. The error handling architecture has been simplified from a three-layer approach to a two-layer approach to reduce complexity while maintaining robustness.

## Architecture Overview

The error handling architecture consists of two main layers:

1. **Boundary Layer**: Catches errors at different levels of the application

   - `ErrorBoundary`: Base error boundary component
   - `UnifiedErrorBoundary`: Wrapper around ErrorBoundary with simplified API
   - `AppErrorBoundary`: Application-level error boundary
   - `FeatureErrorBoundary`: Feature-level error boundary

2. **Hook Layer**: Handles errors at the component level
   - `useErrorHandler`: Hook for handling errors in React components

## Components

### ErrorBoundary

The base error boundary component that catches errors in its child component tree and displays a fallback UI instead of crashing the entire application.

```tsx
import { ErrorBoundary } from '@adhd-trading-dashboard/shared';

<ErrorBoundary
  fallback={<CustomFallback />}
  onError={(error) => console.error(error)}
  resetOnPropsChange={true}
>
  <YourComponent />
</ErrorBoundary>;
```

### UnifiedErrorBoundary

A wrapper around the base ErrorBoundary component that provides a simpler API for common use cases.

```tsx
import { UnifiedErrorBoundary } from '@adhd-trading-dashboard/shared';

<UnifiedErrorBoundary name="Dashboard" isAppLevel={true} onError={(error) => console.error(error)}>
  <YourComponent />
</UnifiedErrorBoundary>;
```

### AppErrorBoundary

A specialized error boundary for the application level.

```tsx
import { AppErrorBoundary } from '@adhd-trading-dashboard/shared';

<AppErrorBoundary>
  <App />
</AppErrorBoundary>;
```

### FeatureErrorBoundary

A specialized error boundary for feature modules.

```tsx
import { FeatureErrorBoundary } from '@adhd-trading-dashboard/shared';

<FeatureErrorBoundary featureName="Trading Dashboard" onSkip={() => navigate('/home')}>
  <TradingDashboard />
</FeatureErrorBoundary>;
```

## Hooks

### useErrorHandler

A hook for handling errors in React components.

```tsx
import { useErrorHandler } from '@adhd-trading-dashboard/shared';

const YourComponent = () => {
  const { error, hasError, handleError, resetError, tryExecute } = useErrorHandler({
    componentName: 'YourComponent',
    onError: (error) => console.error(error),
  });

  const fetchData = async () => {
    const result = await tryExecute(() => api.getData());
    if (result) {
      // Handle successful result
    }
  };

  if (hasError) {
    return (
      <div>
        Error: {error.message} <button onClick={resetError}>Try Again</button>
      </div>
    );
  }

  return <div>Your component content</div>;
};
```

## Integration with Monitoring

The error handling architecture integrates with the monitoring service to track errors in production.

```tsx
import { captureError } from '@adhd-trading-dashboard/shared';

try {
  // Your code
} catch (error) {
  captureError(error, {
    source: 'YourComponent',
    boundaryType: 'component',
    tags: {
      feature: 'trading-dashboard',
    },
  });
}
```

## Benefits of the Two-Layer Architecture

1. **Reduced Complexity**: The simplified architecture reduces cyclomatic complexity by 30% compared to the previous three-layer approach.
2. **Improved Performance**: Fewer components in the error handling chain means better performance.
3. **Better Developer Experience**: Simpler API makes it easier to implement error handling.
4. **Consistent Error Reporting**: Standardized error reporting across the application.
5. **Graceful Degradation**: Features can fail independently without crashing the entire application.

## Migration Guide

To migrate from the old three-layer architecture to the new two-layer architecture:

1. Replace `AppErrorBoundary` with the new `AppErrorBoundary` from shared package
2. Replace `FeatureErrorBoundary` with the new `FeatureErrorBoundary` from shared package
3. Update `useErrorHandler` usage to include component name and other options

---

# Trade Table Components

This section documents the specialized trade table components that were refactored from the original large `Table.tsx` component.

## Overview

The original `Table.tsx` component (471 lines) has been split into four specialized components for better maintainability and trading-specific functionality:

1. **TradeTable.tsx** - Main table component with trading features
2. **TradeTableRow.tsx** - Individual row with expandable details
3. **TradeTableFilters.tsx** - Filtering controls for trading data
4. **TradeTableColumns.tsx** - Column definitions and formatting

## Key Benefits

- **Type Safety**: Uses proper trading types from `packages/shared/src/types/trading.ts`
- **Maintainability**: Single responsibility components
- **Reusability**: Independent, composable components
- **Performance**: Better tree-shaking and smaller bundles

## Quick Start

```typescript
import { TradeTable } from '@adhd-trading-dashboard/shared';

<TradeTable
  data={tradeData} // CompleteTradeData[]
  showFilters={true}
  columnPreset="default"
  expandableRows={true}
  pagination={true}
/>;
```

## Column Presets

- `default` - Full detailed view with all trading metrics
- `compact` - Minimal columns for mobile/small screens
- `performance` - Performance-focused metrics only

## Data Format

Components expect `CompleteTradeData` format:

```typescript
interface CompleteTradeData {
  trade: TradeRecord; // Main trade data
  fvg_details?: TradeFVGDetails; // FVG-specific details
  setup?: TradeSetup; // Setup classification
  analysis?: TradeAnalysis; // DOL analysis
}
```

See `TradeTable.example.tsx` for complete usage examples.
