import { useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { Modal } from './Modal';
import { Button } from '../atoms/Button';
import { Input } from '../atoms/Input';
import { ThemeProvider } from '../../theme/ThemeProvider';

const meta: Meta<typeof Modal> = {
  title: 'Molecules/Modal',
  component: Modal,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <Story />
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['small', 'medium', 'large', 'fullscreen'],
    },
    closeOnOutsideClick: {
      control: 'boolean',
    },
    showCloseButton: {
      control: 'boolean',
    },
    hasFooter: {
      control: 'boolean',
    },
    primaryActionDisabled: {
      control: 'boolean',
    },
    primaryActionLoading: {
      control: 'boolean',
    },
    secondaryActionDisabled: {
      control: 'boolean',
    },
    centered: {
      control: 'boolean',
    },
    hasBackdrop: {
      control: 'boolean',
    },
    scrollable: {
      control: 'boolean',
    },
    onClose: { action: 'closed' },
    onPrimaryAction: { action: 'primary action clicked' },
    onSecondaryAction: { action: 'secondary action clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof Modal>;

// Wrapper component to control modal state
const ModalWrapper = (args: any) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>Open Modal</Button>
      <Modal
        {...args}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onPrimaryAction={() => {
          args.onPrimaryAction?.();
          if (!args.primaryActionLoading) {
            setIsOpen(false);
          }
        }}
        onSecondaryAction={() => {
          args.onSecondaryAction?.();
          setIsOpen(false);
        }}
      />
    </>
  );
};

export const Default: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Modal Title',
    children: <p>This is a basic modal with a title and content.</p>,
    size: 'medium',
    primaryActionText: 'Confirm',
    secondaryActionText: 'Cancel',
  },
};

export const WithForm: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Edit Profile',
    children: (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <Input label="Name" value="John Doe" onChange={() => {}} fullWidth />
        <Input label="Email" value="<EMAIL>" onChange={() => {}} fullWidth />
        <Input
          label="Bio"
          value="Frontend developer with a passion for UI/UX design."
          onChange={() => {}}
          fullWidth
        />
      </div>
    ),
    size: 'medium',
    primaryActionText: 'Save Changes',
    secondaryActionText: 'Cancel',
  },
};

export const Small: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Small Modal',
    children: <p>This is a small modal with minimal content.</p>,
    size: 'small',
    primaryActionText: 'OK',
  },
};

export const Large: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Large Modal',
    children: (
      <div>
        <p>This is a large modal with more content.</p>
        <p>
          It can fit more information and is suitable for complex forms or detailed information.
        </p>
        <p>The width is set to 800px by default.</p>
      </div>
    ),
    size: 'large',
    primaryActionText: 'Confirm',
    secondaryActionText: 'Cancel',
  },
};

export const WithoutFooter: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Information',
    children: (
      <div>
        <p>This modal doesn't have a footer with action buttons.</p>
        <p>It's useful for displaying information that doesn't require user action.</p>
      </div>
    ),
    hasFooter: false,
  },
};

export const WithCustomFooter: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Custom Footer',
    children: <p>This modal has a custom footer with multiple buttons.</p>,
    footer: (
      <div style={{ display: 'flex', gap: '8px', width: '100%', justifyContent: 'space-between' }}>
        <Button variant="outline" size="small">
          Back
        </Button>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button variant="outline">Cancel</Button>
          <Button variant="secondary">Save Draft</Button>
          <Button>Publish</Button>
        </div>
      </div>
    ),
  },
};

export const LoadingAction: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Processing',
    children: <p>Click the primary action button to see the loading state.</p>,
    primaryActionText: 'Submit',
    secondaryActionText: 'Cancel',
    primaryActionLoading: true,
  },
};

export const LongContent: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Scrollable Content',
    children: (
      <div>
        {Array.from({ length: 20 }).map((_, index) => (
          <p key={index}>
            This is paragraph {index + 1}. The modal has scrollable content when it exceeds the
            available height.
          </p>
        ))}
      </div>
    ),
    primaryActionText: 'Confirm',
    secondaryActionText: 'Cancel',
  },
};

export const Confirmation: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Confirm Action',
    children: <p>Are you sure you want to delete this item? This action cannot be undone.</p>,
    primaryActionText: 'Delete',
    secondaryActionText: 'Cancel',
    size: 'small',
  },
};

export const WithoutTitle: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    children: <p>This modal doesn't have a title, only a close button in the header.</p>,
    primaryActionText: 'Confirm',
    secondaryActionText: 'Cancel',
  },
};
