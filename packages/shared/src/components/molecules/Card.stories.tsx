// React import removed as it's not needed
import { Meta, StoryObj } from '@storybook/react';
import { Card } from './Card';
import { Button } from '../atoms/Button';
import { ThemeProvider } from '../../theme/ThemeProvider';

const meta: Meta<typeof Card> = {
  title: 'Molecules/Card',
  component: Card,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div style={{ padding: '1rem', maxWidth: '600px' }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'primary', 'secondary', 'outlined', 'elevated'],
    },
    padding: {
      control: 'select',
      options: ['none', 'small', 'medium', 'large'],
    },
    bordered: {
      control: 'boolean',
    },
    isLoading: {
      control: 'boolean',
    },
    hasError: {
      control: 'boolean',
    },
    clickable: {
      control: 'boolean',
    },
    onClick: { action: 'clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof Card>;

export const Default: Story = {
  args: {
    title: 'Card Title',
    children: <p>This is a basic card with a title and content.</p>,
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};

export const WithSubtitle: Story = {
  args: {
    title: 'Card Title',
    subtitle: 'Card Subtitle',
    children: <p>This card has both a title and a subtitle.</p>,
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};

export const WithActions: Story = {
  args: {
    title: 'Card with Actions',
    actions: (
      <>
        <Button variant="outline" size="small">
          Cancel
        </Button>
        <Button size="small">Save</Button>
      </>
    ),
    children: <p>This card has action buttons in the header.</p>,
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};

export const WithFooter: Story = {
  args: {
    title: 'Card with Footer',
    children: <p>This card has a footer section.</p>,
    footer: (
      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
        <Button variant="outline">Cancel</Button>
        <Button>Submit</Button>
      </div>
    ),
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};

export const Loading: Story = {
  args: {
    title: 'Loading Card',
    children: <p>This content will be hidden while loading.</p>,
    isLoading: true,
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};

export const Error: Story = {
  args: {
    title: 'Error Card',
    children: <p>This content will be shown after the error message.</p>,
    hasError: true,
    errorMessage: 'An error occurred while loading data.',
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};

export const Clickable: Story = {
  args: {
    title: 'Clickable Card',
    children: <p>Click on this card to trigger an action.</p>,
    clickable: true,
    onClick: () => alert('Card clicked!'),
    variant: 'default',
    padding: 'medium',
    bordered: true,
  },
};

export const Variants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Card title="Default Variant" variant="default">
        <p>Default card variant</p>
      </Card>
      <Card title="Primary Variant" variant="primary">
        <p>Primary card variant</p>
      </Card>
      <Card title="Secondary Variant" variant="secondary">
        <p>Secondary card variant</p>
      </Card>
      <Card title="Outlined Variant" variant="outlined">
        <p>Outlined card variant</p>
      </Card>
      <Card title="Elevated Variant" variant="elevated">
        <p>Elevated card variant</p>
      </Card>
    </div>
  ),
};
