/**
 * Trade Table Filters
 *
 * Filtering controls for trading data tables.
 */
import React from 'react';
import styled from 'styled-components';
import { Input } from '../atoms/Input';
import { Select } from '../atoms/Select';
import { Button } from '../atoms/Button';
import { TradeFilters } from '../../types/trading';

// Typed constants for field names
const TRADE_FILTER_FIELDS = {
  MODEL_TYPE: 'model_type' as const,
  WIN_LOSS: 'win_loss' as const,
  DATE_FROM: 'dateFrom' as const,
  DATE_TO: 'dateTo' as const,
  SESSION: 'session' as const,
  DIRECTION: 'direction' as const,
  MARKET: 'market' as const,
  MIN_R_MULTIPLE: 'min_r_multiple' as const,
  MAX_R_MULTIPLE: 'max_r_multiple' as const,
  MIN_PATTERN_QUALITY: 'min_pattern_quality' as const,
  MAX_PATTERN_QUALITY: 'max_pattern_quality' as const,
} as const;

export interface TradeTableFiltersProps {
  /** Current filter values */
  filters: TradeFilters;
  /** Function called when filters change */
  onFiltersChange: (filters: TradeFilters) => void;
  /** Function called when filters are reset */
  onReset?: () => void;
  /** Whether the filters are in a loading state */
  isLoading?: boolean;
  /** Whether to show advanced filters */
  showAdvanced?: boolean;
  /** Function to toggle advanced filters */
  onToggleAdvanced?: () => void;
}

const FiltersContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
  padding: ${({ theme }) => theme.spacing?.md || '16px'};
  background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;

const FilterRow = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.sm || '12px'};
  align-items: end;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
  min-width: 120px;
`;

const FilterLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;

const FilterActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.sm || '12px'};
  align-items: center;
  margin-left: auto;

  @media (max-width: 768px) {
    margin-left: 0;
    justify-content: flex-end;
  }
`;

const AdvancedFilters = styled.div<{ isVisible: boolean }>`
  display: ${({ isVisible }) => (isVisible ? 'flex' : 'none')};
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
  padding-top: ${({ theme }) => theme.spacing?.md || '16px'};
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;

const RangeInputGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
  align-items: center;
`;

const RangeLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;

/**
 * Trade Table Filters Component
 */
export const TradeTableFilters: React.FC<TradeTableFiltersProps> = ({
  filters,
  onFiltersChange,
  onReset,
  isLoading = false,
  showAdvanced = false,
  onToggleAdvanced,
}) => {
  const handleFilterChange = (key: keyof TradeFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleReset = () => {
    const resetFilters: TradeFilters = {};
    onFiltersChange(resetFilters);
    onReset?.();
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) => value !== undefined && value !== '' && value !== null
  );

  return (
    <FiltersContainer>
      <FilterRow>
        <FilterGroup>
          <FilterLabel>Date From</FilterLabel>
          <Input
            type="date"
            value={filters.dateFrom || ''}
            onChange={(e) => handleFilterChange(TRADE_FILTER_FIELDS.DATE_FROM, e.target.value)}
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Date To</FilterLabel>
          <Input
            type="date"
            value={filters.dateTo || ''}
            onChange={(e) => handleFilterChange(TRADE_FILTER_FIELDS.DATE_TO, e.target.value)}
            disabled={isLoading}
          />
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Model Type</FilterLabel>
          <Select
            value={filters.model_type || ''}
            onChange={(e) => handleFilterChange(TRADE_FILTER_FIELDS.MODEL_TYPE, e.target.value)}
            disabled={isLoading}
          >
            <option value="">All Models</option>
            <option value="RD-Cont">RD-Cont</option>
            <option value="FVG-RD">FVG-RD</option>
            <option value="True-RD">True-RD</option>
            <option value="IMM-RD">IMM-RD</option>
            <option value="Dispersed-RD">Dispersed-RD</option>
            <option value="Wide-Gap-RD">Wide-Gap-RD</option>
          </Select>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Session</FilterLabel>
          <Select
            value={filters.session || ''}
            onChange={(e) => handleFilterChange(TRADE_FILTER_FIELDS.SESSION, e.target.value)}
            disabled={isLoading}
          >
            <option value="">All Sessions</option>
            <option value="Pre-Market">Pre-Market</option>
            <option value="NY Open">NY Open</option>
            <option value="10:50-11:10">10:50-11:10</option>
            <option value="11:50-12:10">11:50-12:10</option>
            <option value="Lunch Macro">Lunch Macro</option>
            <option value="13:50-14:10">13:50-14:10</option>
            <option value="14:50-15:10">14:50-15:10</option>
            <option value="15:15-15:45">15:15-15:45</option>
            <option value="MOC">MOC</option>
            <option value="Post MOC">Post MOC</option>
          </Select>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Direction</FilterLabel>
          <Select
            value={filters.direction || ''}
            onChange={(e) =>
              handleFilterChange(
                TRADE_FILTER_FIELDS.DIRECTION,
                e.target.value as 'Long' | 'Short' | ''
              )
            }
            disabled={isLoading}
          >
            <option value="">All Directions</option>
            <option value="Long">Long</option>
            <option value="Short">Short</option>
          </Select>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel>Result</FilterLabel>
          <Select
            value={filters.win_loss || ''}
            onChange={(e) =>
              handleFilterChange(
                TRADE_FILTER_FIELDS.WIN_LOSS,
                e.target.value as 'Win' | 'Loss' | ''
              )
            }
            disabled={isLoading}
          >
            <option value="">All Results</option>
            <option value="Win">Win</option>
            <option value="Loss">Loss</option>
          </Select>
        </FilterGroup>

        <FilterActions>
          {onToggleAdvanced && (
            <Button variant="outline" size="small" onClick={onToggleAdvanced} disabled={isLoading}>
              {showAdvanced ? 'Hide' : 'Show'} Advanced
            </Button>
          )}

          <Button
            variant="outline"
            size="small"
            onClick={handleReset}
            disabled={isLoading || !hasActiveFilters}
          >
            Reset
          </Button>
        </FilterActions>
      </FilterRow>

      <AdvancedFilters isVisible={showAdvanced}>
        <FilterRow>
          <FilterGroup>
            <FilterLabel>Market</FilterLabel>
            <Select
              value={filters.market || ''}
              onChange={(e) => handleFilterChange(TRADE_FILTER_FIELDS.MARKET, e.target.value)}
              disabled={isLoading}
            >
              <option value="">All Markets</option>
              <option value="MNQ">MNQ</option>
              <option value="NQ">NQ</option>
              <option value="ES">ES</option>
              <option value="MES">MES</option>
              <option value="YM">YM</option>
              <option value="MYM">MYM</option>
            </Select>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>R Multiple Range</FilterLabel>
            <RangeInputGroup>
              <Input
                type="number"
                placeholder="Min"
                step="0.1"
                value={filters.min_r_multiple || ''}
                onChange={(e) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MIN_R_MULTIPLE,
                    e.target.value ? Number(e.target.value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
              <RangeLabel>to</RangeLabel>
              <Input
                type="number"
                placeholder="Max"
                step="0.1"
                value={filters.max_r_multiple || ''}
                onChange={(e) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MAX_R_MULTIPLE,
                    e.target.value ? Number(e.target.value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
            </RangeInputGroup>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Pattern Quality Range</FilterLabel>
            <RangeInputGroup>
              <Input
                type="number"
                placeholder="Min"
                min="1"
                max="5"
                step="0.1"
                value={filters.min_pattern_quality || ''}
                onChange={(e) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MIN_PATTERN_QUALITY,
                    e.target.value ? Number(e.target.value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
              <RangeLabel>to</RangeLabel>
              <Input
                type="number"
                placeholder="Max"
                min="1"
                max="5"
                step="0.1"
                value={filters.max_pattern_quality || ''}
                onChange={(e) =>
                  handleFilterChange(
                    TRADE_FILTER_FIELDS.MAX_PATTERN_QUALITY,
                    e.target.value ? Number(e.target.value) : undefined
                  )
                }
                disabled={isLoading}
                style={{ width: '80px' }}
              />
            </RangeInputGroup>
          </FilterGroup>
        </FilterRow>
      </AdvancedFilters>
    </FiltersContainer>
  );
};
