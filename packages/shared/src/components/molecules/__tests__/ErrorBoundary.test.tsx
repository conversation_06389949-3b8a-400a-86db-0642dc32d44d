/**
 * ErrorBoundary Tests
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '../../../theme';
import { ErrorBoundary } from '../ErrorBoundary';
import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest';

// Mock console.error to avoid test output noise
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = vi.fn();
});
afterAll(() => {
  console.error = originalConsoleError;
});

// Component that throws an error
const ErrorThrowingComponent = ({ shouldThrow = true }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('ErrorBoundary', () => {
  it('renders children when there is no error', () => {
    render(
      <ThemeProvider>
        <ErrorBoundary>
          <div data-testid="child">Child content</div>
        </ErrorBoundary>
      </ThemeProvider>
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
    expect(screen.getByText('Child content')).toBeInTheDocument();
  });

  it('renders the default fallback UI when an error occurs', () => {
    // Suppress React's error boundary warning in test output
    const spy = vi.spyOn(console, 'error');
    spy.mockImplementation(() => {});

    render(
      <ThemeProvider>
        <ErrorBoundary>
          <ErrorThrowingComponent />
        </ErrorBoundary>
      </ThemeProvider>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('Test error')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();

    spy.mockRestore();
  });

  it('renders a custom fallback when provided', () => {
    // Suppress React's error boundary warning in test output
    const spy = vi.spyOn(console, 'error');
    spy.mockImplementation(() => {});

    render(
      <ThemeProvider>
        <ErrorBoundary fallback={<div data-testid="custom-fallback">Custom error UI</div>}>
          <ErrorThrowingComponent />
        </ErrorBoundary>
      </ThemeProvider>
    );

    expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
    expect(screen.getByText('Custom error UI')).toBeInTheDocument();

    spy.mockRestore();
  });

  it('calls onError when an error occurs', () => {
    // Suppress React's error boundary warning in test output
    const spy = vi.spyOn(console, 'error');
    spy.mockImplementation(() => {});

    const onError = vi.fn();
    render(
      <ThemeProvider>
        <ErrorBoundary onError={onError}>
          <ErrorThrowingComponent />
        </ErrorBoundary>
      </ThemeProvider>
    );

    expect(onError).toHaveBeenCalledTimes(1);
    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({ message: 'Test error' }),
      expect.objectContaining({ componentStack: expect.any(String) })
    );

    spy.mockRestore();
  });
});
