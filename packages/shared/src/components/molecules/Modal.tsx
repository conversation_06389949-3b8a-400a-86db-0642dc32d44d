/**
 * Modal Component
 *
 * A customizable modal component that follows the design system.
 */
import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import styled, { css, keyframes } from 'styled-components';
import { Button } from '../atoms/Button';

export interface ModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** The title of the modal */
  title?: string;
  /** The content of the modal */
  children: React.ReactNode;
  /** Function called when the modal is closed */
  onClose: () => void;
  /** The size of the modal */
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  /** Whether the modal can be closed by clicking outside or pressing escape */
  closeOnOutsideClick?: boolean;
  /** Whether the modal has a close button */
  showCloseButton?: boolean;
  /** Custom footer content */
  footer?: React.ReactNode;
  /** Whether the modal has a footer */
  hasFooter?: boolean;
  /** Primary action button text */
  primaryActionText?: string;
  /** Function called when the primary action button is clicked */
  onPrimaryAction?: () => void;
  /** Whether the primary action button is disabled */
  primaryActionDisabled?: boolean;
  /** Whether the primary action button is loading */
  primaryActionLoading?: boolean;
  /** Secondary action button text */
  secondaryActionText?: string;
  /** Function called when the secondary action button is clicked */
  onSecondaryAction?: () => void;
  /** Whether the secondary action button is disabled */
  secondaryActionDisabled?: boolean;
  /** Additional CSS class names */
  className?: string;
  /** Z-index for the modal */
  zIndex?: number;
  /** Whether the modal is centered vertically */
  centered?: boolean;
  /** Whether the modal has a backdrop */
  hasBackdrop?: boolean;
  /** Whether the modal is scrollable */
  scrollable?: boolean;
}

const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const slideIn = keyframes`
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

const Backdrop = styled.div<{ zIndex?: number }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ zIndex }) => zIndex || 1000};
  animation: ${fadeIn} 0.2s ease-out;
`;

const ModalContainer = styled.div<{
  size: 'small' | 'medium' | 'large' | 'fullscreen';
  centered: boolean;
  scrollable: boolean;
}>`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  display: flex;
  flex-direction: column;
  max-height: ${({ size }) => (size === 'fullscreen' ? '100vh' : '90vh')};
  width: ${({ size }) => {
    switch (size) {
      case 'small':
        return '400px';
      case 'medium':
        return '600px';
      case 'large':
        return '800px';
      case 'fullscreen':
        return '100vw';
      default:
        return '600px';
    }
  }};
  max-width: 95vw;
  animation: ${slideIn} 0.2s ease-out;
  position: relative;

  ${({ size }) =>
    size === 'fullscreen' &&
    css`
      height: 100vh;
      border-radius: 0;
    `}

  ${({ centered }) =>
    centered &&
    css`
      margin: auto;
    `}
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => `${theme.spacing.md} ${theme.spacing.lg}`};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes.xl};
  color: ${({ theme }) => theme.colors.textSecondary};
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};

  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary}33;
  }
`;

const ModalContent = styled.div<{ scrollable: boolean }>`
  padding: ${({ theme }) => theme.spacing.lg};
  ${({ scrollable }) =>
    scrollable &&
    css`
      overflow-y: auto;
      flex: 1;
    `}
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => `${theme.spacing.md} ${theme.spacing.lg}`};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`;

/**
 * Modal Component
 *
 * A customizable modal component that follows the design system.
 */
export const Modal: React.FC<ModalProps> = ({
  isOpen,
  title,
  children,
  onClose,
  size = 'medium',
  closeOnOutsideClick = true,
  showCloseButton = true,
  footer,
  hasFooter = true,
  primaryActionText,
  onPrimaryAction,
  primaryActionDisabled = false,
  primaryActionLoading = false,
  secondaryActionText,
  onSecondaryAction,
  secondaryActionDisabled = false,
  className,
  zIndex = 1000,
  centered = true,
  // hasBackdrop = true, // Unused prop
  scrollable = true,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle escape key press
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && closeOnOutsideClick) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose, closeOnOutsideClick]);

  // Handle outside click
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (
      modalRef.current &&
      !modalRef.current.contains(event.target as Node) &&
      closeOnOutsideClick
    ) {
      onClose();
    }
  };

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Default footer with action buttons
  const defaultFooter = (
    <>
      {secondaryActionText && (
        <Button variant="outline" onClick={onSecondaryAction} disabled={secondaryActionDisabled}>
          {secondaryActionText}
        </Button>
      )}
      {primaryActionText && (
        <Button
          onClick={onPrimaryAction}
          disabled={primaryActionDisabled}
          loading={primaryActionLoading}
        >
          {primaryActionText}
        </Button>
      )}
    </>
  );

  if (!isOpen) return null;

  const modalContent = (
    <Backdrop onClick={handleBackdropClick} zIndex={zIndex}>
      <ModalContainer
        ref={modalRef}
        size={size}
        className={className}
        centered={centered}
        scrollable={scrollable}
        onClick={(e) => e.stopPropagation()}
      >
        {(title || showCloseButton) && (
          <ModalHeader>
            {title && <ModalTitle>{title}</ModalTitle>}
            {showCloseButton && (
              <CloseButton onClick={onClose} aria-label="Close">
                ×
              </CloseButton>
            )}
          </ModalHeader>
        )}

        <ModalContent scrollable={scrollable}>{children}</ModalContent>

        {hasFooter && (footer || primaryActionText || secondaryActionText) && (
          <ModalFooter>{footer || defaultFooter}</ModalFooter>
        )}
      </ModalContainer>
    </Backdrop>
  );

  // Use portal to render modal outside of the component hierarchy
  return createPortal(modalContent, document.body);
};
