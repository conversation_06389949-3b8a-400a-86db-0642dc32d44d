import { useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { Table } from './Table';
import { ThemeProvider } from '../../theme/ThemeProvider';
import { Button } from '../atoms/Button';

const meta: Meta<typeof Table> = {
  title: 'Molecules/Table',
  component: Table,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div style={{ padding: '1rem', maxWidth: '800px' }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    bordered: {
      control: 'boolean',
    },
    striped: {
      control: 'boolean',
    },
    hoverable: {
      control: 'boolean',
    },
    compact: {
      control: 'boolean',
    },
    stickyHeader: {
      control: 'boolean',
    },
    isLoading: {
      control: 'boolean',
    },
    pagination: {
      control: 'boolean',
    },
    scrollable: {
      control: 'boolean',
    },
    onRowClick: { action: 'row clicked' },
    onSort: { action: 'sort changed' },
    onPageChange: { action: 'page changed' },
    onPageSizeChange: { action: 'page size changed' },
  },
};

export default meta;
type Story = StoryObj<typeof Table>;

// Sample data
interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'pending';
  lastLogin: string;
}

const users: User[] = Array.from({ length: 50 }).map((_, index) => ({
  id: index + 1,
  name: `User ${index + 1}`,
  email: `user${index + 1}@example.com`,
  role: ['Admin', 'User', 'Editor', 'Viewer'][Math.floor(Math.random() * 4)],
  status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)] as
    | 'active'
    | 'inactive'
    | 'pending',
  lastLogin: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
    .toISOString()
    .split('T')[0],
}));

// Status badge component
const StatusBadge = ({ status }: { status: 'active' | 'inactive' | 'pending' }) => {
  const getColor = () => {
    switch (status) {
      case 'active':
        return { bg: '#e6f7e6', text: '#2e7d32' };
      case 'inactive':
        return { bg: '#ffeaea', text: '#d32f2f' };
      case 'pending':
        return { bg: '#fff8e1', text: '#f57c00' };
      default:
        return { bg: '#f5f5f5', text: '#757575' };
    }
  };

  const { bg, text } = getColor();

  return (
    <span
      style={{
        backgroundColor: bg,
        color: text,
        padding: '4px 8px',
        borderRadius: '4px',
        fontSize: '12px',
        fontWeight: 500,
        textTransform: 'capitalize',
      }}
    >
      {status}
    </span>
  );
};

// Table with pagination and sorting
const SortableTable = (args: any) => {
  const [sortColumn, setSortColumn] = useState<string>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    setSortColumn(columnId);
    setSortDirection(direction);
    args.onSort?.(columnId, direction);
  };

  const sortedData = [...users].sort((a, b) => {
    const aValue = a[sortColumn as keyof User];
    const bValue = b[sortColumn as keyof User];

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  return (
    <Table
      {...args}
      columns={[
        {
          id: 'id',
          header: 'ID',
          cell: (row: User) => row.id,
          sortable: true,
          width: '60px',
        },
        {
          id: 'name',
          header: 'Name',
          cell: (row: User) => row.name,
          sortable: true,
        },
        {
          id: 'email',
          header: 'Email',
          cell: (row: User) => row.email,
          sortable: true,
        },
        {
          id: 'role',
          header: 'Role',
          cell: (row: User) => row.role,
          sortable: true,
          width: '100px',
        },
        {
          id: 'status',
          header: 'Status',
          cell: (row: User) => <StatusBadge status={row.status} />,
          sortable: true,
          width: '100px',
          align: 'center',
        },
        {
          id: 'lastLogin',
          header: 'Last Login',
          cell: (row: User) => row.lastLogin,
          sortable: true,
          width: '120px',
        },
        {
          id: 'actions',
          header: 'Actions',
          cell: (_row: User) => (
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button size="small" variant="outline">
                Edit
              </Button>
              <Button size="small" variant="outline">
                Delete
              </Button>
            </div>
          ),
          width: '150px',
          align: 'right',
        },
      ]}
      data={sortedData}
      sortColumn={sortColumn}
      sortDirection={sortDirection}
      onSort={handleSort}
      pagination={args.pagination}
      currentPage={currentPage}
      pageSize={pageSize}
      totalRows={users.length}
      onPageChange={setCurrentPage}
      onPageSizeChange={setPageSize}
      onRowClick={(row, index) => {
        setSelectedRow(selectedRow === index ? null : index);
        args.onRowClick?.(row, index);
      }}
      isRowSelected={(_, index) => index === selectedRow}
    />
  );
};

export const Default: Story = {
  render: (args) => <SortableTable {...args} />,
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};

export const Loading: Story = {
  render: (args) => <SortableTable {...args} />,
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: true,
    pagination: true,
    scrollable: true,
  },
};

export const Compact: Story = {
  render: (args) => <SortableTable {...args} />,
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: true,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};

export const WithoutBorders: Story = {
  render: (args) => <SortableTable {...args} />,
  args: {
    bordered: false,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};

export const WithoutStripes: Story = {
  render: (args) => <SortableTable {...args} />,
  args: {
    bordered: true,
    striped: false,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: true,
    scrollable: true,
  },
};

export const WithStickyHeader: Story = {
  render: (args) => <SortableTable {...args} />,
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: true,
    isLoading: false,
    pagination: true,
    scrollable: true,
    height: '300px',
  },
};

export const WithoutPagination: Story = {
  render: (args) => <SortableTable {...args} />,
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: false,
    scrollable: true,
  },
};

export const EmptyTable: Story = {
  render: (args) => (
    <Table
      {...args}
      columns={[
        { id: 'id', header: 'ID', cell: (row: any) => row.id },
        { id: 'name', header: 'Name', cell: (row: any) => row.name },
        { id: 'email', header: 'Email', cell: (row: any) => row.email },
      ]}
      data={[]}
      emptyMessage="No users found"
    />
  ),
  args: {
    bordered: true,
    striped: true,
    hoverable: true,
    compact: false,
    stickyHeader: false,
    isLoading: false,
    pagination: false,
    scrollable: true,
  },
};
