/**
 * Table Component
 *
 * A customizable table component that follows the design system.
 *
 * @deprecated This component is being split into smaller, focused components.
 * Use TradeTable, TradeTableRow, TradeTableFilters, and TradeTableColumns instead.
 */
import React, { useMemo } from 'react';
import styled, { css } from 'styled-components';
import { Button } from '../atoms/Button';

export interface TableColumn<T extends Record<string, any>> {
  /** Unique identifier for the column */
  id: string;
  /** Header text for the column */
  header: React.ReactNode;
  /** Function to render the cell content */
  cell: (row: T, rowIndex: number) => React.ReactNode;
  /** Whether the column is sortable */
  sortable?: boolean;
  /** Width of the column (e.g., '100px', '20%') */
  width?: string;
  /** Whether the column is hidden */
  hidden?: boolean;
  /** Alignment of the column content */
  align?: 'left' | 'center' | 'right';
}

export interface TableProps<T extends Record<string, any>> {
  /** The columns configuration */
  columns: TableColumn<T>[];
  /** The data to display */
  data: T[];
  /** Whether the table is loading */
  isLoading?: boolean;
  /** Whether the table has a border */
  bordered?: boolean;
  /** Whether the table has striped rows */
  striped?: boolean;
  /** Whether the table has hoverable rows */
  hoverable?: boolean;
  /** Whether the table is compact */
  compact?: boolean;
  /** Whether the table has a sticky header */
  stickyHeader?: boolean;
  /** The height of the table (e.g., '400px') */
  height?: string;
  /** Function called when a row is clicked */
  onRowClick?: (row: T, index: number) => void;
  /** Function to determine if a row is selected */
  isRowSelected?: (row: T, index: number) => boolean;
  /** Function called when a sort header is clicked */
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
  /** The ID of the column to sort by */
  sortColumn?: string;
  /** The direction to sort */
  sortDirection?: 'asc' | 'desc';
  /** Whether the table has pagination */
  pagination?: boolean;
  /** The current page */
  currentPage?: number;
  /** The number of rows per page */
  pageSize?: number;
  /** The total number of rows */
  totalRows?: number;
  /** Function called when the page changes */
  onPageChange?: (page: number) => void;
  /** Function called when the page size changes */
  onPageSizeChange?: (pageSize: number) => void;
  /** Additional CSS class names */
  className?: string;
  /** Empty state message */
  emptyMessage?: string;
  /** Whether the table is scrollable horizontally */
  scrollable?: boolean;
}

const TableContainer = styled.div<{ height?: string; scrollable?: boolean }>`
  width: 100%;
  overflow: auto;
  ${({ height }) => height && `height: ${height};`}
  ${({ scrollable }) => scrollable && `overflow-x: auto;`}
`;

const StyledTable = styled.table<{
  bordered?: boolean;
  striped?: boolean;
  compact?: boolean;
}>`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};

  ${({ bordered, theme }) =>
    bordered &&
    css`
      border: 1px solid ${theme.colors.border};
      border-radius: ${theme.borderRadius.sm};
    `}

  ${({ compact, theme }) =>
    compact
      ? css`
          th,
          td {
            padding: ${theme.spacing.xs} ${theme.spacing.sm};
          }
        `
      : css`
          th,
          td {
            padding: ${theme.spacing.sm} ${theme.spacing.md};
          }
        `}
`;

const TableHeader = styled.thead<{ stickyHeader?: boolean }>`
  ${({ stickyHeader }) =>
    stickyHeader &&
    css`
      position: sticky;
      top: 0;
      z-index: 1;
    `}
`;

const TableHeaderRow = styled.tr`
  background-color: ${({ theme }) => theme.colors.background};
`;

const TableHeaderCell = styled.th<{
  sortable?: boolean;
  isSorted?: boolean;
  align?: 'left' | 'center' | 'right';
  width?: string;
}>`
  text-align: ${({ align }) => align || 'left'};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.textSecondary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  white-space: nowrap;
  ${({ width }) => width && `width: ${width};`}

  ${({ sortable }) =>
    sortable &&
    css`
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: ${({ theme }) => theme.colors.background}aa;
      }
    `}

  ${({ isSorted, theme }) =>
    isSorted &&
    css`
      color: ${theme.colors.primary};
    `}
`;

const SortIcon = styled.span<{ direction?: 'asc' | 'desc' }>`
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing.xs};

  &::after {
    content: '${({ direction }) => (direction === 'asc' ? '↑' : direction === 'desc' ? '↓' : '↕')}';
  }
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr<{
  hoverable?: boolean;
  striped?: boolean;
  isSelected?: boolean;
  isClickable?: boolean;
}>`
  ${({ striped, theme, isSelected }) =>
    striped &&
    !isSelected &&
    css`
      &:nth-child(even) {
        background-color: ${theme.colors.background}50;
      }
    `}

  ${({ hoverable, theme, isSelected }) =>
    hoverable &&
    !isSelected &&
    css`
      &:hover {
        background-color: ${theme.colors.background}aa;
      }
    `}

  ${({ isSelected, theme }) =>
    isSelected &&
    css`
      background-color: ${theme.colors.primary}15;
    `}

  ${({ isClickable }) =>
    isClickable &&
    css`
      cursor: pointer;
    `}
`;

const TableCell = styled.td<{
  align?: 'left' | 'center' | 'right';
}>`
  text-align: ${({ align }) => align || 'left'};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing.xl};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md} 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const PageInfo = styled.div`
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const PaginationControls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const PageSizeSelector = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-right: ${({ theme }) => theme.spacing.md};
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => `${theme.colors.background}80`};
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors.background};
  border-top: 3px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

/**
 * Table Component
 *
 * A customizable table component that follows the design system.
 */
export function Table<T extends Record<string, any>>({
  columns,
  data,
  isLoading = false,
  bordered = true,
  striped = true,
  hoverable = true,
  compact = false,
  stickyHeader = false,
  height,
  onRowClick,
  isRowSelected,
  onSort,
  sortColumn,
  sortDirection,
  pagination = false,
  currentPage = 1,
  pageSize = 10,
  totalRows = 0,
  onPageChange,
  onPageSizeChange,
  className,
  emptyMessage = 'No data available',
  scrollable = true,
}: TableProps<T>) {
  // Filter out hidden columns
  const visibleColumns = useMemo(() => columns.filter((col) => !col.hidden), [columns]);

  // Calculate pagination
  const totalPages = useMemo(() => Math.ceil(totalRows / pageSize), [totalRows, pageSize]);
  const paginatedData = useMemo(() => {
    if (!pagination) return data;

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // If we have totalRows, assume data is already paginated from the server
    if (totalRows > 0 && data.length <= pageSize) {
      return data;
    }

    return data.slice(startIndex, endIndex);
  }, [data, pagination, currentPage, pageSize, totalRows]);

  // Handle sort
  const handleSort = (columnId: string) => {
    if (!onSort) return;

    const newDirection = sortColumn === columnId && sortDirection === 'asc' ? 'desc' : 'asc';

    onSort(columnId, newDirection);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || !onPageChange) return;
    onPageChange(page);
  };

  return (
    <div style={{ position: 'relative' }}>
      {isLoading && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}

      <TableContainer height={height} scrollable={scrollable}>
        <StyledTable bordered={bordered} striped={striped} compact={compact} className={className}>
          <TableHeader stickyHeader={stickyHeader}>
            <TableHeaderRow>
              {visibleColumns.map((column) => (
                <TableHeaderCell
                  key={column.id}
                  sortable={column.sortable}
                  isSorted={sortColumn === column.id}
                  align={column.align}
                  width={column.width}
                  onClick={() => column.sortable && handleSort(column.id)}
                >
                  {column.header}
                  {column.sortable && (
                    <SortIcon direction={sortColumn === column.id ? sortDirection : undefined} />
                  )}
                </TableHeaderCell>
              ))}
            </TableHeaderRow>
          </TableHeader>

          <TableBody>
            {paginatedData.length > 0 ? (
              paginatedData.map((row, rowIndex) => (
                <TableRow
                  key={rowIndex}
                  hoverable={hoverable}
                  striped={striped}
                  isSelected={isRowSelected ? isRowSelected(row, rowIndex) : false}
                  isClickable={!!onRowClick}
                  onClick={() => onRowClick && onRowClick(row, rowIndex)}
                >
                  {visibleColumns.map((column) => (
                    <TableCell key={column.id} align={column.align}>
                      {column.cell(row, rowIndex)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <tr>
                <td colSpan={visibleColumns.length}>
                  <EmptyState>{emptyMessage}</EmptyState>
                </td>
              </tr>
            )}
          </TableBody>
        </StyledTable>
      </TableContainer>

      {pagination && totalPages > 0 && (
        <PaginationContainer>
          <PageInfo>
            Showing {Math.min((currentPage - 1) * pageSize + 1, totalRows)} to{' '}
            {Math.min(currentPage * pageSize, totalRows)} of {totalRows} entries
          </PageInfo>

          <div style={{ display: 'flex', alignItems: 'center' }}>
            {onPageSizeChange && (
              <PageSizeSelector>
                <span>Show</span>
                <select
                  value={pageSize}
                  onChange={(e) => onPageSizeChange(Number(e.target.value))}
                  style={{
                    padding: '4px 8px',
                    borderRadius: '4px',
                    border: '1px solid #ccc',
                  }}
                >
                  {[10, 25, 50, 100].map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
                <span>entries</span>
              </PageSizeSelector>
            )}

            <PaginationControls>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1}
              >
                First
              </Button>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Prev
              </Button>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages}
              >
                Last
              </Button>
            </PaginationControls>
          </div>
        </PaginationContainer>
      )}
    </div>
  );
}
