/**
 * Trade Table Example
 *
 * Example usage of the new trade table components.
 */
import React, { useState, useMemo } from 'react';
import { TradeTable } from './TradeTable';
import { CompleteTradeData, TradeFilters } from '../../services/tradeStorage';

// Mock data for demonstration
const mockTradeData: CompleteTradeData[] = [
  {
    trade: {
      id: 1,
      date: '2024-01-15',
      model_type: 'FVG-RD',
      session: 'NY Open',
      direction: 'Long',
      market: 'MNQ',
      entry_price: 16850.25,
      exit_price: 16875.50,
      r_multiple: 2.5,
      achieved_pl: 125.25,
      win_loss: 'Win',
      pattern_quality_rating: 4.2,
      entry_time: '09:35:00',
      exit_time: '10:15:00',
      notes: 'Clean FVG redelivery with strong momentum',
    },
    fvg_details: {
      trade_id: 1,
      rd_type: 'True-RD',
      entry_version: 'Simple-Entry',
      draw_on_liquidity: 'MNOR-FVG',
    },
    setup: {
      trade_id: 1,
      primary_setup: 'FVG Redelivery Setup',
      secondary_setup: 'High/Low Reversal Setup',
      liquidity_taken: 'Premarket-H/L',
    },
    analysis: {
      trade_id: 1,
      dol_target_type: 'FVG Target',
      path_quality: 'Clean',
      clustering: 'Low',
    },
  },
  {
    trade: {
      id: 2,
      date: '2024-01-15',
      model_type: 'RD-Cont',
      session: 'Lunch Macro',
      direction: 'Short',
      market: 'MNQ',
      entry_price: 16920.75,
      exit_price: 16905.25,
      r_multiple: -1.2,
      achieved_pl: -77.50,
      win_loss: 'Loss',
      pattern_quality_rating: 2.8,
      entry_time: '12:45:00',
      exit_time: '13:20:00',
      notes: 'Failed continuation, stopped out at resistance',
    },
    setup: {
      trade_id: 2,
      primary_setup: 'True-RD Continuation',
      liquidity_taken: 'Lunch-H/L',
    },
    analysis: {
      trade_id: 2,
      dol_target_type: 'Liquidity Target',
      path_quality: 'Choppy',
      clustering: 'High',
    },
  },
  {
    trade: {
      id: 3,
      date: '2024-01-16',
      model_type: 'FVG-RD',
      session: 'MOC',
      direction: 'Long',
      market: 'MNQ',
      entry_price: 16780.00,
      exit_price: 16825.75,
      r_multiple: 3.8,
      achieved_pl: 228.75,
      win_loss: 'Win',
      pattern_quality_rating: 4.8,
      entry_time: '15:50:00',
      exit_time: '16:00:00',
      notes: 'Perfect MOC setup with strong institutional flow',
    },
    fvg_details: {
      trade_id: 3,
      rd_type: 'IMM-RD',
      entry_version: 'Complex-Entry',
      draw_on_liquidity: 'AM-FPFVG',
    },
    setup: {
      trade_id: 3,
      primary_setup: 'Strong-FVG Reversal Setup',
      secondary_setup: 'Multi-Array Confluence Setup',
      liquidity_taken: 'Monthly-H/L',
    },
    analysis: {
      trade_id: 3,
      dol_target_type: 'FVG Target',
      path_quality: 'Excellent',
      clustering: 'Low',
    },
  },
];

/**
 * Basic Trade Table Example
 */
export const BasicTradeTableExample: React.FC = () => {
  const [sortColumn, setSortColumn] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedTrade, setSelectedTrade] = useState<CompleteTradeData | null>(null);

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    setSortColumn(columnId);
    setSortDirection(direction);
  };

  const handleRowClick = (trade: CompleteTradeData, index: number) => {
    setSelectedTrade(trade);
    console.log('Selected trade:', trade);
  };

  const isRowSelected = (trade: CompleteTradeData, index: number) => {
    return selectedTrade?.trade.id === trade.trade.id;
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Basic Trade Table</h2>
      <TradeTable
        data={mockTradeData}
        onSort={handleSort}
        sortColumn={sortColumn}
        sortDirection={sortDirection}
        onRowClick={handleRowClick}
        isRowSelected={isRowSelected}
        expandableRows={true}
      />
    </div>
  );
};

/**
 * Filtered Trade Table Example
 */
export const FilteredTradeTableExample: React.FC = () => {
  const [filters, setFilters] = useState<TradeFilters>({});
  const [sortColumn, setSortColumn] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Filter data based on current filters
  const filteredData = useMemo(() => {
    let filtered = [...mockTradeData];

    if (filters.dateFrom) {
      filtered = filtered.filter(trade => trade.trade.date >= filters.dateFrom!);
    }
    if (filters.dateTo) {
      filtered = filtered.filter(trade => trade.trade.date <= filters.dateTo!);
    }
    if (filters.model_type) {
      filtered = filtered.filter(trade => trade.trade.model_type === filters.model_type);
    }
    if (filters.session) {
      filtered = filtered.filter(trade => trade.trade.session === filters.session);
    }
    if (filters.direction) {
      filtered = filtered.filter(trade => trade.trade.direction === filters.direction);
    }
    if (filters.win_loss) {
      filtered = filtered.filter(trade => trade.trade.win_loss === filters.win_loss);
    }
    if (filters.min_r_multiple !== undefined) {
      filtered = filtered.filter(trade => 
        trade.trade.r_multiple !== undefined && trade.trade.r_multiple >= filters.min_r_multiple!
      );
    }
    if (filters.max_r_multiple !== undefined) {
      filtered = filtered.filter(trade => 
        trade.trade.r_multiple !== undefined && trade.trade.r_multiple <= filters.max_r_multiple!
      );
    }

    return filtered;
  }, [filters]);

  // Sort data
  const sortedData = useMemo(() => {
    const sorted = [...filteredData];
    
    sorted.sort((a, b) => {
      const aValue = a.trade[sortColumn as keyof typeof a.trade];
      const bValue = b.trade[sortColumn as keyof typeof b.trade];
      
      if (aValue === undefined || aValue === null) return 1;
      if (bValue === undefined || bValue === null) return -1;
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      return 0;
    });
    
    return sorted;
  }, [filteredData, sortColumn, sortDirection]);

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    setSortColumn(columnId);
    setSortDirection(direction);
  };

  const handleFiltersChange = (newFilters: TradeFilters) => {
    setFilters(newFilters);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Filtered Trade Table</h2>
      <TradeTable
        data={sortedData}
        showFilters={true}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSort={handleSort}
        sortColumn={sortColumn}
        sortDirection={sortDirection}
        expandableRows={true}
        pagination={true}
        pageSize={10}
        totalRows={sortedData.length}
      />
    </div>
  );
};

/**
 * Compact Trade Table Example
 */
export const CompactTradeTableExample: React.FC = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h2>Compact Trade Table</h2>
      <TradeTable
        data={mockTradeData}
        columnPreset="compact"
        compact={true}
        height="300px"
      />
    </div>
  );
};

/**
 * Performance Trade Table Example
 */
export const PerformanceTradeTableExample: React.FC = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h2>Performance-Focused Trade Table</h2>
      <TradeTable
        data={mockTradeData}
        columnPreset="performance"
        stickyHeader={true}
        height="400px"
      />
    </div>
  );
};

/**
 * All Examples Combined
 */
export const TradeTableExamples: React.FC = () => {
  const [activeExample, setActiveExample] = useState<string>('basic');

  const examples = {
    basic: <BasicTradeTableExample />,
    filtered: <FilteredTradeTableExample />,
    compact: <CompactTradeTableExample />,
    performance: <PerformanceTradeTableExample />,
  };

  return (
    <div>
      <div style={{ padding: '20px', borderBottom: '1px solid #e5e7eb' }}>
        <h1>Trade Table Examples</h1>
        <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
          {Object.keys(examples).map((key) => (
            <button
              key={key}
              onClick={() => setActiveExample(key)}
              style={{
                padding: '8px 16px',
                border: '1px solid #d1d5db',
                borderRadius: '4px',
                backgroundColor: activeExample === key ? '#3b82f6' : 'white',
                color: activeExample === key ? 'white' : 'black',
                cursor: 'pointer',
              }}
            >
              {key.charAt(0).toUpperCase() + key.slice(1)}
            </button>
          ))}
        </div>
      </div>
      
      {examples[activeExample as keyof typeof examples]}
    </div>
  );
};
