/**
 * Trade Table
 *
 * Main table component for displaying trading data with filtering and sorting.
 */
import React, { useMemo, useState } from 'react';
import styled, { css } from 'styled-components';
import { Button } from '../atoms/Button';
import { CompleteTradeData, TradeFilters } from '../../types/trading';
import {
  TableColumn,
  getTradeTableColumns,
  getCompactTradeTableColumns,
  getPerformanceTradeTableColumns,
} from './TradeTableColumns';
import { TradeTableRow } from './TradeTableRow';
import { TradeTableFilters } from './TradeTableFilters';

export interface TradeTableProps {
  /** The trade data to display */
  data: CompleteTradeData[];
  /** Whether the table is loading */
  isLoading?: boolean;
  /** Whether the table has a border */
  bordered?: boolean;
  /** Whether the table has striped rows */
  striped?: boolean;
  /** Whether the table has hoverable rows */
  hoverable?: boolean;
  /** Whether the table is compact */
  compact?: boolean;
  /** Whether the table has a sticky header */
  stickyHeader?: boolean;
  /** The height of the table (e.g., '400px') */
  height?: string;
  /** Function called when a row is clicked */
  onRowClick?: (trade: CompleteTradeData, index: number) => void;
  /** Function to determine if a row is selected */
  isRowSelected?: (trade: CompleteTradeData, index: number) => boolean;
  /** Function called when a sort header is clicked */
  onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
  /** The ID of the column to sort by */
  sortColumn?: string;
  /** The direction to sort */
  sortDirection?: 'asc' | 'desc';
  /** Whether the table has pagination */
  pagination?: boolean;
  /** The current page */
  currentPage?: number;
  /** The number of rows per page */
  pageSize?: number;
  /** The total number of rows */
  totalRows?: number;
  /** Function called when the page changes */
  onPageChange?: (page: number) => void;
  /** Function called when the page size changes */
  onPageSizeChange?: (pageSize: number) => void;
  /** Additional CSS class names */
  className?: string;
  /** Empty state message */
  emptyMessage?: string;
  /** Whether the table is scrollable horizontally */
  scrollable?: boolean;
  /** Whether to show filters */
  showFilters?: boolean;
  /** Current filter values */
  filters?: TradeFilters;
  /** Function called when filters change */
  onFiltersChange?: (filters: TradeFilters) => void;
  /** Column preset to use */
  columnPreset?: 'default' | 'compact' | 'performance';
  /** Custom columns (overrides preset) */
  customColumns?: TableColumn<CompleteTradeData>[];
  /** Whether rows are expandable */
  expandableRows?: boolean;
  /** Function to render expanded content */
  renderExpandedContent?: (trade: CompleteTradeData) => React.ReactNode;
}

const TableContainer = styled.div<{ height?: string; scrollable?: boolean }>`
  width: 100%;
  overflow: auto;
  ${({ height }) => height && `height: ${height};`}
  ${({ scrollable }) => scrollable && `overflow-x: auto;`}
`;

const StyledTable = styled.table<{
  bordered?: boolean;
  striped?: boolean;
  compact?: boolean;
}>`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};

  ${({ bordered, theme }) =>
    bordered &&
    css`
      border: 1px solid ${theme.colors?.border || '#e5e7eb'};
      border-radius: ${theme.borderRadius?.sm || '4px'};
    `}

  ${({ compact, theme }) =>
    compact
      ? css`
          th,
          td {
            padding: ${theme.spacing?.xs || '8px'} ${theme.spacing?.sm || '12px'};
          }
        `
      : css`
          th,
          td {
            padding: ${theme.spacing?.sm || '12px'} ${theme.spacing?.md || '16px'};
          }
        `}
`;

const TableHeader = styled.thead<{ stickyHeader?: boolean }>`
  ${({ stickyHeader }) =>
    stickyHeader &&
    css`
      position: sticky;
      top: 0;
      z-index: 1;
    `}
`;

const TableHeaderRow = styled.tr`
  background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'};
`;

const TableHeaderCell = styled.th<{
  sortable?: boolean;
  isSorted?: boolean;
  align?: 'left' | 'center' | 'right';
  width?: string;
}>`
  text-align: ${({ align }) => align || 'left'};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
  white-space: nowrap;
  ${({ width }) => width && `width: ${width};`}

  ${({ sortable }) =>
    sortable &&
    css`
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'}aa;
      }
    `}

  ${({ isSorted, theme }) =>
    isSorted &&
    css`
      color: ${theme.colors?.primary || '#3b82f6'};
    `}
`;

const SortIcon = styled.span<{ direction?: 'asc' | 'desc' }>`
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing?.xs || '8px'};

  &::after {
    content: '${({ direction }) => (direction === 'asc' ? '↑' : direction === 'desc' ? '↓' : '↕')}';
  }
`;

const TableBody = styled.tbody``;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing?.xl || '32px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => `${theme.colors?.background || '#ffffff'}80`};
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors?.background || '#f8f9fa'};
  border-top: 3px solid ${({ theme }) => theme.colors?.primary || '#3b82f6'};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.md || '16px'} 0;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
`;

const PageInfo = styled.div`
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;

const PaginationControls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
`;

/**
 * Trade Table Component
 */
export const TradeTable: React.FC<TradeTableProps> = ({
  data,
  isLoading = false,
  bordered = true,
  striped = true,
  hoverable = true,
  compact = false,
  stickyHeader = false,
  height,
  onRowClick,
  isRowSelected,
  onSort,
  sortColumn,
  sortDirection,
  pagination = false,
  currentPage = 1,
  pageSize = 10,
  totalRows = 0,
  onPageChange,
  onPageSizeChange,
  className,
  emptyMessage = 'No trades available',
  scrollable = true,
  showFilters = false,
  filters = {},
  onFiltersChange,
  columnPreset = 'default',
  customColumns,
  expandableRows = false,
  renderExpandedContent,
}) => {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Get columns based on preset or custom
  const columns = useMemo(() => {
    if (customColumns) return customColumns;

    switch (columnPreset) {
      case 'compact':
        return getCompactTradeTableColumns();
      case 'performance':
        return getPerformanceTradeTableColumns();
      default:
        return getTradeTableColumns();
    }
  }, [customColumns, columnPreset]);

  // Filter out hidden columns
  const visibleColumns = useMemo(() => columns.filter((col) => !col.hidden), [columns]);

  // Calculate pagination
  const totalPages = useMemo(() => Math.ceil(totalRows / pageSize), [totalRows, pageSize]);
  const paginatedData = useMemo(() => {
    if (!pagination) return data;

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // If we have totalRows, assume data is already paginated from the server
    if (totalRows > 0 && data.length <= pageSize) {
      return data;
    }

    return data.slice(startIndex, endIndex);
  }, [data, pagination, currentPage, pageSize, totalRows]);

  // Handle sort
  const handleSort = (columnId: string) => {
    if (!onSort) return;

    const newDirection = sortColumn === columnId && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(columnId, newDirection);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || !onPageChange) return;
    onPageChange(page);
  };

  return (
    <div>
      {showFilters && onFiltersChange && (
        <TradeTableFilters
          filters={filters}
          onFiltersChange={onFiltersChange}
          isLoading={isLoading}
          showAdvanced={showAdvancedFilters}
          onToggleAdvanced={() => setShowAdvancedFilters(!showAdvancedFilters)}
        />
      )}

      <div style={{ position: 'relative' }}>
        {isLoading && (
          <LoadingOverlay>
            <LoadingSpinner />
          </LoadingOverlay>
        )}

        <TableContainer height={height} scrollable={scrollable}>
          <StyledTable
            bordered={bordered}
            striped={striped}
            compact={compact}
            className={className}
          >
            <TableHeader stickyHeader={stickyHeader}>
              <TableHeaderRow>
                {expandableRows && (
                  <TableHeaderCell width="40px" align="center">
                    {/* Expand column header */}
                  </TableHeaderCell>
                )}

                {visibleColumns.map((column) => (
                  <TableHeaderCell
                    key={column.id}
                    sortable={column.sortable}
                    isSorted={sortColumn === column.id}
                    align={column.align}
                    width={column.width}
                    onClick={() => column.sortable && handleSort(column.id)}
                  >
                    {column.header}
                    {column.sortable && (
                      <SortIcon direction={sortColumn === column.id ? sortDirection : undefined} />
                    )}
                  </TableHeaderCell>
                ))}
              </TableHeaderRow>
            </TableHeader>

            <TableBody>
              {paginatedData.length > 0 ? (
                paginatedData.map((trade, index) => (
                  <TradeTableRow
                    key={trade.trade.id || index}
                    trade={trade}
                    index={index}
                    columns={visibleColumns}
                    isSelected={isRowSelected ? isRowSelected(trade, index) : false}
                    hoverable={hoverable}
                    striped={striped}
                    expandable={expandableRows}
                    onRowClick={onRowClick}
                    expandedContent={renderExpandedContent?.(trade)}
                  />
                ))
              ) : (
                <tr>
                  <td colSpan={visibleColumns.length + (expandableRows ? 1 : 0)}>
                    <EmptyState>{emptyMessage}</EmptyState>
                  </td>
                </tr>
              )}
            </TableBody>
          </StyledTable>
        </TableContainer>

        {pagination && totalPages > 0 && (
          <PaginationContainer>
            <PageInfo>
              Showing {Math.min((currentPage - 1) * pageSize + 1, totalRows)} to{' '}
              {Math.min(currentPage * pageSize, totalRows)} of {totalRows} entries
            </PageInfo>

            <PaginationControls>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1}
              >
                First
              </Button>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Prev
              </Button>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
              <Button
                size="small"
                variant="outline"
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages}
              >
                Last
              </Button>
            </PaginationControls>
          </PaginationContainer>
        )}
      </div>
    </div>
  );
};
