/**
 * Trade Table Row
 *
 * Individual row component for trading data tables.
 */
import React, { useState } from 'react';
import styled, { css } from 'styled-components';
import { CompleteTradeData } from '../../types/trading';
import { TableColumn } from './TradeTableColumns';

export interface TradeTableRowProps {
  /** The trade data for this row */
  trade: CompleteTradeData;
  /** The row index */
  index: number;
  /** Column definitions */
  columns: TableColumn<CompleteTradeData>[];
  /** Whether the row is selected */
  isSelected?: boolean;
  /** Whether the row is hoverable */
  hoverable?: boolean;
  /** Whether the table has striped rows */
  striped?: boolean;
  /** Whether the row is expandable */
  expandable?: boolean;
  /** Whether the row is expanded */
  isExpanded?: boolean;
  /** Function called when the row is clicked */
  onRowClick?: (trade: CompleteTradeData, index: number) => void;
  /** Function called when the row expand toggle is clicked */
  onToggleExpand?: (trade: CompleteTradeData, index: number) => void;
  /** Custom expanded content */
  expandedContent?: React.ReactNode;
}

const TableRow = styled.tr<{
  hoverable?: boolean;
  striped?: boolean;
  isSelected?: boolean;
  isClickable?: boolean;
  isExpanded?: boolean;
}>`
  ${({ striped, theme, isSelected }) =>
    striped &&
    !isSelected &&
    css`
      &:nth-child(even) {
        background-color: ${theme.colors?.background || '#f8f9fa'}50;
      }
    `}

  ${({ hoverable, theme, isSelected }) =>
    hoverable &&
    !isSelected &&
    css`
      &:hover {
        background-color: ${theme.colors?.background || '#f8f9fa'}aa;
      }
    `}

  ${({ isSelected, theme }) =>
    isSelected &&
    css`
      background-color: ${theme.colors?.primary || '#3b82f6'}15;
    `}

  ${({ isClickable }) =>
    isClickable &&
    css`
      cursor: pointer;
    `}

  ${({ isExpanded, theme }) =>
    isExpanded &&
    css`
      border-bottom: 2px solid ${theme.colors?.primary || '#3b82f6'};
    `}
`;

const TableCell = styled.td<{
  align?: 'left' | 'center' | 'right';
}>`
  text-align: ${({ align }) => align || 'left'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#111827'};
  padding: ${({ theme }) => theme.spacing?.sm || '12px'}
    ${({ theme }) => theme.spacing?.md || '16px'};
  vertical-align: middle;
`;

const ExpandedRow = styled.tr<{ isVisible: boolean }>`
  display: ${({ isVisible }) => (isVisible ? 'table-row' : 'none')};
`;

const ExpandedCell = styled.td`
  padding: 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;

const ExpandedContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.md || '16px'};
  background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'}30;
  border-left: 3px solid ${({ theme }) => theme.colors?.primary || '#3b82f6'};
`;

const ExpandButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing?.xs || '8px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'};
    color: ${({ theme }) => theme.colors?.primary || '#3b82f6'};
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors?.primary || '#3b82f6'};
    outline-offset: 2px;
  }
`;

const ExpandIcon = styled.span<{ isExpanded: boolean }>`
  display: inline-block;
  transition: transform 0.2s ease;
  transform: ${({ isExpanded }) => (isExpanded ? 'rotate(90deg)' : 'rotate(0deg)')};

  &::after {
    content: '▶';
  }
`;

const TradeDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
`;

const DetailGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
`;

const DetailLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;

const DetailValue = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#111827'};
`;

/**
 * Default expanded content for trade rows
 */
const DefaultExpandedContent: React.FC<{ trade: CompleteTradeData }> = ({ trade }) => (
  <TradeDetails>
    {trade.fvg_details && (
      <DetailGroup>
        <DetailLabel>FVG Details</DetailLabel>
        <DetailValue>Type: {trade.fvg_details.rd_type || '-'}</DetailValue>
        <DetailValue>Entry Version: {trade.fvg_details.entry_version || '-'}</DetailValue>
        <DetailValue>Draw on Liquidity: {trade.fvg_details.draw_on_liquidity || '-'}</DetailValue>
      </DetailGroup>
    )}

    {trade.setup && (
      <DetailGroup>
        <DetailLabel>Setup Classification</DetailLabel>
        <DetailValue>Primary: {trade.setup.primary_setup || '-'}</DetailValue>
        <DetailValue>Secondary: {trade.setup.secondary_setup || '-'}</DetailValue>
        <DetailValue>Liquidity: {trade.setup.liquidity_taken || '-'}</DetailValue>
      </DetailGroup>
    )}

    {trade.analysis && (
      <DetailGroup>
        <DetailLabel>Analysis</DetailLabel>
        <DetailValue>DOL Target: {trade.analysis.dol_target_type || '-'}</DetailValue>
        <DetailValue>Path Quality: {trade.analysis.path_quality || '-'}</DetailValue>
        <DetailValue>Clustering: {trade.analysis.clustering || '-'}</DetailValue>
      </DetailGroup>
    )}

    <DetailGroup>
      <DetailLabel>Timing</DetailLabel>
      <DetailValue>Entry: {trade.trade.entry_time || '-'}</DetailValue>
      <DetailValue>Exit: {trade.trade.exit_time || '-'}</DetailValue>
      <DetailValue>FVG: {trade.trade.fvg_time || '-'}</DetailValue>
      <DetailValue>RD: {trade.trade.rd_time || '-'}</DetailValue>
    </DetailGroup>

    {trade.trade.notes && (
      <DetailGroup style={{ gridColumn: '1 / -1' }}>
        <DetailLabel>Notes</DetailLabel>
        <DetailValue>{trade.trade.notes}</DetailValue>
      </DetailGroup>
    )}
  </TradeDetails>
);

/**
 * Trade Table Row Component
 */
export const TradeTableRow: React.FC<TradeTableRowProps> = ({
  trade,
  index,
  columns,
  isSelected = false,
  hoverable = true,
  striped = true,
  expandable = false,
  isExpanded = false,
  onRowClick,
  onToggleExpand,
  expandedContent,
}) => {
  const [localExpanded, setLocalExpanded] = useState(false);

  const expanded = isExpanded !== undefined ? isExpanded : localExpanded;

  const handleRowClick = (e: React.MouseEvent) => {
    // Don't trigger row click if clicking on expand button
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    onRowClick?.(trade, index);
  };

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onToggleExpand) {
      onToggleExpand(trade, index);
    } else {
      setLocalExpanded(!localExpanded);
    }
  };

  // Filter out hidden columns
  const visibleColumns = columns.filter((col) => !col.hidden);

  return (
    <>
      <TableRow
        hoverable={hoverable}
        striped={striped}
        isSelected={isSelected}
        isClickable={!!onRowClick}
        isExpanded={expanded}
        onClick={handleRowClick}
      >
        {expandable && (
          <TableCell align="center" style={{ width: '40px', padding: '8px' }}>
            <ExpandButton onClick={handleToggleExpand}>
              <ExpandIcon isExpanded={expanded} />
            </ExpandButton>
          </TableCell>
        )}

        {visibleColumns.map((column) => (
          <TableCell key={column.id} align={column.align}>
            {column.cell(trade, index)}
          </TableCell>
        ))}
      </TableRow>

      {expandable && (
        <ExpandedRow isVisible={expanded}>
          <ExpandedCell colSpan={visibleColumns.length + 1}>
            <ExpandedContent>
              {expandedContent || <DefaultExpandedContent trade={trade} />}
            </ExpandedContent>
          </ExpandedCell>
        </ExpandedRow>
      )}
    </>
  );
};
