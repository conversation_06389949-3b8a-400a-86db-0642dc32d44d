/**
 * Error Handler Hook
 *
 * A simplified hook for handling errors in React components.
 * This is part of the unified error handling approach.
 */
import { useState, useCallback, useEffect } from 'react';

export interface ErrorHandlerOptions {
  /** Name of the component for error reporting */
  componentName?: string;
  /** Whether to log errors to the console */
  logToConsole?: boolean;
  /** Whether to report errors to monitoring service */
  reportToMonitoring?: boolean;
  /** Custom error handler */
  onError?: (error: Error) => void;
}

/**
 * Error handler hook
 *
 * @param options - Options for the error handler
 * @returns An object with error state and functions to handle errors
 */
export function useErrorHandler(options: ErrorHandlerOptions = {}) {
  const { componentName, logToConsole = true, reportToMonitoring = true, onError } = options;

  const [error, setError] = useState<Error | null>(null);
  const [hasError, setHasError] = useState(false);

  /**
   * Handle an error
   * @param error - The error to handle
   */
  const handleError = useCallback(
    (error: Error) => {
      // Set the error state
      setError(error);
      setHasError(true);

      // Log to console if enabled
      if (logToConsole) {
        const context = componentName ? `[${componentName}]` : '';
        console.error(`Error caught by useErrorHandler${context}:`, error);
      }

      // Report to monitoring service if enabled
      if (reportToMonitoring) {
        // Here you could add Sentry integration
        // if (typeof window !== 'undefined' && window.Sentry) {
        //   window.Sentry.withScope((scope) => {
        //     if (componentName) {
        //       scope.setTag('component', componentName);
        //     }
        //     scope.setTag('handler', 'useErrorHandler');
        //     window.Sentry.captureException(error);
        //   });
        // }
      }

      // Call the custom error handler if provided
      if (onError) {
        onError(error);
      }
    },
    [componentName, logToConsole, reportToMonitoring, onError]
  );

  /**
   * Reset the error state
   */
  const resetError = useCallback(() => {
    setError(null);
    setHasError(false);
  }, []);

  /**
   * Try to execute a function and handle any errors
   * @param fn - The function to execute
   * @returns The result of the function
   */
  const tryExecute = useCallback(
    async <T>(fn: () => Promise<T> | T): Promise<T | undefined> => {
      try {
        return await fn();
      } catch (err) {
        handleError(err as Error);
        return undefined;
      }
    },
    [handleError]
  );

  // Clean up error state on unmount
  useEffect(() => {
    return () => {
      setError(null);
      setHasError(false);
    };
  }, []);

  return {
    error,
    hasError,
    handleError,
    resetError,
    tryExecute,
  };
}

export default useErrorHandler;
