/**
 * useAsyncData Hook
 *
 * A hook for handling async data fetching with loading, error, and data states.
 */
import { useState, useCallback, useEffect } from 'react';

export interface AsyncDataState<T> {
  /** The data returned from the async function */
  data: T | null;
  /** Whether the async function is currently loading */
  isLoading: boolean;
  /** Any error that occurred during the async function */
  error: Error | null;
  /** Whether the async function has been called at least once */
  isInitialized: boolean;
}

export interface UseAsyncDataOptions {
  /** Whether to fetch data immediately on mount */
  fetchOnMount?: boolean;
  /** Dependencies array for refetching when values change */
  dependencies?: any[];
}

/**
 * useAsyncData Hook
 *
 * @param asyncFn - The async function to call
 * @param options - Options for the hook
 * @returns An object with data, loading, and error states, plus a fetch function
 */
export function useAsyncData<T, P extends any[] = []>(
  asyncFn: (...params: P) => Promise<T>,
  options: UseAsyncDataOptions = {}
) {
  const { fetchOnMount = true, dependencies = [] } = options;

  const [state, setState] = useState<AsyncDataState<T>>({
    data: null,
    isLoading: false,
    error: null,
    isInitialized: false,
  });

  const fetchData = useCallback(
    async (...params: P) => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const data = await asyncFn(...params);
        setState({
          data,
          isLoading: false,
          error: null,
          isInitialized: true,
        });
        return data;
      } catch (error) {
        const errorObject = error instanceof Error ? error : new Error(String(error));
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorObject,
          isInitialized: true,
        }));
        throw errorObject;
      }
    },
    [asyncFn]
  );

  useEffect(() => {
    if (fetchOnMount) {
      fetchData(...([] as unknown as P));
    }
  }, [fetchOnMount, fetchData, ...dependencies]);

  return {
    ...state,
    fetchData,
    refetch: () => fetchData(...([] as unknown as P)),
  };
}
