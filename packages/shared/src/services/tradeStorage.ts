/**
 * Trade Storage Service
 *
 * This service provides methods for storing and retrieving trade data
 * using IndexedDB with the new schema structure.
 */

// Import all types from centralized location
import {
  PerformanceMetrics,
  TradeRecord,
  TradeFvgDetails,
  TradeSetup,
  TradeAnalysis,
  CompleteTradeData,
  TradeFilters,
  Trade, // Legacy interface for backward compatibility
} from '../types/trading';

// Typed constants for database field names
const TRADE_FIELDS = {
  MODEL_TYPE: 'model_type' as const,
  WIN_LOSS: 'win_loss' as const,
  R_MULTIPLE: 'r_multiple' as const,
  DATE: 'date' as const,
  SESSION: 'session' as const,
  DIRECTION: 'direction' as const,
  MARKET: 'market' as const,
  ACHIEVED_PL: 'achieved_pl' as const,
  PATTERN_QUALITY_RATING: 'pattern_quality_rating' as const,
} as const;

/**
 * Trade Storage Service
 */
class TradeStorageService {
  private dbName = 'adhd-trading-dashboard';
  private version = 2; // Increment version for schema changes
  private db: IDBDatabase | null = null;

  // Store names for different tables
  private stores = {
    trades: 'trades',
    fvg_details: 'trade_fvg_details',
    setups: 'trade_setups',
    analysis: 'trade_analysis',
    sessions: 'trading_sessions',
  };

  /**
   * Initialize the database with new schema
   * @returns A promise that resolves when the database is initialized
   */
  private async initDB(): Promise<IDBDatabase> {
    if (this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create trades store
        if (!db.objectStoreNames.contains(this.stores.trades)) {
          const tradesStore = db.createObjectStore(this.stores.trades, {
            keyPath: 'id',
            autoIncrement: true,
          });

          // Create indexes for performance
          tradesStore.createIndex(TRADE_FIELDS.DATE, TRADE_FIELDS.DATE, { unique: false });
          tradesStore.createIndex(TRADE_FIELDS.MODEL_TYPE, TRADE_FIELDS.MODEL_TYPE, {
            unique: false,
          });
          tradesStore.createIndex(TRADE_FIELDS.SESSION, TRADE_FIELDS.SESSION, { unique: false });
          tradesStore.createIndex(TRADE_FIELDS.WIN_LOSS, TRADE_FIELDS.WIN_LOSS, { unique: false });
          tradesStore.createIndex(TRADE_FIELDS.R_MULTIPLE, TRADE_FIELDS.R_MULTIPLE, {
            unique: false,
          });
        }

        // Create FVG details store
        if (!db.objectStoreNames.contains(this.stores.fvg_details)) {
          const fvgStore = db.createObjectStore(this.stores.fvg_details, {
            keyPath: 'id',
            autoIncrement: true,
          });
          fvgStore.createIndex('trade_id', 'trade_id', { unique: false });
        }

        // Create setups store
        if (!db.objectStoreNames.contains(this.stores.setups)) {
          const setupsStore = db.createObjectStore(this.stores.setups, {
            keyPath: 'id',
            autoIncrement: true,
          });
          setupsStore.createIndex('trade_id', 'trade_id', { unique: false });
        }

        // Create analysis store
        if (!db.objectStoreNames.contains(this.stores.analysis)) {
          const analysisStore = db.createObjectStore(this.stores.analysis, {
            keyPath: 'id',
            autoIncrement: true,
          });
          analysisStore.createIndex('trade_id', 'trade_id', { unique: false });
        }

        // Create sessions store
        if (!db.objectStoreNames.contains(this.stores.sessions)) {
          const sessionsStore = db.createObjectStore(this.stores.sessions, {
            keyPath: 'id',
            autoIncrement: true,
          });
          sessionsStore.createIndex('name', 'name', { unique: true });

          // Pre-populate with common sessions
          const commonSessions = [
            {
              name: 'Pre-Market',
              start_time: '04:00:00',
              end_time: '09:30:00',
              description: 'Pre-market trading hours',
            },
            {
              name: 'NY Open',
              start_time: '09:30:00',
              end_time: '10:30:00',
              description: 'New York opening hour',
            },
            {
              name: '10:50-11:10',
              start_time: '10:50:00',
              end_time: '11:10:00',
              description: 'Mid-morning macro window',
            },
            {
              name: '11:50-12:10',
              start_time: '11:50:00',
              end_time: '12:10:00',
              description: 'Pre-lunch macro window',
            },
            {
              name: 'Lunch Macro',
              start_time: '12:00:00',
              end_time: '13:30:00',
              description: 'Lunch time trading',
            },
            {
              name: '13:50-14:10',
              start_time: '13:50:00',
              end_time: '14:10:00',
              description: 'Post-lunch macro window',
            },
            {
              name: '14:50-15:10',
              start_time: '14:50:00',
              end_time: '15:10:00',
              description: 'Pre-close macro window',
            },
            {
              name: '15:15-15:45',
              start_time: '15:15:00',
              end_time: '15:45:00',
              description: 'Late afternoon window',
            },
            {
              name: 'MOC',
              start_time: '15:45:00',
              end_time: '16:00:00',
              description: 'Market on close',
            },
            {
              name: 'Post MOC',
              start_time: '16:00:00',
              end_time: '20:00:00',
              description: 'After hours trading',
            },
          ];

          // Add sessions after store is created
          request.transaction?.addEventListener('complete', () => {
            const transaction = db.transaction([this.stores.sessions], 'readwrite');
            const store = transaction.objectStore(this.stores.sessions);
            commonSessions.forEach((session) => store.add(session));
          });
        }
      };

      request.onsuccess = (event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
        resolve(this.db);
      };

      request.onerror = (event) => {
        console.error('Error opening IndexedDB:', event);
        reject(new Error('Failed to open IndexedDB'));
      };
    });
  }

  /**
   * Save a complete trade with all related details
   * @param tradeData Complete trade data including all related tables
   * @returns A promise that resolves with the saved trade ID
   */
  async saveTradeWithDetails(tradeData: CompleteTradeData): Promise<number> {
    try {
      const db = await this.initDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(
          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],
          'readwrite'
        );

        transaction.onerror = (event) => {
          console.error('Transaction error:', event);
          reject(new Error('Failed to save trade with details'));
        };

        // Save main trade record first
        const tradesStore = transaction.objectStore(this.stores.trades);
        const tradeRecord = {
          ...tradeData.trade,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        const tradeRequest = tradesStore.add(tradeRecord);

        tradeRequest.onsuccess = () => {
          const tradeId = tradeRequest.result as number;

          // Save related data with the trade ID
          const promises: Promise<void>[] = [];

          if (tradeData.fvg_details) {
            const fvgStore = transaction.objectStore(this.stores.fvg_details);
            const fvgData = { ...tradeData.fvg_details, trade_id: tradeId };
            promises.push(
              new Promise((res, rej) => {
                const req = fvgStore.add(fvgData);
                req.onsuccess = () => res();
                req.onerror = () => rej(new Error('Failed to save FVG details'));
              })
            );
          }

          if (tradeData.setup) {
            const setupStore = transaction.objectStore(this.stores.setups);
            const setupData = { ...tradeData.setup, trade_id: tradeId };
            promises.push(
              new Promise((res, rej) => {
                const req = setupStore.add(setupData);
                req.onsuccess = () => res();
                req.onerror = () => rej(new Error('Failed to save setup data'));
              })
            );
          }

          if (tradeData.analysis) {
            const analysisStore = transaction.objectStore(this.stores.analysis);
            const analysisData = { ...tradeData.analysis, trade_id: tradeId };
            promises.push(
              new Promise((res, rej) => {
                const req = analysisStore.add(analysisData);
                req.onsuccess = () => res();
                req.onerror = () => rej(new Error('Failed to save analysis data'));
              })
            );
          }

          transaction.oncomplete = () => {
            resolve(tradeId);
          };
        };

        tradeRequest.onerror = (event) => {
          console.error('Error saving trade:', event);
          reject(new Error('Failed to save trade'));
        };
      });
    } catch (error) {
      console.error('Error in saveTradeWithDetails:', error);
      throw new Error('Failed to save trade with details');
    }
  }

  /**
   * Get a complete trade by ID with all related data
   * @param id The ID of the trade to get
   * @returns A promise that resolves with the complete trade data
   */
  async getTradeById(id: number): Promise<CompleteTradeData | null> {
    try {
      const db = await this.initDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(
          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],
          'readonly'
        );

        const tradesStore = transaction.objectStore(this.stores.trades);
        const tradeRequest = tradesStore.get(id);

        tradeRequest.onsuccess = () => {
          const trade = tradeRequest.result;
          if (!trade) {
            resolve(null);
            return;
          }

          const result: CompleteTradeData = { trade };

          // Get FVG details
          const fvgStore = transaction.objectStore(this.stores.fvg_details);
          const fvgIndex = fvgStore.index('trade_id');
          const fvgRequest = fvgIndex.get(id);

          fvgRequest.onsuccess = () => {
            if (fvgRequest.result) {
              result.fvg_details = fvgRequest.result;
            }

            // Get setup data
            const setupStore = transaction.objectStore(this.stores.setups);
            const setupIndex = setupStore.index('trade_id');
            const setupRequest = setupIndex.get(id);

            setupRequest.onsuccess = () => {
              if (setupRequest.result) {
                result.setup = setupRequest.result;
              }

              // Get analysis data
              const analysisStore = transaction.objectStore(this.stores.analysis);
              const analysisIndex = analysisStore.index('trade_id');
              const analysisRequest = analysisIndex.get(id);

              analysisRequest.onsuccess = () => {
                if (analysisRequest.result) {
                  result.analysis = analysisRequest.result;
                }
                resolve(result);
              };

              analysisRequest.onerror = (event) => {
                console.error('Error getting analysis data:', event);
                resolve(result); // Return partial data
              };
            };

            setupRequest.onerror = (event) => {
              console.error('Error getting setup data:', event);
              resolve(result); // Return partial data
            };
          };

          fvgRequest.onerror = (event) => {
            console.error('Error getting FVG details:', event);
            resolve(result); // Return partial data
          };
        };

        tradeRequest.onerror = (event) => {
          console.error('Error getting trade:', event);
          reject(new Error('Failed to get trade'));
        };
      });
    } catch (error) {
      console.error('Error in getTradeById:', error);
      return null;
    }
  }

  /**
   * Get performance metrics from all trades
   * @returns A promise that resolves with performance metrics
   */
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      const db = await this.initDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.stores.trades], 'readonly');
        const store = transaction.objectStore(this.stores.trades);
        const request = store.getAll();

        request.onsuccess = () => {
          const trades: TradeRecord[] = request.result;

          if (trades.length === 0) {
            resolve({
              totalTrades: 0,
              winningTrades: 0,
              losingTrades: 0,
              winRate: 0,
              profitFactor: 0,
              averageWin: 0,
              averageLoss: 0,
              largestWin: 0,
              largestLoss: 0,
              totalPnl: 0,
              maxDrawdown: 0,
              maxDrawdownPercent: 0,
              sharpeRatio: 0,
              sortinoRatio: 0,
              calmarRatio: 0,
              averageRMultiple: 0,
              expectancy: 0,
              sqn: 0,
              period: 'all',
              startDate: '',
              endDate: '',
            });
            return;
          }

          // Calculate basic metrics
          const totalTrades = trades.length;
          const winningTrades = trades.filter((t) => t[TRADE_FIELDS.WIN_LOSS] === 'Win').length;
          const losingTrades = trades.filter((t) => t[TRADE_FIELDS.WIN_LOSS] === 'Loss').length;
          const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

          // Calculate P&L metrics
          const pnlValues = trades
            .filter((t) => t.achieved_pl !== undefined)
            .map((t) => t.achieved_pl!);
          const totalPnl = pnlValues.reduce((sum, pnl) => sum + pnl, 0);

          const wins = pnlValues.filter((pnl) => pnl > 0);
          const losses = pnlValues.filter((pnl) => pnl < 0);

          const averageWin =
            wins.length > 0 ? wins.reduce((sum, win) => sum + win, 0) / wins.length : 0;
          const averageLoss =
            losses.length > 0
              ? Math.abs(losses.reduce((sum, loss) => sum + loss, 0) / losses.length)
              : 0;
          const largestWin = wins.length > 0 ? Math.max(...wins) : 0;
          const largestLoss = losses.length > 0 ? Math.abs(Math.min(...losses)) : 0;

          const grossProfit = wins.reduce((sum, win) => sum + win, 0);
          const grossLoss = Math.abs(losses.reduce((sum, loss) => sum + loss, 0));
          const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : 0;

          // Calculate R-multiple metrics
          const rMultiples = trades
            .filter((t) => t[TRADE_FIELDS.R_MULTIPLE] !== undefined)
            .map((t) => t[TRADE_FIELDS.R_MULTIPLE]!);
          const averageRMultiple =
            rMultiples.length > 0
              ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length
              : 0;
          const expectancy = averageRMultiple * (winRate / 100);

          // Calculate drawdown (simplified)
          let runningPnl = 0;
          let peak = 0;
          let maxDrawdown = 0;

          for (const trade of trades) {
            if (trade.achieved_pl !== undefined) {
              runningPnl += trade.achieved_pl;
              if (runningPnl > peak) {
                peak = runningPnl;
              }
              const drawdown = peak - runningPnl;
              if (drawdown > maxDrawdown) {
                maxDrawdown = drawdown;
              }
            }
          }

          const maxDrawdownPercent = peak > 0 ? (maxDrawdown / peak) * 100 : 0;

          // Calculate SQN (System Quality Number)
          const sqn =
            rMultiples.length > 0
              ? (Math.sqrt(rMultiples.length) * averageRMultiple) /
                Math.sqrt(
                  rMultiples.reduce((sum, r) => sum + Math.pow(r - averageRMultiple, 2), 0) /
                    rMultiples.length
                )
              : 0;

          // Get date range
          const dates = trades.map((t) => t.date).sort();
          const startDate = dates.length > 0 ? dates[0] : '';
          const endDate = dates.length > 0 ? dates[dates.length - 1] : '';

          resolve({
            totalTrades,
            winningTrades,
            losingTrades,
            winRate,
            profitFactor,
            averageWin,
            averageLoss,
            largestWin,
            largestLoss,
            totalPnl,
            maxDrawdown,
            maxDrawdownPercent,
            sharpeRatio: 0, // Would need daily returns to calculate
            sortinoRatio: 0, // Would need daily returns to calculate
            calmarRatio: 0, // Would need daily returns to calculate
            averageRMultiple,
            expectancy,
            sqn,
            period: 'all',
            startDate,
            endDate,
          });
        };

        request.onerror = (event) => {
          console.error('Error getting performance metrics:', event);
          reject(new Error('Failed to get performance metrics'));
        };
      });
    } catch (error) {
      console.error('Error in getPerformanceMetrics:', error);
      throw new Error('Failed to get performance metrics');
    }
  }

  /**
   * Filter trades based on criteria
   * @param filters The filter criteria
   * @returns A promise that resolves with filtered trades
   */
  async filterTrades(filters: TradeFilters): Promise<CompleteTradeData[]> {
    try {
      const db = await this.initDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(
          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],
          'readonly'
        );

        const tradesStore = transaction.objectStore(this.stores.trades);
        const request = tradesStore.getAll();

        request.onsuccess = async () => {
          let trades: TradeRecord[] = request.result;

          // Apply filters
          if (filters.dateFrom) {
            trades = trades.filter((t) => t.date >= filters.dateFrom!);
          }
          if (filters.dateTo) {
            trades = trades.filter((t) => t.date <= filters.dateTo!);
          }
          if (filters.model_type) {
            trades = trades.filter((t) => t[TRADE_FIELDS.MODEL_TYPE] === filters.model_type);
          }
          if (filters.session) {
            trades = trades.filter((t) => t[TRADE_FIELDS.SESSION] === filters.session);
          }
          if (filters.direction) {
            trades = trades.filter((t) => t[TRADE_FIELDS.DIRECTION] === filters.direction);
          }
          if (filters.win_loss) {
            trades = trades.filter((t) => t[TRADE_FIELDS.WIN_LOSS] === filters.win_loss);
          }
          if (filters.market) {
            trades = trades.filter((t) => t[TRADE_FIELDS.MARKET] === filters.market);
          }
          if (filters.min_r_multiple !== undefined) {
            trades = trades.filter(
              (t) =>
                t[TRADE_FIELDS.R_MULTIPLE] !== undefined &&
                t[TRADE_FIELDS.R_MULTIPLE]! >= filters.min_r_multiple!
            );
          }
          if (filters.max_r_multiple !== undefined) {
            trades = trades.filter(
              (t) =>
                t[TRADE_FIELDS.R_MULTIPLE] !== undefined &&
                t[TRADE_FIELDS.R_MULTIPLE]! <= filters.max_r_multiple!
            );
          }
          if (filters.min_pattern_quality !== undefined) {
            trades = trades.filter(
              (t) =>
                t[TRADE_FIELDS.PATTERN_QUALITY_RATING] !== undefined &&
                t[TRADE_FIELDS.PATTERN_QUALITY_RATING]! >= filters.min_pattern_quality!
            );
          }
          if (filters.max_pattern_quality !== undefined) {
            trades = trades.filter(
              (t) =>
                t[TRADE_FIELDS.PATTERN_QUALITY_RATING] !== undefined &&
                t[TRADE_FIELDS.PATTERN_QUALITY_RATING]! <= filters.max_pattern_quality!
            );
          }

          // Get related data for each filtered trade
          const results: CompleteTradeData[] = [];

          for (const trade of trades) {
            const completeData: CompleteTradeData = { trade };

            // Get FVG details
            const fvgStore = transaction.objectStore(this.stores.fvg_details);
            const fvgIndex = fvgStore.index('trade_id');
            const fvgRequest = fvgIndex.get(trade.id!);

            await new Promise<void>((res) => {
              fvgRequest.onsuccess = () => {
                if (fvgRequest.result) {
                  completeData.fvg_details = fvgRequest.result;
                }
                res();
              };
              fvgRequest.onerror = () => res();
            });

            // Get setup data
            const setupStore = transaction.objectStore(this.stores.setups);
            const setupIndex = setupStore.index('trade_id');
            const setupRequest = setupIndex.get(trade.id!);

            await new Promise<void>((res) => {
              setupRequest.onsuccess = () => {
                if (setupRequest.result) {
                  completeData.setup = setupRequest.result;
                }
                res();
              };
              setupRequest.onerror = () => res();
            });

            // Get analysis data
            const analysisStore = transaction.objectStore(this.stores.analysis);
            const analysisIndex = analysisStore.index('trade_id');
            const analysisRequest = analysisIndex.get(trade.id!);

            await new Promise<void>((res) => {
              analysisRequest.onsuccess = () => {
                if (analysisRequest.result) {
                  completeData.analysis = analysisRequest.result;
                }
                res();
              };
              analysisRequest.onerror = () => res();
            });

            results.push(completeData);
          }

          resolve(results);
        };

        request.onerror = (event) => {
          console.error('Error filtering trades:', event);
          reject(new Error('Failed to filter trades'));
        };
      });
    } catch (error) {
      console.error('Error in filterTrades:', error);
      throw new Error('Failed to filter trades');
    }
  }

  /**
   * Get all trades (simplified version for backward compatibility)
   * @returns A promise that resolves with all trades
   */
  async getAllTrades(): Promise<CompleteTradeData[]> {
    try {
      return await this.filterTrades({});
    } catch (error) {
      console.error('Error in getAllTrades:', error);
      return [];
    }
  }

  /**
   * Delete a trade and all related data
   * @param id The ID of the trade to delete
   * @returns A promise that resolves when the trade is deleted
   */
  async deleteTrade(id: number): Promise<void> {
    try {
      const db = await this.initDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(
          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],
          'readwrite'
        );

        transaction.onerror = (event) => {
          console.error('Transaction error:', event);
          reject(new Error('Failed to delete trade'));
        };

        // Delete from all related tables (foreign key cascade simulation)
        const fvgStore = transaction.objectStore(this.stores.fvg_details);
        const fvgIndex = fvgStore.index('trade_id');
        const fvgRequest = fvgIndex.openCursor(IDBKeyRange.only(id));

        fvgRequest.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          }
        };

        const setupStore = transaction.objectStore(this.stores.setups);
        const setupIndex = setupStore.index('trade_id');
        const setupRequest = setupIndex.openCursor(IDBKeyRange.only(id));

        setupRequest.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          }
        };

        const analysisStore = transaction.objectStore(this.stores.analysis);
        const analysisIndex = analysisStore.index('trade_id');
        const analysisRequest = analysisIndex.openCursor(IDBKeyRange.only(id));

        analysisRequest.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          }
        };

        // Delete main trade record
        const tradesStore = transaction.objectStore(this.stores.trades);
        const tradeRequest = tradesStore.delete(id);

        transaction.oncomplete = () => {
          resolve();
        };

        tradeRequest.onerror = (event) => {
          console.error('Error deleting trade:', event);
          reject(new Error('Failed to delete trade'));
        };
      });
    } catch (error) {
      console.error('Error in deleteTrade:', error);
      throw new Error('Failed to delete trade');
    }
  }

  /**
   * Update a trade with all related data
   * @param id The trade ID to update
   * @param tradeData Updated trade data
   * @returns A promise that resolves when the trade is updated
   */
  async updateTradeWithDetails(id: number, tradeData: CompleteTradeData): Promise<void> {
    try {
      const db = await this.initDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(
          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],
          'readwrite'
        );

        transaction.onerror = (event) => {
          console.error('Transaction error:', event);
          reject(new Error('Failed to update trade'));
        };

        // Update main trade record
        const tradesStore = transaction.objectStore(this.stores.trades);
        const updatedTrade = {
          ...tradeData.trade,
          id,
          updated_at: new Date().toISOString(),
        };

        const tradeRequest = tradesStore.put(updatedTrade);

        tradeRequest.onsuccess = () => {
          // Update or create related data
          if (tradeData.fvg_details) {
            const fvgStore = transaction.objectStore(this.stores.fvg_details);
            const fvgData = { ...tradeData.fvg_details, trade_id: id };
            fvgStore.put(fvgData);
          }

          if (tradeData.setup) {
            const setupStore = transaction.objectStore(this.stores.setups);
            const setupData = { ...tradeData.setup, trade_id: id };
            setupStore.put(setupData);
          }

          if (tradeData.analysis) {
            const analysisStore = transaction.objectStore(this.stores.analysis);
            const analysisData = { ...tradeData.analysis, trade_id: id };
            analysisStore.put(analysisData);
          }
        };

        transaction.oncomplete = () => {
          resolve();
        };

        tradeRequest.onerror = (event) => {
          console.error('Error updating trade:', event);
          reject(new Error('Failed to update trade'));
        };
      });
    } catch (error) {
      console.error('Error in updateTradeWithDetails:', error);
      throw new Error('Failed to update trade');
    }
  }
}

// Create a singleton instance
export const tradeStorage = new TradeStorageService();

// Export the singleton as tradeStorageService for consistency
export const tradeStorageService = tradeStorage;

export default tradeStorage;
