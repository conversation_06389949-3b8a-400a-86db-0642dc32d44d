/**
 * Create Selector
 *
 * A utility for creating memoized selectors.
 */

/**
 * Selector function type
 */
export type Selector<S, R> = (state: S) => R;

/**
 * Input selector function type
 */
export type InputSelector<S, R> = Selector<S, R>;

/**
 * Result function type
 */
export type ResultFunc<R, T> = (...args: R[]) => T;

/**
 * Create a memoized selector
 * 
 * @param inputSelectors - The input selectors
 * @param resultFunc - The result function
 * @returns A memoized selector
 */
export function createSelector<S, R1, T>(
  inputSelector1: InputSelector<S, R1>,
  resultFunc: (res1: R1) => T
): Selector<S, T>;

export function createSelector<S, R1, R2, T>(
  inputSelector1: InputSelector<S, R1>,
  inputSelector2: InputSelector<S, R2>,
  resultFunc: (res1: R1, res2: R2) => T
): Selector<S, T>;

export function createSelector<S, R1, R2, R3, T>(
  inputSelector1: InputSelector<S, R1>,
  inputSelector2: InputSelector<S, R2>,
  inputSelector3: InputSelector<S, R3>,
  resultFunc: (res1: R1, res2: R2, res3: R3) => T
): Selector<S, T>;

export function createSelector<S, R1, R2, R3, R4, T>(
  inputSelector1: InputSelector<S, R1>,
  inputSelector2: InputSelector<S, R2>,
  inputSelector3: InputSelector<S, R3>,
  inputSelector4: InputSelector<S, R4>,
  resultFunc: (res1: R1, res2: R2, res3: R3, res4: R4) => T
): Selector<S, T>;

export function createSelector<S, R1, R2, R3, R4, R5, T>(
  inputSelector1: InputSelector<S, R1>,
  inputSelector2: InputSelector<S, R2>,
  inputSelector3: InputSelector<S, R3>,
  inputSelector4: InputSelector<S, R4>,
  inputSelector5: InputSelector<S, R5>,
  resultFunc: (res1: R1, res2: R2, res3: R3, res4: R4, res5: R5) => T
): Selector<S, T>;

export function createSelector<S, R1, R2, R3, R4, R5, R6, T>(
  inputSelector1: InputSelector<S, R1>,
  inputSelector2: InputSelector<S, R2>,
  inputSelector3: InputSelector<S, R3>,
  inputSelector4: InputSelector<S, R4>,
  inputSelector5: InputSelector<S, R5>,
  inputSelector6: InputSelector<S, R6>,
  resultFunc: (res1: R1, res2: R2, res3: R3, res4: R4, res5: R5, res6: R6) => T
): Selector<S, T>;

export function createSelector<S, T>(
  ...args: Array<InputSelector<S, any> | ResultFunc<any, T>>
): Selector<S, T> {
  const resultFunc = args.pop() as ResultFunc<any, T>;
  const inputSelectors = args as Array<InputSelector<S, any>>;

  let lastInputs: any[] | null = null;
  let lastResult: T | null = null;

  return (state: S) => {
    const inputs = inputSelectors.map(selector => selector(state));
    
    // Check if inputs have changed
    if (
      lastInputs === null ||
      inputs.length !== lastInputs.length ||
      inputs.some((input, index) => input !== lastInputs![index])
    ) {
      // Apply the result function to the new inputs
      lastResult = resultFunc(...inputs);
      lastInputs = inputs;
    }
    
    return lastResult as T;
  };
}
