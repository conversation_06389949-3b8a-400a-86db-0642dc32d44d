/**
 * Create Store Context
 *
 * A utility for creating a typed context store with actions and selectors.
 */
import React, { createContext, useContext, useReducer, useMemo, ReactNode } from 'react';

/**
 * Action with type and payload
 */
export interface Action<T = string, P = any> {
  type: T;
  payload?: P;
}

/**
 * Reducer function type
 */
export type Reducer<S, A extends Action> = (state: S, action: A) => S;

/**
 * Selector function type
 */
export type Selector<S, R> = (state: S) => R;

/**
 * Action creator function type
 */
export type ActionCreator<A extends Action> = (...args: any[]) => A;

/**
 * Dispatch function type
 */
export type Dispatch<A extends Action> = (action: A) => void;

/**
 * Store context type
 */
export interface StoreContext<S, A extends Action> {
  state: S;
  dispatch: Dispatch<A>;
}

/**
 * Store provider props
 */
export interface StoreProviderProps {
  children: ReactNode;
  initialState?: any;
}

/**
 * Create a store context with a reducer
 * 
 * @param reducer - The reducer function
 * @param initialState - The initial state
 * @param displayName - The display name for the context
 * @returns An object with the context, provider, and hooks
 */
export function createStoreContext<S, A extends Action>(
  reducer: Reducer<S, A>,
  initialState: S,
  displayName = 'StoreContext'
) {
  // Create the context
  const Context = createContext<StoreContext<S, A> | undefined>(undefined);
  Context.displayName = displayName;

  // Create the provider
  const Provider: React.FC<StoreProviderProps> = ({ 
    children, 
    initialState: overrideInitialState 
  }) => {
    const [state, dispatch] = useReducer(reducer, overrideInitialState || initialState);
    const value = useMemo(() => ({ state, dispatch }), [state]);
    return <Context.Provider value={value}>{children}</Context.Provider>;
  };

  // Create the hook to use the context
  function useStore(): StoreContext<S, A> {
    const context = useContext(Context);
    if (context === undefined) {
      throw new Error(`use${displayName} must be used within a ${displayName}Provider`);
    }
    return context;
  }

  // Create a hook to use a selector
  function useSelector<R>(selector: Selector<S, R>): R {
    const { state } = useStore();
    return selector(state);
  }

  // Create a hook to use an action creator
  function useAction<T extends ActionCreator<A>>(actionCreator: T): (...args: Parameters<T>) => void {
    const { dispatch } = useStore();
    return useMemo(
      () => (...args: Parameters<T>) => {
        dispatch(actionCreator(...args));
      },
      [dispatch, actionCreator]
    );
  }

  // Create a hook to use multiple action creators
  function useActions<T extends Record<string, ActionCreator<A>>>(
    actionCreators: T
  ): { [K in keyof T]: (...args: Parameters<T[K]>) => void } {
    const { dispatch } = useStore();
    return useMemo(
      () => {
        const boundActionCreators = {} as { [K in keyof T]: (...args: Parameters<T[K]>) => void };
        for (const key in actionCreators) {
          boundActionCreators[key] = (...args: Parameters<T[typeof key]>) => {
            dispatch(actionCreators[key](...args));
          };
        }
        return boundActionCreators;
      },
      [dispatch, actionCreators]
    );
  }

  return {
    Context,
    Provider,
    useStore,
    useSelector,
    useAction,
    useActions,
  };
}
