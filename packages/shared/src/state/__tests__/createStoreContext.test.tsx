/**
 * Create Store Context Tests
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { createStoreContext } from '../createStoreContext';

// Define test types
interface TestState {
  count: number;
  text: string;
}

type TestAction =
  | { type: 'INCREMENT'; payload?: number }
  | { type: 'DECREMENT'; payload?: number }
  | { type: 'SET_TEXT'; payload: string };

// Define test reducer
const testReducer = (state: TestState, action: TestAction): TestState => {
  switch (action.type) {
    case 'INCREMENT':
      return {
        ...state,
        count: state.count + (action.payload || 1),
      };
    case 'DECREMENT':
      return {
        ...state,
        count: state.count - (action.payload || 1),
      };
    case 'SET_TEXT':
      return {
        ...state,
        text: action.payload,
      };
    default:
      return state;
  }
};

// Define initial state
const initialState: TestState = {
  count: 0,
  text: '',
};

describe('createStoreContext', () => {
  it('should create a context and provider', () => {
    const { Provider } = createStoreContext(testReducer, initialState);
    
    const TestComponent = () => <div>Test</div>;
    
    render(
      <Provider>
        <TestComponent />
      </Provider>
    );
    
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
  
  it('should provide state and dispatch through useStore', () => {
    const { Provider, useStore } = createStoreContext(testReducer, initialState);
    
    const TestComponent = () => {
      const { state, dispatch } = useStore();
      
      return (
        <div>
          <div data-testid="count">{state.count}</div>
          <button onClick={() => dispatch({ type: 'INCREMENT' })}>Increment</button>
        </div>
      );
    };
    
    render(
      <Provider>
        <TestComponent />
      </Provider>
    );
    
    expect(screen.getByTestId('count')).toHaveTextContent('0');
    
    fireEvent.click(screen.getByText('Increment'));
    
    expect(screen.getByTestId('count')).toHaveTextContent('1');
  });
  
  it('should provide state through useSelector', () => {
    const { Provider, useSelector } = createStoreContext(testReducer, initialState);
    
    const TestComponent = () => {
      const count = useSelector(state => state.count);
      const text = useSelector(state => state.text);
      
      return (
        <div>
          <div data-testid="count">{count}</div>
          <div data-testid="text">{text || 'empty'}</div>
        </div>
      );
    };
    
    render(
      <Provider>
        <TestComponent />
      </Provider>
    );
    
    expect(screen.getByTestId('count')).toHaveTextContent('0');
    expect(screen.getByTestId('text')).toHaveTextContent('empty');
  });
  
  it('should provide actions through useAction', () => {
    const { Provider, useStore, useAction } = createStoreContext(testReducer, initialState);
    
    const TestComponent = () => {
      const { state } = useStore();
      const increment = useAction((amount = 1) => ({ type: 'INCREMENT', payload: amount }));
      const setText = useAction((text: string) => ({ type: 'SET_TEXT', payload: text }));
      
      return (
        <div>
          <div data-testid="count">{state.count}</div>
          <div data-testid="text">{state.text || 'empty'}</div>
          <button onClick={() => increment(5)}>Increment by 5</button>
          <button onClick={() => setText('Hello')}>Set Text</button>
        </div>
      );
    };
    
    render(
      <Provider>
        <TestComponent />
      </Provider>
    );
    
    expect(screen.getByTestId('count')).toHaveTextContent('0');
    expect(screen.getByTestId('text')).toHaveTextContent('empty');
    
    fireEvent.click(screen.getByText('Increment by 5'));
    
    expect(screen.getByTestId('count')).toHaveTextContent('5');
    
    fireEvent.click(screen.getByText('Set Text'));
    
    expect(screen.getByTestId('text')).toHaveTextContent('Hello');
  });
  
  it('should provide multiple actions through useActions', () => {
    const { Provider, useStore, useActions } = createStoreContext(testReducer, initialState);
    
    const actionCreators = {
      increment: (amount = 1) => ({ type: 'INCREMENT', payload: amount } as const),
      decrement: (amount = 1) => ({ type: 'DECREMENT', payload: amount } as const),
      setText: (text: string) => ({ type: 'SET_TEXT', payload: text } as const),
    };
    
    const TestComponent = () => {
      const { state } = useStore();
      const actions = useActions(actionCreators);
      
      return (
        <div>
          <div data-testid="count">{state.count}</div>
          <button onClick={() => actions.increment(3)}>Increment by 3</button>
          <button onClick={() => actions.decrement(2)}>Decrement by 2</button>
        </div>
      );
    };
    
    render(
      <Provider>
        <TestComponent />
      </Provider>
    );
    
    expect(screen.getByTestId('count')).toHaveTextContent('0');
    
    fireEvent.click(screen.getByText('Increment by 3'));
    
    expect(screen.getByTestId('count')).toHaveTextContent('3');
    
    fireEvent.click(screen.getByText('Decrement by 2'));
    
    expect(screen.getByTestId('count')).toHaveTextContent('1');
  });
  
  it('should allow overriding initial state', () => {
    const { Provider, useSelector } = createStoreContext(testReducer, initialState);
    
    const TestComponent = () => {
      const count = useSelector(state => state.count);
      
      return <div data-testid="count">{count}</div>;
    };
    
    render(
      <Provider initialState={{ count: 10, text: 'Initial' }}>
        <TestComponent />
      </Provider>
    );
    
    expect(screen.getByTestId('count')).toHaveTextContent('10');
  });
});
