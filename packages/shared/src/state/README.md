# State Management Utilities

This module provides utilities for state management in the ADHD Trading Dashboard.

## Features

- **Context-based State Management**: Create typed context stores with actions and selectors.
- **Memoized Selectors**: Create memoized selectors for derived state.
- **Persistence**: Persist state to local storage with versioning and migration.

## Usage

### Creating a Store

```tsx
import { createStoreContext } from '@adhd-trading-dashboard/shared';

// Define state interface
interface CounterState {
  count: number;
}

// Define action types
type CounterAction =
  | { type: 'INCREMENT'; payload?: number }
  | { type: 'DECREMENT'; payload?: number }
  | { type: 'RESET' };

// Define reducer
const counterReducer = (state: CounterState, action: CounterAction): CounterState => {
  switch (action.type) {
    case 'INCREMENT':
      return {
        ...state,
        count: state.count + (action.payload || 1),
      };
    case 'DECREMENT':
      return {
        ...state,
        count: state.count - (action.payload || 1),
      };
    case 'RESET':
      return {
        ...state,
        count: 0,
      };
    default:
      return state;
  }
};

// Define initial state
const initialState: CounterState = {
  count: 0,
};

// Create store context
export const {
  Context: CounterContext,
  Provider: CounterProvider,
  useStore: useCounterStore,
  useSelector: useCounterSelector,
  useAction: useCounterAction,
  useActions: useCounterActions,
} = createStoreContext<CounterState, CounterAction>(
  counterReducer,
  initialState,
  'CounterContext'
);
```

### Using the Store

```tsx
import { CounterProvider, useCounterStore } from './counterState';

// Wrap your app with the provider
const App = () => {
  return (
    <CounterProvider>
      <Counter />
    </CounterProvider>
  );
};

// Use the store in your components
const Counter = () => {
  const { state, dispatch } = useCounterStore();
  
  return (
    <div>
      <p>Count: {state.count}</p>
      <button onClick={() => dispatch({ type: 'INCREMENT' })}>Increment</button>
      <button onClick={() => dispatch({ type: 'DECREMENT' })}>Decrement</button>
      <button onClick={() => dispatch({ type: 'RESET' })}>Reset</button>
    </div>
  );
};
```

### Using Selectors

```tsx
import { useCounterSelector } from './counterState';

const CountDisplay = () => {
  const count = useCounterSelector(state => state.count);
  
  return <p>Count: {count}</p>;
};
```

### Using Action Creators

```tsx
import { useCounterAction, useCounterActions } from './counterState';

// Define action creators
const increment = (amount = 1) => ({ type: 'INCREMENT', payload: amount } as const);
const decrement = (amount = 1) => ({ type: 'DECREMENT', payload: amount } as const);
const reset = () => ({ type: 'RESET' } as const);

// Use a single action
const IncrementButton = () => {
  const incrementAction = useCounterAction(increment);
  
  return <button onClick={() => incrementAction(5)}>Increment by 5</button>;
};

// Use multiple actions
const CounterControls = () => {
  const actions = useCounterActions({
    increment,
    decrement,
    reset,
  });
  
  return (
    <div>
      <button onClick={() => actions.increment(5)}>Increment by 5</button>
      <button onClick={() => actions.decrement(3)}>Decrement by 3</button>
      <button onClick={actions.reset}>Reset</button>
    </div>
  );
};
```

### Creating Memoized Selectors

```tsx
import { createSelector } from '@adhd-trading-dashboard/shared';

// Define selectors
const selectCount = (state: CounterState) => state.count;
const selectIsPositive = createSelector(
  selectCount,
  (count) => count > 0
);
const selectIsNegative = createSelector(
  selectCount,
  (count) => count < 0
);
const selectIsZero = createSelector(
  selectCount,
  (count) => count === 0
);

// Use selectors
const CountStatus = () => {
  const isPositive = useCounterSelector(selectIsPositive);
  const isNegative = useCounterSelector(selectIsNegative);
  const isZero = useCounterSelector(selectIsZero);
  
  return (
    <div>
      {isPositive && <p>Count is positive</p>}
      {isNegative && <p>Count is negative</p>}
      {isZero && <p>Count is zero</p>}
    </div>
  );
};
```

### Persisting State

```tsx
import { persistState, createStoreContext } from '@adhd-trading-dashboard/shared';

// Define state, actions, and reducer as before

// Persist state
const { reducer: persistedReducer, initialState: persistedInitialState } = persistState(
  counterReducer,
  {
    key: 'counter',
    initialState,
    version: 1,
    filter: (state) => state, // Persist all state
  }
);

// Create store context with persisted state
export const {
  Context: CounterContext,
  Provider: CounterProvider,
  useStore: useCounterStore,
  useSelector: useCounterSelector,
  useAction: useCounterAction,
  useActions: useCounterActions,
} = createStoreContext<CounterState, CounterAction>(
  persistedReducer,
  persistedInitialState,
  'CounterContext'
);
```

## Architecture

The state management utilities are organized into the following modules:

- **createStoreContext**: Create a typed context store with actions and selectors.
- **createSelector**: Create memoized selectors for derived state.
- **persistState**: Persist state to local storage with versioning and migration.

## Best Practices

1. **Organize by Feature**: Create a separate state module for each feature.
2. **Use Selectors**: Use selectors to access state and derive computed values.
3. **Use Action Creators**: Use action creators to create actions.
4. **Persist User Preferences**: Persist user preferences to local storage.
5. **Version Persisted State**: Version persisted state to handle migrations.
6. **Filter Sensitive Data**: Filter sensitive data when persisting state.
7. **Use TypeScript**: Use TypeScript to ensure type safety.
