/**
 * Styled Components Declaration File
 *
 * This file extends the DefaultTheme interface from styled-components
 * to include our custom theme properties and provides enhanced type checking.
 */

import "styled-components";
import { Theme } from "./theme/types";

/**
 * Extend the DefaultTheme interface from styled-components
 * with our custom theme properties
 */
declare module "styled-components" {
  export interface DefaultTheme extends Theme {}
}

/**
 * Add CSS helper types for better type checking in styled-components
 */
declare module "styled-components" {
  interface CSSProperties {
    [key: string]: any;
  }

  interface CSSPseudos {
    [key: string]: any;
  }

  interface CSSObject extends CSSProperties {
    [key: string]: any;
  }

  // Enhanced type checking for theme props
  export type ThemeProps<T> = {
    theme: T;
  };

  // Helper type for theme-aware props
  export type WithTheme<P = {}> = P & {
    theme: DefaultTheme;
  };

  // Helper for creating theme-aware styled components
  export type StyledComponent<P, T> = React.ComponentType<P & { theme?: T }>;
}
