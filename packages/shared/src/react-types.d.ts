/**
 * React Types Declaration File
 *
 * This file provides type compatibility between different versions of React types
 * and ensures proper integration with styled-components.
 */

import "react";

/**
 * Fix ReactNode compatibility issues between different React versions
 * and ensure proper integration with styled-components
 */
declare module "react" {
  // Ensure ReactNode is compatible across different versions
  type ReactNode =
    | ReactElement<any, string | JSXElementConstructor<any>>
    | ReactFragment
    | ReactPortal
    | ReactText
    | boolean
    | null
    | undefined;

  type ReactText = string | number;

  interface ReactPortal extends ReactElement<any, any> {
    key: Key | null;
    children: ReactNode;
  }

  // Enhanced component type for better props typing
  interface FunctionComponent<P = {}> {
    (props: PropsWithChildren<P>, context?: any): ReactElement<any, any> | null;
    propTypes?: WeakValidationMap<P> | undefined;
    contextTypes?: ValidationMap<any> | undefined;
    defaultProps?: Partial<P> | undefined;
    displayName?: string | undefined;
  }

  // Ensure FC is compatible with FunctionComponent
  type FC<P = {}> = FunctionComponent<P>;

  // Enhanced ComponentProps for better type inference
  type ComponentProps<
    T extends keyof JSX.IntrinsicElements | JSXElementConstructor<any>
  > = T extends JSXElementConstructor<infer P>
    ? P
    : T extends keyof JSX.IntrinsicElements
    ? JSX.IntrinsicElements[T]
    : {};
}
