# API Abstraction Layer

This module provides a robust API abstraction layer for the ADHD Trading Dashboard.

## Features

- **Robust API Client**: A flexible and extensible API client with error handling, retries, and interceptors.
- **Type Safety**: Strong TypeScript typing for all API operations.
- **Caching**: Built-in caching for frequently accessed data with configurable expiration.
- **Interceptors**: Request and response interceptors for logging, transformation, and error handling.
- **Hooks**: Custom hooks for making API queries and mutations with loading, error, and caching.
- **Automatic Environment Detection**: Automatically switches between real and mock APIs based on the environment.
- **Retry Mechanism**: Configurable retry mechanism for handling transient failures.
- **Abort Controller Integration**: Support for cancelling requests using the Abort Controller API.
- **Timeout Handling**: Automatic timeout handling to prevent hanging requests.

## Usage

### Basic Usage

```tsx
import { apiClient } from '@adhd-trading-dashboard/shared';

// Make a GET request
const data = await apiClient.get('/api/trades');

// Make a POST request
const result = await apiClient.post('/api/trades', { symbol: 'AAPL', action: 'BUY' });
```

### Using the API Provider

```tsx
import { ApiProvider, useApi } from '@adhd-trading-dashboard/shared';

// Wrap your app with the API provider
const App = () => {
  return (
    <ApiProvider>
      <YourApp />
    </ApiProvider>
  );
};

// Use the API client in your components
const YourComponent = () => {
  const api = useApi();

  const handleClick = async () => {
    const data = await api.get('/api/trades');
    console.log(data);
  };

  return <button onClick={handleClick}>Fetch Trades</button>;
};
```

### Using the API Hooks

```tsx
import { useApiQuery, useApiMutation } from '@adhd-trading-dashboard/shared';

// Query hook
const YourComponent = () => {
  const { data, isLoading, error, refetch } = useApiQuery(() => apiClient.get('/api/trades'), {
    enabled: true,
    refetchOnWindowFocus: true,
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <button onClick={refetch}>Refresh</button>
      <ul>
        {data?.map((trade) => (
          <li key={trade.id}>{trade.symbol}</li>
        ))}
      </ul>
    </div>
  );
};

// Mutation hook
const YourForm = () => {
  const { mutate, isLoading, error } = useApiMutation((data) =>
    apiClient.post('/api/trades', data)
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    mutate({ symbol: 'AAPL', action: 'BUY' });
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button type="submit" disabled={isLoading}>
        {isLoading ? 'Submitting...' : 'Submit'}
      </button>
      {error && <div>Error: {error.message}</div>}
    </form>
  );
};
```

### Using Specialized Hooks

```tsx
import {
  useTradingData,
  usePerformanceMetrics,
  useMarketNews,
  useUserPreferences,
} from '@adhd-trading-dashboard/shared';

// Trading data hook
const TradingComponent = () => {
  const { data, isLoading, error } = useTradingData({
    tradingOptions: {
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      symbol: 'AAPL',
    },
  });

  // Render component
};

// Performance metrics hook
const PerformanceComponent = () => {
  const { data, isLoading, error } = usePerformanceMetrics({
    metricsOptions: {
      period: 'month',
      detailed: true,
    },
  });

  // Render component
};

// User preferences hook
const SettingsComponent = () => {
  const { preferences, isLoading, error, savePreferences } = useUserPreferences();

  const handleSave = () => {
    savePreferences({
      theme: 'f1',
      refreshInterval: 5,
      showNotifications: true,
    });
  };

  // Render component
};
```

## Architecture

The API abstraction layer is organized into the following modules:

- **core**: Core API client classes and utilities.
  - `ApiClient.ts`: Base API client with core functionality.
  - `TradingApiClient.ts`: Trading-specific API client extending the base client.
- **hooks**: Custom hooks for making API queries and mutations.
  - `useApiQuery.ts`: Hook for making API queries with caching and loading states.
  - `useApiMutation.ts`: Hook for making API mutations with loading and error states.
  - Specialized hooks for specific data types (trading data, performance metrics, etc.).
- **context**: Context provider for the API client.
  - `ApiContext.tsx`: React context for providing the API client to components.
- **clients**: Legacy API clients (being phased out).
  - `gasApi.ts`: Google Apps Script API client.
  - `mockApi.ts`: Mock API client for development and testing.
- **types**: TypeScript types for API responses and requests.

## Migrating from Legacy API Clients

We are in the process of migrating from the legacy API clients (`gasApi.ts` and `mockApi.ts`) to the new `ApiClient` and `TradingApiClient` classes. The new API client provides more robust error handling, retries, caching, and other advanced features.

### Migration Steps

1. Replace imports from `@adhd-trading-dashboard/shared/api` with `@adhd-trading-dashboard/shared`.
2. Replace direct calls to `api.getTrades()` with `apiClient.getTrades()`.
3. Use the new hooks (`useApiQuery`, `useApiMutation`) instead of direct API calls.

## Extending

### Adding a New API Endpoint

1. Define the request and response types in `types.ts`.
2. Add a method to the `TradingApiClient` class.
3. Create a specialized hook for the endpoint.

Example:

```typescript
// 1. Define types
export interface GetTradeHistoryRequest {
  startDate: string;
  endDate: string;
  symbol?: string;
}

export interface TradeHistoryResponse {
  trades: Trade[];
  totalCount: number;
}

// 2. Add method to TradingApiClient
class TradingApiClient extends ApiClient {
  // ...existing methods

  public async getTradeHistory(params: GetTradeHistoryRequest): Promise<TradeHistoryResponse> {
    return this.get('/api/trades/history', { params });
  }
}

// 3. Create specialized hook
export function useTradeHistory(params: GetTradeHistoryRequest, options?: ApiQueryOptions) {
  return useApiQuery(() => apiClient.getTradeHistory(params), options);
}
```

### Adding a New Interceptor

```tsx
import { apiClient } from '@adhd-trading-dashboard/shared';

// Add a request interceptor
apiClient.addRequestInterceptor({
  onRequest: (config) => {
    console.log('Request:', config);
    return config;
  },
  onRequestError: (error) => {
    console.error('Request Error:', error);
    throw error;
  },
});

// Add a response interceptor
apiClient.addResponseInterceptor({
  onResponse: (response) => {
    console.log('Response:', response);
    return response;
  },
  onResponseError: (error) => {
    console.error('Response Error:', error);
    throw error;
  },
});
```
