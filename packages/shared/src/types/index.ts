/**
 * Shared Types
 *
 * This module exports all type definitions for the shared package.
 */

// Export trading data types
export * from './trading';

/**
 * Common utility types
 */
export type ID = string | number;

/**
 * API Response
 *
 * Base interface for all API responses
 */
export interface ApiResponse {
  success: boolean;
  error?: string;
}

/**
 * Configuration Response
 */
export interface ConfigurationResponse extends ApiResponse {
  theme?: {
    colors: Record<string, string>;
  };
  features?: Record<string, boolean>;
}

/**
 * Performance Data Point
 */
export interface PerformanceDataPoint {
  date: string;
  value: number;
}

/**
 * News Item
 */
export interface NewsItem {
  title: string;
  date: string;
  source: string;
  url?: string;
}

/**
 * Dashboard Data Response
 */
export interface DashboardDataResponse extends ApiResponse {
  summary?: {
    totalTrades: number;
    winRate: number;
    profitFactor: number;
    netProfit: number;
  };
  recentTrades?: any[]; // Will be typed properly with Trade interface
  performance?: {
    daily: PerformanceDataPoint[];
  };
  news?: NewsItem[];
}

/**
 * Trading Data Item
 */
export interface TradingDataItem {
  date: string;
  symbol: string;
  action: string;
  price: number;
  quantity: number;
  profit: number;
}

/**
 * Trading Data Options
 */
export interface TradingDataOptions {
  startDate?: string;
  endDate?: string;
  symbols?: string[];
  limit?: number;
  page?: number;
}

/**
 * Trading Data Response
 */
export interface TradingDataResponse extends ApiResponse {
  data?: TradingDataItem[];
}

/**
 * Performance Metrics Options
 */
export interface PerformanceMetricsOptions {
  period?: 'day' | 'week' | 'month' | 'year' | 'all';
  startDate?: string;
  endDate?: string;
}

/**
 * Performance Metrics Response
 */
export interface PerformanceMetricsResponse extends ApiResponse {
  metrics?: Record<string, number>;
  chartData?: PerformanceDataPoint[];
}

/**
 * Market News Options
 */
export interface MarketNewsOptions {
  limit?: number;
  sources?: string[];
  categories?: string[];
}

/**
 * Market News Response
 */
export interface MarketNewsResponse extends ApiResponse {
  news?: NewsItem[];
}

/**
 * User Preferences
 */
export interface UserPreferences {
  theme: string;
  refreshInterval: number;
  showNotifications: boolean;
}

/**
 * User Preferences Response
 */
export interface UserPreferencesResponse extends ApiResponse {
  preferences?: UserPreferences;
}
