{"program": {"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/typescript/lib/lib.es2022.d.ts", "../../node_modules/typescript/lib/lib.esnext.d.ts", "../../node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.array.d.ts", "../../node_modules/typescript/lib/lib.es2022.error.d.ts", "../../node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.object.d.ts", "../../node_modules/typescript/lib/lib.es2022.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2022.string.d.ts", "../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/@types/react/global.d.ts", "../../node_modules/csstype/index.d.ts", "../../node_modules/@types/prop-types/index.d.ts", "../../node_modules/@types/react/index.d.ts", "../../node_modules/@types/react/jsx-runtime.d.ts", "./src/types/trading.ts", "./src/types/index.ts", "./src/api/types/index.ts", "./src/services/tradestorage.ts", "./src/api/index.ts", "../../node_modules/@types/hoist-non-react-statics/index.d.ts", "../../node_modules/@types/styled-components/index.d.ts", "./src/components/atoms/badge.tsx", "./src/components/atoms/button.tsx", "./src/components/atoms/input.tsx", "./src/components/atoms/loadingplaceholder.tsx", "./src/components/atoms/select.tsx", "./src/components/atoms/statusindicator.tsx", "./src/components/atoms/tag.tsx", "./src/components/atoms/index.ts", "./src/components/molecules/card.tsx", "./src/components/molecules/emptystate.tsx", "./src/components/molecules/errorboundary.tsx", "./src/components/molecules/unifiederrorboundary.tsx", "./src/components/molecules/formfield.tsx", "../../node_modules/@types/react-dom/index.d.ts", "./src/components/molecules/modal.tsx", "./src/components/molecules/table.tsx", "./src/components/molecules/tradetablecolumns.tsx", "./src/components/molecules/tradetablerow.tsx", "./src/components/molecules/tradetablefilters.tsx", "./src/components/molecules/tradetable.tsx", "./src/components/molecules/index.ts", "./src/components/organisms/datacard.tsx", "./src/components/organisms/index.ts", "./src/components/templates/dashboardtemplate.tsx", "./src/components/templates/index.ts", "./src/components/index.ts", "./src/hooks/useasyncdata.ts", "./src/hooks/usedebounce.ts", "./src/hooks/useerrorhandler.ts", "./src/hooks/uselocalstorage.ts", "./src/hooks/usepagination.ts", "./src/hooks/index.ts", "./src/theme/types.ts", "./src/theme/tokens.ts", "./src/theme/f1theme.ts", "./src/theme/lighttheme.ts", "./src/theme/darktheme.ts", "./src/theme/globalstyles.tsx", "./src/theme/themeprovider.tsx", "./src/theme/index.ts", "./src/state/createstorecontext.tsx", "./src/state/createselector.ts", "./src/state/persiststate.ts", "./src/state/index.ts", "./src/utils/index.ts", "./src/monitoring/index.ts", "./src/services/index.ts", "./src/index.ts", "./src/react-types.d.ts", "./src/styled.d.ts", "./src/api/context/index.ts", "./src/components/base.tsx", "../../node_modules/file-system-cache/lib/filesystemcache.d.ts", "../../node_modules/file-system-cache/lib/index.d.ts", "../../node_modules/@babel/types/lib/index.d.ts", "../../node_modules/@types/babel__generator/index.d.ts", "../../node_modules/@babel/parser/typings/babel-parser.d.ts", "../../node_modules/@types/babel__template/index.d.ts", "../../node_modules/@types/babel__traverse/index.d.ts", "../../node_modules/@types/babel__core/index.d.ts", "../../node_modules/@types/node/assert.d.ts", "../../node_modules/@types/node/assert/strict.d.ts", "../../node_modules/@types/node/globals.d.ts", "../../node_modules/@types/node/async_hooks.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/child_process.d.ts", "../../node_modules/@types/node/cluster.d.ts", "../../node_modules/@types/node/console.d.ts", "../../node_modules/@types/node/constants.d.ts", "../../node_modules/@types/node/crypto.d.ts", "../../node_modules/@types/node/dgram.d.ts", "../../node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/@types/node/dns.d.ts", "../../node_modules/@types/node/dns/promises.d.ts", "../../node_modules/@types/node/domain.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/fs/promises.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/http2.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/inspector.d.ts", "../../node_modules/@types/node/module.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/os.d.ts", "../../node_modules/@types/node/path.d.ts", "../../node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/@types/node/process.d.ts", "../../node_modules/@types/node/punycode.d.ts", "../../node_modules/@types/node/querystring.d.ts", "../../node_modules/@types/node/readline.d.ts", "../../node_modules/@types/node/repl.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/stream/promises.d.ts", "../../node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/@types/node/stream/web.d.ts", "../../node_modules/@types/node/string_decoder.d.ts", "../../node_modules/@types/node/test.d.ts", "../../node_modules/@types/node/timers.d.ts", "../../node_modules/@types/node/timers/promises.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/trace_events.d.ts", "../../node_modules/@types/node/tty.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/v8.d.ts", "../../node_modules/@types/node/vm.d.ts", "../../node_modules/@types/node/wasi.d.ts", "../../node_modules/@types/node/worker_threads.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/globals.global.d.ts", "../../node_modules/@types/node/index.d.ts", "../../node_modules/@types/mime/index.d.ts", "../../node_modules/@types/send/index.d.ts", "../../node_modules/@types/qs/index.d.ts", "../../node_modules/@types/range-parser/index.d.ts", "../../node_modules/@types/express-serve-static-core/index.d.ts", "../../node_modules/@types/http-errors/index.d.ts", "../../node_modules/@types/serve-static/index.d.ts", "../../node_modules/@types/connect/index.d.ts", "../../node_modules/@types/body-parser/index.d.ts", "../../node_modules/@types/express/node_modules/@types/express-serve-static-core/index.d.ts", "../../node_modules/@types/express/index.d.ts", "../../node_modules/@storybook/channels/dist/main-c55d8855.d.ts", "../../node_modules/@storybook/channels/dist/postmessage/index.d.ts", "../../node_modules/@storybook/channels/dist/websocket/index.d.ts", "../../node_modules/@storybook/channels/dist/index.d.ts", "../../node_modules/@storybook/types/dist/index.d.ts", "../../node_modules/@storybook/react/dist/types-0fc72a6d.d.ts", "../../node_modules/type-fest/source/primitive.d.ts", "../../node_modules/type-fest/source/typed-array.d.ts", "../../node_modules/type-fest/source/basic.d.ts", "../../node_modules/type-fest/source/observable-like.d.ts", "../../node_modules/type-fest/source/internal.d.ts", "../../node_modules/type-fest/source/except.d.ts", "../../node_modules/type-fest/source/simplify.d.ts", "../../node_modules/type-fest/source/writable.d.ts", "../../node_modules/type-fest/source/mutable.d.ts", "../../node_modules/type-fest/source/merge.d.ts", "../../node_modules/type-fest/source/merge-exclusive.d.ts", "../../node_modules/type-fest/source/require-at-least-one.d.ts", "../../node_modules/type-fest/source/require-exactly-one.d.ts", "../../node_modules/type-fest/source/require-all-or-none.d.ts", "../../node_modules/type-fest/source/remove-index-signature.d.ts", "../../node_modules/type-fest/source/partial-deep.d.ts", "../../node_modules/type-fest/source/partial-on-undefined-deep.d.ts", "../../node_modules/type-fest/source/readonly-deep.d.ts", "../../node_modules/type-fest/source/literal-union.d.ts", "../../node_modules/type-fest/source/promisable.d.ts", "../../node_modules/type-fest/source/opaque.d.ts", "../../node_modules/type-fest/source/invariant-of.d.ts", "../../node_modules/type-fest/source/set-optional.d.ts", "../../node_modules/type-fest/source/set-required.d.ts", "../../node_modules/type-fest/source/set-non-nullable.d.ts", "../../node_modules/type-fest/source/value-of.d.ts", "../../node_modules/type-fest/source/promise-value.d.ts", "../../node_modules/type-fest/source/async-return-type.d.ts", "../../node_modules/type-fest/source/conditional-keys.d.ts", "../../node_modules/type-fest/source/conditional-except.d.ts", "../../node_modules/type-fest/source/conditional-pick.d.ts", "../../node_modules/type-fest/source/union-to-intersection.d.ts", "../../node_modules/type-fest/source/stringified.d.ts", "../../node_modules/type-fest/source/fixed-length-array.d.ts", "../../node_modules/type-fest/source/multidimensional-array.d.ts", "../../node_modules/type-fest/source/multidimensional-readonly-array.d.ts", "../../node_modules/type-fest/source/iterable-element.d.ts", "../../node_modules/type-fest/source/entry.d.ts", "../../node_modules/type-fest/source/entries.d.ts", "../../node_modules/type-fest/source/set-return-type.d.ts", "../../node_modules/type-fest/source/asyncify.d.ts", "../../node_modules/type-fest/source/numeric.d.ts", "../../node_modules/type-fest/source/jsonify.d.ts", "../../node_modules/type-fest/source/schema.d.ts", "../../node_modules/type-fest/source/literal-to-primitive.d.ts", "../../node_modules/type-fest/source/string-key-of.d.ts", "../../node_modules/type-fest/source/exact.d.ts", "../../node_modules/type-fest/source/readonly-tuple.d.ts", "../../node_modules/type-fest/source/optional-keys-of.d.ts", "../../node_modules/type-fest/source/has-optional-keys.d.ts", "../../node_modules/type-fest/source/required-keys-of.d.ts", "../../node_modules/type-fest/source/has-required-keys.d.ts", "../../node_modules/type-fest/source/spread.d.ts", "../../node_modules/type-fest/source/split.d.ts", "../../node_modules/type-fest/source/camel-case.d.ts", "../../node_modules/type-fest/source/camel-cased-properties.d.ts", "../../node_modules/type-fest/source/camel-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/delimiter-case.d.ts", "../../node_modules/type-fest/source/kebab-case.d.ts", "../../node_modules/type-fest/source/delimiter-cased-properties.d.ts", "../../node_modules/type-fest/source/kebab-cased-properties.d.ts", "../../node_modules/type-fest/source/delimiter-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/kebab-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/pascal-case.d.ts", "../../node_modules/type-fest/source/pascal-cased-properties.d.ts", "../../node_modules/type-fest/source/pascal-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/snake-case.d.ts", "../../node_modules/type-fest/source/snake-cased-properties.d.ts", "../../node_modules/type-fest/source/snake-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/includes.d.ts", "../../node_modules/type-fest/source/screaming-snake-case.d.ts", "../../node_modules/type-fest/source/join.d.ts", "../../node_modules/type-fest/source/trim.d.ts", "../../node_modules/type-fest/source/replace.d.ts", "../../node_modules/type-fest/source/get.d.ts", "../../node_modules/type-fest/source/last-array-element.d.ts", "../../node_modules/type-fest/source/package-json.d.ts", "../../node_modules/type-fest/source/tsconfig-json.d.ts", "../../node_modules/type-fest/index.d.ts", "../../node_modules/@storybook/react/dist/index.d.ts", "./src/components/atoms/badge.stories.tsx", "./src/components/atoms/input.stories.tsx", "./src/components/atoms/select.stories.tsx", "./src/components/atoms/tag.stories.tsx", "./src/components/molecules/card.stories.tsx", "./src/components/molecules/modal.stories.tsx", "./src/components/molecules/table.stories.tsx", "./src/components/molecules/tradetable.example.tsx", "./src/components/organisms/datacard.stories.tsx", "./src/components/templates/dashboardtemplate.stories.tsx", "./src/theme/theme.types.ts", "./src/theme/tokens/colors.ts", "./src/theme/tokens/spacing.ts", "./src/theme/tokens/typography.ts", "./src/theme/tokens/index.ts", "./src/theme/variants/f1theme.ts", "./src/theme/variants/lighttheme.ts", "./src/theme/variants/index.ts", "../../node_modules/@vitest/utils/dist/types.d.ts", "../../node_modules/@vitest/utils/dist/helpers.d.ts", "../../node_modules/pretty-format/build/types.d.ts", "../../node_modules/pretty-format/build/index.d.ts", "../../node_modules/@vitest/utils/dist/index.d.ts", "../../node_modules/@vitest/runner/dist/tasks-c965d7f6.d.ts", "../../node_modules/@vitest/runner/dist/runner-3b8473ea.d.ts", "../../node_modules/@vitest/runner/dist/index.d.ts", "../../node_modules/@types/chai/index.d.ts", "../../node_modules/@vitest/expect/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment-38cdead3.d.ts", "../../node_modules/@vitest/snapshot/dist/index-6461367c.d.ts", "../../node_modules/@vitest/snapshot/dist/index.d.ts", "../../node_modules/esbuild/lib/main.d.ts", "../../node_modules/vitest/node_modules/vite/types/metadata.d.ts", "../../node_modules/vitest/node_modules/vite/types/hmrpayload.d.ts", "../../node_modules/vitest/node_modules/vite/types/customevent.d.ts", "../../node_modules/vitest/node_modules/rollup/dist/rollup.d.ts", "../../node_modules/vitest/node_modules/vite/types/importglob.d.ts", "../../node_modules/source-map-js/source-map.d.ts", "../../node_modules/postcss/lib/previous-map.d.ts", "../../node_modules/postcss/lib/input.d.ts", "../../node_modules/postcss/lib/css-syntax-error.d.ts", "../../node_modules/postcss/lib/declaration.d.ts", "../../node_modules/postcss/lib/root.d.ts", "../../node_modules/postcss/lib/warning.d.ts", "../../node_modules/postcss/lib/lazy-result.d.ts", "../../node_modules/postcss/lib/no-work-result.d.ts", "../../node_modules/postcss/lib/processor.d.ts", "../../node_modules/postcss/lib/result.d.ts", "../../node_modules/postcss/lib/document.d.ts", "../../node_modules/postcss/lib/rule.d.ts", "../../node_modules/postcss/lib/node.d.ts", "../../node_modules/postcss/lib/comment.d.ts", "../../node_modules/postcss/lib/container.d.ts", "../../node_modules/postcss/lib/at-rule.d.ts", "../../node_modules/postcss/lib/list.d.ts", "../../node_modules/postcss/lib/postcss.d.ts", "../../node_modules/vitest/node_modules/vite/dist/node/index.d.ts", "../../node_modules/@vitest/runner/dist/types.d.ts", "../../node_modules/@vitest/runner/types.d.ts", "../../node_modules/@vitest/utils/dist/diff.d.ts", "../../node_modules/@vitest/utils/diff.d.ts", "../../node_modules/@vitest/runner/dist/utils.d.ts", "../../node_modules/@vitest/runner/utils.d.ts", "../../node_modules/tinybench/dist/index.d.cts", "../../node_modules/vite-node/dist/types.d-1e7e3fdf.d.ts", "../../node_modules/vite-node/dist/types-c39b64bb.d.ts", "../../node_modules/vite-node/dist/client.d.ts", "../../node_modules/@vitest/snapshot/dist/manager.d.ts", "../../node_modules/@vitest/snapshot/manager.d.ts", "../../node_modules/vite-node/dist/index.d.ts", "../../node_modules/source-map/source-map.d.ts", "../../node_modules/vite-node/node_modules/vite/dist/node/index.d.ts", "../../node_modules/vite-node/dist/server.d.ts", "../../node_modules/vitest/dist/types-e3c9754d.d.ts", "../../node_modules/tinyspy/dist/index.d.ts", "../../node_modules/@vitest/spy/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment.d.ts", "../../node_modules/@vitest/snapshot/environment.d.ts", "../../node_modules/vitest/dist/config.d.ts", "../../node_modules/vitest/dist/index.d.ts", "../../node_modules/vitest/globals.d.ts", "../../node_modules/@types/node/ts5.6/buffer.buffer.d.ts", "../../node_modules/@types/node/ts5.6/index.d.ts", "../../node_modules/vite/dist/node/index.d.ts"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "4b421cbfb3a38a27c279dec1e9112c3d1da296f77a1a85ddadf7e7a425d45d18", "1fc5ab7a764205c68fa10d381b08417795fc73111d6dd16b5b1ed36badb743d9", "746d62152361558ea6d6115cf0da4dd10ede041d14882ede3568bce5dc4b4f1f", "d11a03592451da2d1065e09e61f4e2a9bf68f780f4f6623c18b57816a9679d17", "aea179452def8a6152f98f63b191b84e7cbd69b0e248c91e61fb2e52328abe8c", {"version": "3aafcb693fe5b5c3bd277bd4c3a617b53db474fe498fc5df067c5603b1eebde7", "affectsGlobalScope": true}, {"version": "f3d4da15233e593eacb3965cde7960f3fddf5878528d882bcedd5cbaba0193c7", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "81cac4cbc92c0c839c70f8ffb94eb61e2d32dc1c3cf6d95844ca099463cf37ea", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "6e7997ef61de3132e4d4b2250e75343f487903ddf5370e7ce33cf1b9db9a63ed", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "5e5e095c4470c8bab227dbbc61374878ecead104c74ab9960d3adcccfee23205", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "d7f680a43f8cd12a6b6122c07c54ba40952b0c8aa140dcfcf32eb9e6cb028596", "affectsGlobalScope": true}, {"version": "3787b83e297de7c315d55d4a7c546ae28e5f6c0a361b7a1dcec1f1f50a54ef11", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "2768ef564cfc0689a1b76106c421a2909bdff0acbe87da010785adab80efdd5c", "affectsGlobalScope": true}, {"version": "b248e32ca52e8f5571390a4142558ae4f203ae2f94d5bac38a3084d529ef4e58", "affectsGlobalScope": true}, {"version": "6c55633c733c8378db65ac3da7a767c3cf2cf3057f0565a9124a16a3a2019e87", "affectsGlobalScope": true}, {"version": "fb4416144c1bf0323ccbc9afb0ab289c07312214e8820ad17d709498c865a3fe", "affectsGlobalScope": true}, {"version": "5b0ca94ec819d68d33da516306c15297acec88efeb0ae9e2b39f71dbd9685ef7", "affectsGlobalScope": true}, {"version": "34c839eaaa6d78c8674ae2c37af2236dee6831b13db7b4ef4df3ec889a04d4f2", "affectsGlobalScope": true}, {"version": "34478567f8a80171f88f2f30808beb7da15eac0538ae91282dd33dce928d98ed", "affectsGlobalScope": true}, {"version": "ab7d58e6161a550ff92e5aff755dc37fe896245348332cd5f1e1203479fe0ed1", "affectsGlobalScope": true}, {"version": "6bda95ea27a59a276e46043b7065b55bd4b316c25e70e29b572958fa77565d43", "affectsGlobalScope": true}, {"version": "aedb8de1abb2ff1095c153854a6df7deae4a5709c37297f9d6e9948b6806fa66", "affectsGlobalScope": true}, {"version": "a4da0551fd39b90ca7ce5f68fb55d4dc0c1396d589b612e1902f68ee090aaada", "affectsGlobalScope": true}, {"version": "11ffe3c281f375fff9ffdde8bbec7669b4dd671905509079f866f2354a788064", "affectsGlobalScope": true}, {"version": "52d1bb7ab7a3306fd0375c8bff560feed26ed676a5b0457fa8027b563aecb9a4", "affectsGlobalScope": true}, {"version": "bbdf156fea2fabed31a569445835aeedcc33643d404fcbaa54541f06c109df3f", "affectsGlobalScope": true}, "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "65ff5a0aefd7817a03c1ad04fee85c9cdd3ec415cc3c9efec85d8008d4d5e4ee", {"version": "7fb6faf1006c3503d44e4922c8c65772ddc98e92bad7c2ae2d5db123f3e297b3", "affectsGlobalScope": true}, "af7fd2870746deed40e130fc0a3966de74e8f52a97ec114d0fbb35876ab05ca9", {"version": "3ef097d5530c458c92213083ea7fad510f710e0f1a2b557b3a8e9928805ec962", "signature": "effe435900e187a7128daee2015457e38423674de0abc175765b4bd58e822ced"}, {"version": "88a876e7d3ceeaaadff65329fa0f9a9a889984c0bf69f01e0d72bd690805d3e1", "signature": "b8d8a42730fe23a92c085b37d4b5c054e51ad8809b6e36bdb2225fb33568a376"}, {"version": "da06936c3681e4e10f886ac24c0d65fa9a418365c9ac4cfa694e80a2046910c1", "signature": "fc815c9632ef06f139b61e183a425a4c003fdfca73fbc948c93fd6028eea44e9"}, {"version": "198b97c114ede1b9726584f3c6c78785bd70313c849ff9ac791b81c6a26a0a94", "signature": "b2d95e806679d236c43c9443f471a5c2dea8fec91caf810db3dc8381e35331a2"}, {"version": "259ff325a5d94e90b90ffc91d9a9ddb4de89a746cc422095c4429c42d326cd27", "signature": "2aefa182ea141825ec2a48342d9f617a6129462838e26a7825464f81a4965522"}, "b2d0630483bf337ef9dac326c3334a245aa4946e9f60f12baf7da5be44beafbb", {"version": "356701ea5df9eea3bf62b0f29857cb950d95eec9b9063f85c17be705926cdd2a", "affectsGlobalScope": true}, {"version": "f1110a3e215bfb4950578252244f82dd7b173d233acffdd336d221d73a870e13", "signature": "60a3a7233e2df45950eb7b1282bd868f9b7634ae4e17da34c33db6e9f895ef17"}, {"version": "310e4d7e40fcb9675863899d2445a92d89d5ad6ca64f2108f44fd5cede6f42d8", "signature": "00c9a154ae1d69bb464c7f6e95cc6a8ac35445ded0363362d0a108f35db53ef5"}, {"version": "02f3e92bd37b7369bee5d59db27ac47b53a4ab2ab22b92be810b0ba7b8837788", "signature": "0922052849a355c264eaac0dd8c3991f3edbbd39279a41dd9cfe0898afbc9866"}, {"version": "1b9c9bded329d01483fc1b611e2c37f057ff1ba3e7ddd16d14a92f890305cf81", "signature": "ab662e295546cd9f0eef865c3cbb30537b01357c13b7e8b55dd3021e8c18158b"}, {"version": "0294e0fea67d84e13a67b03940753b56d4fcae40a7a0d21cdfe1c32a0b4800b5", "signature": "a18a77499e896888859dcd4afd83e20c0692afd8bd4b6cbcffe3eddeabfbacef"}, {"version": "c987735d6fb322f6f8348888b30def278e4bfd9adcfc32c9b7a2151edc88deab", "signature": "b9b83650f31e5196cff804033548f87a49dd28c21afef0ec87a3e4f1e304c133"}, {"version": "21c1e0f5f836e9d2c719fb74fe1678d4c9c50aa2661683fe4b38762b6b448683", "signature": "50b8130f23a090d7554fb74b1c01dd3928e5dbcca120be02d98df2cc908b2207"}, {"version": "4e0b8fc4064459b3e541e138f7035e6eb366f4a120d45170eb1e6f8e0c8214e9", "signature": "36e48fa7fd0045ba3acb3e23c290fdc2a82c149111b52e52fc74e625f00f4f92"}, {"version": "16a4bb6129c9101d318ffd19431ac052c18d3ccea7fad357b4e18826d58512a6", "signature": "fd1ad4a9b2e43c6938ffc5b3548366d6a46d6f26f1e85899d4befe96b7289e85"}, {"version": "53bb4cce087e89310441aa4043e9971c624577df319b9a7760a29a6ce488a190", "signature": "7caae5ebc25c8f0f3523143e6b21d372f5f8819509bcb7e35882db1438c8f48f"}, {"version": "0250d2834a9401d652ec9c95b775c7808a7fcbe1e9c20d6e3c3855978b987d16", "signature": "64b020068997020aa693d61dc29a77eab4b811f5798ac903474fd947c9148119"}, {"version": "37dfddfd36695286a2e4bc8c04b4b38e9785b7bed7f3e2fac23cb844724e902f", "signature": "3f8aa8dcda6b82462274d91f7fd40ab14b76142b2363249c830158218ac29957"}, {"version": "5a3fe44ad52607589d5846c2833f1413992e9343b900b7e8329ab47d8d2bbb7d", "signature": "d7a5984f8b2316f5e9d3b4b6e3e22eb034e1211bbf0699e8217054f07ff30639"}, "d035565d969404edfb3dfce8a2e762fbed98f6dfd7388ac01af173aa1ef665bd", {"version": "219d7d3d957908508be1ed087f4dbbba4f8ac151243b3743b192a5d0101a7401", "signature": "fe47c0e6580b3d6c3c8f6eaed17a99b450e747f6c7ce2fb71f0716b93a7ae065"}, {"version": "edfff5cb38b3cefee11379202852299d337eab76f8adbbaa5dcf7a6c2f35f198", "signature": "d53dbcf51d80f46d7a168d7992cc8ddb24ae321221cd1b3f6ac3460176cd2902"}, {"version": "0cd9e047544c808f4ae156cdb569b7888869973ce6fe581030797d6616214abf", "signature": "b5aff9d62409e833b542a18526682eeae2c0694338592d635f271f439cb6d7f0"}, {"version": "a79e73234876fd501747ae93192d6d9e49453b6c04f1c3acc708c7e6d1101fdd", "signature": "d7cc071106a5533afef1e18702ebbb27eb7014868990fc0748e03e97869c25a1"}, {"version": "bbf843ee1ecefe39b4c29c541e5ab40cbe2f32209c7f94c5630595ff689c4be7", "signature": "0fcc468f661bd4572874920959777ee1445f3495b0ba6003c0fd2569b89c2820"}, {"version": "4daa959605309453fdaa6c6eefa4867a80c01762967092107b297a56a8addaef", "signature": "568efecc8a3155ff14197c4014278f99b932442855863764028a4fdb138c1d3f"}, {"version": "c5431f46b92b659bfa3a3f40cc972400ed2d42b680ca006df53cac3dc57a4514", "signature": "3a1555f251106492acb860dbc97050b0a017d8bdeed168eeb5f97493db48b06f"}, {"version": "6c01c6c262820449c0651ac0e206b3c7dd4cd1390ccb59ba0798225b533df4c3", "signature": "10e89f572ad64d0f57435a781ab2858c07b92e3df5abd0277d5df6f4da852fb8"}, {"version": "6164e3abd5c09caf0d13cd4ea419a77a0d5d4379e8da9ad5778ba1f220d6d207", "signature": "83e11b928d55ad5c00595940ef12d3579d936f1a42f0c717c07100f95ecb43e6"}, {"version": "6a22b4db11576bd6e963e75cb192dafd91bea655551438c8aa9de3cccc886302", "signature": "5e5e6bffea4a22348866436cd5ce9d14799472f5c38b28d2b4ad5931f3acb82e"}, {"version": "43275c9d772ff1ae8c559b40e64925a2a2d09fa50968da37c5121c35d0a5f4b2", "signature": "ea406f2e284dd1807b7e233fa60d2cbdd9a20bb49c58ec3d05031673af4b2f1d"}, "44252454362b4906deff42c8e4dc614ac54fbfd1b1535b8e5e9302458739be74", {"version": "7fb97dd37451a0a82bc3666c828059ecb8f85f30c936aee235534378591cecaa", "signature": "1611b3614cda42d89f972930fbc4d9aa1e05d3d38831f68fd2cad9141d2f9516"}, {"version": "6592751274d70359c777f7ae5556c2f3511b695a2ca948c3f3737642e737e278", "signature": "49be59afcc0e363501c9030de849f7a84914452e929e7694069f000f2cfbdc54"}, {"version": "dc28cad3c02e889688310454340d3cd306fa1912eb5d80bd52284492aaec04e6", "signature": "3dabf72165671795d9340e5eb6cc87defab3c289bbc63d4cf8da146afb33d19d"}, {"version": "ecfde4d7d55b4bfffcac18d7cc0506d057a134e370430e2cfef3f9e72c122880", "signature": "e229bfc631039f7bcddac5c23d72aae1fe845cf41c02c6d55497880a92127e66"}, {"version": "5da2606b45a508e9e06babeff5ed84c2293ec315665a9ce207fd9ce9327ff07e", "signature": "96cfc664f755adec47a54a55c38b9301129a63f7839187b63acc7fadbe84c4f9"}, {"version": "c768ade274b1969eab6a272e2f9fb8ec3e7bfe3a40347c27d78cbd9e786a3f9b", "signature": "1f24e3a655c6656beee51d2335ce20fcb6af8372b1a2784d40932740d1c8ba92"}, {"version": "6ca090128573b3a8de0cda8dc181b4edc03d9334cc718c09a04782b2ae2e8c7b", "signature": "140a2a25f1f7e58c7213a4554528b9c09d2e17126f49452ecf65ccad8c44c2ae"}, {"version": "41938df4d441539133a4e3cbd6a21648ab878cf0be4d735e3d483d7a2b4a4540", "signature": "08f3280a99f266be32ef14c6dff32c7da284b95facd30c013c6e2cc1f1ed28d8"}, {"version": "fdfc58c30c0a7838ba797c6afc49058b63ef393f42c5cf8352f81a920c7e548b", "signature": "323ac96f767b60c22cf204bfea5d90febd7b0c5694306a840cf415ae0c761c33"}, {"version": "d048c0207488cddac047ad9825d7b5f02a6cd25959f6b2617ef08a2080204d18", "signature": "34850517c79dc77ccb9c7d6293327bc1f1532322c718ef910da398bad4ad928a"}, {"version": "2774044c51909b144d6d512c75a5473050c929b0ec684b38a69381099bacc06e", "signature": "5fc84ffcee9b7e97045175a59a2c1c53fa415eb5b1b02f53496643a74c4a674b"}, {"version": "23d5ffdbb95e262154c18cbfbdf0af543a1cffa3164ad1336699ced7e648567e", "signature": "6ebb66f9dd30174913afc3e1a5f612b71dcdac7c0aefe22695ee9f53f20f24d7"}, {"version": "9b101ad6c5a1408d427c93c890e4e3eb314771c39b778e79f2d26e1d8308cbbe", "signature": "fdd2af11c6984c5cf596854127acae6395558946628edca73c463d5d31f47e6f"}, {"version": "359b4017f59e2bd2d70185c47a286be2e85251086f5bd35f5388cd870f6ed1b1", "signature": "bff5a73bbb262afff2d2439832e4474cc987a4df372eb79f712716ea6d5cabc6"}, {"version": "584d8104b6cf47a3c5b3a3886359b2f7933bfddb0a15f8ffc0a0e2b554b7b12d", "signature": "79a6208002863dabdae187305adc60c1c43c2a32caf224a5c3f94ce4b90445db"}, {"version": "4e6b747ea6b0163220f34c4c45f48faec1bcd02bedff385d36e00140d6a4f648", "signature": "81a23e587fe38c5b62a0958341e0eec1232f5f13813dd7dc9fa5b9ccc16c611c"}, {"version": "0d876725d7d9d204b58acb0d9bebcc1ff94c321f4d34a9b9f9c79fe5a4d95466", "signature": "f3aedb1eeb48db8fc285775fed72f181ed0a5a4884dd2b09cea87e87e53be692"}, {"version": "168f2a527a1561a149afc53c4946550b7d3d0ced02c22523a9caeec53b7f2d0f", "signature": "128b3dea95613cd9b2d34a664a1125ca94d8353e93f04a43c8e9b64ee7c6bd91"}, {"version": "f231ac6263e4c45d64abfe2fc67d5a5c6e6b302d48619144be2238ddc9c7ad92", "signature": "297d25a22035f96035ca751fad7ae1f47ca64c22409cd5266dc61cd43c34e9bd"}, {"version": "73e91c6731aab0ada9cbfaa6557eedb004f24b515f034065712b1fd9fb89f2df", "signature": "664239d3d85cafaa9ed03ffb1d8ba89e21a63474002ab051674fb0f4dca69492"}, "f046f38284b20212859c522217c73179dc91c99555a1a9369ca81d968a2503c4", {"version": "ec69080ab1cfb36f05877a7243a365064bd18e9863a9fd92353f31c826c2a121", "signature": "fffbe0323d1893efcca1f6f4fb261e25d369771ed85df4b7dc394448dd061656"}, "f0430d9dd6a4b725b4ae925e3ed447efa14bb20c1c8cd662539750f75daea0ce", "f05d7b73c53261ac35a40889d25343c6b26972f21bbf7721c97b85d257811f8e", {"version": "9bfc302ddfbd8845bf915f1b78ff3d40eb57b88bdf513e5dc280ddce79bad7ab", "signature": "7909d2c62bfcf82a391fc78df596d2f4301cef2063a42a2f1a2422ef5e30bc5a"}, {"version": "1cc0c92eaf5793eebe00d3ae52cec7f67b934798c3aa98faf9f76becf25fae61", "signature": "1cc5930eb7b3f24cfbe85d167e39fb3b418d207d89503b6dcc022bc0117376c2"}, "76473bcf4c38aeed6cde4eaaf8dcc808741dbe5280b957b982603a85421163a6", "40c4f089842382be316ea12fd4304184b83181ab0a6aa1050d3014d3a34c5f8f", "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "b6d03c9cfe2cf0ba4c673c209fcd7c46c815b2619fd2aad59fc4229aaef2ed43", "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "81df92841a7a12d551fcbc7e4e83dbb7d54e0c73f33a82162d13e9ae89700079", "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "4911d4c3a7f7c11bad0e2cec329a19a385d10ea83b0b69c76e032359e388f624", "a69c09dbea52352f479d3e7ac949fde3d17b195abe90b045d619f747b38d6d1a", {"version": "4f6463a60e5754bbc4a864b2aaf8fecb7706b96a21b88f27b534589b801978b6", "affectsGlobalScope": true}, "56d13f223ab40f71840795f5bef2552a397a70666ee60878222407f3893fb8d0", {"version": "4ffef5c4698e94e49dcf150e3270bad2b24a2aeab48b24acbe7c1366edff377d", "affectsGlobalScope": true}, "2534e46a52653b55dfb5a41ce427ec430c4afbaaf3bfcb1ae09b185c5d6bf169", "afc6e96061af46bcff47246158caee7e056f5288783f2d83d6858cd25be1c565", {"version": "34f5bcac12b36d70304b73de5f5aab3bb91bd9919f984be80579ebcad03a624e", "affectsGlobalScope": true}, "82408ed3e959ddc60d3e9904481b5a8dc16469928257af22a3f7d1a3bc7fd8c4", "3f2478baf49cf27aa1335ba5299e2394131284e9d50a3845e3f95e52664ff518", "f50c975ab7b50e25a69e3d8a3773894125b44e9698924105f23b812bf7488baf", "8bd106053ee0345dde7f626ed1f6100a89fb85f13ea65352627cf78c5f30c553", "76650408392bf49a8fbf3e2b6b302712a92d76af77b06e2da1cc8077359c4409", "0af3121e68297b2247dd331c0d24dba599e50736a7517a5622d5591aae4a3122", "06ccebc2c2db57d6bdbca63b71c4ae5e6ddc42d972fd8f122d4c1a28aa111b25", {"version": "81e8508d1e82278f5d3fee936f267e00c308af36219bfcee2631f9513c9c4017", "affectsGlobalScope": true}, "413a4be7f94f631235bbc83dad36c4d15e5a2ff02bca1efdbd03636d6454631b", "20c468256fd68d3ef1fa53526e76d51d6aa91711e84d72c0343589b99238287e", "4198acced75d48a039c078734c4efca7788ff8c78609c270a2b63ec20e3e1676", "8d4c16a26d59e3ce49741a7d4a6e8206b884e226cf308667c7778a0b2c0fee7f", "288dd0c774a5c6e3964084c7a2bc8cc6b746d70f44a9892d028d04f915cf7ebc", "d61c7c41eb1960b1285e242fd102c162b65c0522985b839fadda59874308a170", {"version": "e8b18c6385ff784228a6f369694fcf1a6b475355ba89090a88de13587a9391d5", "affectsGlobalScope": true}, "1805e0e4d1ed00f6361db25dff6887c7fa9b5b39f32599a34e8551da7daaa9c2", "abc1c425b2ad6720433f40f1877abfa4223f0f3dd486c9c28c492179ca183cb6", "fb0989383c6109f20281b3d31265293daefdd76d0d30551782c1654e93704f48", "a4210a84a82b3e7a8cec5b2f3616e46d523f4f10cc1576d8f2fb89d0987b341e", {"version": "8207e7e6db9aa5fc7e61c8f17ba74cf9c115d26f51f91ee93f790815a7ea9dfb", "affectsGlobalScope": true}, "9f1069b9e2c051737b1f9b4f1baf50e4a63385a6a89c32235549ae87fc3d5492", "22d48bfb37261136423ac687f1fa7bd4dda3083f767416d409a8260cf92bc8fc", "29c2706fa0cc49a2bd90c83234da33d08bb9554ecec675e91c1f85087f5a5324", "0acbf26bf958f9e80c1ffa587b74749d2697b75b484062d36e103c137c562bc3", "95518ff86843e226b62a800f679f6968ad8dac8ccbe30fbfe63de3afb13761a2", "1b952304137851e45bc009785de89ada562d9376177c97e37702e39e60c2f1ff", "698ab660b477b9c2cd5ccbd99e7e7df8b4a6134c1f5711fa615ed7aab51cb7f7", "33eee034727baf564056b4ea719075c23d3b4767d0b5f9c6933b81f3d77774d2", "c33a6ea7147af60d8e98f1ac127047f4b0d4e2ce28b8f08ff3de07ca7cc00637", "a4471d2bdba495b2a6a30b8765d5e0282fa7009d88345a9528f73c37869d3b93", {"version": "aee7013623e7632fba449d4df1da92925b27d9b816cb05546044dbfe54c88ef4", "affectsGlobalScope": true}, "e10177274a35a9d07c825615340b2fcde2f610f53f3fb40269fd196b4288dda6", "c9d70d3d7191a66a81cb554557f8ed1cf736ea8397c44a864fe52689de18865a", "998a3de5237518c0b3ac00a11b3b4417affb008aa20aedee52f3fdae3cb86151", "ad41008ffe077206e1811fc873f4d9005b5fd7f6ab52bb6118fef600815a5cb4", {"version": "1aad825534c73852a1f3275e527d729a2c0640f539198fdfdfeb83b839851910", "affectsGlobalScope": true}, "badae0df9a8016ac36994b0a0e7b82ba6aaa3528e175a8c3cb161e4683eec03e", "c3db860bcaaaeb3bbc23f353bbda1f8ab82756c8d5e973bebb3953cb09ea68f2", "235a53595bd20b0b0eeb1a29cb2887c67c48375e92f03749b2488fbd46d0b1a0", "bc09393cd4cd13f69cf1366d4236fbae5359bb550f0de4e15767e9a91d63dfb1", "9c266243b01545e11d2733a55ad02b4c00ecdbda99c561cd1674f96e89cdc958", "c71155c05fc76ff948a4759abc1cb9feec036509f500174bc18dad4c7827a60c", {"version": "ab9b9a36e5284fd8d3bf2f7d5fcbc60052f25f27e4d20954782099282c60d23e", "affectsGlobalScope": true}, "88003d9ab15507806f41b120be6d407c1afe566c2f6689ebe3a034dd5ec0c8dc", "d3f2d715f57df3f04bf7b16dde01dec10366f64fce44503c92b8f78f614c1769", "b78cd10245a90e27e62d0558564f5d9a16576294eee724a59ae21b91f9269e4a", "baac9896d29bcc55391d769e408ff400d61273d832dd500f21de766205255acb", "2f5747b1508ccf83fad0c251ba1e5da2f5a30b78b09ffa1cfaf633045160afed", {"version": "86ea91bfa7fef1eeb958056f30f1db4e0680bc9b5132e5e9d6e9cfd773c0c4fd", "affectsGlobalScope": true}, "b71c603a539078a5e3a039b20f2b0a0d1708967530cf97dec8850a9ca45baa2b", "0e13570a7e86c6d83dd92e81758a930f63747483e2cd34ef36fcdb47d1f9726a", "104c67f0da1bdf0d94865419247e20eded83ce7f9911a1aa75fc675c077ca66e", "cc0d0b339f31ce0ab3b7a5b714d8e578ce698f1e13d7f8c60bfb766baeb1d35c", {"version": "a45c25e77c911c1f2a04cade78f6f42b4d7d896a3882d4e226efd3a3fcd5f2c4", "affectsGlobalScope": true}, "5c45abf1e13e4463eacfd5dedda06855da8748a6a6cb3334f582b52e219acc04", "62f2d6b25ff2f5a3e73023781800892ff84ea55e9f97c1b23e1a32890a0d1487", "858c5fcc43edd48a3795281bd611d0ccc0ff9d7fdce15007be9e30dad87feb8e", "7d1466792b53ca98aa82a54dbed78b778a3996d4cbda4c1f3ba3e2ed7ba5683a", "1828d8c12af983359ea7d8b87ec847bbaf0f7e6f3c62fb306098467720072354", {"version": "25c28649e44aeead69a588b73b91ba27b0a08a7cdb7782f52ee1d8122305912c", "affectsGlobalScope": true}, "a7a9834ecced9288b5b6f5859443307adedaf02a59b1412c8a9af2055081823f", "cd51ceafea7762ad639afb3ca5b68e1e4ffeaacaa402d7ef2cae17016e29e098", "1b8357b3fef5be61b5de6d6a4805a534d68fe3e040c11f1944e27d4aec85936a", "4a15fc59b27b65b9894952048be2afc561865ec37606cd0f5e929ee4a102233b", {"version": "744e7c636288493667d553c8f8ebd666ccbc0e715df445a4a7c4a48812f20544", "affectsGlobalScope": true}, "c05dcfbd5bd0abcefa3ad7d2931424d4d8090bc55bbe4f5c8acb8d2ca5886b2e", "326da4aebf555d54b995854ff8f3432f63ba067be354fa16c6e1f50daa0667de", "90748076a143bbeb455f8d5e8ad1cc451424c4856d41410e491268a496165256", "76e3f3a30c533bf20840d4185ce2d143dc18ca955b64400ac09670a89d388198", "144dfcee38ebc38aae93a85bc47211c9268d529b099127b74d61242ec5c17f35", "2cf38989b23031694f04308b6797877534a49818b2f5257f4a5d824e7ea82a5a", "f981ffdbd651f67db134479a5352dac96648ca195f981284e79dc0a1dbc53fd5", "e4ace1cf5316aa7720e58c8dd511ba86bab1c981336996fb694fa64b8231d5f0", "a1c85a61ff2b66291676ab84ae03c1b1ff7139ffde1942173f6aee8dc4ee357b", "f35a727758da36dd885a70dd13a74d9167691aaff662d50eaaf66ed591957702", "116205156fb819f2afe33f9c6378ea11b6123fa3090f858211c23f667fff75da", "8fe68442c15f8952b8816fa4e7e6bd8d5c45542832206bd7bcf3ebdc77d1c3f3", "3add9402f56a60e9b379593f69729831ac0fc9eae604b6fafde5fa86d2f8a4b9", "cc28c8b188905e790de427f3cd00b96734c9c662fb849d68ff9d5f0327165c0d", "da2aa652d2bf03cc042e2ff31e4194f4f18f042b8344dcb2568f761daaf7869f", "03ed68319c97cd4ce8f1c4ded110d9b40b8a283c3242b9fe934ccfa834e45572", "de2b56099545de410af72a7e430ead88894e43e4f959de29663d4d0ba464944d", "eec9e706eef30b4f1c6ff674738d3fca572829b7fa1715f37742863dabb3d2f2", "cec67731fce8577b0a90aa67ef0522ddb9f1fd681bece50cdcb80a833b4ed06f", "a14679c24962a81ef24b6f4e95bbc31601551f150d91af2dc0bce51f7961f223", "3f4d43bb3f61d173a4646c19557e090a06e9a2ec9415313a6d84af388df64923", "18b86125c67d99150f54225df07349ddd07acde086b55f3eeac1c34c81e424d8", "d5a5025f04e7a3264ecfa3030ca9a3cb0353450f1915a26d5b84f596240a11cd", "03f4449c691dd9c51e42efd51155b63c8b89a5f56b5cf3015062e2f818be8959", "23b213ec3af677b3d33ec17d9526a88d5f226506e1b50e28ce4090fb7e4050a8", "f0abf96437a6e57b9751a792ba2ebb765729a40d0d573f7f6800b305691b1afb", "7d30aee3d35e64b4f49c235d17a09e7a7ce2961bebb3996ee1db5aa192f3feba", "eb1625bab70cfed00931a1e09ecb7834b61a666b0011913b0ec24a8e219023ef", "1a923815c127b27f7f375c143bb0d9313ccf3c66478d5d2965375eeb7da72a4c", "4f92df9d64e5413d4b34020ae6b382edda84347daec97099e7c008a9d5c0910b", "fcc438e50c00c9e865d9c1777627d3fdc1e13a4078c996fb4b04e67e462648c8", "d0f07efa072420758194c452edb3f04f8eabc01cd4b3884a23e7274d4e2a7b69", "7086cca41a87b3bf52c6abfc37cda0a0ec86bb7e8e5ef166b07976abec73fa5e", "4571a6886b4414403eacdd1b4cdbd854453626900ece196a173e15fb2b795155", "c122227064c2ebf6a5bd2800383181395b56bb71fd6683d5e92add550302e45f", "60f476f1c4de44a08d6a566c6f1e1b7de6cbe53d9153c9cc2284ca0022e21fba", "84315d5153613eeb4b34990fb3bc3a1261879a06812ee7ae481141e30876d8dc", "4f0781ec008bb24dc1923285d25d648ea48fb5a3c36d0786e2ee82eb00eff426", "8fefaef4be2d484cdfc35a1b514ee7e7bb51680ef998fb9f651f532c0b169e6b", "8be5c5be3dbf0003a628f99ad870e31bebc2364c28ea3b96231089a94e09f7a6", "6626bbc69c25a92f6d32e6d2f25038f156b4c2380cbf29a420f7084fb1d2f7d7", "f351eaa598ba2046e3078e5480a7533be7051e4db9212bb40f4eeb84279aa24d", "5126032fe6e999f333827ee8e67f7ca1d5f3d6418025878aa5ebf13b499c2024", "4ce53edb8fb1d2f8b2f6814084b773cdf5846f49bf5a426fbe4029327bda95bf", "1edc9192dfc277c60b92525cdfa1980e1bfd161ae77286c96777d10db36be73c", "1573cae51ae8a5b889ec55ecb58e88978fe251fd3962efa5c4fdb69ce00b23ba", "75a7db3b7ddf0ca49651629bb665e0294fda8d19ba04fddc8a14d32bb35eb248", "f2d1ac34b05bb6ce326ea1702befb0216363f1d5eccdd1b4b0b2f5a7e953ed8a", "789665f0cd78bc675a31140d8f133ec6a482d753a514012fe1bb7f86d0a21040", "bb30fb0534dceb2e41a884c1e4e2bb7a0c668dadd148092bba9ff15aafb94790", "6ef829366514e4a8f75ce55fa390ebe080810b347e6f4a87bbeecb41e612c079", "8f313aa8055158f08bd75e3a57161fa473a50884c20142f3318f89f19bfc0373", "e789eb929b46299187312a01ff71905222f67907e546e491952c384b6f956a63", "a0147b607f8c88a5433a5313cdc10443c6a45ed430e1b0a335a413dc2b099fd5", "a86492d82baf906c071536e8de073e601eaa5deed138c2d9c42d471d72395d7e", "6b1071c06abcbe1c9f60638d570fdbfe944b6768f95d9f28ebc06c7eec9b4087", "92eb8a98444729aa61be5e6e489602363d763da27d1bcfdf89356c1d360484da", "1285ddb279c6d0bc5fe46162a893855078ae5b708d804cd93bfc4a23d1e903d9", "d729b8b400507b9b51ff40d11e012379dbf0acd6e2f66bf596a3bc59444d9bf1", "fc3ee92b81a6188a545cba5c15dc7c5d38ee0aaca3d8adc29af419d9bdb1fdb9", "a14371dc39f95c27264f8eb02ce2f80fd84ac693a2750983ac422877f0ae586d", "755bcc456b4dd032244b51a8b4fe68ee3b2d2e463cf795f3fde970bb3f269fb1", "c00b402135ef36fb09d59519e34d03445fd6541c09e68b189abb64151f211b12", "e08e58ac493a27b29ceee80da90bb31ec64341b520907d480df6244cdbec01f8", "c0fe2b1135ca803efa203408c953e1e12645b8065e1a4c1336ad8bb11ea1101b", "f3dedc92d06e0fdc43e76c2e1acca21759dd63d2572c9ec78a5188249965d944", "25b1108faedaf2043a97a76218240b1b537459bbca5ae9e2207c236c40dcfdef", "a1d1e49ccd2ac07ed8a49a3f98dfd2f7357cf03649b9e348b58b97bb75116f18", "7ad042f7d744ccfbcf6398216203c7712f01359d6fd4348c8bd8df8164e98096", "0e0b8353d6d7f7cc3344adbabf3866e64f2f2813b23477254ba51f69e8fdf0eb", "8e7653c13989dca094412bc4de20d5c449457fc92735546331d5e9cdd79ac16e", "189dedb255e41c8556d0d61d7f1c18506501896354d0925cbd47060bcddccab1", "48f0819c2e14214770232f1ab0058125bafdde1d04c4be84339d5533098bf60a", "2641aff32336e35a5b702aa2d870a0891da29dc1c19ae48602678e2050614041", "e133066d15e9e860ca96220a548dee28640039a8ac33a9130d0f83c814a78605", "574346a298daa75f0ae64ff8a9f5d2c90e1e3596364434ba70d5a7ed32ec41c9", {"version": "327cffcd6ed7bc29cfb5d1bfa2aea97ad9740967f5e396e0cbcd8a098ab589ae", "signature": "a750520ed7c3d70b1a28ab9f04e668e46ce14835129969cee972195dd2748ea2"}, {"version": "9a58e2e9e66c6cb39e1f5d210550376c524dad6b4dcf1f68750d3f62ebd724d4", "signature": "398fef4b1b4e9789ef27ad5fcef908cafbfc96ac080aa64a12054f89aad2017c"}, {"version": "82b79d4bd79636dd0757291068bdecdb9b430e8717c54f188850502234795d37", "signature": "7f4b0f70d0667827da8ba526fe35ef7c0429bdfc3d159b5b8d858b589d23147b"}, {"version": "1c6b232d352e101fca659307d6e4645993703f88ad08cb6eb2ca7bfc8caa47a8", "signature": "3cc6b7e81a9e9fb3e4417825a82eb2784b5f07aac7916f54dd92dfb8fc8423a7"}, {"version": "15aeb9a060ce1058b856b582358bd22fb4aadf10bd0fb153e4e43b702aef99b5", "signature": "8506285003b4d37becb074df1cd82614423a586eb8105912fbdbd09c6ecdbca9"}, {"version": "e5f86ec4efea2aa85e6373768bcfb90d5d4ab4c85365e92e6ad31ee04721795b", "signature": "d0ff03735dd105a9762fa2e3d36dae6018d69a3dc05a181260cd348fd18e5471"}, "21dbdb259c834c842cada3a8764f9ee1c75be991cfbca936355dd18cd1d275fd", {"version": "ff895714477d4ecdc88500ec4e6c64c81445bd7f5184f79b8176fae07eac5015", "signature": "f44ea3df3d0252614970cfab03340f57ceec96cb2044607b2a869424699481af"}, {"version": "1140924540e46c1bf6aaded99c9c510dbf9430280e20fbbc5d6d9228ab39865e", "signature": "996fb72445bdd9f6bd57a1dc43036875f9f2facc300e4234f8c3a0f23fadbe72"}, {"version": "215bac0441e3993f55858b2be91b51106c5832f218974c6cba3efbf4ebbf2557", "signature": "c73a8c024c3002093d6f78f2edd65612e352b7a56a6a263455125e01b7d680bd"}, {"version": "2156d5a20b66ccf7c8ba3b78dd3287745c9da6215a829a2b0c13a3a7cf7e3bc1", "signature": "b318aa68c9d59fa6b2b1710513670f1ff5f3c0b1d71b75fa1d7f0f84df98d2a8"}, {"version": "5296ce883cf1680a9d35aa6dbd599077f8d4766a1df9bd554da74ee8ccbc2f0d", "signature": "45693fed81c0e4b4772eabb3f53a9d6528461f549c77e6946d30dc723270b6d1"}, {"version": "9a90fce9f8b3d28317c078c374cf1d1baf0e034a97c8679953e196b103364bf8", "signature": "182b41b716105cc05c0f09f4fa9e8e7b24c2747fb190fff50011c70b975b74b7"}, {"version": "1640706c0a02f9325b0a269c0d5f9f0255718d61addfa9d39cd8b1c0c8aabfd4", "signature": "995f25a1c3f9c5037aef551d9e489b6c1b6dbb5b063811d4c26ec1a030e4c8d6"}, {"version": "dc4eb3c99676c9d7758737ff27ce0d5b08885bfa0cfd2c44d06a510694b18838", "signature": "58f5967c3ae06a5306503f6509991bbd345b09886ded908ff023300629e1f971"}, {"version": "11568d38bbc433ba2d0966470dddb98a67918737f4328a16b9983964d58fa343", "signature": "82c2aa837212f94e106e27ed04acfdce9f661d88cb71f6f3f92240d1310ede95"}, {"version": "dee5888ea122780bcc3ffc3b94b691b604b84d08848186047bdd30f440e5296a", "signature": "8a957bf8e83e719c979e7c45a8c3f7dbce8bc30f0a4b81053ce990aadeb88ec4"}, {"version": "f16c77068592436c275e5b530e29ce977fce5e73ea8a08001647486230b4572c", "signature": "070e64250b784df6f6aac9dfc0599ba64d97190274329841562556207a31c6b0"}, "19ce9ec982b542ef6d04d29ce678aad2fa52a67d8087e9c6cd95a4d6d98784c8", "4af47b2f19621ce8f638167a32f141a3a2c0e71ce8ebf51384393ca1c2654e60", "462bccdf75fcafc1ae8c30400c9425e1a4681db5d605d1a0edb4f990a54d8094", "5923d8facbac6ecf7c84739a5c701a57af94a6f6648d6229a6c768cf28f0f8cb", "0beeeb5964025d8564c03cb0bf1a4b60dc40c01f065ad1a4e9030415f5bc7bc2", "370d29706526cf66ee767a4a3ee4218c9544a252ce22f231648414348704cb4c", "6bf53608a27a76ef8580f9577618174f0dd5325142cafb8b3a19aa4850319afb", "821fe27bd167988c3cc518af8d9591ac1bd8d9e9d231ee9eac7b82786dd9f3a6", {"version": "eef204f061321360559bd19235ea32a9d55b3ec22a362cc78d14ef50d4db4490", "affectsGlobalScope": true}, "2ea3c07887a34c982f1d902139569a86bfa4fbf5ab79c3397aec80b2ceb20b05", "73b67d2e87ac7d7baaca64ca33fd1523c0b3c850cb7db5b9c014f1be7996bed1", "5d5ae61fce1581fd6424269790a9071e3f8e69b029f5d0fcb46ce618c5dbc565", "38a0ccc7106312a9f60e034e7cd8ac218774d8aa65f832cee3363a7e65f99325", "850040826cfa77593d44f44487133af21917f4f21507258bd4269501b80d32f0", "8f07f2b6514744ac96e51d7cb8518c0f4de319471237ea10cf688b8d0e9d0225", "bcb6ea18f23dae2c48459d7b86d3adccd6898f824fcbf9da08b935f559896580", "1363ba7d52f2353d0c4306d0ecdaf171bf4509c0148842f9fd8d3986c098a2eb", "3a24f4a428f24cad90b83fab329a620c4adbace083a8eda62c63365065b79e73", "739c2c46edc112421fc023c24b4898b1f413f792bb6a02b40ba182c648e56c2f", "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "33f3718dababfc26dfd9832c150149ea4e934f255130f8c118a59ae69e5ed441", "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "1bb61aa2f08ab4506d41dbe16c5f3f5010f014bbf46fa3d715c0cbe3b00f4e1c", "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "e42820cd611b15910c204cd133f692dcd602532b39317d4f2a19389b27e6f03d", "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "f234315aeb08f02d74769341155afa47ef6ec6873607f55c6a9104d50fc27383", "1c53e1884dc6550ce179c68e9e3086f54af258fff39eb70274ea9294eb7ce6df", "2d57bdaafc7cd0ebc006f0e77c869d6fe6148809c08e8b5677aef4150cf4a7c7", "05c7aef6a4e496b93c2e682cced8903c0dfe6340d04f3fe616176e2782193435", "67856637713bace00feca7f0d4a9907e6a85bcceeb507e07df852cb5f6467834", "e666e31d323fef5642f87db0da48a83e58f0aaf9e3823e87eabd8ec7e0441a36", "69bf2422313487956e4dacf049f30cb91b34968912058d244cb19e4baa24da97", "d12ab69ace581804d4f264eabc71094ca8dadfa70ae2bf5ccd54a8d6105ab84b", "973af20e33ebed2f6c3af36062c214b03daf2a0a3193554f6538ea928228b671", "ca179564af22b92d588ce07d527042767d37bacce79fb78cd6fc7d8ff8c1f329", "e72396ce544667ab49df38ffb91cb3f80ff17d2ad3df903ec30b1d2ca8ea68de", "b7e28e06011460436d5c2ec2996846ac0c451e135357fc5a7269e5665a32fbd7", "d422e50d00562af6bb419fca3a81c8437391acc13f52672dcffdfc3da2a93125", "2887592574fcdfd087647c539dcb0fbe5af2521270dad4a37f9d17c16190d579", "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "632d6fe34a09b481d3007779ad03e4202e4ed4f73bee02fdfb6b23e51ca4aebd", {"version": "7c4d59b36618af873cc489404906c46e2a2f0629a8416ee08b711f5f02096f3f", "affectsGlobalScope": true}, "3fa571018b674c0cdc74584b04f32c421829c409236e1332af4a87ad904b504d", "2d37a18dbc84466a72a4b00d0293ecfe3170fc664ca32126db0b7eace05824d5", "f63e23230f3960b712450cf08f0f31e56acdae3ce65e0bf31bfdd6442b29d115", "f2d1a59a658165341b0e2b7879aa2e19ea6a709146b2d3f70ee8a07159d3d08e", "5309c17206a98ed2bdd130eb25a213e864697f5b017f774141c12030e82db573", "8a3ff3da0cc09f4c5523f6e336635cd0e2cd5cc7e5297186b44b6a6b06e3ef96", {"version": "829325a03054bf6c70506fa5cfcd997944faf73c54c9285d1a2d043d239f4565", "affectsGlobalScope": true}], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "esModuleInterop": true, "jsx": 4, "module": 99, "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": false, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": false, "strictNullChecks": false, "target": 7}, "fileIdsList": [[123, 173], [173], [173, 192, 193, 194], [173, 192], [60, 173, 196, 197, 276], [60, 173, 196], [60, 122, 128, 147, 173, 191, 195], [123, 124, 125, 126, 127, 173], [123, 125, 173], [147, 173, 180, 188], [147, 173, 180], [144, 147, 173, 180, 182, 183, 184], [173, 183, 185, 187, 189, 190], [60, 173], [129, 173], [132, 173], [133, 138, 164, 173], [134, 144, 145, 152, 161, 172, 173], [134, 135, 144, 152, 173], [136, 173], [137, 138, 145, 153, 173], [138, 161, 169, 173], [139, 141, 144, 152, 173], [140, 173], [141, 142, 173], [143, 144, 173], [144, 173], [144, 145, 146, 161, 172, 173], [144, 145, 146, 161, 173], [147, 152, 161, 172, 173], [144, 145, 147, 148, 152, 161, 169, 172, 173], [147, 149, 161, 169, 172, 173], [129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179], [144, 150, 173], [151, 172, 173], [141, 144, 152, 161, 173], [153, 173], [154, 173], [132, 155, 173], [156, 171, 173, 177], [157, 173], [158, 173], [144, 159, 173], [159, 160, 173, 175], [133, 144, 161, 162, 163, 173], [133, 161, 163, 173], [161, 162, 173], [164, 173], [165, 173], [144, 167, 168, 173], [167, 168, 173], [138, 152, 169, 173], [170, 173], [152, 171, 173], [133, 147, 158, 172, 173], [138, 173], [161, 173, 174], [173, 175], [173, 176], [133, 138, 144, 146, 155, 161, 172, 173, 175, 177], [161, 173, 178], [57, 58, 59, 173], [145, 161, 173, 180, 181], [147, 173, 180, 182, 186], [58, 60, 67, 173], [173, 300, 304], [173, 300, 301, 302], [173, 301], [173, 300], [173, 300, 301, 338], [173, 335], [173, 339], [173, 306], [173, 299, 306], [173, 299, 306, 307], [173, 354], [173, 345], [173, 352], [173, 337], [173, 296], [173, 296, 297, 299], [121, 173], [173, 330], [173, 328, 330], [173, 319, 327, 328, 329, 331], [173, 317], [173, 320, 325, 330, 333], [173, 316, 333], [173, 320, 321, 324, 325, 326, 333], [173, 320, 321, 322, 324, 325, 333], [173, 317, 318, 319, 320, 321, 325, 326, 327, 329, 330, 331, 333], [173, 315, 317, 318, 319, 320, 321, 322, 324, 325, 326, 327, 328, 329, 330, 331, 332], [173, 315, 333], [173, 320, 322, 323, 325, 326, 333], [173, 324, 333], [173, 325, 326, 330, 333], [173, 318, 328], [173, 298], [173, 198, 199, 200, 201, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275], [173, 224], [173, 224, 237], [173, 202, 251], [173, 252], [173, 203, 226], [173, 226], [173, 202], [173, 255], [173, 235], [173, 202, 243, 251], [173, 246], [173, 248], [173, 198], [173, 218], [173, 199, 200, 239], [173, 259], [173, 257], [173, 203, 204], [173, 205], [173, 216], [173, 202, 207], [173, 261], [173, 203], [173, 255, 264, 267], [173, 203, 204, 248], [173, 342, 343], [173, 334, 342, 343, 351], [173, 342], [144, 145, 147, 149, 152, 161, 169, 172, 173, 178, 180, 309, 310, 311, 312, 313, 314, 333], [145, 173, 177, 300, 303, 304, 305, 308, 334, 336, 340, 341, 344, 346, 347, 348, 350, 351], [145, 173, 177, 300, 303, 304, 305, 308, 334, 336, 340, 341, 344, 346, 347, 348, 350, 351, 353, 355, 356], [173, 357], [173, 311], [173, 313], [61, 173], [61, 64, 65, 173], [61, 64, 173], [61, 69, 107, 173, 277], [60, 61, 68, 118, 173], [61, 69, 70, 71, 72, 73, 74, 75, 173], [60, 61, 71, 107, 173, 277], [60, 61, 73, 107, 173, 277], [61, 75, 107, 173, 277], [60, 61, 173], [61, 76, 89, 91, 93, 173], [61, 70, 77, 107, 173, 277], [60, 61, 68, 70, 118, 173], [61, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 173], [60, 61, 70, 71, 83, 107, 173, 277], [60, 61, 68, 70, 82, 118, 173], [60, 61, 70, 84, 107, 173, 277], [60, 61, 65, 88, 173], [60, 61, 62, 68, 70, 85, 86, 87, 118, 173], [60, 61, 62, 68, 69, 118, 173], [60, 61, 62, 68, 70, 71, 73, 118, 173], [60, 61, 62, 68, 85, 118, 173], [60, 61, 79, 173], [61, 70, 90, 107, 173, 277], [60, 61, 68, 72, 77, 78, 118, 173], [61, 90, 173], [61, 70, 77, 92, 173, 277], [61, 92, 173], [61, 95, 96, 97, 98, 99, 173], [60, 61, 98, 173], [61, 63, 66, 94, 100, 108, 112, 113, 114, 115, 173], [61, 65, 173], [61, 62, 173], [61, 109, 110, 111, 173], [61, 109, 173], [68, 101, 118, 173], [61, 101, 102, 173], [61, 68, 101, 118, 173], [61, 101, 102, 103, 104, 105, 107, 173], [60, 61, 68, 101, 103, 104, 105, 106, 118, 173], [61, 173, 289, 290, 291], [61, 173, 293, 294], [123, 133, 359], [133, 359], [133, 192, 193, 194, 359], [133, 192, 359], [60, 133, 196, 197, 276, 359], [60, 133, 196, 359], [123, 124, 125, 126, 127, 133, 359], [123, 125, 133, 359], [133, 147, 188, 359, 360], [133, 147, 359, 360], [133, 144, 147, 182, 183, 184, 359, 360], [133, 183, 185, 187, 189, 190, 359], [60, 133, 359], [57, 58, 59, 133, 359], [133, 145, 161, 181, 359, 360], [133, 147, 182, 186, 359, 360], [58, 60, 67, 133, 359], [133, 300, 304, 359], [133, 300, 301, 302, 359], [133, 301, 359], [133, 300, 359], [133, 300, 301, 338, 359], [133, 335, 359], [133, 339, 359], [133, 306, 359], [133, 299, 306, 359], [133, 299, 306, 307, 359], [133, 354, 359], [133, 345, 359], [133, 352, 359], [133, 337, 359], [133, 296, 359], [133, 173, 296, 297, 299, 359], [121, 133, 359], [133, 330, 359], [133, 328, 330, 359], [133, 319, 327, 328, 329, 331, 359], [133, 317, 359], [133, 320, 325, 330, 333, 359], [133, 316, 333, 359], [133, 320, 321, 324, 325, 326, 333, 359], [133, 320, 321, 322, 324, 325, 333, 359], [133, 317, 318, 319, 320, 321, 325, 326, 327, 329, 330, 331, 333, 359], [133, 315, 317, 318, 319, 320, 321, 322, 324, 325, 326, 327, 328, 329, 330, 331, 332, 359], [133, 315, 333, 359], [133, 320, 322, 323, 325, 326, 333, 359], [133, 324, 333, 359], [133, 325, 326, 330, 333, 359], [133, 318, 328, 359], [133, 298, 359], [133, 198, 199, 200, 201, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 359], [133, 224, 359], [133, 224, 237, 359], [133, 202, 251, 359], [133, 252, 359], [133, 203, 226, 359], [133, 226, 359], [133, 202, 359], [133, 255, 359], [133, 235, 359], [133, 202, 243, 251, 359], [133, 246, 359], [133, 248, 359], [133, 198, 359], [133, 218, 359], [133, 199, 200, 239, 359], [133, 259, 359], [133, 257, 359], [133, 203, 204, 359], [133, 205, 359], [133, 216, 359], [133, 202, 207, 359], [133, 261, 359], [133, 203, 359], [133, 255, 264, 267, 359], [133, 203, 204, 248, 359], [133, 342, 343, 359], [133, 342, 343, 351, 359, 361], [133, 342, 359], [133, 145, 177, 300, 303, 304, 305, 308, 336, 340, 341, 344, 346, 347, 348, 350, 351, 359, 361], [133, 145, 177, 300, 303, 304, 305, 308, 336, 340, 341, 344, 346, 347, 348, 350, 351, 353, 355, 356, 359, 361], [133, 357, 359], [64, 65], [64], [69, 277], [60], [69, 70, 71, 72, 73, 74, 75], [71, 277], [73, 277], [75, 277], [77, 277], [77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88], [83, 277], [60, 62, 85], [60, 62], [60, 79], [90, 277], [60, 77], [90], [92, 277], [92], [95, 96, 97, 98, 99], [63, 66, 94, 100, 108, 112, 113, 114, 115], [62], [109, 110, 111], [109], [68, 101, 118, 133, 359], [101], [68, 101, 118], [101, 102, 103, 104, 105, 107], [60, 101], [289, 290, 291], [293, 294]], "referencedMap": [[125, 1], [123, 2], [195, 3], [192, 2], [193, 4], [194, 4], [277, 5], [197, 6], [196, 7], [128, 8], [124, 1], [126, 9], [127, 1], [189, 10], [304, 2], [188, 11], [185, 12], [191, 13], [190, 12], [67, 14], [186, 2], [181, 2], [129, 15], [130, 15], [132, 16], [133, 17], [134, 18], [135, 19], [136, 20], [137, 21], [138, 22], [139, 23], [140, 24], [141, 25], [142, 25], [143, 26], [144, 27], [145, 28], [146, 29], [131, 2], [179, 2], [147, 30], [148, 31], [149, 32], [180, 33], [150, 34], [151, 35], [152, 36], [153, 37], [154, 38], [155, 39], [156, 40], [157, 41], [158, 42], [159, 43], [160, 44], [161, 45], [163, 46], [162, 47], [164, 48], [165, 49], [166, 2], [167, 50], [168, 51], [169, 52], [170, 53], [171, 54], [172, 55], [173, 56], [174, 57], [175, 58], [176, 59], [177, 60], [178, 61], [59, 2], [183, 2], [184, 2], [82, 14], [57, 2], [60, 62], [61, 14], [182, 63], [187, 64], [68, 65], [305, 66], [303, 67], [302, 68], [301, 69], [335, 67], [339, 70], [336, 71], [340, 72], [306, 2], [354, 73], [307, 74], [308, 75], [345, 75], [355, 76], [346, 77], [353, 78], [338, 79], [337, 2], [297, 80], [300, 81], [296, 2], [58, 2], [309, 2], [121, 2], [122, 82], [331, 83], [329, 84], [330, 85], [318, 86], [319, 84], [326, 87], [317, 88], [322, 89], [332, 2], [323, 90], [328, 91], [333, 92], [316, 93], [324, 94], [325, 95], [320, 96], [327, 83], [321, 97], [299, 98], [298, 2], [315, 2], [348, 2], [341, 2], [352, 2], [276, 99], [225, 100], [238, 101], [200, 2], [252, 102], [254, 103], [253, 103], [227, 104], [226, 2], [228, 105], [255, 106], [259, 107], [257, 107], [236, 108], [235, 2], [244, 106], [203, 106], [231, 2], [272, 109], [247, 110], [249, 111], [267, 106], [202, 112], [219, 113], [234, 2], [269, 2], [240, 114], [256, 107], [260, 115], [258, 116], [273, 2], [242, 2], [216, 112], [208, 2], [207, 117], [232, 106], [233, 106], [206, 118], [239, 2], [201, 2], [218, 2], [246, 2], [274, 119], [213, 106], [214, 120], [261, 103], [263, 121], [262, 121], [198, 2], [217, 2], [224, 2], [215, 106], [245, 2], [212, 2], [271, 2], [211, 2], [209, 122], [210, 2], [248, 2], [241, 2], [268, 123], [222, 117], [220, 117], [221, 117], [237, 2], [204, 2], [264, 107], [266, 115], [265, 116], [251, 2], [250, 124], [243, 2], [230, 2], [270, 2], [275, 2], [199, 2], [229, 2], [223, 2], [205, 117], [11, 2], [12, 2], [14, 2], [13, 2], [2, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [20, 2], [21, 2], [22, 2], [3, 2], [4, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [8, 2], [49, 2], [46, 2], [47, 2], [48, 2], [50, 2], [9, 2], [51, 2], [52, 2], [53, 2], [54, 2], [55, 2], [1, 2], [10, 2], [56, 2], [344, 125], [347, 125], [350, 126], [343, 127], [342, 2], [349, 128], [356, 129], [357, 130], [351, 129], [358, 131], [313, 2], [334, 128], [312, 132], [311, 2], [314, 2], [310, 133], [119, 134], [66, 135], [64, 136], [278, 137], [69, 138], [70, 138], [76, 139], [279, 140], [71, 138], [72, 138], [280, 141], [73, 138], [74, 138], [281, 142], [75, 138], [120, 143], [94, 144], [282, 145], [77, 138], [78, 146], [79, 138], [81, 138], [89, 147], [283, 148], [83, 149], [284, 150], [84, 146], [285, 151], [88, 152], [85, 153], [87, 154], [86, 155], [80, 156], [286, 157], [90, 158], [91, 159], [287, 160], [92, 138], [93, 161], [100, 162], [95, 143], [96, 143], [97, 143], [98, 143], [99, 163], [116, 164], [114, 134], [117, 14], [115, 165], [65, 166], [110, 134], [109, 143], [112, 167], [111, 168], [118, 169], [105, 170], [103, 170], [106, 171], [108, 172], [104, 170], [288, 134], [107, 173], [102, 134], [289, 134], [292, 174], [290, 134], [291, 134], [101, 134], [293, 170], [295, 175], [294, 170], [63, 166], [62, 134], [113, 134]], "exportedModulesMap": [[125, 176], [123, 177], [195, 178], [192, 177], [193, 179], [194, 179], [277, 180], [197, 181], [196, 7], [128, 182], [124, 176], [126, 183], [127, 176], [189, 184], [304, 177], [188, 185], [185, 186], [191, 187], [190, 186], [67, 188], [186, 177], [181, 177], [129, 15], [130, 15], [132, 16], [133, 17], [134, 18], [135, 19], [136, 20], [137, 21], [138, 22], [139, 23], [140, 24], [141, 25], [142, 25], [143, 26], [144, 27], [145, 28], [146, 29], [131, 2], [179, 2], [147, 30], [148, 31], [149, 32], [180, 33], [150, 34], [151, 35], [152, 36], [153, 37], [154, 38], [155, 39], [156, 40], [157, 41], [158, 42], [159, 43], [160, 44], [161, 45], [163, 46], [162, 47], [164, 48], [165, 49], [166, 2], [167, 50], [168, 51], [169, 52], [170, 53], [171, 54], [172, 55], [173, 56], [174, 57], [175, 58], [176, 59], [177, 60], [178, 61], [59, 177], [183, 177], [184, 177], [82, 188], [57, 177], [60, 189], [61, 188], [182, 190], [187, 191], [68, 192], [305, 193], [303, 194], [302, 195], [301, 196], [335, 194], [339, 197], [336, 198], [340, 199], [306, 177], [354, 200], [307, 201], [308, 202], [345, 202], [355, 203], [346, 204], [353, 205], [338, 206], [337, 177], [297, 207], [300, 208], [296, 177], [58, 177], [309, 177], [121, 177], [122, 209], [331, 210], [329, 211], [330, 212], [318, 213], [319, 211], [326, 214], [317, 215], [322, 216], [332, 177], [323, 217], [328, 218], [333, 219], [316, 220], [324, 221], [325, 222], [320, 223], [327, 210], [321, 224], [299, 225], [298, 177], [315, 177], [348, 177], [341, 177], [352, 177], [276, 226], [225, 227], [238, 228], [200, 177], [252, 229], [254, 230], [253, 230], [227, 231], [226, 177], [228, 232], [255, 233], [259, 234], [257, 234], [236, 235], [235, 177], [244, 233], [203, 233], [231, 177], [272, 236], [247, 237], [249, 238], [267, 233], [202, 239], [219, 240], [234, 177], [269, 177], [240, 241], [256, 234], [260, 242], [258, 243], [273, 177], [242, 177], [216, 239], [208, 177], [207, 244], [232, 233], [233, 233], [206, 245], [239, 177], [201, 177], [218, 177], [246, 177], [274, 246], [213, 233], [214, 247], [261, 230], [263, 248], [262, 248], [198, 177], [217, 177], [224, 177], [215, 233], [245, 177], [212, 177], [271, 177], [211, 177], [209, 249], [210, 177], [248, 177], [241, 177], [268, 250], [222, 244], [220, 244], [221, 244], [237, 177], [204, 177], [264, 234], [266, 242], [265, 243], [251, 177], [250, 251], [243, 177], [230, 177], [270, 177], [275, 177], [199, 177], [229, 177], [223, 177], [205, 244], [11, 177], [12, 177], [14, 177], [13, 177], [2, 177], [15, 177], [16, 177], [17, 177], [18, 177], [19, 177], [20, 177], [21, 177], [22, 177], [3, 177], [4, 177], [26, 177], [23, 177], [24, 177], [25, 177], [27, 177], [28, 177], [29, 177], [5, 177], [30, 177], [31, 177], [32, 177], [33, 177], [6, 177], [37, 177], [34, 177], [35, 177], [36, 177], [38, 177], [7, 177], [39, 177], [44, 177], [45, 177], [40, 177], [41, 177], [42, 177], [43, 177], [8, 177], [49, 177], [46, 177], [47, 177], [48, 177], [50, 177], [9, 177], [51, 177], [52, 177], [53, 177], [54, 177], [55, 177], [1, 177], [10, 177], [56, 177], [344, 252], [347, 252], [350, 253], [343, 254], [342, 177], [349, 128], [356, 255], [357, 256], [351, 255], [358, 257], [313, 2], [334, 128], [312, 132], [311, 2], [314, 2], [310, 133], [66, 258], [64, 259], [278, 260], [69, 261], [70, 261], [76, 262], [279, 263], [71, 261], [72, 261], [280, 264], [73, 261], [74, 261], [281, 265], [75, 261], [120, 261], [94, 144], [282, 266], [77, 261], [78, 261], [79, 261], [81, 261], [89, 267], [283, 268], [83, 261], [284, 150], [84, 261], [285, 261], [88, 269], [85, 270], [87, 270], [86, 269], [80, 271], [286, 272], [90, 273], [91, 274], [287, 275], [92, 261], [93, 276], [100, 277], [116, 278], [117, 188], [115, 165], [65, 279], [109, 261], [112, 280], [111, 281], [118, 282], [105, 283], [103, 283], [106, 284], [108, 285], [104, 283], [107, 286], [292, 287], [293, 283], [295, 288], [294, 283], [63, 279]], "semanticDiagnosticsPerFile": [125, 123, 195, 192, 193, 194, 277, 197, 196, 128, 124, 126, 127, 189, 304, 188, 185, 191, 190, 67, 186, 181, 129, 130, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 131, 179, 147, 148, 149, 180, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 163, 162, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 59, 183, 184, 82, 57, 60, 61, 182, 187, 68, 305, 303, 302, 301, 335, 339, 336, 340, 306, 354, 307, 308, 345, 355, 346, 353, 338, 337, 297, 300, 296, 58, 309, 121, 122, 331, 329, 330, 318, 319, 326, 317, 322, 332, 323, 328, 333, 316, 324, 325, 320, 327, 321, 299, 298, 315, 348, 341, 352, 276, 225, 238, 200, 252, 254, 253, 227, 226, 228, 255, 259, 257, 236, 235, 244, 203, 231, 272, 247, 249, 267, 202, 219, 234, 269, 240, 256, 260, 258, 273, 242, 216, 208, 207, 232, 233, 206, 239, 201, 218, 246, 274, 213, 214, 261, 263, 262, 198, 217, 224, 215, 245, 212, 271, 211, 209, 210, 248, 241, 268, 222, 220, 221, 237, 204, 264, 266, 265, 251, 250, 243, 230, 270, 275, 199, 229, 223, 205, 11, 12, 14, 13, 2, 15, 16, 17, 18, 19, 20, 21, 22, 3, 4, 26, 23, 24, 25, 27, 28, 29, 5, 30, 31, 32, 33, 6, 37, 34, 35, 36, 38, 7, 39, 44, 45, 40, 41, 42, 43, 8, 49, 46, 47, 48, 50, 9, 51, 52, 53, 54, 55, 1, 10, 56, 344, 347, 350, 343, 342, 349, 356, 357, 351, 358, 313, 334, 312, 311, 314, 310, 119, 66, [64, [{"file": "./src/api/types/index.ts", "start": 236, "length": 18, "messageText": "Cannot find module '../types/trading' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/api/types/index.ts", "start": 723, "length": 21, "messageText": "Cannot find name 'ConfigurationResponse'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 777, "length": 21, "messageText": "Cannot find name 'DashboardDataResponse'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 828, "length": 18, "messageText": "Cannot find name 'TradingDataOptions'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 857, "length": 19, "messageText": "Cannot find name 'TradingDataResponse'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 913, "length": 25, "messageText": "Cannot find name 'PerformanceMetricsOptions'. Did you mean 'PerformanceMarkOptions'?", "category": 1, "code": 2552}, {"file": "./src/api/types/index.ts", "start": 949, "length": 26, "messageText": "Cannot find name 'PerformanceMetricsResponse'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 1004, "length": 17, "messageText": "Cannot find name 'MarketNewsOptions'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 1032, "length": 18, "messageText": "Cannot find name 'MarketNewsResponse'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 1088, "length": 15, "messageText": "Cannot find name 'UserPreferences'.", "category": 1, "code": 2304}, {"file": "./src/api/types/index.ts", "start": 1114, "length": 11, "messageText": "Cannot find name 'ApiResponse'. Did you mean 'Response'?", "category": 1, "code": 2552, "relatedInformation": [{"file": "../../node_modules/typescript/lib/lib.dom.d.ts", "start": 494986, "length": 8, "messageText": "'Response' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/api/types/index.ts", "start": 1160, "length": 23, "messageText": "Cannot find name 'UserPreferencesResponse'.", "category": 1, "code": 2304}]], 278, [69, [{"file": "./src/components/atoms/badge.tsx", "start": 6386, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ children, variant, size, solid, className, onClick, rounded, dot, counter, outlined, startIcon, endIcon, max, inline, }: { children: any; variant?: string; size?: string; solid?: boolean; className: any; onClick: any; rounded?: boolean; dot?: boolean; counter?: boolean; outlined?: boolean; startIcon: any; endIcon...' is not assignable to type 'FC<BadgeProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'BadgeProps' is not assignable to type '{ children: any; variant?: string; size?: string; solid?: boolean; className: any; onClick: any; rounded?: boolean; dot?: boolean; counter?: boolean; outlined?: boolean; startIcon: any; endIcon: any; max: any; inline?: boolean; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'className' is optional in type 'BadgeProps' but required in type '{ children: any; variant?: string; size?: string; solid?: boolean; className: any; onClick: any; rounded?: boolean; dot?: boolean; counter?: boolean; outlined?: boolean; startIcon: any; endIcon: any; max: any; inline?: boolean; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/atoms/badge.tsx", "start": 6856, "length": 272, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"span\", DefaultTheme, { variant: BadgeVariant; size: BadgeSize; solid: boolean; clickable: boolean; rounded?: boolean; dot?: boolean; counter?: boolean; outlined?: boolean; inline?: boolean; }, never, \"span\", \"span\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'BadgeVariant'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"span\", DefaultTheme, { variant: BadgeVariant; size: BadgeSize; solid: boolean; clickable: boolean; rounded?: boolean; dot?: boolean; counter?: boolean; outlined?: boolean; inline?: boolean; }, never, \"span\", \"span\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'BadgeSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/badge.tsx", "start": 5028, "length": 7, "messageText": "The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSpanElement>; key?: Key; ... 261 more ...; clickable: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/badge.tsx", "start": 5053, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSpanElement>; key?: Key; ... 261 more ...; clickable: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], [70, [{"file": "./src/components/atoms/button.tsx", "start": 6397, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ children, variant, disabled, loading, size, fullWidth, startIcon, endIcon, onClick, className, type, ...rest }: { [x: string]: any; children: any; variant?: string; disabled?: boolean; loading?: boolean; size?: string; fullWidth?: boolean; startIcon: any; endIcon: any; onClick: any; className: any; type?: string;...' is not assignable to type 'FC<ButtonProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'ButtonProps' is not assignable to type '{ [x: string]: any; children: any; variant?: string; disabled?: boolean; loading?: boolean; size?: string; fullWidth?: boolean; startIcon: any; endIcon: any; onClick: any; className: any; type?: string; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'startIcon' is optional in type 'ButtonProps' but required in type '{ [x: string]: any; children: any; variant?: string; disabled?: boolean; loading?: boolean; size?: string; fullWidth?: boolean; startIcon: any; endIcon: any; onClick: any; className: any; type?: string; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/atoms/button.tsx", "start": 6646, "length": 300, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"button\", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StyledButtonProps, never, \"button\", \"button\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"text\" | \"outline\" | \"primary\" | \"secondary\" | \"success\" | \"danger\"'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"button\", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StyledButtonProps, never, \"button\", \"button\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"small\" | \"medium\" | \"large\"'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"button\", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StyledButtonProps, never, \"button\", \"button\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"button\" | \"submit\" | \"reset\"'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/button.tsx", "start": 4892, "length": 7, "messageText": "The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLButtonElement>; ... 267 more ...; $hasEndIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/button.tsx", "start": 4928, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLButtonElement>; ... 267 more ...; $hasEndIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "../../node_modules/@types/react/index.d.ts", "start": 92139, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLButtonElement>; ... 267 more ...; $hasEndIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], 76, 279, [71, [{"file": "./src/components/atoms/input.tsx", "start": 6375, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ value, onChange, placeholder, disabled, error, type, name, id, className, required, autoComplete, label, helperText, startIcon, endIcon, loading, success, clearable, onClear, maxLength, showCharCount, size, fullWidth, ...rest }: { [x: string]: any; value: any; onChange: any; placeholder: any; disabled?: boolean; ...' is not assignable to type 'FC<InputProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'InputProps' is not assignable to type '{ [x: string]: any; value: any; onChange: any; placeholder: any; disabled?: boolean; error: any; type?: string; name: any; id: any; className: any; required?: boolean; autoComplete: any; label: any; helperText: any; ... 9 more ...; fullWidth?: boolean; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'placeholder' is optional in type 'InputProps' but required in type '{ [x: string]: any; value: any; onChange: any; placeholder: any; disabled?: boolean; error: any; type?: string; name: any; id: any; className: any; required?: boolean; autoComplete: any; label: any; helperText: any; ... 9 more ...; fullWidth?: boolean; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/atoms/input.tsx", "start": 7948, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; defaultChecked?: boolean; defaultValue?: string | ... 1 more ... | readonly string[]; ... 257 more ...; hasEndIcon?: boolean; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"small\" | \"medium\" | \"large\"'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", Default<PERSON><PERSON>e, InputContainerProps, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"small\" | \"medium\" | \"large\"'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/input.tsx", "start": 2642, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 259 more ...; hasEndIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/input.tsx", "start": 2642, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 259 more ...; hasEndIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/atoms/input.tsx", "start": 8670, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; pattern?: string; ref?: Ref<HTMLInputElement>; type?: HTMLInputTypeAttribute; ... 283 more ...; hasEndIcon?: boolean; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'any' is not assignable to type 'never'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"input\", DefaultTheme, StyledInputProps, never, \"input\", \"input\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'any' is not assignable to type 'never'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "../../node_modules/@types/react/index.d.ts", "start": 97709, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; pattern?: string; ref?: Ref<HTMLInputElement>; ... 284 more ...; hasEndIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "../../node_modules/@types/react/index.d.ts", "start": 97709, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; pattern?: string; ref?: Ref<HTMLInputElement>; ... 284 more ...; hasEndIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], [72, [{"file": "./src/components/atoms/loadingplaceholder.tsx", "start": 3148, "length": 18, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ variant, size, height, width, text, showSpinner, className, }: { variant?: string; size?: string; height?: string; width: any; text?: string; showSpinner?: boolean; className: any; }) => Element' is not assignable to type 'FC<LoadingPlaceholderProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'LoadingPlaceholderProps' is not assignable to type '{ variant?: string; size?: string; height?: string; width: any; text?: string; showSpinner?: boolean; className: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'width' is optional in type 'LoadingPlaceholderProps' but required in type '{ variant?: string; size?: string; height?: string; width: any; text?: string; showSpinner?: boolean; className: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/atoms/loadingplaceholder.tsx", "start": 3359, "length": 140, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { variant: LoadingPlaceholderVariant; size: LoadingPlaceholderSize; customHeight: string; customWidth?: string; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'LoadingPlaceholderVariant'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { variant: LoadingPlaceholderVariant; size: LoadingPlaceholderSize; customHeight: string; customWidth?: string; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'LoadingPlaceholderSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/loadingplaceholder.tsx", "start": 2077, "length": 7, "messageText": "The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 256 more ...; customWidth?: string; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/loadingplaceholder.tsx", "start": 2115, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 256 more ...; customWidth?: string; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], 280, [73, [{"file": "./src/components/atoms/select.tsx", "start": 6339, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ options, value, onChange, disabled, error, name, id, className, required, placeholder, label, helperText, size, fullWidth, loading, success, startIcon, ...rest }: { [x: string]: any; options: any; value: any; onChange: any; disabled?: boolean; error: any; name: any; id: any; className: any; required?: boolean; pl...' is not assignable to type 'FC<SelectProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'SelectProps' is not assignable to type '{ [x: string]: any; options: any; value: any; onChange: any; disabled?: boolean; error: any; name: any; id: any; className: any; required?: boolean; placeholder: any; label: any; helperText: any; size?: string; fullWidth?: boolean; loading?: boolean; success?: boolean; startIcon: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'error' is optional in type 'SelectProps' but required in type '{ [x: string]: any; options: any; value: any; onChange: any; disabled?: boolean; error: any; name: any; id: any; className: any; required?: boolean; placeholder: any; label: any; helperText: any; size?: string; fullWidth?: boolean; loading?: boolean; success?: boolean; startIcon: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/atoms/select.tsx", "start": 7792, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; defaultChecked?: boolean; defaultValue?: string | ... 1 more ... | readonly string[]; ... 256 more ...; hasStartIcon?: boolean; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"small\" | \"medium\" | \"large\"'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SelectContainerProps, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"small\" | \"medium\" | \"large\"'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/select.tsx", "start": 2532, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 258 more ...; hasStartIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/select.tsx", "start": 2532, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 258 more ...; hasStartIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/atoms/select.tsx", "start": 8289, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSelectElement>; key?: Key; defaultChecked?: boolean; ... 259 more ...; hasStartIcon?: boolean; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'any' is not assignable to type 'never'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"select\", Default<PERSON><PERSON>e, StyledSelectProps, never, \"select\", \"select\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'any' is not assignable to type 'never'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "../../node_modules/@types/react/index.d.ts", "start": 103247, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSelectElement>; ... 261 more ...; hasStartIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "../../node_modules/@types/react/index.d.ts", "start": 103247, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSelectElement>; ... 261 more ...; hasStartIcon?: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], [74, [{"file": "./src/components/atoms/statusindicator.tsx", "start": 3583, "length": 15, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ status, size, pulse, showLabel, label, className, }: { status: any; size?: string; pulse?: boolean; showLabel?: boolean; label: any; className: any; }) => Element' is not assignable to type 'FC<StatusIndicatorProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'StatusIndicatorProps' is not assignable to type '{ status: any; size?: string; pulse?: boolean; showLabel?: boolean; label: any; className: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'label' is optional in type 'StatusIndicatorProps' but required in type '{ status: any; size?: string; pulse?: boolean; showLabel?: boolean; label: any; className: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/atoms/statusindicator.tsx", "start": 3899, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; defaultChecked?: boolean; defaultValue?: string | ... 1 more ... | readonly string[]; ... 253 more ...; pulse: boolean; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'StatusSize'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { status: StatusType; size: StatusSize; pulse: boolean; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'StatusSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/statusindicator.tsx", "start": 1813, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 255 more ...; pulse: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/statusindicator.tsx", "start": 1813, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 255 more ...; pulse: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/atoms/statusindicator.tsx", "start": 3981, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSpanElement>; key?: Key; defaultChecked?: boolean; defaultValue?: string | ... 1 more ... | readonly string[]; ... 252 more ...; status: StatusType; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'StatusSize'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"span\", DefaultTheme, { status: StatusType; size: StatusSize; }, never, \"span\", \"span\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'StatusSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/statusindicator.tsx", "start": 2849, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSpanElement>; key?: Key; ... 254 more ...; status: StatusType; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/statusindicator.tsx", "start": 2849, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSpanElement>; key?: Key; ... 254 more ...; status: StatusType; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], 281, [75, [{"file": "./src/components/atoms/tag.tsx", "start": 4348, "length": 3, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ children, variant, size, removable, onRemove, className, onClick, }: { children: any; variant?: string; size?: string; removable?: boolean; onRemove: any; className: any; onClick: any; }) => Element' is not assignable to type 'FC<TagProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'TagProps' is not assignable to type '{ children: any; variant?: string; size?: string; removable?: boolean; onRemove: any; className: any; onClick: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'onRemove' is optional in type 'TagProps' but required in type '{ children: any; variant?: string; size?: string; removable?: boolean; onRemove: any; className: any; onClick: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/atoms/tag.tsx", "start": 4617, "length": 138, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"span\", DefaultTheme, { variant: TagVariant; size: TagSize; clickable: boolean; }, never, \"span\", \"span\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'TagVariant'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"span\", DefaultTheme, { variant: TagVariant; size: TagSize; clickable: boolean; }, never, \"span\", \"span\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'TagSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/tag.tsx", "start": 3011, "length": 7, "messageText": "The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSpanElement>; key?: Key; ... 255 more ...; clickable: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/tag.tsx", "start": 3034, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLSpanElement>; key?: Key; ... 255 more ...; clickable: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/atoms/tag.tsx", "start": 4817, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLButtonElement>; type?: \"button\" | \"submit\" | \"reset\"; ... 262 more ...; formTarget?: string; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'TagSize'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"button\", DefaultTheme, { size: TagSize; }, never, \"button\", \"button\">): ReactElement<StyledComponentPropsWithAs<\"button\", ... 4 more ..., \"button\">, string | JSXElementConstructor<...>>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'TagSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/atoms/tag.tsx", "start": 3679, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLButtonElement>; ... 263 more ...; formTarget?: string; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/atoms/tag.tsx", "start": 3679, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; name?: string; form?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLButtonElement>; ... 263 more ...; formTarget?: string; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], [120, [{"file": "./src/components/base.tsx", "start": 647, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ children, variant, disabled, loading, size, onClick, }: { children: any; variant?: string; disabled?: boolean; loading?: boolean; size?: string; onClick: any; }) => Element' is not assignable to type 'FC<ButtonProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'ButtonProps' is not assignable to type '{ children: any; variant?: string; disabled?: boolean; loading?: boolean; size?: string; onClick: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'onClick' is optional in type 'ButtonProps' but required in type '{ children: any; variant?: string; disabled?: boolean; loading?: boolean; size?: string; onClick: any; }'.", "category": 1, "code": 2327}]}]}]}}]], 94, 282, [77, [{"file": "./src/components/molecules/card.tsx", "start": 3001, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'medium' does not exist on type 'ThemeShadows'."}, {"file": "./src/components/molecules/card.tsx", "start": 5090, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ children, title, subtitle, bordered, variant, padding, className, footer, actions, isLoading, hasError, errorMessage, clickable, onClick, ...rest }: { [x: string]: any; children: any; title: any; subtitle: any; bordered?: boolean; variant?: string; padding?: string; className: any; footer: any; actions: any; isLo...' is not assignable to type 'FC<CardProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'CardProps' is not assignable to type '{ [x: string]: any; children: any; title: any; subtitle: any; bordered?: boolean; variant?: string; padding?: string; className: any; footer: any; actions: any; isLoading?: boolean; hasError?: boolean; errorMessage?: string; clickable?: boolean; onClick: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'title' is optional in type 'CardProps' but required in type '{ [x: string]: any; children: any; title: any; subtitle: any; bordered?: boolean; variant?: string; padding?: string; className: any; footer: any; actions: any; isLoading?: boolean; hasError?: boolean; errorMessage?: string; clickable?: boolean; onClick: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/molecules/card.tsx", "start": 5447, "length": 214, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { bordered: boolean; variant: CardVariant; padding: CardPadding; clickable: boolean; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'CardVariant'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { bordered: boolean; variant: CardVariant; padding: CardPadding; clickable: boolean; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'CardPadding'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/molecules/card.tsx", "start": 2284, "length": 7, "messageText": "The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 256 more ...; padding: CardPadding; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/molecules/card.tsx", "start": 2308, "length": 7, "messageText": "The expected type comes from property 'padding' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 256 more ...; padding: CardPadding; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], [78, [{"file": "./src/components/molecules/emptystate.tsx", "start": 4018, "length": 10, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ title, description, icon, actionText, onAction, variant, size, className, children, }: { title: any; description: any; icon: any; actionText: any; onAction: any; variant?: string; size?: string; className: any; children: any; }) => Element' is not assignable to type 'FC<EmptyStateProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'EmptyStateProps' is not assignable to type '{ title: any; description: any; icon: any; actionText: any; onAction: any; variant?: string; size?: string; className: any; children: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'title' is optional in type 'EmptyStateProps' but required in type '{ title: any; description: any; icon: any; actionText: any; onAction: any; variant?: string; size?: string; className: any; children: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/molecules/emptystate.tsx", "start": 4209, "length": 63, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { variant: EmptyStateVariant; size: EmptyStateSize; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateVariant'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { variant: EmptyStateVariant; size: EmptyStateSize; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/molecules/emptystate.tsx", "start": 2394, "length": 7, "messageText": "The expected type comes from property 'variant' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 254 more ...; variant: EmptyStateVariant; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/molecules/emptystate.tsx", "start": 2424, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 254 more ...; variant: EmptyStateVariant; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/molecules/emptystate.tsx", "start": 4303, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; defaultChecked?: boolean; defaultValue?: string | ... 1 more ... | readonly string[]; ... 251 more ...; size: EmptyStateSize; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateSize'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { size: EmptyStateSize; }, never, \"div\", \"div\">): ReactElement<StyledComponentPropsWithAs<...>, string | JSXElementConstructor<...>>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/molecules/emptystate.tsx", "start": 3226, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 253 more ...; size: EmptyStateSize; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/molecules/emptystate.tsx", "start": 3226, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 253 more ...; size: EmptyStateSize; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/molecules/emptystate.tsx", "start": 4363, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLHeadingElement>; key?: Key; defaultChecked?: boolean; defaultValue?: string | ... 1 more ... | readonly string[]; ... 251 more ...; size: EmptyStateSize; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateSize'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"h3\", DefaultTheme, { size: EmptyStateSize; }, never, \"h3\", \"h3\">): ReactElement<StyledComponentPropsWithAs<\"h3\", ... 4 more ..., \"h3\">, string | JSXElementConstructor<...>>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/molecules/emptystate.tsx", "start": 1071, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLHeadingElement>; ... 254 more ...; size: EmptyStateSize; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/molecules/emptystate.tsx", "start": 1071, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLHeadingElement>; ... 254 more ...; size: EmptyStateSize; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/molecules/emptystate.tsx", "start": 4427, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLParagraphElement>; key?: Key; defaultChecked?: boolean; ... 252 more ...; size: EmptyStateSize; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateSize'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"p\", DefaultTheme, { size: EmptyStateSize; }, never, \"p\", \"p\">): ReactElement<StyledComponentPropsWithAs<\"p\", ... 4 more ..., \"p\">, string | JSXElementConstructor<...>>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'EmptyStateSize'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/molecules/emptystate.tsx", "start": 1518, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLParagraphElement>; ... 254 more ...; size: EmptyStateSize; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/molecules/emptystate.tsx", "start": 1518, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLParagraphElement>; ... 254 more ...; size: EmptyStateSize; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], [79, [{"file": "./src/components/molecules/errorboundary.tsx", "start": 293, "length": 5, "messageText": "'React' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [81, [{"file": "./src/components/molecules/formfield.tsx", "start": 2386, "length": 14, "messageText": "Spread types may only be created from object types.", "category": 1, "code": 2698}]], [89, [{"file": "./src/components/molecules/index.ts", "start": 504, "length": 36, "messageText": "Module './Table' has already exported a member named 'TableColumn'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}]], 283, [83, [{"file": "./src/components/molecules/modal.tsx", "start": 4990, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ isOpen, title, children, onClose, size, closeOnOutsideClick, showCloseButton, footer, hasFooter, primaryActionText, onPrimaryAction, primaryActionDisabled, primaryActionLoading, secondaryActionText, onSecondaryAction, secondaryActionDisabled, className, zIndex, centered, scrollable, }: { isOpen: any; title: any; ...' is not assignable to type 'FC<ModalProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'ModalProps' is not assignable to type '{ isOpen: any; title: any; children: any; onClose: any; size?: string; closeOnOutsideClick?: boolean; showCloseButton?: boolean; footer: any; hasFooter?: boolean; primaryActionText: any; onPrimaryAction: any; ... 8 more ...; scrollable?: boolean; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'title' is optional in type 'ModalProps' but required in type '{ isOpen: any; title: any; children: any; onClose: any; size?: string; closeOnOutsideClick?: boolean; showCloseButton?: boolean; footer: any; hasFooter?: boolean; primaryActionText: any; onPrimaryAction: any; ... 8 more ...; scrollable?: boolean; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/molecules/modal.tsx", "start": 7135, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; defaultChecked?: boolean; defaultValue?: string | ... 1 more ... | readonly string[]; ... 253 more ...; centered: boolean; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"small\" | \"medium\" | \"large\" | \"fullscreen\"'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", DefaultTheme, { size: \"small\" | \"medium\" | \"large\" | \"fullscreen\"; centered: boolean; scrollable: boolean; }, never, \"div\", \"div\">): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"small\" | \"medium\" | \"large\" | \"fullscreen\"'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "./src/components/molecules/modal.tsx", "start": 2466, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 255 more ...; centered: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"file": "./src/components/molecules/modal.tsx", "start": 2466, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'IntrinsicAttributes & { id?: string; slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; key?: Key; ... 255 more ...; centered: boolean; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], 284, 84, [285, [{"file": "./src/components/molecules/tradetable.example.tsx", "start": 188, "length": 17, "messageText": "Module '\"../../services/tradeStorage\"' has no exported member 'CompleteTradeData'. Did you mean to use 'import CompleteTradeData from \"../../services/tradeStorage\"' instead?", "category": 1, "code": 2614}, {"file": "./src/components/molecules/tradetable.example.tsx", "start": 207, "length": 12, "messageText": "Module '\"../../services/tradeStorage\"' has no exported member 'TradeFilters'. Did you mean to use 'import TradeFilters from \"../../services/tradeStorage\"' instead?", "category": 1, "code": 2614}, {"file": "./src/components/molecules/tradetable.example.tsx", "start": 3396, "length": 5, "messageText": "'index' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/components/molecules/tradetable.example.tsx", "start": 3545, "length": 5, "messageText": "'index' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [88, [{"file": "./src/components/molecules/tradetable.tsx", "start": 6557, "length": 10, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ data, isLoading, bordered, striped, hoverable, compact, stickyHeader, height, onRowClick, isRowSelected, onSort, sortColumn, sortDirection, pagination, currentPage, pageSize, totalRows, onPageChange, onPageSizeChange, className, emptyMessage, scrollable, showFilters, filters, onFiltersChange, columnPreset, custom...' is not assignable to type 'FC<TradeTableProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'TradeTableProps' is not assignable to type '{ data: any; isLoading?: boolean; bordered?: boolean; striped?: boolean; hoverable?: boolean; compact?: boolean; stickyHeader?: boolean; height: any; onRowClick: any; isRowSelected: any; onSort: any; sortColumn: any; ... 16 more ...; renderExpandedContent: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'height' is optional in type 'TradeTableProps' but required in type '{ data: any; isLoading?: boolean; bordered?: boolean; striped?: boolean; hoverable?: boolean; compact?: boolean; stickyHeader?: boolean; height: any; onRowClick: any; isRowSelected: any; onSort: any; sortColumn: any; ... 16 more ...; renderExpandedContent: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/molecules/tradetable.tsx", "start": 6904, "length": 16, "messageText": "'onPageSizeChange' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [85, [{"file": "./src/components/molecules/tradetablecolumns.tsx", "start": 380, "length": 15, "messageText": "'TradeTableField' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}]], [87, [{"file": "./src/components/molecules/tradetablefilters.tsx", "start": 3637, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 3931, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 4097, "length": 6, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: PropsWithChildren<SelectProps>, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}, {"messageText": "Overload 2 of 2, '(props: SelectProps, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}]}, "relatedInformation": [{"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}, {"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 4213, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 4776, "length": 6, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: PropsWithChildren<SelectProps>, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}, {"messageText": "Overload 2 of 2, '(props: SelectProps, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}]}, "relatedInformation": [{"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}, {"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 4886, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 5699, "length": 6, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: PropsWithChildren<SelectProps>, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}, {"messageText": "Overload 2 of 2, '(props: SelectProps, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}]}, "relatedInformation": [{"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}, {"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 5827, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 6195, "length": 6, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: PropsWithChildren<SelectProps>, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}, {"messageText": "Overload 2 of 2, '(props: SelectProps, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}]}, "relatedInformation": [{"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}, {"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 6307, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 7233, "length": 6, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: PropsWithChildren<SelectProps>, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}, {"messageText": "Overload 2 of 2, '(props: SelectProps, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'options' is missing in type '{ children: Element[]; value: any; onChange: (e: string) => void; disabled: boolean; }' but required in type 'SelectProps'.", "category": 1, "code": 2741}]}]}, "relatedInformation": [{"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}, {"file": "./src/components/atoms/select.tsx", "start": 811, "length": 7, "messageText": "'options' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 7345, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 8191, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 8215, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 8713, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 8737, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 9423, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 9447, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 10003, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}, {"file": "./src/components/molecules/tradetablefilters.tsx", "start": 10027, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'target' does not exist on type 'string'."}]], 86, [80, [{"file": "./src/components/molecules/unifiederrorboundary.tsx", "start": 708, "length": 20, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ isAppLevel, isFeatureBoundary, ...props }: { [x: string]: any; isAppLevel: any; isFeatureBoundary: any; }) => Element' is not assignable to type 'FC<UnifiedErrorBoundaryProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'UnifiedErrorBoundaryProps' is not assignable to type '{ [x: string]: any; isAppLevel: any; isFeatureBoundary: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isAppLevel' is optional in type 'UnifiedErrorBoundaryProps' but required in type '{ [x: string]: any; isAppLevel: any; isFeatureBoundary: any; }'.", "category": 1, "code": 2327}]}]}]}}, {"file": "./src/components/molecules/unifiederrorboundary.tsx", "start": 1338, "length": 13, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ children?: ReactNode; fallback?: ReactNode | ((props: { error: Error; resetError: () => void; }) => ReactNode); onError?: (error: Error, errorInfo: ErrorInfo) => void; ... 4 more ...; onSkip?: () => void; }' is not assignable to type 'Readonly<ErrorBoundaryProps>'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'children' is optional in type '{ children?: ReactNode; fallback?: ReactNode | ((props: { error: Error; resetError: () => void; }) => ReactNode); onError?: (error: Error, errorInfo: ErrorInfo) => void; ... 4 more ...; onSkip?: () => void; }' but required in type 'Readonly<ErrorBoundaryProps>'.", "category": 1, "code": 2327}]}}, {"file": "./src/components/molecules/unifiederrorboundary.tsx", "start": 1948, "length": 20, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: PropsWithChildren<UnifiedErrorBoundaryProps>, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'children' is missing in type '{ isFeatureBoundary: true; name: any; }' but required in type 'UnifiedErrorBoundaryProps'.", "category": 1, "code": 2741}]}, {"messageText": "Overload 2 of 2, '(props: UnifiedErrorBoundaryProps, context?: any): ReactElement<any, any>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Property 'children' is missing in type '{ isFeatureBoundary: true; name: any; }' but required in type 'UnifiedErrorBoundaryProps'.", "category": 1, "code": 2741}]}]}, "relatedInformation": [{"file": "./src/components/molecules/errorboundary.tsx", "start": 487, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}, {"file": "./src/components/molecules/errorboundary.tsx", "start": 487, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}]}]], 286, 90, 91, 287, 92, 93, 100, 95, 96, 97, 98, 99, [116, [{"file": "./src/index.ts", "start": 215, "length": 22, "messageText": "Module './types' has already exported a member named 'Trade'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"file": "./src/index.ts", "start": 409, "length": 24, "messageText": "Module './components' has already exported a member named 'formatCurrency'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"file": "./src/index.ts", "start": 409, "length": 24, "messageText": "Module './components' has already exported a member named 'formatDate'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"file": "./src/index.ts", "start": 409, "length": 24, "messageText": "Module './components' has already exported a member named 'formatPercentage'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}]], 114, 117, 115, [65, [{"file": "./src/services/tradestorage.ts", "start": 252, "length": 15, "messageText": "'TradeFvgDetails' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/services/tradestorage.ts", "start": 271, "length": 10, "messageText": "'TradeSetup' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/services/tradestorage.ts", "start": 285, "length": 13, "messageText": "'TradeAnalysis' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/services/tradestorage.ts", "start": 339, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 110, 109, 112, 111, 118, 105, 103, 106, 108, 104, 288, [107, [{"file": "./src/theme/themeprovider.tsx", "start": 4227, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ position: string; top: string; left: number; padding: string; background: string; color: string; zIndex: number; fontSize: string; fontFamily: string; }' is not assignable to type 'CSSProperties'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'position' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type 'Position'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "../../node_modules/@types/react/index.d.ts", "start": 84031, "length": 5, "messageText": "The expected type comes from property 'style' which is declared here on type 'DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement>'", "category": 3, "code": 6500}]}]], 102, 289, 292, 290, 291, 101, 293, 295, 294, 63, 62, 113], "affectedFilesPendingEmit": [[125, 1], [123, 1], [195, 1], [192, 1], [193, 1], [194, 1], [277, 1], [197, 1], [196, 1], [128, 1], [124, 1], [126, 1], [127, 1], [189, 1], [304, 1], [188, 1], [185, 1], [191, 1], [190, 1], [67, 1], [186, 1], [181, 1], [129, 1], [130, 1], [132, 1], [133, 1], [134, 1], [135, 1], [136, 1], [137, 1], [138, 1], [139, 1], [140, 1], [141, 1], [142, 1], [143, 1], [144, 1], [145, 1], [146, 1], [131, 1], [179, 1], [147, 1], [148, 1], [149, 1], [180, 1], [150, 1], [151, 1], [152, 1], [153, 1], [154, 1], [155, 1], [156, 1], [157, 1], [158, 1], [159, 1], [160, 1], [161, 1], [163, 1], [162, 1], [164, 1], [165, 1], [166, 1], [167, 1], [168, 1], [169, 1], [170, 1], [171, 1], [172, 1], [173, 1], [174, 1], [175, 1], [176, 1], [177, 1], [178, 1], [59, 1], [183, 1], [184, 1], [82, 1], [57, 1], [60, 1], [61, 1], [182, 1], [187, 1], [68, 1], [305, 1], [303, 1], [302, 1], [301, 1], [335, 1], [339, 1], [336, 1], [340, 1], [306, 1], [354, 1], [307, 1], [308, 1], [345, 1], [355, 1], [346, 1], [353, 1], [338, 1], [337, 1], [297, 1], [300, 1], [296, 1], [58, 1], [309, 1], [121, 1], [122, 1], [331, 1], [329, 1], [330, 1], [318, 1], [319, 1], [326, 1], [317, 1], [322, 1], [332, 1], [323, 1], [328, 1], [333, 1], [316, 1], [324, 1], [325, 1], [320, 1], [327, 1], [321, 1], [299, 1], [298, 1], [315, 1], [348, 1], [341, 1], [352, 1], [276, 1], [225, 1], [238, 1], [200, 1], [252, 1], [254, 1], [253, 1], [227, 1], [226, 1], [228, 1], [255, 1], [259, 1], [257, 1], [236, 1], [235, 1], [244, 1], [203, 1], [231, 1], [272, 1], [247, 1], [249, 1], [267, 1], [202, 1], [219, 1], [234, 1], [269, 1], [240, 1], [256, 1], [260, 1], [258, 1], [273, 1], [242, 1], [216, 1], [208, 1], [207, 1], [232, 1], [233, 1], [206, 1], [239, 1], [201, 1], [218, 1], [246, 1], [274, 1], [213, 1], [214, 1], [261, 1], [263, 1], [262, 1], [198, 1], [217, 1], [224, 1], [215, 1], [245, 1], [212, 1], [271, 1], [211, 1], [209, 1], [210, 1], [248, 1], [241, 1], [268, 1], [222, 1], [220, 1], [221, 1], [237, 1], [204, 1], [264, 1], [266, 1], [265, 1], [251, 1], [250, 1], [243, 1], [230, 1], [270, 1], [275, 1], [199, 1], [229, 1], [223, 1], [205, 1], [11, 1], [12, 1], [14, 1], [13, 1], [2, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [20, 1], [21, 1], [22, 1], [3, 1], [4, 1], [26, 1], [23, 1], [24, 1], [25, 1], [27, 1], [28, 1], [29, 1], [5, 1], [30, 1], [31, 1], [32, 1], [33, 1], [6, 1], [37, 1], [34, 1], [35, 1], [36, 1], [38, 1], [7, 1], [39, 1], [44, 1], [45, 1], [40, 1], [41, 1], [42, 1], [43, 1], [8, 1], [49, 1], [46, 1], [47, 1], [48, 1], [50, 1], [9, 1], [51, 1], [52, 1], [53, 1], [54, 1], [55, 1], [1, 1], [10, 1], [56, 1], [344, 1], [347, 1], [350, 1], [343, 1], [342, 1], [349, 1], [356, 1], [357, 1], [351, 1], [358, 1], [313, 1], [334, 1], [312, 1], [311, 1], [314, 1], [310, 1], [119, 1], [66, 1], [64, 1], [278, 1], [69, 1], [70, 1], [76, 1], [279, 1], [71, 1], [72, 1], [280, 1], [73, 1], [74, 1], [281, 1], [75, 1], [120, 1], [94, 1], [282, 1], [77, 1], [78, 1], [79, 1], [81, 1], [89, 1], [283, 1], [83, 1], [284, 1], [84, 1], [285, 1], [88, 1], [85, 1], [87, 1], [86, 1], [80, 1], [286, 1], [90, 1], [91, 1], [287, 1], [92, 1], [93, 1], [100, 1], [95, 1], [96, 1], [97, 1], [98, 1], [99, 1], [116, 1], [114, 1], [117, 1], [115, 1], [65, 1], [110, 1], [109, 1], [112, 1], [111, 1], [118, 1], [105, 1], [103, 1], [106, 1], [108, 1], [104, 1], [288, 1], [107, 1], [102, 1], [289, 1], [292, 1], [290, 1], [291, 1], [101, 1], [293, 1], [295, 1], [294, 1], [63, 1], [62, 1], [113, 1]], "emitSignatures": [62, 63, [64, "3ea202c6449d26aee2e94211695a4076242582b76ab78c5815d1599fc8ece49a"], [65, "1504aa615746727c16dc707d9e59afa100512ca23dbe3a39714f174ffc74981c"], [66, "3c97150d02c282c3e4dc447f4c70e2057fc3652491aa05762a7b8156946e228b"], [84, "d9168c9cdfb92450e1afe1631a89155b62b26414c8ac55b4940bb447924bf3b9"], 85, 86, 87, 88, [89, "3c841576f663d5ec3f0229955ce9450169b7d7388af41666db4ee4de32d2fa69"], [94, "f92d20082fc0c241666f904de2c2a497d239841f3b8d49477f78eefab064adec"], [115, "8a4de6e0af2b8635f261f24819b2279df85ea7a4e2b69a586c52b70dcbb97f44"], [116, "eb7c81874da0c214864a63c2cdd73b37cc2618588a08e84005eab9df326a6164"], [284, "692dd223f3b2172633ff6d64b6da4c75e4ba69451df7567affcf57ed01b7b9da"], 285], "latestChangedDtsFile": "./dist/services/tradeStorage.d.ts"}, "version": "4.9.4"}