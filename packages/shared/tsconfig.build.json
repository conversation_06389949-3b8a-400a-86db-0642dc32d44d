{"extends": "./tsconfig.json", "compilerOptions": {"target": "es2015", "module": "commonjs", "jsx": "react-jsx", "noEmit": false, "declaration": true, "declarationMap": true, "outDir": "./dist", "skipLibCheck": true, "isolatedModules": true, "esModuleInterop": true, "removeComments": false, "sourceMap": true, "strict": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true}, "include": ["src/**/*"], "exclude": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/__tests__/*"]}