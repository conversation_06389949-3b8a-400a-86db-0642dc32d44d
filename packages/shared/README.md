# ADHD Trading Dashboard Shared Package

This package contains shared components, hooks, utilities, and theme definitions for the ADHD Trading Dashboard.

## Architecture

The shared package follows atomic design principles and provides a foundation for both the core and dashboard packages.

### Components

Components are organized according to atomic design principles:

- **Atoms**: Basic UI elements (Button, Input, Select)
- **Molecules**: Combinations of atoms (Card, FormField)
- **Organisms**: Complex UI components composed of molecules and atoms
- **Templates**: Page layouts that define the structure of different page types

### API Layer

The API layer provides a clean abstraction for Google Apps Script communication with fallbacks for local development:

- **Types**: Type definitions for API responses
- **Clients**: API client implementations (gasApi, mockApi)
- **Hooks**: Custom hooks for API calls

### Theme System

The theme system provides a comprehensive set of design tokens and theme variants:

- **Tokens**: Design tokens (colors, spacing, typography)
- **Variants**: Theme variants (F1, light)
- **ThemeProvider**: Context provider for theme switching

## Usage

### Components

```tsx
import { But<PERSON>, Input, Card } from "@adhd-trading-dashboard/shared";

const MyComponent = () => {
  return (
    <Card title="My Card">
      <Input value={value} onChange={setValue} />
      <Button onClick={handleClick}>Submit</Button>
    </Card>
  );
};
```

### API

```tsx
import { useDashboardData } from "@adhd-trading-dashboard/shared";

const MyComponent = () => {
  const { data, isLoading, error } = useDashboardData();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return <div>{data?.summary?.totalTrades} trades</div>;
};
```

### Theme

```tsx
import { ThemeProvider, useTheme } from "@adhd-trading-dashboard/shared";

// In your root component
const App = () => {
  return (
    <ThemeProvider initialTheme="f1">
      <MyComponent />
    </ThemeProvider>
  );
};

// In a child component
const MyComponent = () => {
  const { theme, setTheme } = useTheme();

  return (
    <div>
      <button onClick={() => setTheme("light")}>
        Switch to Light Theme
      </button>
    </div>
  );
};
```
