/**
 * API Types
 *
 * This module exports type definitions for the API.
 */
export * from './trading';
/**
 * API Response
 *
 * Base interface for all API responses
 */
export interface ApiResponse {
    success: boolean;
    error?: string;
}
/**
 * Configuration Response
 */
export interface ConfigurationResponse extends ApiResponse {
    theme?: {
        colors: Record<string, string>;
    };
    features?: Record<string, boolean>;
}
/**
 * Trade interface
 */
export interface Trade {
    id: string | number;
    date: string;
    symbol: string;
    result?: string;
    profit?: number;
    profitLoss?: number;
}
/**
 * Performance Data Point
 */
export interface PerformanceDataPoint {
    date: string;
    value: number;
}
/**
 * News Item
 */
export interface NewsItem {
    title: string;
    date: string;
    source: string;
    url?: string;
}
/**
 * Dashboard Data Response
 */
export interface DashboardDataResponse extends ApiResponse {
    summary?: {
        totalTrades: number;
        winRate: number;
        profitFactor: number;
        netProfit: number;
    };
    recentTrades?: Trade[];
    performance?: {
        daily: PerformanceDataPoint[];
    };
    news?: NewsItem[];
}
/**
 * Trading Data Item
 */
export interface TradingDataItem {
    date: string;
    symbol: string;
    action: string;
    price: number;
    quantity: number;
    profit: number;
}
/**
 * Trading Data Options
 */
export interface TradingDataOptions {
    startDate?: string;
    endDate?: string;
    symbols?: string[];
    limit?: number;
    page?: number;
}
/**
 * Trading Data Response
 */
export interface TradingDataResponse extends ApiResponse {
    data?: TradingDataItem[];
}
/**
 * Performance Metrics Options
 */
export interface PerformanceMetricsOptions {
    period?: 'day' | 'week' | 'month' | 'year' | 'all';
    startDate?: string;
    endDate?: string;
}
/**
 * Performance Metrics Response
 */
export interface PerformanceMetricsResponse extends ApiResponse {
    metrics?: Record<string, number>;
    chartData?: PerformanceDataPoint[];
}
/**
 * Market News Options
 */
export interface MarketNewsOptions {
    limit?: number;
    sources?: string[];
    categories?: string[];
}
/**
 * Market News Response
 */
export interface MarketNewsResponse extends ApiResponse {
    news?: NewsItem[];
}
/**
 * User Preferences
 */
export interface UserPreferences {
    theme: string;
    refreshInterval: number;
    showNotifications: boolean;
}
/**
 * User Preferences Response
 */
export interface UserPreferencesResponse extends ApiResponse {
    preferences?: UserPreferences;
}
/**
 * Trade Storage Service Interface
 *
 * Defines the methods that the trade storage service implements
 */
export interface TradeStorageService {
    getConfiguration(): Promise<ConfigurationResponse>;
    getDashboardData(): Promise<DashboardDataResponse>;
    getTradingData(options?: TradingDataOptions): Promise<TradingDataResponse>;
    getPerformanceMetrics(options?: PerformanceMetricsOptions): Promise<PerformanceMetricsResponse>;
    getMarketNews(options?: MarketNewsOptions): Promise<MarketNewsResponse>;
    saveUserPreferences(preferences: UserPreferences): Promise<ApiResponse>;
    getUserPreferences(): Promise<UserPreferencesResponse>;
}
//# sourceMappingURL=index.d.ts.map