/**
 * Trading Data Types
 *
 * This module exports type definitions for trading data.
 */
/**
 * Trade Direction
 */
export declare enum TradeDirection {
    LONG = "LONG",
    SHORT = "SHORT"
}
/**
 * Trade Status
 */
export declare enum TradeStatus {
    OPEN = "OPEN",
    CLOSED = "CLOSED",
    CANCELED = "CANCELED",
    REJECTED = "REJECTED",
    PENDING = "PENDING"
}
/**
 * Order Type
 */
export declare enum OrderType {
    MARKET = "MARKET",
    LIMIT = "LIMIT",
    STOP = "STOP",
    STOP_LIMIT = "STOP_LIMIT"
}
/**
 * Order Side
 */
export declare enum OrderSide {
    BUY = "BUY",
    SELL = "SELL"
}
/**
 * Order Status
 */
export declare enum OrderStatus {
    PENDING = "PENDING",
    FILLED = "FILLED",
    PARTIALLY_FILLED = "PARTIALLY_FILLED",
    CANCELED = "CANCELED",
    REJECTED = "REJECTED"
}
/**
 * Time in Force
 */
export declare enum TimeInForce {
    GTC = "GTC",
    IOC = "IOC",
    FOK = "FOK",
    DAY = "DAY"
}
/**
 * Position
 */
export interface Position {
    /** Unique identifier for the position */
    id: string;
    /** Symbol of the instrument */
    symbol: string;
    /** Quantity of the position */
    quantity: number;
    /** Average entry price */
    entryPrice: number;
    /** Current market price */
    currentPrice: number;
    /** Direction of the position (LONG or SHORT) */
    direction: TradeDirection;
    /** Unrealized profit/loss */
    unrealizedPnl: number;
    /** Realized profit/loss */
    realizedPnl: number;
    /** Initial margin required */
    initialMargin: number;
    /** Maintenance margin required */
    maintenanceMargin: number;
    /** Timestamp when the position was opened */
    openTime: string;
    /** Timestamp when the position was last updated */
    updateTime: string;
    /** Associated orders (entry, take profit, stop loss) */
    orders: string[];
    /** Risk to reward ratio */
    riskRewardRatio?: number;
    /** Risk per trade */
    risk?: number;
    /** Tags for the position */
    tags?: string[];
    /** Notes for the position */
    notes?: string;
}
/**
 * Order
 */
export interface Order {
    /** Unique identifier for the order */
    id: string;
    /** Symbol of the instrument */
    symbol: string;
    /** Order type (MARKET, LIMIT, STOP, STOP_LIMIT) */
    type: OrderType;
    /** Order side (BUY, SELL) */
    side: OrderSide;
    /** Order status */
    status: OrderStatus;
    /** Quantity of the order */
    quantity: number;
    /** Filled quantity */
    filledQuantity: number;
    /** Price of the order (for LIMIT and STOP_LIMIT orders) */
    price?: number;
    /** Stop price (for STOP and STOP_LIMIT orders) */
    stopPrice?: number;
    /** Time in force */
    timeInForce: TimeInForce;
    /** Whether the order is a reduce-only order */
    reduceOnly: boolean;
    /** Whether the order is a post-only order */
    postOnly: boolean;
    /** Timestamp when the order was created */
    createTime: string;
    /** Timestamp when the order was last updated */
    updateTime: string;
    /** Timestamp when the order was filled */
    fillTime?: string;
    /** Average fill price */
    avgFillPrice?: number;
    /** Commission paid */
    commission?: number;
    /** Associated position ID */
    positionId?: string;
    /** Parent order ID (for OCO orders) */
    parentOrderId?: string;
    /** Client order ID */
    clientOrderId?: string;
    /** Order purpose (ENTRY, TAKE_PROFIT, STOP_LOSS) */
    purpose?: 'ENTRY' | 'TAKE_PROFIT' | 'STOP_LOSS';
}
/**
 * Market Data
 */
export interface MarketData {
    /** Symbol of the instrument */
    symbol: string;
    /** Last price */
    lastPrice: number;
    /** Bid price */
    bidPrice: number;
    /** Ask price */
    askPrice: number;
    /** 24-hour high price */
    highPrice: number;
    /** 24-hour low price */
    lowPrice: number;
    /** 24-hour volume */
    volume: number;
    /** 24-hour price change */
    priceChange: number;
    /** 24-hour price change percentage */
    priceChangePercent: number;
    /** Timestamp of the data */
    timestamp: string;
}
/**
 * Performance Metrics
 */
export interface PerformanceMetrics {
    /** Total number of trades */
    totalTrades: number;
    /** Number of winning trades */
    winningTrades: number;
    /** Number of losing trades */
    losingTrades: number;
    /** Win rate (percentage) */
    winRate: number;
    /** Profit factor (gross profit / gross loss) */
    profitFactor: number;
    /** Average win amount */
    averageWin: number;
    /** Average loss amount */
    averageLoss: number;
    /** Largest win amount */
    largestWin: number;
    /** Largest loss amount */
    largestLoss: number;
    /** Total profit/loss */
    totalPnl: number;
    /** Maximum drawdown */
    maxDrawdown: number;
    /** Maximum drawdown percentage */
    maxDrawdownPercent: number;
    /** Sharpe ratio */
    sharpeRatio: number;
    /** Sortino ratio */
    sortinoRatio: number;
    /** Calmar ratio */
    calmarRatio: number;
    /** Average R multiple */
    averageRMultiple: number;
    /** Expectancy (average R multiple * win rate) */
    expectancy: number;
    /** System quality number (SQN) */
    sqn: number;
    /** Time period for the metrics */
    period: 'day' | 'week' | 'month' | 'year' | 'all';
    /** Start date of the period */
    startDate: string;
    /** End date of the period */
    endDate: string;
}
/**
 * Trading Session
 */
export interface TradingSession {
    /** Unique identifier for the session */
    id: string;
    /** Session name */
    name: string;
    /** Session date */
    date: string;
    /** Session start time */
    startTime: string;
    /** Session end time */
    endTime?: string;
    /** Session status (ACTIVE, COMPLETED) */
    status: 'ACTIVE' | 'COMPLETED';
    /** Session notes */
    notes?: string;
    /** Session tags */
    tags?: string[];
    /** Session metrics */
    metrics?: {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        winRate: number;
        totalPnl: number;
    };
    /** Associated trade IDs */
    tradeIds: string[];
}
//# sourceMappingURL=trading.d.ts.map