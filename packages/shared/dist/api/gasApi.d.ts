/**
 * Trade Storage Service Wrapper
 *
 * This module provides a wrapper around the TradeStorageService
 * to make it easier to work with in the application.
 */
/**
 * Trade Storage Service Wrapper
 */
export declare const tradeStorageService: {
    /**
     * Get configuration (mock implementation)
     * @returns Promise that resolves with the configuration
     */
    getConfiguration(): Promise<any>;
    /**
     * Get dashboard data
     * @returns Promise that resolves with the dashboard data
     */
    getDashboardData(): Promise<any>;
    /**
     * Get trading data
     * @param options - Options for data retrieval
     * @returns Promise that resolves with the trading data
     */
    getTradingData(options?: any): Promise<any>;
    /**
     * Get performance metrics
     * @param options - Options for metrics retrieval
     * @returns Promise that resolves with the performance metrics
     */
    getPerformanceMetrics(options?: any): Promise<any>;
    /**
     * Get market news (mock implementation)
     * @param options - Options for news retrieval
     * @returns Promise that resolves with the market news
     */
    getMarketNews(options?: any): Promise<any>;
    /**
     * Save user preferences (mock implementation)
     * @param preferences - User preferences to save
     * @returns Promise that resolves with the success status
     */
    saveUserPreferences(preferences: any): Promise<any>;
    /**
     * Get user preferences (mock implementation)
     * @returns Promise that resolves with the user preferences
     */
    getUserPreferences(): Promise<any>;
    /**
     * Close the dialog (no-op)
     */
    closeDialog(): void;
};
export default tradeStorageService;
//# sourceMappingURL=gasApi.d.ts.map