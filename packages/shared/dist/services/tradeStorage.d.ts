/**
 * Trade Storage Service
 *
 * This service provides methods for storing and retrieving trade data
 * using IndexedDB.
 */
import { Trade } from '../api/types';
/**
 * Trade Storage Service
 */
declare class TradeStorageService {
    private dbName;
    private storeName;
    private db;
    /**
     * Initialize the database
     * @returns A promise that resolves when the database is initialized
     */
    private initDB;
    /**
     * Get all trades from the database
     * @returns A promise that resolves with all trades
     */
    getAllTrades(): Promise<Trade[]>;
    /**
     * Save a trade to the database
     * @param trade The trade to save
     * @returns A promise that resolves with the saved trade
     */
    saveTrade(trade: Omit<Trade, 'id'>): Promise<Trade>;
    /**
     * Update a trade in the database
     * @param trade The trade to update
     * @returns A promise that resolves with the updated trade
     */
    updateTrade(trade: Trade): Promise<Trade>;
    /**
     * Delete a trade from the database
     * @param id The ID of the trade to delete
     * @returns A promise that resolves when the trade is deleted
     */
    deleteTrade(id: string): Promise<void>;
    /**
     * Get a trade by ID
     * @param id The ID of the trade to get
     * @returns A promise that resolves with the trade
     */
    getTradeById(id: string): Promise<Trade | null>;
}
export declare const tradeStorage: TradeStorageService;
export default tradeStorage;
//# sourceMappingURL=tradeStorage.d.ts.map