import me, { useState as O, use<PERSON><PERSON>back as V, use<PERSON><PERSON> as ne, useEffect as re, createContext as Oe, use<PERSON>emo as ae, useContext as Ae, Component as tt, useReducer as ot } from "react";
import h, { css as g, keyframes as Se, createGlobalStyle as nt, ThemeProvider as at } from "styled-components";
import { createPortal as st } from "react-dom";
function le(e, ...r) {
  return new Promise((o, t) => {
    if (!window.google || !window.google.script || !window.google.script.run) {
      t(new Error("Not in a Google Apps Script environment"));
      return;
    }
    const i = window.google.script.run.withSuccessHandler((d) => {
      o(d);
    }).withFailureHandler((d) => {
      console.error(`Error calling ${e}:`, d), t(d);
    });
    typeof i[e] == "function" ? i[e].apply(i, r) : t(new Error(`Function ${e} not found on server`));
  });
}
const it = {
  /**
   * Get configuration from the server
   * @returns Promise that resolves with the configuration
   */
  getConfiguration() {
    return le("getConfiguration");
  },
  /**
   * Get dashboard data from the server
   * @returns Promise that resolves with the dashboard data
   */
  getDashboardData() {
    return le("getDashboardData");
  },
  /**
   * Get trading data from the server
   * @param options - Options for data retrieval
   * @returns Promise that resolves with the trading data
   */
  getTradingData(e) {
    return le("getTradingData", e);
  },
  /**
   * Get performance metrics from the server
   * @param options - Options for metrics retrieval
   * @returns Promise that resolves with the performance metrics
   */
  getPerformanceMetrics(e) {
    return le("getPerformanceMetrics", e);
  },
  /**
   * Get market news from the server
   * @param options - Options for news retrieval
   * @returns Promise that resolves with the market news
   */
  getMarketNews(e) {
    return le("getMarketNews", e);
  },
  /**
   * Save user preferences to the server
   * @param preferences - User preferences to save
   * @returns Promise that resolves with the success status
   */
  saveUserPreferences(e) {
    return le("saveUserPreferences", e);
  },
  /**
   * Get user preferences from the server
   * @returns Promise that resolves with the user preferences
   */
  getUserPreferences() {
    return le("getUserPreferences");
  },
  /**
   * Close the dialog
   */
  closeDialog() {
    window.google && window.google.script && window.google.script.host && window.google.script.host.close();
  }
}, ct = {
  /**
   * Get configuration from the server
   * @returns Promise that resolves with the configuration
   */
  getConfiguration: async function() {
    return console.log("Mock: getConfiguration called"), {
      success: !0,
      theme: {
        colors: {
          primary: "#e10600",
          // F1 red
          background: "#1a1f2c",
          cardBackground: "#252a37",
          text: "#ffffff",
          secondaryText: "#aaaaaa",
          positive: "#4caf50",
          negative: "#f44336",
          neutral: "#9e9e9e",
          border: "#333333",
          tabActive: "#e10600",
          tabInactive: "#555555",
          chartGrid: "#333333",
          chartLine: "#e10600",
          tooltipBackground: "rgba(37, 42, 55, 0.9)"
        }
      },
      features: {
        performanceChart: !0,
        newsEvents: !0,
        recentTrades: !0
      }
    };
  },
  /**
   * Get dashboard data from the server
   * @returns Promise that resolves with the dashboard data
   */
  getDashboardData: async function() {
    return console.log("Mock: getDashboardData called"), {
      success: !0,
      summary: {
        totalTrades: 120,
        winRate: 0.65,
        profitFactor: 2.3,
        netProfit: 12500
      },
      recentTrades: [{
        id: 1,
        date: "2025-01-15",
        symbol: "AAPL",
        result: "win",
        profit: 350
      }, {
        id: 2,
        date: "2025-01-14",
        symbol: "MSFT",
        result: "loss",
        profit: -150
      }, {
        id: 3,
        date: "2025-01-13",
        symbol: "GOOGL",
        result: "win",
        profit: 420
      }],
      performance: {
        daily: [{
          date: "2025-01-11",
          value: 10200
        }, {
          date: "2025-01-12",
          value: 10500
        }, {
          date: "2025-01-13",
          value: 11200
        }, {
          date: "2025-01-14",
          value: 11e3
        }, {
          date: "2025-01-15",
          value: 12500
        }]
      }
    };
  },
  /**
   * Get trading data from the server
   * @param options - Options for data retrieval
   * @returns Promise that resolves with the trading data
   */
  getTradingData: async function(e) {
    return console.log("Mock: getTradingData called with options:", e), {
      success: !0,
      data: [{
        date: "2025-01-15",
        symbol: "AAPL",
        action: "BUY",
        price: 185.25,
        quantity: 10,
        profit: 350
      }, {
        date: "2025-01-14",
        symbol: "MSFT",
        action: "SELL",
        price: 390.12,
        quantity: 5,
        profit: -150
      }, {
        date: "2025-01-13",
        symbol: "GOOGL",
        action: "BUY",
        price: 142.75,
        quantity: 8,
        profit: 420
      }]
    };
  },
  /**
   * Get performance metrics from the server
   * @param options - Options for metrics retrieval
   * @returns Promise that resolves with the performance metrics
   */
  getPerformanceMetrics: async function(e) {
    return console.log("Mock: getPerformanceMetrics called with options:", e), {
      success: !0,
      metrics: {
        "Win Rate": 0.65,
        "Profit Factor": 2.3,
        "Net Profit": 12500,
        "Average Win": 350,
        "Average Loss": -180
      },
      chartData: [{
        date: "2025-01-11",
        value: 10200
      }, {
        date: "2025-01-12",
        value: 10500
      }, {
        date: "2025-01-13",
        value: 11200
      }, {
        date: "2025-01-14",
        value: 11e3
      }, {
        date: "2025-01-15",
        value: 12500
      }]
    };
  },
  /**
   * Get market news from the server
   * @param options - Options for news retrieval
   * @returns Promise that resolves with the market news
   */
  getMarketNews: async function(e) {
    return console.log("Mock: getMarketNews called with options:", e), {
      success: !0,
      news: [{
        title: "Market Update: Stocks Rally on Fed Decision",
        date: "2025-01-15",
        source: "Financial Times"
      }, {
        title: "Tech Stocks Lead Market Higher",
        date: "2025-01-14",
        source: "Wall Street Journal"
      }, {
        title: "Economic Data Shows Strong Growth",
        date: "2025-01-13",
        source: "Bloomberg"
      }]
    };
  },
  /**
   * Save user preferences to the server
   * @param preferences - User preferences to save
   * @returns Promise that resolves with the success status
   */
  saveUserPreferences: async function(e) {
    return console.log("Mock: saveUserPreferences called with preferences:", e), {
      success: !0
    };
  },
  /**
   * Get user preferences from the server
   * @returns Promise that resolves with the user preferences
   */
  getUserPreferences: async function() {
    return console.log("Mock: getUserPreferences called"), {
      success: !0,
      preferences: {
        theme: "f1",
        refreshInterval: 5,
        showNotifications: !0
      }
    };
  },
  /**
   * Close the dialog
   */
  closeDialog: function() {
    console.log("Mock: closeDialog called");
  }
};
function lt(e) {
  const [r, o] = O(null), [t, a] = O(!1), [c, i] = O(null), d = V(async (...u) => {
    try {
      a(!0), i(null);
      const f = await e(...u);
      if (!f.success && f.error)
        throw new Error(f.error);
      return o(f), f;
    } catch (f) {
      const x = f instanceof Error ? f.message : "An unknown error occurred";
      throw i(x), f;
    } finally {
      a(!1);
    }
  }, [e]), l = V(() => {
    o(null), a(!1), i(null);
  }, []);
  return {
    data: r,
    isLoading: t,
    error: c,
    execute: d,
    reset: l
  };
}
function kn() {
  return lt(vt.getDashboardData);
}
function Ee(e, r = {}) {
  const {
    enabled: o = !0,
    params: t,
    refetchOnWindowFocus: a = !0,
    refetchOnReconnect: c = !0,
    refetchInterval: i,
    retry: d = 3,
    retryDelay: l = 1e3,
    onSuccess: u,
    onError: f,
    onSettled: x,
    initialData: v,
    keepPreviousData: y = !1,
    select: S,
    // cacheTime = 5 * 60 * 1000, // 5 minutes (unused)
    staleTime: E = 0
  } = r, [P, w] = O(v), [I, R] = O(null), [_, z] = O(o), [A, N] = O(o), [T, k] = O(!1), [Q, Y] = O(!1), [C, q] = O(0), G = ne(e), te = ne(t), oe = ne(0), K = ne(null), ie = ne(!0);
  re(() => {
    G.current = e, te.current = t;
  }, [e, t]), re(() => () => {
    ie.current = !1, K.current && clearInterval(K.current);
  }, []);
  const Z = V(async () => {
    if (!ie.current)
      return P;
    N(!0), !y && !P && z(!0);
    try {
      const F = await G.current(te.current);
      if (!ie.current)
        return F;
      if (F && typeof F == "object" && "success" in F) {
        const ce = F;
        if (!ce.success && ce.error)
          throw new Error(ce.error);
      }
      const X = S ? S(F) : F;
      return w(X), k(!0), Y(!1), R(null), q(Date.now()), u && u(X), x && x(X, null), oe.current = 0, X;
    } catch (F) {
      if (!ie.current)
        throw F;
      const X = F instanceof Error ? F : new Error(String(F));
      if (Y(!0), R(X), f && f(X), x && x(void 0, X), oe.current < d) {
        oe.current++;
        const ce = typeof l == "function" ? l(oe.current) : l;
        return await new Promise((Ie) => setTimeout(Ie, ce)), Z();
      }
      throw X;
    } finally {
      ie.current && (z(!1), N(!1));
    }
  }, [P, y, f, x, u, d, l, S]), be = V(async () => Z(), [Z]);
  return re(() => o ? ((!C || Date.now() - C > E || P === void 0) && Z().catch(() => {
  }), i && (K.current = setInterval(() => {
    Z().catch(() => {
    });
  }, i)), () => {
    K.current && clearInterval(K.current);
  }) : void 0, [o, Z, i, P, C, E]), re(() => {
    if (!a)
      return;
    const F = () => {
      (!C || Date.now() - C > E) && o && Z().catch(() => {
      });
    };
    return window.addEventListener("focus", F), () => {
      window.removeEventListener("focus", F);
    };
  }, [a, Z, o, C, E]), re(() => {
    if (!c)
      return;
    const F = () => {
      (!C || Date.now() - C > E) && o && Z().catch(() => {
      });
    };
    return window.addEventListener("online", F), () => {
      window.removeEventListener("online", F);
    };
  }, [c, Z, o, C, E]), {
    data: P,
    isLoading: _,
    isFetching: A,
    isSuccess: T,
    isError: Q,
    error: I,
    refetch: be
  };
}
function dt(e, r = {}) {
  const {
    onSuccess: o,
    onError: t,
    onSettled: a,
    onMutate: c,
    retry: i = 0,
    retryDelay: d = 1e3
  } = r, [l, u] = O(void 0), [f, x] = O(null), [v, y] = O(!1), [S, E] = O(!1), [P, w] = O(!1), I = ne(e), R = ne(0), _ = ne(!0);
  O(() => {
    I.current = e;
  }), O(() => () => {
    _.current = !1;
  });
  const z = V(() => {
    u(void 0), x(null), y(!1), E(!1), w(!1), R.current = 0;
  }, []), A = V(async (N) => {
    if (!_.current)
      return l;
    y(!0), E(!1), w(!1), x(null), c && await c(N);
    try {
      const T = await I.current(N);
      if (!_.current)
        return T;
      if (T && typeof T == "object" && "success" in T) {
        const k = T;
        if (!k.success && k.error)
          throw new Error(k.error);
      }
      return u(T), E(!0), o && o(T, N), a && a(T, null, N), R.current = 0, T;
    } catch (T) {
      if (!_.current)
        throw T;
      const k = T instanceof Error ? T : new Error(String(T));
      if (w(!0), x(k), t && t(k, N), a && a(void 0, k, N), R.current < i) {
        R.current++;
        const Q = typeof d == "function" ? d(R.current) : d;
        return await new Promise((Y) => setTimeout(Y, Q)), A(N);
      }
      throw k;
    } finally {
      _.current && y(!1);
    }
  }, [l, t, c, a, o, i, d]);
  return {
    mutate: A,
    reset: z,
    data: l,
    isLoading: v,
    isSuccess: S,
    isError: P,
    error: f
  };
}
var s = {}, ut = {
  get exports() {
    return s;
  },
  set exports(e) {
    s = e;
  }
}, ge = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ur;
function ft() {
  if (ur)
    return ge;
  ur = 1;
  var e = me, r = Symbol.for("react.element"), o = Symbol.for("react.fragment"), t = Object.prototype.hasOwnProperty, a = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, c = { key: !0, ref: !0, __self: !0, __source: !0 };
  function i(d, l, u) {
    var f, x = {}, v = null, y = null;
    u !== void 0 && (v = "" + u), l.key !== void 0 && (v = "" + l.key), l.ref !== void 0 && (y = l.ref);
    for (f in l)
      t.call(l, f) && !c.hasOwnProperty(f) && (x[f] = l[f]);
    if (d && d.defaultProps)
      for (f in l = d.defaultProps, l)
        x[f] === void 0 && (x[f] = l[f]);
    return { $$typeof: r, type: d, key: v, ref: y, props: x, _owner: a.current };
  }
  return ge.Fragment = o, ge.jsx = i, ge.jsxs = i, ge;
}
var he = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var fr;
function pt() {
  return fr || (fr = 1, process.env.NODE_ENV !== "production" && function() {
    var e = me, r = Symbol.for("react.element"), o = Symbol.for("react.portal"), t = Symbol.for("react.fragment"), a = Symbol.for("react.strict_mode"), c = Symbol.for("react.profiler"), i = Symbol.for("react.provider"), d = Symbol.for("react.context"), l = Symbol.for("react.forward_ref"), u = Symbol.for("react.suspense"), f = Symbol.for("react.suspense_list"), x = Symbol.for("react.memo"), v = Symbol.for("react.lazy"), y = Symbol.for("react.offscreen"), S = Symbol.iterator, E = "@@iterator";
    function P(n) {
      if (n === null || typeof n != "object")
        return null;
      var p = S && n[S] || n[E];
      return typeof p == "function" ? p : null;
    }
    var w = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function I(n) {
      {
        for (var p = arguments.length, m = new Array(p > 1 ? p - 1 : 0), j = 1; j < p; j++)
          m[j - 1] = arguments[j];
        R("error", n, m);
      }
    }
    function R(n, p, m) {
      {
        var j = w.ReactDebugCurrentFrame, B = j.getStackAddendum();
        B !== "" && (p += "%s", m = m.concat([B]));
        var L = m.map(function($) {
          return String($);
        });
        L.unshift("Warning: " + p), Function.prototype.apply.call(console[n], console, L);
      }
    }
    var _ = !1, z = !1, A = !1, N = !1, T = !1, k;
    k = Symbol.for("react.module.reference");
    function Q(n) {
      return !!(typeof n == "string" || typeof n == "function" || n === t || n === c || T || n === a || n === u || n === f || N || n === y || _ || z || A || typeof n == "object" && n !== null && (n.$$typeof === v || n.$$typeof === x || n.$$typeof === i || n.$$typeof === d || n.$$typeof === l || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      n.$$typeof === k || n.getModuleId !== void 0));
    }
    function Y(n, p, m) {
      var j = n.displayName;
      if (j)
        return j;
      var B = p.displayName || p.name || "";
      return B !== "" ? m + "(" + B + ")" : m;
    }
    function C(n) {
      return n.displayName || "Context";
    }
    function q(n) {
      if (n == null)
        return null;
      if (typeof n.tag == "number" && I("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof n == "function")
        return n.displayName || n.name || null;
      if (typeof n == "string")
        return n;
      switch (n) {
        case t:
          return "Fragment";
        case o:
          return "Portal";
        case c:
          return "Profiler";
        case a:
          return "StrictMode";
        case u:
          return "Suspense";
        case f:
          return "SuspenseList";
      }
      if (typeof n == "object")
        switch (n.$$typeof) {
          case d:
            var p = n;
            return C(p) + ".Consumer";
          case i:
            var m = n;
            return C(m._context) + ".Provider";
          case l:
            return Y(n, n.render, "ForwardRef");
          case x:
            var j = n.displayName || null;
            return j !== null ? j : q(n.type) || "Memo";
          case v: {
            var B = n, L = B._payload, $ = B._init;
            try {
              return q($(L));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var G = Object.assign, te = 0, oe, K, ie, Z, be, F, X;
    function ce() {
    }
    ce.__reactDisabledLog = !0;
    function Ie() {
      {
        if (te === 0) {
          oe = console.log, K = console.info, ie = console.warn, Z = console.error, be = console.group, F = console.groupCollapsed, X = console.groupEnd;
          var n = {
            configurable: !0,
            enumerable: !0,
            value: ce,
            writable: !0
          };
          Object.defineProperties(console, {
            info: n,
            log: n,
            warn: n,
            error: n,
            group: n,
            groupCollapsed: n,
            groupEnd: n
          });
        }
        te++;
      }
    }
    function $r() {
      {
        if (te--, te === 0) {
          var n = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: G({}, n, {
              value: oe
            }),
            info: G({}, n, {
              value: K
            }),
            warn: G({}, n, {
              value: ie
            }),
            error: G({}, n, {
              value: Z
            }),
            group: G({}, n, {
              value: be
            }),
            groupCollapsed: G({}, n, {
              value: F
            }),
            groupEnd: G({}, n, {
              value: X
            })
          });
        }
        te < 0 && I("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var Te = w.ReactCurrentDispatcher, Re;
    function xe(n, p, m) {
      {
        if (Re === void 0)
          try {
            throw Error();
          } catch (B) {
            var j = B.stack.trim().match(/\n( *(at )?)/);
            Re = j && j[1] || "";
          }
        return `
` + Re + n;
      }
    }
    var De = !1, ye;
    {
      var Br = typeof WeakMap == "function" ? WeakMap : Map;
      ye = new Br();
    }
    function Xe(n, p) {
      if (!n || De)
        return "";
      {
        var m = ye.get(n);
        if (m !== void 0)
          return m;
      }
      var j;
      De = !0;
      var B = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var L;
      L = Te.current, Te.current = null, Ie();
      try {
        if (p) {
          var $ = function() {
            throw Error();
          };
          if (Object.defineProperty($.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct($, []);
            } catch (se) {
              j = se;
            }
            Reflect.construct(n, [], $);
          } else {
            try {
              $.call();
            } catch (se) {
              j = se;
            }
            n.call($.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (se) {
            j = se;
          }
          n();
        }
      } catch (se) {
        if (se && j && typeof se.stack == "string") {
          for (var D = se.stack.split(`
`), J = j.stack.split(`
`), U = D.length - 1, W = J.length - 1; U >= 1 && W >= 0 && D[U] !== J[W]; )
            W--;
          for (; U >= 1 && W >= 0; U--, W--)
            if (D[U] !== J[W]) {
              if (U !== 1 || W !== 1)
                do
                  if (U--, W--, W < 0 || D[U] !== J[W]) {
                    var ee = `
` + D[U].replace(" at new ", " at ");
                    return n.displayName && ee.includes("<anonymous>") && (ee = ee.replace("<anonymous>", n.displayName)), typeof n == "function" && ye.set(n, ee), ee;
                  }
                while (U >= 1 && W >= 0);
              break;
            }
        }
      } finally {
        De = !1, Te.current = L, $r(), Error.prepareStackTrace = B;
      }
      var fe = n ? n.displayName || n.name : "", dr = fe ? xe(fe) : "";
      return typeof n == "function" && ye.set(n, dr), dr;
    }
    function Nr(n, p, m) {
      return Xe(n, !1);
    }
    function Mr(n) {
      var p = n.prototype;
      return !!(p && p.isReactComponent);
    }
    function ve(n, p, m) {
      if (n == null)
        return "";
      if (typeof n == "function")
        return Xe(n, Mr(n));
      if (typeof n == "string")
        return xe(n);
      switch (n) {
        case u:
          return xe("Suspense");
        case f:
          return xe("SuspenseList");
      }
      if (typeof n == "object")
        switch (n.$$typeof) {
          case l:
            return Nr(n.render);
          case x:
            return ve(n.type, p, m);
          case v: {
            var j = n, B = j._payload, L = j._init;
            try {
              return ve(L(B), p, m);
            } catch {
            }
          }
        }
      return "";
    }
    var we = Object.prototype.hasOwnProperty, Qe = {}, Ze = w.ReactDebugCurrentFrame;
    function ke(n) {
      if (n) {
        var p = n._owner, m = ve(n.type, n._source, p ? p.type : null);
        Ze.setExtraStackFrame(m);
      } else
        Ze.setExtraStackFrame(null);
    }
    function Lr(n, p, m, j, B) {
      {
        var L = Function.call.bind(we);
        for (var $ in n)
          if (L(n, $)) {
            var D = void 0;
            try {
              if (typeof n[$] != "function") {
                var J = Error((j || "React class") + ": " + m + " type `" + $ + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof n[$] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw J.name = "Invariant Violation", J;
              }
              D = n[$](p, $, j, m, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (U) {
              D = U;
            }
            D && !(D instanceof Error) && (ke(B), I("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", j || "React class", m, $, typeof D), ke(null)), D instanceof Error && !(D.message in Qe) && (Qe[D.message] = !0, ke(B), I("Failed %s type: %s", m, D.message), ke(null));
          }
      }
    }
    var Or = Array.isArray;
    function Pe(n) {
      return Or(n);
    }
    function Ar(n) {
      {
        var p = typeof Symbol == "function" && Symbol.toStringTag, m = p && n[Symbol.toStringTag] || n.constructor.name || "Object";
        return m;
      }
    }
    function _r(n) {
      try {
        return er(n), !1;
      } catch {
        return !0;
      }
    }
    function er(n) {
      return "" + n;
    }
    function rr(n) {
      if (_r(n))
        return I("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Ar(n)), er(n);
    }
    var pe = w.ReactCurrentOwner, Fr = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, tr, or, $e;
    $e = {};
    function zr(n) {
      if (we.call(n, "ref")) {
        var p = Object.getOwnPropertyDescriptor(n, "ref").get;
        if (p && p.isReactWarning)
          return !1;
      }
      return n.ref !== void 0;
    }
    function qr(n) {
      if (we.call(n, "key")) {
        var p = Object.getOwnPropertyDescriptor(n, "key").get;
        if (p && p.isReactWarning)
          return !1;
      }
      return n.key !== void 0;
    }
    function Ur(n, p) {
      if (typeof n.ref == "string" && pe.current && p && pe.current.stateNode !== p) {
        var m = q(pe.current.type);
        $e[m] || (I('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', q(pe.current.type), n.ref), $e[m] = !0);
      }
    }
    function Wr(n, p) {
      {
        var m = function() {
          tr || (tr = !0, I("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", p));
        };
        m.isReactWarning = !0, Object.defineProperty(n, "key", {
          get: m,
          configurable: !0
        });
      }
    }
    function Hr(n, p) {
      {
        var m = function() {
          or || (or = !0, I("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", p));
        };
        m.isReactWarning = !0, Object.defineProperty(n, "ref", {
          get: m,
          configurable: !0
        });
      }
    }
    var Yr = function(n, p, m, j, B, L, $) {
      var D = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: r,
        // Built-in properties that belong on the element
        type: n,
        key: p,
        ref: m,
        props: $,
        // Record the component responsible for creating this element.
        _owner: L
      };
      return D._store = {}, Object.defineProperty(D._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(D, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: j
      }), Object.defineProperty(D, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: B
      }), Object.freeze && (Object.freeze(D.props), Object.freeze(D)), D;
    };
    function Gr(n, p, m, j, B) {
      {
        var L, $ = {}, D = null, J = null;
        m !== void 0 && (rr(m), D = "" + m), qr(p) && (rr(p.key), D = "" + p.key), zr(p) && (J = p.ref, Ur(p, B));
        for (L in p)
          we.call(p, L) && !Fr.hasOwnProperty(L) && ($[L] = p[L]);
        if (n && n.defaultProps) {
          var U = n.defaultProps;
          for (L in U)
            $[L] === void 0 && ($[L] = U[L]);
        }
        if (D || J) {
          var W = typeof n == "function" ? n.displayName || n.name || "Unknown" : n;
          D && Wr($, W), J && Hr($, W);
        }
        return Yr(n, D, J, B, j, pe.current, $);
      }
    }
    var Be = w.ReactCurrentOwner, nr = w.ReactDebugCurrentFrame;
    function ue(n) {
      if (n) {
        var p = n._owner, m = ve(n.type, n._source, p ? p.type : null);
        nr.setExtraStackFrame(m);
      } else
        nr.setExtraStackFrame(null);
    }
    var Ne;
    Ne = !1;
    function Me(n) {
      return typeof n == "object" && n !== null && n.$$typeof === r;
    }
    function ar() {
      {
        if (Be.current) {
          var n = q(Be.current.type);
          if (n)
            return `

Check the render method of \`` + n + "`.";
        }
        return "";
      }
    }
    function Vr(n) {
      {
        if (n !== void 0) {
          var p = n.fileName.replace(/^.*[\\\/]/, ""), m = n.lineNumber;
          return `

Check your code at ` + p + ":" + m + ".";
        }
        return "";
      }
    }
    var sr = {};
    function Jr(n) {
      {
        var p = ar();
        if (!p) {
          var m = typeof n == "string" ? n : n.displayName || n.name;
          m && (p = `

Check the top-level render call using <` + m + ">.");
        }
        return p;
      }
    }
    function ir(n, p) {
      {
        if (!n._store || n._store.validated || n.key != null)
          return;
        n._store.validated = !0;
        var m = Jr(p);
        if (sr[m])
          return;
        sr[m] = !0;
        var j = "";
        n && n._owner && n._owner !== Be.current && (j = " It was passed a child from " + q(n._owner.type) + "."), ue(n), I('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', m, j), ue(null);
      }
    }
    function cr(n, p) {
      {
        if (typeof n != "object")
          return;
        if (Pe(n))
          for (var m = 0; m < n.length; m++) {
            var j = n[m];
            Me(j) && ir(j, p);
          }
        else if (Me(n))
          n._store && (n._store.validated = !0);
        else if (n) {
          var B = P(n);
          if (typeof B == "function" && B !== n.entries)
            for (var L = B.call(n), $; !($ = L.next()).done; )
              Me($.value) && ir($.value, p);
        }
      }
    }
    function Kr(n) {
      {
        var p = n.type;
        if (p == null || typeof p == "string")
          return;
        var m;
        if (typeof p == "function")
          m = p.propTypes;
        else if (typeof p == "object" && (p.$$typeof === l || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        p.$$typeof === x))
          m = p.propTypes;
        else
          return;
        if (m) {
          var j = q(p);
          Lr(m, n.props, "prop", j, n);
        } else if (p.PropTypes !== void 0 && !Ne) {
          Ne = !0;
          var B = q(p);
          I("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", B || "Unknown");
        }
        typeof p.getDefaultProps == "function" && !p.getDefaultProps.isReactClassApproved && I("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function Xr(n) {
      {
        for (var p = Object.keys(n.props), m = 0; m < p.length; m++) {
          var j = p[m];
          if (j !== "children" && j !== "key") {
            ue(n), I("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", j), ue(null);
            break;
          }
        }
        n.ref !== null && (ue(n), I("Invalid attribute `ref` supplied to `React.Fragment`."), ue(null));
      }
    }
    function lr(n, p, m, j, B, L) {
      {
        var $ = Q(n);
        if (!$) {
          var D = "";
          (n === void 0 || typeof n == "object" && n !== null && Object.keys(n).length === 0) && (D += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var J = Vr(B);
          J ? D += J : D += ar();
          var U;
          n === null ? U = "null" : Pe(n) ? U = "array" : n !== void 0 && n.$$typeof === r ? (U = "<" + (q(n.type) || "Unknown") + " />", D = " Did you accidentally export a JSX literal instead of a component?") : U = typeof n, I("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", U, D);
        }
        var W = Gr(n, p, m, B, L);
        if (W == null)
          return W;
        if ($) {
          var ee = p.children;
          if (ee !== void 0)
            if (j)
              if (Pe(ee)) {
                for (var fe = 0; fe < ee.length; fe++)
                  cr(ee[fe], n);
                Object.freeze && Object.freeze(ee);
              } else
                I("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              cr(ee, n);
        }
        return n === t ? Xr(W) : Kr(W), W;
      }
    }
    function Qr(n, p, m) {
      return lr(n, p, m, !0);
    }
    function Zr(n, p, m) {
      return lr(n, p, m, !1);
    }
    var et = Zr, rt = Qr;
    he.Fragment = t, he.jsx = et, he.jsxs = rt;
  }()), he;
}
(function(e) {
  process.env.NODE_ENV === "production" ? e.exports = ft() : e.exports = pt();
})(ut);
class Cr {
  /**
   * Create a new circuit breaker
   * @param options - Circuit breaker options
   */
  constructor(r = {}) {
    this.state = "CLOSED", this.failureCount = 0, this.successCount = 0, this.nextAttempt = Date.now(), this.options = {
      failureThreshold: r.failureThreshold || 5,
      resetTimeout: r.resetTimeout || 3e4,
      // 30 seconds
      successThreshold: r.successThreshold || 2,
      requestTimeout: r.requestTimeout || 1e4,
      // 10 seconds
      logging: r.logging !== void 0 ? r.logging : !0
    };
  }
  /**
   * Execute a function with circuit breaker protection
   * @param fn - The function to execute
   * @returns The result of the function
   * @throws Error if the circuit is open
   */
  async execute(r) {
    if (this.state === "OPEN") {
      if (Date.now() < this.nextAttempt)
        throw this.log(`Circuit is OPEN. Next attempt at ${new Date(this.nextAttempt).toISOString()}`), new Error("Circuit is open");
      this.log("Circuit is switching to HALF_OPEN"), this.state = "HALF_OPEN";
    }
    try {
      const o = await this.executeWithTimeout(r);
      return this.handleSuccess(), o;
    } catch (o) {
      throw this.handleFailure(o), o;
    }
  }
  /**
   * Execute a function with a timeout
   * @param fn - The function to execute
   * @returns The result of the function
   * @throws Error if the function times out
   */
  async executeWithTimeout(r) {
    return this.options.requestTimeout ? Promise.race([r(), new Promise((o, t) => {
      setTimeout(() => {
        t(new Error(`Request timed out after ${this.options.requestTimeout}ms`));
      }, this.options.requestTimeout);
    })]) : r();
  }
  /**
   * Handle a successful request
   */
  handleSuccess() {
    this.state === "HALF_OPEN" ? (this.successCount++, this.successCount >= this.options.successThreshold && (this.log("Circuit is switching to CLOSED"), this.state = "CLOSED", this.failureCount = 0, this.successCount = 0)) : this.failureCount = 0;
  }
  /**
   * Handle a failed request
   * @param error - The error that occurred
   */
  handleFailure(r) {
    this.state === "HALF_OPEN" ? (this.log(`Request failed in HALF_OPEN state: ${r.message}`), this.state = "OPEN", this.nextAttempt = Date.now() + this.options.resetTimeout, this.successCount = 0) : this.state === "CLOSED" && (this.failureCount++, this.log(`Failure count: ${this.failureCount}/${this.options.failureThreshold}`), this.failureCount >= this.options.failureThreshold && (this.log("Circuit is switching to OPEN"), this.state = "OPEN", this.nextAttempt = Date.now() + this.options.resetTimeout));
  }
  /**
   * Log a message if logging is enabled
   * @param message - The message to log
   */
  log(r) {
    this.options.logging && console.log(`[CircuitBreaker] ${r}`);
  }
  /**
   * Get the current state of the circuit breaker
   * @returns The current state
   */
  getState() {
    return this.state;
  }
  /**
   * Reset the circuit breaker to its initial state
   */
  reset() {
    this.state = "CLOSED", this.failureCount = 0, this.successCount = 0, this.nextAttempt = Date.now(), this.log("Circuit has been reset");
  }
}
function Cn(e = {}) {
  console.log("Monitoring service initialized", e);
}
function Sr(e, r) {
  console.error("Error captured by monitoring service:", e, r);
}
function Sn(e) {
  console.log("User set for monitoring service:", e);
}
function En(e, r) {
  const o = performance.now();
  return {
    name: e,
    startTime: o,
    finish: () => {
      const a = performance.now() - o;
      console.log(`Transaction "${e}" finished in ${a.toFixed(2)}ms`, r);
    }
  };
}
function gt(e, r = {}, o) {
  return o.has(e) || o.set(e, new Cr(r)), o.get(e);
}
function ht(e, r, o) {
  if (o) {
    const t = r * Math.pow(2, e), a = 0.5 + Math.random() * 0.5;
    return t * a;
  }
  return r;
}
function mt(e) {
  return e >= 500 || e === 429;
}
async function bt(e, r, o, t) {
  try {
    return await t.execute(() => r());
  } catch (a) {
    const i = a;
    throw i.isCircuitOpen = !0, i;
  }
}
function pr(e, r, o, t) {
  var a;
  Sr(e, {
    source: "ApiClient",
    tags: {
      method: r,
      url: o,
      status: ((a = e.status) == null ? void 0 : a.toString()) || "unknown"
    },
    data: {
      retries: t,
      isTimeout: e.isTimeout,
      isCircuitOpen: e.isCircuitOpen
    }
  });
}
class xt {
  /**
   * Create a new API client
   * @param options - API client options
   */
  constructor(r = {}) {
    this.requestInterceptors = [], this.responseInterceptors = [], this.cache = /* @__PURE__ */ new Map(), this.abortControllers = /* @__PURE__ */ new Map(), this.endpointCircuitBreakers = /* @__PURE__ */ new Map(), this.baseUrl = r.baseUrl ?? "", this.defaultHeaders = r.defaultHeaders || {
      "Content-Type": "application/json"
    }, this.defaultTimeout = r.defaultTimeout || 3e4, this.defaultMaxRetries = r.defaultMaxRetries || 3, this.defaultRetryDelay = r.defaultRetryDelay || 1e3, this.useExponentialBackoff = r.useExponentialBackoff || !1, this.enableCache = r.enableCache || !1, this.defaultCacheExpiration = r.defaultCacheExpiration || 5 * 60 * 1e3, this.useCircuitBreaker = r.useCircuitBreaker || !1, this.reportErrorsToMonitoring = r.reportErrorsToMonitoring || !1, this.circuitBreaker = new Cr(r.circuitBreakerOptions);
  }
  /**
   * Add a request interceptor
   * @param interceptor - Request interceptor
   * @returns Function to remove the interceptor
   */
  addRequestInterceptor(r) {
    return this.requestInterceptors.push(r), () => {
      const o = this.requestInterceptors.indexOf(r);
      o !== -1 && this.requestInterceptors.splice(o, 1);
    };
  }
  /**
   * Add a response interceptor
   * @param interceptor - Response interceptor
   * @returns Function to remove the interceptor
   */
  addResponseInterceptor(r) {
    return this.responseInterceptors.push(r), () => {
      const o = this.responseInterceptors.indexOf(r);
      o !== -1 && this.responseInterceptors.splice(o, 1);
    };
  }
  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear();
  }
  /**
   * Make a request
   * @param method - HTTP method
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise that resolves with the response
   */
  async request(r, o, t = {
    baseUrl: ""
  }) {
    let a = {
      ...t,
      baseUrl: t.baseUrl || this.baseUrl,
      headers: {
        ...this.defaultHeaders,
        ...t.headers
      },
      timeout: t.timeout ?? this.defaultTimeout,
      maxRetries: t.maxRetries ?? this.defaultMaxRetries,
      retryDelay: t.retryDelay ?? this.defaultRetryDelay,
      useExponentialBackoff: t.useExponentialBackoff ?? this.useExponentialBackoff,
      cache: t.cache !== void 0 ? t.cache : this.enableCache,
      cacheExpiration: t.cacheExpiration ?? this.defaultCacheExpiration,
      useCircuitBreaker: t.useCircuitBreaker ?? this.useCircuitBreaker,
      isIdempotent: t.isIdempotent ?? r.toUpperCase() !== "POST"
    };
    for (const d of this.requestInterceptors)
      try {
        a = await d.onRequest(a);
      } catch (l) {
        if (d.onRequestError)
          return d.onRequestError(l);
        throw l;
      }
    const c = this.getCacheKey(r, o, a);
    if (a.cache && r.toUpperCase() === "GET") {
      const d = this.cache.get(c);
      if (d && d.expiration > Date.now())
        return d.data;
    }
    if (a.requestId && a.abortPrevious) {
      const d = this.abortControllers.get(a.requestId);
      d && d.abort();
    }
    const i = new AbortController();
    if (a.requestId && this.abortControllers.set(a.requestId, i), a.useCircuitBreaker)
      try {
        const d = gt(o, a.circuitBreakerOptions, this.endpointCircuitBreakers);
        return await bt(o, () => this.executeRequest(r, o, a, i), a, d);
      } catch (d) {
        const u = d;
        throw u.isCircuitOpen = !0, this.reportErrorsToMonitoring && pr(u, r, o, 0), u;
      }
    return this.executeRequest(r, o, a, i);
  }
  /**
   * Execute a request with retries
   * @param method - HTTP method
   * @param url - Request URL
   * @param requestConfig - Request configuration
   * @param controller - Abort controller
   * @returns Promise that resolves with the response
   */
  async executeRequest(r, o, t, a) {
    let c = 0, i = new Error("Unknown error");
    const d = this.getCacheKey(r, o, t);
    for (; c <= (t.maxRetries || 0); )
      try {
        const l = this.buildUrl(o, t), u = setTimeout(() => a.abort(), t.timeout), f = await fetch(l, {
          method: r,
          headers: t.headers,
          body: t.data ? JSON.stringify(t.data) : void 0,
          signal: a.signal,
          credentials: t.withCredentials ? "include" : "same-origin"
        });
        if (clearTimeout(u), !f.ok) {
          const S = new Error(`HTTP error ${f.status}: ${f.statusText}`);
          throw S.status = f.status, S.isRetryable = mt(f.status) && t.isIdempotent, S;
        }
        let x;
        const v = f.headers.get("content-type");
        v && v.includes("application/json") ? x = await f.json() : x = await f.text();
        let y = x;
        for (const S of this.responseInterceptors)
          try {
            y = await S.onResponse(y);
          } catch (E) {
            if (S.onResponseError)
              y = await S.onResponseError(E);
            else
              throw E;
          }
        return t.cache && r.toUpperCase() === "GET" && this.cache.set(d, {
          data: y,
          expiration: Date.now() + (t.cacheExpiration || this.defaultCacheExpiration)
        }), t.requestId && this.abortControllers.delete(t.requestId), y;
      } catch (l) {
        const u = l;
        if (i = u, u.name === "AbortError") {
          i.isTimeout = !0;
          break;
        }
        if (i.isRetryable === !1 || c >= (t.maxRetries || 0))
          break;
        const f = ht(c, t.retryDelay || this.defaultRetryDelay, t.useExponentialBackoff || this.useExponentialBackoff);
        await new Promise((x) => setTimeout(x, f)), c++;
      }
    t.requestId && this.abortControllers.delete(t.requestId);
    for (const l of this.responseInterceptors)
      if (l.onResponseError)
        try {
          return await l.onResponseError(i);
        } catch (u) {
          i = u;
        }
    throw this.reportErrorsToMonitoring && pr(i, r, o, c), i;
  }
  /**
   * Make a GET request
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise that resolves with the response
   */
  get(r, o = {
    baseUrl: ""
  }) {
    return this.request("GET", r, o);
  }
  /**
   * Make a POST request
   * @param url - Request URL
   * @param data - Request data
   * @param config - Request configuration
   * @returns Promise that resolves with the response
   */
  post(r, o, t = {
    baseUrl: ""
  }) {
    return this.request("POST", r, {
      ...t,
      data: o
    });
  }
  /**
   * Make a PUT request
   * @param url - Request URL
   * @param data - Request data
   * @param config - Request configuration
   * @returns Promise that resolves with the response
   */
  put(r, o, t = {
    baseUrl: ""
  }) {
    return this.request("PUT", r, {
      ...t,
      data: o
    });
  }
  /**
   * Make a DELETE request
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise that resolves with the response
   */
  delete(r, o = {
    baseUrl: ""
  }) {
    return this.request("DELETE", r, o);
  }
  /**
   * Make a PATCH request
   * @param url - Request URL
   * @param data - Request data
   * @param config - Request configuration
   * @returns Promise that resolves with the response
   */
  patch(r, o, t = {
    baseUrl: ""
  }) {
    return this.request("PATCH", r, {
      ...t,
      data: o
    });
  }
  /**
   * Build the full URL
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Full URL
   */
  buildUrl(r, o) {
    const t = o.baseUrl || "", a = r.startsWith("http") ? r : `${t}${r}`;
    if (!o.params)
      return a;
    const c = new URLSearchParams();
    for (const [d, l] of Object.entries(o.params))
      l != null && (Array.isArray(l) ? l.forEach((u) => {
        c.append(`${d}[]`, String(u));
      }) : c.append(d, String(l)));
    const i = c.toString();
    return i ? `${a}${a.includes("?") ? "&" : "?"}${i}` : a;
  }
  /**
   * Get a cache key for the request
   * @param method - HTTP method
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Cache key
   */
  getCacheKey(r, o, t) {
    const a = t.params ? JSON.stringify(t.params) : "";
    return `${r}:${o}:${a}`;
  }
}
class Er extends xt {
  /**
   * Create a new Trading API client
   * @param options - Trading API client options
   */
  constructor(r = {}) {
    const o = {
      ...r,
      defaultMaxRetries: r.defaultMaxRetries || 3,
      useExponentialBackoff: r.useExponentialBackoff !== void 0 ? r.useExponentialBackoff : !0,
      useCircuitBreaker: r.useCircuitBreaker !== void 0 ? r.useCircuitBreaker : !0,
      reportErrorsToMonitoring: r.reportErrorsToMonitoring !== void 0 ? r.reportErrorsToMonitoring : !0,
      circuitBreakerOptions: {
        failureThreshold: 3,
        resetTimeout: 3e4,
        successThreshold: 2,
        ...r.circuitBreakerOptions || {}
      }
    };
    super(o), this.useMockData = r.useMockData || !1, this.mockDataDelay = r.mockDataDelay || 500, this.addResponseInterceptor({
      onResponse: (t) => {
        if (t && typeof t == "object" && "success" in t && !t.success && t.error)
          throw new Error(t.error);
        return t;
      },
      onResponseError: (t) => {
        throw console.error("Trading API Error:", t), o.reportErrorsToMonitoring && Sr(t, {
          source: "TradingApiClient",
          tags: {
            type: "api_error"
          }
        }), t;
      }
    });
  }
  /**
   * Get configuration from the server
   * @param config - Request configuration
   * @returns Promise that resolves with the configuration
   */
  async getConfiguration(r = {
    baseUrl: ""
  }) {
    return this.useMockData ? (await this.delay(), this.getMockConfiguration()) : this.get("/api/configuration", {
      ...r,
      cache: !0,
      useCircuitBreaker: !0,
      isIdempotent: !0,
      maxRetries: 3
    });
  }
  /**
   * Get dashboard data from the server
   * @param config - Request configuration
   * @returns Promise that resolves with the dashboard data
   */
  async getDashboardData(r = {
    baseUrl: ""
  }) {
    return this.useMockData ? (await this.delay(), this.getMockDashboardData()) : this.get("/api/dashboard", {
      ...r,
      useCircuitBreaker: !0,
      isIdempotent: !0,
      maxRetries: 2
    });
  }
  /**
   * Get trading data from the server
   * @param options - Options for data retrieval
   * @param config - Request configuration
   * @returns Promise that resolves with the trading data
   */
  async getTradingData(r = {}, o = {
    baseUrl: ""
  }) {
    return this.useMockData ? (await this.delay(), this.getMockTradingData(r)) : this.get("/api/trading", {
      ...o,
      params: r,
      useCircuitBreaker: !0,
      isIdempotent: !0,
      maxRetries: 3,
      useExponentialBackoff: !0,
      requestId: "trading-data",
      abortPrevious: !0
    });
  }
  /**
   * Get performance metrics from the server
   * @param options - Options for metrics retrieval
   * @param config - Request configuration
   * @returns Promise that resolves with the performance metrics
   */
  async getPerformanceMetrics(r = {}, o = {
    baseUrl: ""
  }) {
    return this.useMockData ? (await this.delay(), this.getMockPerformanceMetrics(r)) : this.get("/api/metrics", {
      ...o,
      params: r,
      useCircuitBreaker: !0,
      isIdempotent: !0,
      maxRetries: 2,
      cache: !0,
      cacheExpiration: 5 * 60 * 1e3
      // 5 minutes
    });
  }
  /**
   * Get market news from the server
   * @param options - Options for news retrieval
   * @param config - Request configuration
   * @returns Promise that resolves with the market news
   */
  async getMarketNews(r = {}, o = {
    baseUrl: ""
  }) {
    return this.useMockData ? (await this.delay(), this.getMockMarketNews(r)) : this.get("/api/news", {
      ...o,
      params: r,
      useCircuitBreaker: !0,
      isIdempotent: !0,
      maxRetries: 2,
      cache: !0,
      cacheExpiration: 15 * 60 * 1e3
      // 15 minutes
    });
  }
  /**
   * Save user preferences to the server
   * @param preferences - User preferences to save
   * @param config - Request configuration
   * @returns Promise that resolves with the success status
   */
  async saveUserPreferences(r, o = {
    baseUrl: ""
  }) {
    return this.useMockData ? (await this.delay(), {
      success: !0
    }) : this.post("/api/preferences", r, {
      ...o,
      useCircuitBreaker: !0,
      isIdempotent: !0,
      // Saving preferences should be idempotent
      maxRetries: 2
    });
  }
  /**
   * Get user preferences from the server
   * @param config - Request configuration
   * @returns Promise that resolves with the user preferences
   */
  async getUserPreferences(r = {
    baseUrl: ""
  }) {
    return this.useMockData ? (await this.delay(), this.getMockUserPreferences()) : this.get("/api/preferences", {
      ...r,
      cache: !0,
      useCircuitBreaker: !0,
      isIdempotent: !0,
      maxRetries: 2,
      cacheExpiration: 30 * 60 * 1e3
      // 30 minutes
    });
  }
  /**
   * Close the dialog (for Google Apps Script integration)
   */
  closeDialog() {
    window.google && window.google.script && window.google.script.host && window.google.script.host.close();
  }
  /**
   * Delay for mock data
   * @returns Promise that resolves after the delay
   */
  async delay() {
    return new Promise((r) => setTimeout(r, this.mockDataDelay));
  }
  /**
   * Get mock configuration
   * @returns Mock configuration
   */
  getMockConfiguration() {
    return {
      success: !0,
      theme: {
        colors: {
          primary: "#e10600",
          // F1 red
          background: "#1a1f2c",
          cardBackground: "#252a37",
          text: "#ffffff",
          secondaryText: "#aaaaaa",
          positive: "#4caf50",
          negative: "#f44336",
          neutral: "#9e9e9e",
          border: "#333333",
          tabActive: "#e10600",
          tabInactive: "#555555",
          chartGrid: "#333333",
          chartLine: "#e10600",
          tooltipBackground: "rgba(37, 42, 55, 0.9)"
        }
      },
      features: {
        performanceChart: !0,
        newsEvents: !0,
        recentTrades: !0
      }
    };
  }
  /**
   * Get mock dashboard data
   * @returns Mock dashboard data
   */
  getMockDashboardData() {
    return {
      success: !0,
      summary: {
        totalTrades: 120,
        winRate: 0.65,
        profitFactor: 2.3,
        netProfit: 12500
      },
      recentTrades: [{
        id: 1,
        date: "2025-01-15",
        symbol: "AAPL",
        result: "win",
        profit: 350
      }, {
        id: 2,
        date: "2025-01-14",
        symbol: "MSFT",
        result: "loss",
        profit: -150
      }, {
        id: 3,
        date: "2025-01-13",
        symbol: "GOOGL",
        result: "win",
        profit: 420
      }],
      performance: {
        daily: [{
          date: "2025-01-11",
          value: 10200
        }, {
          date: "2025-01-12",
          value: 10500
        }, {
          date: "2025-01-13",
          value: 11200
        }, {
          date: "2025-01-14",
          value: 11e3
        }, {
          date: "2025-01-15",
          value: 12500
        }]
      }
    };
  }
  /**
   * Get mock trading data
   * @param options - Options for data retrieval
   * @returns Mock trading data
   */
  getMockTradingData(r) {
    return {
      success: !0,
      data: [{
        date: "2025-01-15",
        symbol: "AAPL",
        action: "BUY",
        price: 185.25,
        quantity: 10,
        profit: 350
      }, {
        date: "2025-01-14",
        symbol: "MSFT",
        action: "SELL",
        price: 390.12,
        quantity: 5,
        profit: -150
      }, {
        date: "2025-01-13",
        symbol: "GOOGL",
        action: "BUY",
        price: 142.75,
        quantity: 8,
        profit: 420
      }]
    };
  }
  /**
   * Get mock performance metrics
   * @param options - Options for metrics retrieval
   * @returns Mock performance metrics
   */
  getMockPerformanceMetrics(r) {
    return {
      success: !0,
      metrics: {
        "Win Rate": 0.65,
        "Profit Factor": 2.3,
        "Net Profit": 12500,
        "Average Win": 350,
        "Average Loss": -180
      },
      chartData: [{
        date: "2025-01-11",
        value: 10200
      }, {
        date: "2025-01-12",
        value: 10500
      }, {
        date: "2025-01-13",
        value: 11200
      }, {
        date: "2025-01-14",
        value: 11e3
      }, {
        date: "2025-01-15",
        value: 12500
      }]
    };
  }
  /**
   * Get mock market news
   * @param options - Options for news retrieval
   * @returns Mock market news
   */
  getMockMarketNews(r) {
    return {
      success: !0,
      news: [{
        title: "Market Update: Stocks Rally on Fed Decision",
        date: "2025-01-15",
        source: "Financial Times"
      }, {
        title: "Tech Stocks Lead Market Higher",
        date: "2025-01-14",
        source: "Wall Street Journal"
      }, {
        title: "Economic Data Shows Strong Growth",
        date: "2025-01-13",
        source: "Bloomberg"
      }]
    };
  }
  /**
   * Get mock user preferences
   * @returns Mock user preferences
   */
  getMockUserPreferences() {
    return {
      success: !0,
      preferences: {
        theme: "f1",
        refreshInterval: 5,
        showNotifications: !0
      }
    };
  }
}
const jr = Oe(void 0), jn = ({
  children: e,
  options: r,
  client: o
}) => {
  const t = ae(() => o || new Er(r), [o, r]);
  return /* @__PURE__ */ s.jsx(jr.Provider, { value: t, children: e });
};
function je() {
  const e = Ae(jr);
  if (e === void 0)
    throw new Error("useApi must be used within an ApiProvider");
  return e;
}
function In(e = {}) {
  const r = je(), {
    tradingOptions: o,
    ...t
  } = e, a = V((c) => r.getTradingData(c || o), [r, o]);
  return Ee(a, {
    ...t,
    params: o
  });
}
function Tn(e = {}) {
  const r = je(), {
    metricsOptions: o,
    ...t
  } = e, a = V((c) => r.getPerformanceMetrics(c || o), [r, o]);
  return Ee(a, {
    ...t,
    params: o
  });
}
function Rn(e = {}) {
  const r = je(), {
    newsOptions: o,
    ...t
  } = e, a = V((c) => r.getMarketNews(c || o), [r, o]);
  return Ee(a, {
    ...t,
    params: o
  });
}
function Dn(e = {}) {
  var d;
  const r = je(), {
    autoFetch: o = !0,
    ...t
  } = e, a = V(() => r.getUserPreferences(), [r]), c = Ee(a, {
    ...t,
    enabled: o
  }), i = dt((l) => r.saveUserPreferences(l), {
    onSuccess: () => {
      c.refetch();
    }
  });
  return {
    preferences: (d = c.data) == null ? void 0 : d.preferences,
    isLoading: c.isLoading,
    isError: c.isError,
    error: c.error,
    refetch: c.refetch,
    savePreferences: i.mutate,
    isSaving: i.isLoading,
    saveError: i.error
  };
}
function _e() {
  return !!(window.google && window.google.script && window.google.script.run);
}
const yt = _e() ? it : ct;
console.log(`Using ${_e() ? "GAS" : "mock"} API implementation`);
const Pn = new Er({
  useMockData: !_e(),
  enableCache: !0,
  defaultMaxRetries: 3
}), vt = yt, wt = {
  small: g(["padding:", ";font-size:", ";min-height:20px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs, ({
    dot: e
  }) => e ? "8px" : "20px"),
  medium: g(["padding:", ";font-size:", ";min-height:24px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm, ({
    dot: e
  }) => e ? "10px" : "24px"),
  large: g(["padding:", ";font-size:", ";min-height:32px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md, ({
    dot: e
  }) => e ? "12px" : "32px")
}, kt = (e, r, o = !1) => g(["", ""], ({
  theme: t
}) => {
  let a, c, i;
  switch (e) {
    case "primary":
      a = r ? t.colors.primary : `${t.colors.primary}20`, c = r ? t.colors.textInverse : t.colors.primary, i = t.colors.primary;
      break;
    case "secondary":
      a = r ? t.colors.secondary : `${t.colors.secondary}20`, c = r ? t.colors.textInverse : t.colors.secondary, i = t.colors.secondary;
      break;
    case "success":
      a = r ? t.colors.success : `${t.colors.success}20`, c = r ? t.colors.textInverse : t.colors.success, i = t.colors.success;
      break;
    case "warning":
      a = r ? t.colors.warning : `${t.colors.warning}20`, c = r ? t.colors.textInverse : t.colors.warning, i = t.colors.warning;
      break;
    case "error":
      a = r ? t.colors.error : `${t.colors.error}20`, c = r ? t.colors.textInverse : t.colors.error, i = t.colors.error;
      break;
    case "info":
      a = r ? t.colors.info : `${t.colors.info}20`, c = r ? t.colors.textInverse : t.colors.info, i = t.colors.info;
      break;
    case "neutral":
      a = r ? t.colors.textSecondary : `${t.colors.textSecondary}10`, c = r ? t.colors.textInverse : t.colors.textSecondary, i = t.colors.textSecondary;
      break;
    default:
      a = r ? t.colors.textSecondary : `${t.colors.textSecondary}20`, c = r ? t.colors.textInverse : t.colors.textSecondary, i = t.colors.textSecondary;
  }
  return o ? `
          background-color: transparent;
          color: ${i};
          border: 1px solid ${i};
        ` : `
        background-color: ${a};
        color: ${c};
        border: 1px solid transparent;
      `;
}), Ir = /* @__PURE__ */ h.span.withConfig({
  displayName: "IconContainer",
  componentId: "sc-10uskub-0"
})(["display:flex;align-items:center;justify-content:center;"]), Ct = /* @__PURE__ */ h(Ir).withConfig({
  displayName: "StartIcon",
  componentId: "sc-10uskub-1"
})(["margin-right:", ";"], ({
  theme: e
}) => e.spacing.xxs), St = /* @__PURE__ */ h(Ir).withConfig({
  displayName: "EndIcon",
  componentId: "sc-10uskub-2"
})(["margin-left:", ";"], ({
  theme: e
}) => e.spacing.xxs), Et = /* @__PURE__ */ h.span.withConfig({
  displayName: "StyledBadge",
  componentId: "sc-10uskub-3"
})(["display:", ";align-items:center;justify-content:center;border-radius:", ";font-weight:", ";white-space:nowrap;", " ", " ", " ", " ", ""], ({
  inline: e
}) => e ? "inline-flex" : "flex", ({
  theme: e,
  rounded: r,
  dot: o
}) => o ? "50%" : r ? "9999px" : e.borderRadius.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => wt[e], ({
  variant: e,
  solid: r,
  outlined: o
}) => kt(e, r, o || !1), ({
  dot: e
}) => e && g(["padding:0;height:8px;width:8px;"]), ({
  counter: e
}) => e && g(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), $n = ({
  children: e,
  variant: r = "default",
  size: o = "medium",
  solid: t = !1,
  className: a,
  onClick: c,
  rounded: i = !1,
  dot: d = !1,
  counter: l = !1,
  outlined: u = !1,
  startIcon: f,
  endIcon: x,
  max: v,
  inline: y = !0
}) => {
  let S = e;
  return l && typeof e == "number" && v !== void 0 && e > v && (S = `${v}+`), /* @__PURE__ */ s.jsx(Et, { variant: r, size: o, solid: t, clickable: !!c, className: a, onClick: c, rounded: i, dot: d, counter: l, outlined: u, inline: y, children: !d && /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    f && /* @__PURE__ */ s.jsx(Ct, { children: f }),
    S,
    x && /* @__PURE__ */ s.jsx(St, { children: x })
  ] }) });
}, jt = /* @__PURE__ */ Se(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), It = /* @__PURE__ */ h.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1rze74q-0"
})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:", " 0.8s linear infinite;margin-right:", ";"], jt, ({
  theme: e
}) => e.spacing.xs), Tt = {
  small: g(["padding:", ";font-size:", ";min-height:32px;"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";min-height:40px;"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";min-height:48px;"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.lg}`, ({
    theme: e
  }) => e.fontSizes.md)
}, Rt = {
  primary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.primaryDark, ({
    theme: e
  }) => e.colors.primaryDark),
  secondary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.secondaryDark, ({
    theme: e
  }) => e.colors.secondaryDark),
  outline: g(["background-color:transparent;color:", ";border:1px solid ", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  text: g(["background-color:transparent;color:", ";border:none;padding-left:", ";padding-right:", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.spacing.xs),
  success: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.success, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.success),
  danger: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.error, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.error)
}, Dt = /* @__PURE__ */ h.button.withConfig({
  displayName: "StyledButton",
  componentId: "sc-1rze74q-1"
})(["display:inline-flex;align-items:center;justify-content:center;border-radius:", ";font-weight:", ";cursor:pointer;transition:all ", ";position:relative;overflow:hidden;", " ", " ", " &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  size: e = "medium"
}) => Tt[e], ({
  variant: e = "primary"
}) => Rt[e], ({
  fullWidth: e
}) => e && g(["width:100%;"]), ({
  $hasStartIcon: e
}) => e && g(["& > *:first-child{margin-right:", ";}"], ({
  theme: r
}) => r.spacing.xs), ({
  $hasEndIcon: e
}) => e && g(["& > *:last-child{margin-left:", ";}"], ({
  theme: r
}) => r.spacing.xs)), Pt = /* @__PURE__ */ h.div.withConfig({
  displayName: "ButtonContent",
  componentId: "sc-1rze74q-2"
})(["display:flex;align-items:center;justify-content:center;"]), de = ({
  children: e,
  variant: r = "primary",
  disabled: o = !1,
  loading: t = !1,
  size: a = "medium",
  fullWidth: c = !1,
  startIcon: i,
  endIcon: d,
  onClick: l,
  className: u,
  type: f = "button",
  ...x
}) => /* @__PURE__ */ s.jsx(Dt, { variant: r, disabled: o || t, size: a, fullWidth: c, onClick: l, className: u, type: f, $hasStartIcon: !!i && !t, $hasEndIcon: !!d && !t, ...x, children: /* @__PURE__ */ s.jsxs(Pt, { children: [
  t && /* @__PURE__ */ s.jsx(It, {}),
  !t && i,
  e,
  !t && d
] }) }), $t = /* @__PURE__ */ h.div.withConfig({
  displayName: "InputWrapper",
  componentId: "sc-uv3rzi-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), Bt = /* @__PURE__ */ h.label.withConfig({
  displayName: "Label",
  componentId: "sc-uv3rzi-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), Nt = /* @__PURE__ */ h.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-uv3rzi-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: o,
  isFocused: t
}) => r ? e.colors.error : o ? e.colors.success : t ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: o,
  hasSuccess: t
}) => e && g(["box-shadow:0 0 0 2px ", ";"], o ? `${r.colors.error}33` : t ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), gr = /* @__PURE__ */ h.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-uv3rzi-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Mt = /* @__PURE__ */ h.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-uv3rzi-4"
})(["flex:1;border:none;background:transparent;color:", ";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:", ";}", " ", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.textDisabled, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  hasEndIcon: e
}) => e && g(["padding-right:0;"]), ({
  size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), Lt = /* @__PURE__ */ h.button.withConfig({
  displayName: "ClearButton",
  componentId: "sc-uv3rzi-5"
})(["background:none;border:none;cursor:pointer;color:", ";padding:0 ", ";display:flex;align-items:center;justify-content:center;&:hover{color:", ";}&:focus{outline:none;}"], ({
  theme: e
}) => e.colors.textDisabled, ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Ot = /* @__PURE__ */ h.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-uv3rzi-6"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: o
}) => r ? e.colors.error : o ? e.colors.success : e.colors.textSecondary), Bn = ({
  value: e,
  onChange: r,
  placeholder: o,
  disabled: t = !1,
  error: a,
  type: c = "text",
  name: i,
  id: d,
  className: l,
  required: u = !1,
  autoComplete: f,
  label: x,
  helperText: v,
  startIcon: y,
  endIcon: S,
  loading: E = !1,
  success: P = !1,
  clearable: w = !1,
  onClear: I,
  maxLength: R,
  showCharCount: _ = !1,
  size: z = "medium",
  fullWidth: A = !1,
  ...N
}) => {
  const [T, k] = O(!1), Q = ne(null), Y = () => {
    I ? I() : r(""), Q.current && Q.current.focus();
  }, C = (K) => {
    k(!0), N.onFocus && N.onFocus(K);
  }, q = (K) => {
    k(!1), N.onBlur && N.onBlur(K);
  }, G = w && e && !t, te = (e == null ? void 0 : e.length) || 0, oe = _ || R !== void 0 && R > 0;
  return /* @__PURE__ */ s.jsxs($t, { className: l, fullWidth: A, children: [
    x && /* @__PURE__ */ s.jsxs(Bt, { htmlFor: d, children: [
      x,
      u && " *"
    ] }),
    /* @__PURE__ */ s.jsxs(Nt, { hasError: !!a, hasSuccess: !!P, disabled: !!t, size: z, hasStartIcon: !!y, hasEndIcon: !!(S || G), isFocused: !!T, children: [
      y && /* @__PURE__ */ s.jsx(gr, { children: y }),
      /* @__PURE__ */ s.jsx(
        Mt,
        {
          ref: Q,
          type: c,
          value: e,
          onChange: (K) => r(K.target.value),
          placeholder: o,
          disabled: !!(t || E),
          name: i,
          id: d,
          required: !!u,
          autoComplete: f,
          hasStartIcon: !!y,
          hasEndIcon: !!(S || G),
          size: z,
          maxLength: R,
          onFocus: C,
          onBlur: q,
          ...N
        }
      ),
      G && /* @__PURE__ */ s.jsx(Lt, { type: "button", onClick: Y, tabIndex: -1, children: "✕" }),
      S && /* @__PURE__ */ s.jsx(gr, { children: S })
    ] }),
    (a || v || oe) && /* @__PURE__ */ s.jsxs(Ot, { hasError: !!a, hasSuccess: !!P, children: [
      /* @__PURE__ */ s.jsx("div", { children: a || v }),
      oe && /* @__PURE__ */ s.jsxs("div", { children: [
        te,
        R !== void 0 && `/${R}`
      ] })
    ] })
  ] });
}, hr = {
  small: g(["height:100px;"]),
  medium: g(["height:200px;"]),
  large: g(["height:300px;"]),
  custom: (e) => g(["height:", ";width:", ";"], e.customHeight, e.customWidth || "100%")
}, At = {
  default: g(["background-color:", ";border-radius:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.md),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm),
  text: g(["background-color:transparent;height:auto !important;min-height:1.5em;"]),
  list: g(["background-color:", ";border-radius:", ";margin-bottom:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.sm, ({
    theme: e
  }) => e.spacing.sm)
}, _t = /* @__PURE__ */ Se(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Ft = /* @__PURE__ */ h.div.withConfig({
  displayName: "Container",
  componentId: "sc-12vczt5-0"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;", " ", ""], ({
  size: e,
  customHeight: r,
  customWidth: o
}) => e === "custom" ? hr.custom({
  customHeight: r,
  customWidth: o
}) : hr[e], ({
  variant: e
}) => At[e]), zt = /* @__PURE__ */ h.div.withConfig({
  displayName: "Spinner",
  componentId: "sc-12vczt5-1"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:", " 1s linear infinite;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary, _t, ({
  theme: e
}) => e.spacing.sm), qt = /* @__PURE__ */ h.div.withConfig({
  displayName: "Text",
  componentId: "sc-12vczt5-2"
})(["color:", ";font-size:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm), Ut = ({
  variant: e = "default",
  size: r = "medium",
  height: o = "200px",
  width: t,
  text: a = "Loading...",
  showSpinner: c = !0,
  className: i
}) => /* @__PURE__ */ s.jsxs(Ft, { variant: e, size: r, customHeight: o, customWidth: t, className: i, children: [
  c && /* @__PURE__ */ s.jsx(zt, {}),
  a && /* @__PURE__ */ s.jsx(qt, { children: a })
] }), Wt = /* @__PURE__ */ h.div.withConfig({
  displayName: "SelectWrapper",
  componentId: "sc-wvk2um-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), Ht = /* @__PURE__ */ h.label.withConfig({
  displayName: "Label",
  componentId: "sc-wvk2um-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), Yt = /* @__PURE__ */ h.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-wvk2um-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: o,
  isFocused: t
}) => r ? e.colors.error : o ? e.colors.success : t ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: o,
  hasSuccess: t
}) => e && g(["box-shadow:0 0 0 2px ", ";"], o ? `${r.colors.error}33` : t ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), Gt = /* @__PURE__ */ h.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-wvk2um-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Vt = /* @__PURE__ */ h.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-wvk2um-4"
})(["flex:1;border:none;background:transparent;color:", `;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `, " center;background-size:16px;padding-right:", ";&:disabled{cursor:not-allowed;}", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.xl, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), Jt = /* @__PURE__ */ h.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-wvk2um-5"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: o
}) => r ? e.colors.error : o ? e.colors.success : e.colors.textSecondary), Kt = /* @__PURE__ */ h.optgroup.withConfig({
  displayName: "OptionGroup",
  componentId: "sc-wvk2um-6"
})(["font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => e.colors.textPrimary), Nn = ({
  options: e,
  value: r,
  onChange: o,
  disabled: t = !1,
  error: a,
  name: c,
  id: i,
  className: d,
  required: l = !1,
  placeholder: u,
  label: f,
  helperText: x,
  size: v = "medium",
  fullWidth: y = !0,
  loading: S = !1,
  success: E = !1,
  startIcon: P,
  ...w
}) => {
  const [I, R] = O(!1), _ = (k) => {
    R(!0), w.onFocus && w.onFocus(k);
  }, z = (k) => {
    R(!1), w.onBlur && w.onBlur(k);
  }, A = {}, N = [];
  e.forEach((k) => {
    k.group ? (A[k.group] || (A[k.group] = []), A[k.group].push(k)) : N.push(k);
  });
  const T = Object.keys(A).length > 0;
  return /* @__PURE__ */ s.jsxs(Wt, { className: d, fullWidth: y, children: [
    f && /* @__PURE__ */ s.jsxs(Ht, { htmlFor: i, children: [
      f,
      l && " *"
    ] }),
    /* @__PURE__ */ s.jsxs(Yt, { hasError: !!a, hasSuccess: !!E, disabled: !!(t || S), size: v, hasStartIcon: !!P, isFocused: !!I, children: [
      P && /* @__PURE__ */ s.jsx(Gt, { children: P }),
      /* @__PURE__ */ s.jsxs(
        Vt,
        {
          value: r,
          onChange: (k) => o(k.target.value),
          disabled: !!(t || S),
          name: c,
          id: i,
          required: !!l,
          hasStartIcon: !!P,
          size: v,
          onFocus: _,
          onBlur: z,
          ...w,
          children: [
            u && /* @__PURE__ */ s.jsx("option", { value: "", disabled: !0, children: u }),
            T ? /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
              N.map((k) => /* @__PURE__ */ s.jsx("option", { value: k.value, disabled: k.disabled, children: k.label }, k.value)),
              Object.entries(A).map(([k, Q]) => /* @__PURE__ */ s.jsx(Kt, { label: k, children: Q.map((Y) => /* @__PURE__ */ s.jsx("option", { value: Y.value, disabled: Y.disabled, children: Y.label }, Y.value)) }, k))
            ] }) : (
              // Render all options without groups
              e.map((k) => /* @__PURE__ */ s.jsx("option", { value: k.value, disabled: k.disabled, children: k.label }, k.value))
            )
          ]
        }
      )
    ] }),
    (a || x) && /* @__PURE__ */ s.jsx(Jt, { hasError: !!a, hasSuccess: !!E, children: /* @__PURE__ */ s.jsx("div", { children: a || x }) })
  ] });
}, mr = {
  small: "8px",
  medium: "12px",
  large: "16px"
}, Xt = {
  small: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.xs, ({
    theme: e
  }) => e.spacing.xs),
  medium: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.sm, ({
    theme: e
  }) => e.spacing.sm),
  large: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.md, ({
    theme: e
  }) => e.spacing.md)
}, Qt = /* @__PURE__ */ g(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]), Zt = /* @__PURE__ */ h.div.withConfig({
  displayName: "Container",
  componentId: "sc-gwj3m-0"
})(["display:inline-flex;align-items:center;"]), eo = /* @__PURE__ */ h.div.withConfig({
  displayName: "Indicator",
  componentId: "sc-gwj3m-1"
})(["border-radius:50%;width:", ";height:", ";", ""], ({
  size: e
}) => mr[e], ({
  size: e
}) => mr[e], ({
  status: e,
  theme: r,
  pulse: o
}) => {
  let t, a;
  switch (e) {
    case "success":
      t = r.colors.success, a = "76, 175, 80";
      break;
    case "error":
      t = r.colors.error, a = "244, 67, 54";
      break;
    case "warning":
      t = r.colors.warning, a = "255, 152, 0";
      break;
    case "info":
      t = r.colors.info, a = "33, 150, 243";
      break;
    default:
      t = r.colors.textSecondary, a = "158, 158, 158";
  }
  return g(["background-color:", ";", ""], t, o && g(["--pulse-color:", ";", ""], a, Qt));
}), ro = /* @__PURE__ */ h.span.withConfig({
  displayName: "Label",
  componentId: "sc-gwj3m-2"
})(["", " ", ""], ({
  size: e
}) => Xt[e], ({
  status: e,
  theme: r
}) => {
  let o;
  switch (e) {
    case "success":
      o = r.colors.success;
      break;
    case "error":
      o = r.colors.error;
      break;
    case "warning":
      o = r.colors.warning;
      break;
    case "info":
      o = r.colors.info;
      break;
    default:
      o = r.colors.textSecondary;
  }
  return g(["color:", ";font-weight:", ";"], o, r.fontWeights.medium);
}), Mn = ({
  status: e,
  size: r = "medium",
  pulse: o = !1,
  showLabel: t = !1,
  label: a,
  className: c
}) => {
  const i = a || e.charAt(0).toUpperCase() + e.slice(1);
  return /* @__PURE__ */ s.jsxs(Zt, { className: c, children: [
    /* @__PURE__ */ s.jsx(eo, { status: e, size: r, pulse: o }),
    t && /* @__PURE__ */ s.jsx(ro, { status: e, size: r, children: i })
  ] });
}, to = {
  small: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md)
}, oo = (e) => g(["", ""], ({
  theme: r
}) => {
  let o, t, a;
  switch (e) {
    case "primary":
      o = `${r.colors.primary}10`, t = r.colors.primary, a = `${r.colors.primary}30`;
      break;
    case "secondary":
      o = `${r.colors.secondary}10`, t = r.colors.secondary, a = `${r.colors.secondary}30`;
      break;
    case "success":
      o = `${r.colors.success}10`, t = r.colors.success, a = `${r.colors.success}30`;
      break;
    case "warning":
      o = `${r.colors.warning}10`, t = r.colors.warning, a = `${r.colors.warning}30`;
      break;
    case "error":
      o = `${r.colors.error}10`, t = r.colors.error, a = `${r.colors.error}30`;
      break;
    case "info":
      o = `${r.colors.info}10`, t = r.colors.info, a = `${r.colors.info}30`;
      break;
    default:
      o = `${r.colors.textSecondary}10`, t = r.colors.textSecondary, a = `${r.colors.textSecondary}30`;
  }
  return `
        background-color: ${o};
        color: ${t};
        border: 1px solid ${a};
      `;
}), no = /* @__PURE__ */ h.span.withConfig({
  displayName: "StyledTag",
  componentId: "sc-11nmnw9-0"
})(["display:inline-flex;align-items:center;border-radius:", ";font-weight:", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.pill, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => to[e], ({
  variant: e
}) => oo(e), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), ao = /* @__PURE__ */ h.button.withConfig({
  displayName: "RemoveButton",
  componentId: "sc-11nmnw9-1"
})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:", ";padding:0;", " &:hover{opacity:1;}"], ({
  theme: e
}) => e.spacing.xs, ({
  size: e,
  theme: r
}) => {
  const o = {
    small: "12px",
    medium: "14px",
    large: "16px"
  };
  return `
      width: ${o[e]};
      height: ${o[e]};
      font-size: ${r.fontSizes.xs};
    `;
}), Ln = ({
  children: e,
  variant: r = "default",
  size: o = "medium",
  removable: t = !1,
  onRemove: a,
  className: c,
  onClick: i
}) => {
  const d = (l) => {
    l.stopPropagation(), a == null || a();
  };
  return /* @__PURE__ */ s.jsxs(no, { variant: r, size: o, clickable: !!i, className: c, onClick: i, children: [
    e,
    t && /* @__PURE__ */ s.jsx(ao, { size: o, onClick: d, children: "×" })
  ] });
}, so = {
  none: g(["padding:0;"]),
  small: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.sm),
  medium: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.md),
  large: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.lg)
}, io = {
  default: g(["background-color:", ";"], ({
    theme: e
  }) => e.colors.surface),
  primary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  secondary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.secondary),
  outlined: g(["background-color:transparent;border:1px solid ", ";"], ({
    theme: e
  }) => e.colors.border),
  elevated: g(["background-color:", ";box-shadow:", ";border:none;"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.shadows.md)
}, co = /* @__PURE__ */ h.div.withConfig({
  displayName: "CardContainer",
  componentId: "sc-mv9m67-0"
})(["border-radius:", ";overflow:hidden;transition:all ", ";position:relative;", " ", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.transitions.fast, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";"], r.colors.border), ({
  padding: e
}) => so[e], ({
  variant: e
}) => io[e], ({
  clickable: e
}) => e && g(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:", ";}&:active{transform:translateY(0);}"], ({
  theme: r
}) => r.shadows.medium)), lo = /* @__PURE__ */ h.div.withConfig({
  displayName: "CardHeader",
  componentId: "sc-mv9m67-1"
})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), uo = /* @__PURE__ */ h.div.withConfig({
  displayName: "HeaderContent",
  componentId: "sc-mv9m67-2"
})(["flex:1;"]), fo = /* @__PURE__ */ h.h3.withConfig({
  displayName: "CardTitle",
  componentId: "sc-mv9m67-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), po = /* @__PURE__ */ h.div.withConfig({
  displayName: "CardSubtitle",
  componentId: "sc-mv9m67-4"
})(["margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary), go = /* @__PURE__ */ h.div.withConfig({
  displayName: "ActionsContainer",
  componentId: "sc-mv9m67-5"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), ho = /* @__PURE__ */ h.div.withConfig({
  displayName: "CardContent",
  componentId: "sc-mv9m67-6"
})([""]), mo = /* @__PURE__ */ h.div.withConfig({
  displayName: "CardFooter",
  componentId: "sc-mv9m67-7"
})(["margin-top:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.border), bo = /* @__PURE__ */ h.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-mv9m67-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), xo = /* @__PURE__ */ h.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-mv9m67-9"
})(["padding:", ";background-color:", "10;border-radius:", ";color:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.md), yo = /* @__PURE__ */ h.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-mv9m67-10"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), vo = ({
  children: e,
  title: r,
  subtitle: o,
  bordered: t = !0,
  variant: a = "default",
  padding: c = "medium",
  className: i,
  footer: d,
  actions: l,
  isLoading: u = !1,
  hasError: f = !1,
  errorMessage: x = "An error occurred",
  clickable: v = !1,
  onClick: y,
  ...S
}) => {
  const E = r || o || l;
  return /* @__PURE__ */ s.jsxs(co, { bordered: t, variant: a, padding: c, clickable: v, className: i, onClick: v ? y : void 0, ...S, children: [
    u && /* @__PURE__ */ s.jsx(bo, { children: /* @__PURE__ */ s.jsx(yo, {}) }),
    E && /* @__PURE__ */ s.jsxs(lo, { children: [
      /* @__PURE__ */ s.jsxs(uo, { children: [
        r && /* @__PURE__ */ s.jsx(fo, { children: r }),
        o && /* @__PURE__ */ s.jsx(po, { children: o })
      ] }),
      l && /* @__PURE__ */ s.jsx(go, { children: l })
    ] }),
    f && /* @__PURE__ */ s.jsx(xo, { children: /* @__PURE__ */ s.jsx("p", { children: x }) }),
    /* @__PURE__ */ s.jsx(ho, { children: e }),
    d && /* @__PURE__ */ s.jsx(mo, { children: d })
  ] });
}, wo = /* @__PURE__ */ h.h3.withConfig({
  displayName: "Title",
  componentId: "sc-1jsjvya-0"
})(["margin:0 0 ", " 0;color:", ";font-weight:", ";", ""], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontWeights.semibold, ({
  size: e,
  theme: r
}) => {
  const o = {
    small: r.fontSizes.md,
    medium: r.fontSizes.lg,
    large: r.fontSizes.xl
  };
  return g(["font-size:", ";"], o[e]);
}), ko = /* @__PURE__ */ h.p.withConfig({
  displayName: "Description",
  componentId: "sc-1jsjvya-1"
})(["margin:0 0 ", " 0;color:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.colors.textSecondary, ({
  size: e,
  theme: r
}) => {
  const o = {
    small: r.fontSizes.sm,
    medium: r.fontSizes.md,
    large: r.fontSizes.lg
  };
  return g(["font-size:", ";"], o[e]);
}), Co = {
  default: g(["background-color:transparent;"]),
  compact: g(["background-color:transparent;text-align:left;align-items:flex-start;"]),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm)
}, So = /* @__PURE__ */ h.div.withConfig({
  displayName: "Container",
  componentId: "sc-1jsjvya-2"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;", " ", ""], ({
  variant: e
}) => Co[e], ({
  size: e,
  theme: r
}) => {
  switch (e) {
    case "small":
      return g(["padding:", ";min-height:120px;"], r.spacing.md);
    case "large":
      return g(["padding:", ";min-height:300px;"], r.spacing.xl);
    default:
      return g(["padding:", ";min-height:200px;"], r.spacing.lg);
  }
}), Eo = /* @__PURE__ */ h.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-1jsjvya-3"
})(["margin-bottom:", ";", ""], ({
  theme: e
}) => e.spacing.md, ({
  size: e,
  theme: r
}) => {
  const o = {
    small: "32px",
    medium: "48px",
    large: "64px"
  };
  return g(["font-size:", ";svg{width:", ";height:", ";color:", ";}"], o[e], o[e], o[e], r.colors.textSecondary);
}), jo = /* @__PURE__ */ h.div.withConfig({
  displayName: "ActionContainer",
  componentId: "sc-1jsjvya-4"
})(["margin-top:", ";"], ({
  theme: e
}) => e.spacing.md), Io = /* @__PURE__ */ h.div.withConfig({
  displayName: "ChildrenContainer",
  componentId: "sc-1jsjvya-5"
})(["margin-top:", ";width:100%;"], ({
  theme: e
}) => e.spacing.lg), br = ({
  title: e,
  description: r,
  icon: o,
  actionText: t,
  onAction: a,
  variant: c = "default",
  size: i = "medium",
  className: d,
  children: l
}) => /* @__PURE__ */ s.jsxs(So, { variant: c, size: i, className: d, children: [
  o && /* @__PURE__ */ s.jsx(Eo, { size: i, children: o }),
  e && /* @__PURE__ */ s.jsx(wo, { size: i, children: e }),
  r && /* @__PURE__ */ s.jsx(ko, { size: i, children: r }),
  t && a && /* @__PURE__ */ s.jsx(jo, { children: /* @__PURE__ */ s.jsx(de, { variant: "primary", size: i === "small" ? "small" : "medium", onClick: a, children: t }) }),
  l && /* @__PURE__ */ s.jsx(Io, { children: l })
] }), xr = /* @__PURE__ */ h.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-jxqb9h-0"
})(["padding:1.5rem;margin:", ";border-radius:0.5rem;background-color:", ";color:#ffffff;", ""], (e) => e.isAppLevel ? "0" : "1rem 0", (e) => e.isAppLevel ? "#1a1f2c" : "#f44336", (e) => e.isAppLevel && `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `), To = /* @__PURE__ */ h.div.withConfig({
  displayName: "ErrorCard",
  componentId: "sc-jxqb9h-1"
})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]), yr = /* @__PURE__ */ h.h3.withConfig({
  displayName: "ErrorTitle",
  componentId: "sc-jxqb9h-2"
})(["margin-top:0;font-size:", ";font-weight:700;text-align:", ";"], (e) => e.isAppLevel ? "1.5rem" : "1.25rem", (e) => e.isAppLevel ? "center" : "left"), Ce = /* @__PURE__ */ h.p.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-jxqb9h-3"
})(["margin-bottom:1rem;text-align:", ";"], (e) => e.isAppLevel ? "center" : "left"), vr = /* @__PURE__ */ h.details.withConfig({
  displayName: "ErrorDetails",
  componentId: "sc-jxqb9h-4"
})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]), wr = /* @__PURE__ */ h.pre.withConfig({
  displayName: "ErrorStack",
  componentId: "sc-jxqb9h-5"
})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]), Ro = /* @__PURE__ */ h.div.withConfig({
  displayName: "ButtonContainer",
  componentId: "sc-jxqb9h-6"
})(["display:flex;gap:0.5rem;justify-content:flex-start;"]), Tr = /* @__PURE__ */ h.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-jxqb9h-7"
})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]), Do = /* @__PURE__ */ h.button.withConfig({
  displayName: "SkipButton",
  componentId: "sc-jxqb9h-8"
})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]), Po = /* @__PURE__ */ h(Tr).withConfig({
  displayName: "ReloadButton",
  componentId: "sc-jxqb9h-9"
})(["margin-top:1rem;width:100%;"]), $o = ({
  error: e,
  resetError: r,
  isAppLevel: o,
  name: t,
  onSkip: a
}) => {
  const c = () => {
    window.location.reload();
  };
  return o ? /* @__PURE__ */ s.jsx(xr, { isAppLevel: !0, children: /* @__PURE__ */ s.jsxs(To, { children: [
    /* @__PURE__ */ s.jsx(yr, { isAppLevel: !0, children: "Something went wrong" }),
    /* @__PURE__ */ s.jsx(Ce, { isAppLevel: !0, children: "We're sorry, but an unexpected error has occurred. Please try reloading the application." }),
    /* @__PURE__ */ s.jsxs(vr, { children: [
      /* @__PURE__ */ s.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ s.jsx(Ce, { children: e.message }),
      e.stack && /* @__PURE__ */ s.jsx(wr, { children: e.stack })
    ] }),
    /* @__PURE__ */ s.jsx(Po, { onClick: c, children: "Reload Application" })
  ] }) }) : /* @__PURE__ */ s.jsxs(xr, { children: [
    /* @__PURE__ */ s.jsx(yr, { children: t ? `Error in ${t}` : "Something went wrong" }),
    /* @__PURE__ */ s.jsx(Ce, { children: t ? `We encountered a problem while loading ${t}. You can try again${a ? " or skip this feature" : ""}.` : "An unexpected error occurred. Please try again." }),
    /* @__PURE__ */ s.jsxs(vr, { children: [
      /* @__PURE__ */ s.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ s.jsx(Ce, { children: e.message }),
      e.stack && /* @__PURE__ */ s.jsx(wr, { children: e.stack })
    ] }),
    /* @__PURE__ */ s.jsxs(Ro, { children: [
      /* @__PURE__ */ s.jsx(Tr, { onClick: r, children: "Try Again" }),
      a && /* @__PURE__ */ s.jsx(Do, { onClick: a, children: "Skip This Feature" })
    ] })
  ] });
};
class Bo extends tt {
  constructor(r) {
    super(r), this.resetError = () => {
      this.setState({
        hasError: !1,
        error: null
      });
    }, this.state = {
      hasError: !1,
      error: null
    };
  }
  static getDerivedStateFromError(r) {
    return {
      hasError: !0,
      error: r
    };
  }
  componentDidCatch(r, o) {
    const {
      name: t
    } = this.props, a = t ? `ErrorBoundary(${t})` : "ErrorBoundary";
    console.error(`Error caught by ${a}:`, r, o), this.props.onError && this.props.onError(r, o);
  }
  componentDidUpdate(r) {
    this.state.hasError && this.props.resetOnPropsChange && r.children !== this.props.children && this.resetError();
  }
  componentWillUnmount() {
    this.state.hasError && this.props.resetOnUnmount && this.resetError();
  }
  render() {
    const {
      hasError: r,
      error: o
    } = this.state, {
      children: t,
      fallback: a,
      name: c,
      isFeatureBoundary: i,
      onSkip: d
    } = this.props;
    return r && o ? typeof a == "function" ? a({
      error: o,
      resetError: this.resetError
    }) : a || /* @__PURE__ */ s.jsx($o, { error: o, resetError: this.resetError, isAppLevel: !i, name: c, onSkip: d }) : t;
  }
}
const Rr = ({
  isAppLevel: e,
  isFeatureBoundary: r,
  ...o
}) => {
  const t = e ? "app" : r ? "feature" : "component", a = {
    resetOnPropsChange: t !== "app",
    // App-level boundaries should not reset on props change
    resetOnUnmount: t !== "app",
    // App-level boundaries should not reset on unmount
    isFeatureBoundary: t === "feature"
  };
  return /* @__PURE__ */ s.jsx(Bo, { ...a, ...o });
}, On = (e) => /* @__PURE__ */ s.jsx(Rr, { isAppLevel: !0, ...e }), An = ({
  featureName: e,
  ...r
}) => /* @__PURE__ */ s.jsx(Rr, { isFeatureBoundary: !0, name: e, ...r }), No = /* @__PURE__ */ h.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-i922jg-0"
})(["display:flex;flex-direction:column;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), Mo = /* @__PURE__ */ h.label.withConfig({
  displayName: "Label",
  componentId: "sc-i922jg-1"
})(["font-size:", ";font-weight:500;margin-bottom:", ";color:", ";.required-indicator{color:", ";margin-left:", ";}"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textPrimary, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.xxs), Lo = /* @__PURE__ */ h.div.withConfig({
  displayName: "HelperText",
  componentId: "sc-i922jg-2"
})(["font-size:", ";color:", ";margin-top:", ";"], ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs), _n = ({
  children: e,
  label: r,
  helperText: o,
  required: t = !1,
  error: a,
  className: c,
  id: i,
  ...d
}) => {
  const l = i || `field-${Math.random().toString(36).substr(2, 9)}`, u = me.Children.map(e, (f) => me.isValidElement(f) ? me.cloneElement(f, {
    id: l,
    required: t,
    error: a,
    ...f.props
  }) : f);
  return /* @__PURE__ */ s.jsxs(No, { className: c, ...d, children: [
    /* @__PURE__ */ s.jsxs(Mo, { htmlFor: l, hasError: !!a, children: [
      r,
      t && /* @__PURE__ */ s.jsx("span", { className: "required-indicator", children: "*" })
    ] }),
    u,
    (o || a) && /* @__PURE__ */ s.jsx(Lo, { hasError: !!a, children: a || o })
  ] });
}, Oo = /* @__PURE__ */ Se(["from{opacity:0;}to{opacity:1;}"]), Ao = /* @__PURE__ */ Se(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]), _o = /* @__PURE__ */ h.div.withConfig({
  displayName: "Backdrop",
  componentId: "sc-1cuqxtr-0"
})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:", ";animation:", " 0.2s ease-out;"], ({
  zIndex: e
}) => e || 1e3, Oo), Fo = /* @__PURE__ */ h.div.withConfig({
  displayName: "ModalContainer",
  componentId: "sc-1cuqxtr-1"
})(["background-color:", ";border-radius:", ";box-shadow:", ";display:flex;flex-direction:column;max-height:", ";width:", ";max-width:95vw;animation:", " 0.2s ease-out;position:relative;", " ", ""], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.shadows.lg, ({
  size: e
}) => e === "fullscreen" ? "100vh" : "90vh", ({
  size: e
}) => {
  switch (e) {
    case "small":
      return "400px";
    case "medium":
      return "600px";
    case "large":
      return "800px";
    case "fullscreen":
      return "100vw";
    default:
      return "600px";
  }
}, Ao, ({
  size: e
}) => e === "fullscreen" && g(["height:100vh;border-radius:0;"]), ({
  centered: e
}) => e && g(["margin:auto;"])), zo = /* @__PURE__ */ h.div.withConfig({
  displayName: "ModalHeader",
  componentId: "sc-1cuqxtr-2"
})(["display:flex;justify-content:space-between;align-items:center;padding:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), qo = /* @__PURE__ */ h.h3.withConfig({
  displayName: "ModalTitle",
  componentId: "sc-1cuqxtr-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), Uo = /* @__PURE__ */ h.button.withConfig({
  displayName: "CloseButton",
  componentId: "sc-1cuqxtr-4"
})(["background:none;border:none;cursor:pointer;font-size:", ";color:", ";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:", ";&:hover{background-color:", ";}&:focus{outline:none;box-shadow:0 0 0 2px ", "33;}"], ({
  theme: e
}) => e.fontSizes.xl, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), Wo = /* @__PURE__ */ h.div.withConfig({
  displayName: "ModalContent",
  componentId: "sc-1cuqxtr-5"
})(["padding:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  scrollable: e
}) => e && g(["overflow-y:auto;flex:1;"])), Ho = /* @__PURE__ */ h.div.withConfig({
  displayName: "ModalFooter",
  componentId: "sc-1cuqxtr-6"
})(["display:flex;justify-content:flex-end;gap:", ";padding:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), Fn = ({
  isOpen: e,
  title: r,
  children: o,
  onClose: t,
  size: a = "medium",
  closeOnOutsideClick: c = !0,
  showCloseButton: i = !0,
  footer: d,
  hasFooter: l = !0,
  primaryActionText: u,
  onPrimaryAction: f,
  primaryActionDisabled: x = !1,
  primaryActionLoading: v = !1,
  secondaryActionText: y,
  onSecondaryAction: S,
  secondaryActionDisabled: E = !1,
  className: P,
  zIndex: w = 1e3,
  centered: I = !0,
  // hasBackdrop = true, // Unused prop
  scrollable: R = !0
}) => {
  const _ = ne(null);
  re(() => {
    const T = (k) => {
      k.key === "Escape" && e && c && t();
    };
    return document.addEventListener("keydown", T), () => {
      document.removeEventListener("keydown", T);
    };
  }, [e, t, c]);
  const z = (T) => {
    _.current && !_.current.contains(T.target) && c && t();
  };
  re(() => (e ? document.body.style.overflow = "hidden" : document.body.style.overflow = "", () => {
    document.body.style.overflow = "";
  }), [e]);
  const A = /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    y && /* @__PURE__ */ s.jsx(de, { variant: "outline", onClick: S, disabled: E, children: y }),
    u && /* @__PURE__ */ s.jsx(de, { onClick: f, disabled: x, loading: v, children: u })
  ] });
  return e ? st(/* @__PURE__ */ s.jsx(_o, { onClick: z, zIndex: w, children: /* @__PURE__ */ s.jsxs(Fo, { ref: _, size: a, className: P, centered: I, scrollable: R, onClick: (T) => T.stopPropagation(), children: [
    (r || i) && /* @__PURE__ */ s.jsxs(zo, { children: [
      r && /* @__PURE__ */ s.jsx(qo, { children: r }),
      i && /* @__PURE__ */ s.jsx(Uo, { onClick: t, "aria-label": "Close", children: "×" })
    ] }),
    /* @__PURE__ */ s.jsx(Wo, { scrollable: R, children: o }),
    l && (d || u || y) && /* @__PURE__ */ s.jsx(Ho, { children: d || A })
  ] }) }), document.body) : null;
}, Yo = /* @__PURE__ */ h.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-4as3uq-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), Go = /* @__PURE__ */ h.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-4as3uq-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => e.fontSizes.sm, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";border-radius:", ";"], r.colors.border, r.borderRadius.sm), ({
  compact: e,
  theme: r
}) => e ? g(["th,td{padding:", " ", ";}"], r.spacing.xs, r.spacing.sm) : g(["th,td{padding:", " ", ";}"], r.spacing.sm, r.spacing.md)), Vo = /* @__PURE__ */ h.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-4as3uq-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), Jo = /* @__PURE__ */ h.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-4as3uq-3"
})(["background-color:", ";"], ({
  theme: e
}) => e.colors.background), Ko = /* @__PURE__ */ h.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-4as3uq-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.colors.border, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => r.colors.background), ({
  isSorted: e,
  theme: r
}) => e && g(["color:", ";"], r.colors.primary)), Xo = /* @__PURE__ */ h.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-4as3uq-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => e.spacing.xs, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), Qo = /* @__PURE__ */ h.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-4as3uq-6"
})([""]), Zo = /* @__PURE__ */ h.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-4as3uq-7"
})(["", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: o
}) => e && !o && g(["&:nth-child(even){background-color:", "50;}"], r.colors.background), ({
  hoverable: e,
  theme: r,
  isSelected: o
}) => e && !o && g(["&:hover{background-color:", "aa;}"], r.colors.background), ({
  isSelected: e,
  theme: r
}) => e && g(["background-color:", "15;"], r.colors.primary), ({
  isClickable: e
}) => e && g(["cursor:pointer;"])), en = /* @__PURE__ */ h.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-4as3uq-8"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.textPrimary), rn = /* @__PURE__ */ h.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-4as3uq-9"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => e.spacing.xl, ({
  theme: e
}) => e.colors.textSecondary), tn = /* @__PURE__ */ h.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-4as3uq-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.fontSizes.sm), on = /* @__PURE__ */ h.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-4as3uq-11"
})(["color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), nn = /* @__PURE__ */ h.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-4as3uq-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), an = /* @__PURE__ */ h.div.withConfig({
  displayName: "PageSizeSelector",
  componentId: "sc-4as3uq-13"
})(["display:flex;align-items:center;gap:", ";margin-right:", ";"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md), sn = /* @__PURE__ */ h.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-4as3uq-14"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), cn = /* @__PURE__ */ h.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-4as3uq-15"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary);
function zn({
  columns: e,
  data: r,
  isLoading: o = !1,
  bordered: t = !0,
  striped: a = !0,
  hoverable: c = !0,
  compact: i = !1,
  stickyHeader: d = !1,
  height: l,
  onRowClick: u,
  isRowSelected: f,
  onSort: x,
  sortColumn: v,
  sortDirection: y,
  pagination: S = !1,
  currentPage: E = 1,
  pageSize: P = 10,
  totalRows: w = 0,
  onPageChange: I,
  onPageSizeChange: R,
  className: _,
  emptyMessage: z = "No data available",
  scrollable: A = !0
}) {
  const N = ae(() => e.filter((C) => !C.hidden), [e]), T = ae(() => Math.ceil(w / P), [w, P]), k = ae(() => {
    if (!S)
      return r;
    const C = (E - 1) * P, q = C + P;
    return w > 0 && r.length <= P ? r : r.slice(C, q);
  }, [r, S, E, P, w]), Q = (C) => {
    if (!x)
      return;
    x(C, v === C && y === "asc" ? "desc" : "asc");
  }, Y = (C) => {
    C < 1 || C > T || !I || I(C);
  };
  return /* @__PURE__ */ s.jsxs("div", { style: {
    position: "relative"
  }, children: [
    o && /* @__PURE__ */ s.jsx(sn, { children: /* @__PURE__ */ s.jsx(cn, {}) }),
    /* @__PURE__ */ s.jsx(Yo, { height: l, scrollable: A, children: /* @__PURE__ */ s.jsxs(Go, { bordered: t, striped: a, compact: i, className: _, children: [
      /* @__PURE__ */ s.jsx(Vo, { stickyHeader: d, children: /* @__PURE__ */ s.jsx(Jo, { children: N.map((C) => /* @__PURE__ */ s.jsxs(Ko, { sortable: C.sortable, isSorted: v === C.id, align: C.align, width: C.width, onClick: () => C.sortable && Q(C.id), children: [
        C.header,
        C.sortable && /* @__PURE__ */ s.jsx(Xo, { direction: v === C.id ? y : void 0 })
      ] }, C.id)) }) }),
      /* @__PURE__ */ s.jsx(Qo, { children: k.length > 0 ? k.map((C, q) => /* @__PURE__ */ s.jsx(Zo, { hoverable: c, striped: a, isSelected: f ? f(C, q) : !1, isClickable: !!u, onClick: () => u && u(C, q), children: N.map((G) => /* @__PURE__ */ s.jsx(en, { align: G.align, children: G.cell(C, q) }, G.id)) }, q)) : /* @__PURE__ */ s.jsx("tr", { children: /* @__PURE__ */ s.jsx("td", { colSpan: N.length, children: /* @__PURE__ */ s.jsx(rn, { children: z }) }) }) })
    ] }) }),
    S && T > 0 && /* @__PURE__ */ s.jsxs(tn, { children: [
      /* @__PURE__ */ s.jsxs(on, { children: [
        "Showing ",
        Math.min((E - 1) * P + 1, w),
        " to",
        " ",
        Math.min(E * P, w),
        " of ",
        w,
        " entries"
      ] }),
      /* @__PURE__ */ s.jsxs("div", { style: {
        display: "flex",
        alignItems: "center"
      }, children: [
        R && /* @__PURE__ */ s.jsxs(an, { children: [
          /* @__PURE__ */ s.jsx("span", { children: "Show" }),
          /* @__PURE__ */ s.jsx("select", { value: P, onChange: (C) => R(Number(C.target.value)), style: {
            padding: "4px 8px",
            borderRadius: "4px",
            border: "1px solid #ccc"
          }, children: [10, 25, 50, 100].map((C) => /* @__PURE__ */ s.jsx("option", { value: C, children: C }, C)) }),
          /* @__PURE__ */ s.jsx("span", { children: "entries" })
        ] }),
        /* @__PURE__ */ s.jsxs(nn, { children: [
          /* @__PURE__ */ s.jsx(de, { size: "small", variant: "outline", onClick: () => Y(1), disabled: E === 1, children: "First" }),
          /* @__PURE__ */ s.jsx(de, { size: "small", variant: "outline", onClick: () => Y(E - 1), disabled: E === 1, children: "Prev" }),
          /* @__PURE__ */ s.jsx(de, { size: "small", variant: "outline", onClick: () => Y(E + 1), disabled: E === T, children: "Next" }),
          /* @__PURE__ */ s.jsx(de, { size: "small", variant: "outline", onClick: () => Y(T), disabled: E === T, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}
const ln = /* @__PURE__ */ h.div.withConfig({
  displayName: "HeaderActions",
  componentId: "sc-1l7c7gv-0"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), qn = ({
  title: e,
  children: r,
  isLoading: o = !1,
  hasError: t = !1,
  errorMessage: a = "An error occurred while loading data",
  showRetry: c = !0,
  onRetry: i,
  isEmpty: d = !1,
  emptyMessage: l = "No data available",
  emptyActionText: u,
  onEmptyAction: f,
  actionButton: x,
  className: v,
  ...y
}) => {
  const S = /* @__PURE__ */ s.jsx(ln, { children: x });
  let E;
  return o ? E = /* @__PURE__ */ s.jsx(Ut, { variant: "card", text: "Loading data..." }) : t ? E = /* @__PURE__ */ s.jsx(br, { title: "Error", description: a, variant: "compact", actionText: c ? "Retry" : void 0, onAction: c ? i : void 0 }) : d ? E = /* @__PURE__ */ s.jsx(br, { title: "No Data", description: l, variant: "compact", actionText: u, onAction: f }) : E = r, /* @__PURE__ */ s.jsx(vo, { title: e, actions: S, className: v, ...y, children: E });
}, dn = /* @__PURE__ */ h.div.withConfig({
  displayName: "Container",
  componentId: "sc-djltr5-0"
})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:", ";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns ", " ease;"], ({
  sidebarCollapsed: e
}) => e ? "auto 1fr" : "240px 1fr", ({
  theme: e
}) => e.transitions.normal), un = /* @__PURE__ */ h.header.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-djltr5-1"
})(["grid-area:header;background-color:", ";border-bottom:1px solid ", ";padding:", ";z-index:", ";"], ({
  theme: e
}) => e.colors.headerBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.zIndex.fixed), fn = /* @__PURE__ */ h.aside.withConfig({
  displayName: "SidebarContainer",
  componentId: "sc-djltr5-2"
})(["grid-area:sidebar;background-color:", ";border-right:1px solid ", ";overflow-y:auto;transition:width ", " ease;width:", ";"], ({
  theme: e
}) => e.colors.sidebarBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.transitions.normal, ({
  collapsed: e
}) => e ? "60px" : "240px"), pn = /* @__PURE__ */ h.main.withConfig({
  displayName: "ContentContainer",
  componentId: "sc-djltr5-3"
})(["grid-area:content;overflow-y:auto;padding:", ";background-color:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.background), Un = ({
  header: e,
  sidebar: r,
  children: o,
  sidebarCollapsed: t = !1,
  // toggleSidebar, // Unused prop
  className: a
}) => /* @__PURE__ */ s.jsxs(dn, { sidebarCollapsed: t, className: a, children: [
  /* @__PURE__ */ s.jsx(un, { children: e }),
  /* @__PURE__ */ s.jsx(fn, { collapsed: t, children: r }),
  /* @__PURE__ */ s.jsx(pn, { children: o })
] });
function Wn(e, r = {}) {
  const {
    fetchOnMount: o = !0,
    dependencies: t = []
  } = r, [a, c] = O({
    data: null,
    isLoading: !1,
    error: null,
    isInitialized: !1
  }), i = V(async (...d) => {
    c((l) => ({
      ...l,
      isLoading: !0,
      error: null
    }));
    try {
      const l = await e(...d);
      return c({
        data: l,
        isLoading: !1,
        error: null,
        isInitialized: !0
      }), l;
    } catch (l) {
      const u = l instanceof Error ? l : new Error(String(l));
      throw c((f) => ({
        ...f,
        isLoading: !1,
        error: u,
        isInitialized: !0
      })), u;
    }
  }, [e]);
  return re(() => {
    o && i();
  }, [o, i, ...t]), {
    ...a,
    fetchData: i,
    refetch: () => i()
  };
}
function Hn(e, r) {
  const [o, t] = O(e);
  return re(() => {
    const a = setTimeout(() => {
      t(e);
    }, r);
    return () => {
      clearTimeout(a);
    };
  }, [e, r]), o;
}
function Yn(e = {}) {
  const {
    componentName: r,
    logToConsole: o = !0,
    reportToMonitoring: t = !0,
    onError: a
  } = e, [c, i] = O(null), [d, l] = O(!1), u = V((v) => {
    if (i(v), l(!0), o) {
      const y = r ? `[${r}]` : "";
      console.error(`Error caught by useErrorHandler${y}:`, v);
    }
    a && a(v);
  }, [r, o, t, a]), f = V(() => {
    i(null), l(!1);
  }, []), x = V(async (v) => {
    try {
      return await v();
    } catch (y) {
      u(y);
      return;
    }
  }, [u]);
  return re(() => () => {
    i(null), l(!1);
  }, []), {
    error: c,
    hasError: d,
    handleError: u,
    resetError: f,
    tryExecute: x
  };
}
function kr(e, r) {
  const o = () => {
    if (typeof window > "u")
      return r;
    try {
      const i = window.localStorage.getItem(e);
      return i ? JSON.parse(i) : r;
    } catch (i) {
      return console.warn(`Error reading localStorage key "${e}":`, i), r;
    }
  }, [t, a] = O(o), c = (i) => {
    try {
      const d = i instanceof Function ? i(t) : i;
      a(d), typeof window < "u" && window.localStorage.setItem(e, JSON.stringify(d));
    } catch (d) {
      console.warn(`Error setting localStorage key "${e}":`, d);
    }
  };
  return re(() => {
    const i = (d) => {
      d.key === e && d.newValue && a(JSON.parse(d.newValue));
    };
    return window.addEventListener("storage", i), () => window.removeEventListener("storage", i);
  }, [e]), [t, c];
}
function Gn(e) {
  const {
    totalItems: r,
    itemsPerPage: o = 10,
    initialPage: t = 1,
    persistKey: a
  } = e, [c, i] = a ? kr(`${a}_page`, t) : O(t), [d, l] = a ? kr(`${a}_itemsPerPage`, o) : O(o), u = ae(() => Math.max(1, Math.ceil(r / d)), [r, d]), f = ae(() => Math.min(Math.max(1, c), u), [c, u]);
  f !== c && i(f);
  const x = (f - 1) * d, v = Math.min(x + d - 1, r - 1), y = f > 1, S = f < u, E = ae(() => {
    const z = [];
    if (u <= 5)
      for (let A = 1; A <= u; A++)
        z.push(A);
    else {
      let A = Math.max(1, f - Math.floor(2.5));
      const N = Math.min(u, A + 5 - 1);
      N === u && (A = Math.max(1, N - 5 + 1));
      for (let T = A; T <= N; T++)
        z.push(T);
    }
    return z;
  }, [f, u]), P = V(() => {
    S && i(f + 1);
  }, [S, f, i]), w = V(() => {
    y && i(f - 1);
  }, [y, f, i]), I = V((_) => {
    const z = Math.min(Math.max(1, _), u);
    i(z);
  }, [u, i]), R = V((_) => {
    l(_), i(1);
  }, [l, i]);
  return {
    currentPage: f,
    itemsPerPage: d,
    totalPages: u,
    hasPreviousPage: y,
    hasNextPage: S,
    startIndex: x,
    endIndex: v,
    pageRange: E,
    nextPage: P,
    previousPage: w,
    goToPage: I,
    setItemsPerPage: R
  };
}
const b = {
  // F1 colors
  f1Red: "#e10600",
  f1RedDark: "#c10500",
  f1RedLight: "#ff3b36",
  f1Blue: "#1e5bc6",
  f1BlueDark: "#1a4da8",
  f1BlueLight: "#4a7dd8",
  // Neutrals
  white: "#ffffff",
  black: "#000000",
  gray50: "#f9fafb",
  gray100: "#f3f4f6",
  gray200: "#e5e7eb",
  gray300: "#d1d5db",
  gray400: "#9ca3af",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray700: "#374151",
  gray800: "#1f2937",
  gray900: "#111827",
  // Status colors
  green: "#4caf50",
  greenDark: "#388e3c",
  greenLight: "#81c784",
  yellow: "#ffeb3b",
  yellowDark: "#fbc02d",
  yellowLight: "#fff59d",
  red: "#f44336",
  redDark: "#d32f2f",
  redLight: "#e57373",
  blue: "#2196f3",
  blueDark: "#1976d2",
  blueLight: "#64b5f6",
  purple: "#9c27b0",
  purpleDark: "#7b1fa2",
  purpleLight: "#ba68c8",
  // Transparent colors
  whiteTransparent10: "rgba(255, 255, 255, 0.1)",
  blackTransparent10: "rgba(0, 0, 0, 0.1)"
}, M = {
  background: "#1a1f2c",
  surface: "#252a37",
  cardBackground: "#252a37",
  border: "#333333",
  divider: "rgba(255, 255, 255, 0.1)",
  textPrimary: "#ffffff",
  textSecondary: "#aaaaaa",
  textDisabled: "#666666",
  textInverse: "#1a1f2c",
  success: b.green,
  warning: b.yellow,
  error: b.red,
  info: b.blue,
  // Chart colors
  chartGrid: "rgba(255, 255, 255, 0.1)",
  chartLine: b.f1Red,
  // Trading specific colors
  profit: b.green,
  loss: b.red,
  neutral: b.gray400,
  // Component specific colors
  tooltipBackground: "rgba(37, 42, 55, 0.9)",
  modalBackground: "rgba(26, 31, 44, 0.8)"
}, H = {
  background: "#f5f5f5",
  surface: "#ffffff",
  cardBackground: "#ffffff",
  border: "#e0e0e0",
  divider: "rgba(0, 0, 0, 0.1)",
  textPrimary: "#333333",
  textSecondary: "#666666",
  textDisabled: "#999999",
  textInverse: "#ffffff",
  success: b.green,
  warning: b.yellow,
  error: b.red,
  info: b.blue,
  // Chart colors
  chartGrid: "rgba(0, 0, 0, 0.1)",
  chartLine: b.f1Red,
  // Trading specific colors
  profit: b.green,
  loss: b.red,
  neutral: b.gray400,
  // Component specific colors
  tooltipBackground: "rgba(255, 255, 255, 0.9)",
  modalBackground: "rgba(255, 255, 255, 0.8)"
}, Fe = {
  xxs: "4px",
  xs: "8px",
  sm: "12px",
  md: "16px",
  lg: "24px",
  xl: "32px",
  xxl: "48px"
}, ze = {
  xs: "0.75rem",
  sm: "0.875rem",
  md: "1rem",
  lg: "1.125rem",
  xl: "1.25rem",
  xxl: "1.5rem",
  h1: "2.5rem",
  h2: "2rem",
  h3: "1.75rem",
  h4: "1.5rem",
  h5: "1.25rem",
  h6: "1rem"
}, qe = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700
}, Ue = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75
}, We = {
  body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"
}, He = {
  xs: "480px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px"
}, Ye = {
  xs: "2px",
  sm: "4px",
  md: "6px",
  lg: "8px",
  xl: "12px",
  pill: "9999px",
  circle: "50%"
}, Ge = {
  sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
  md: "0 4px 6px rgba(0, 0, 0, 0.1)",
  lg: "0 10px 15px rgba(0, 0, 0, 0.1)"
}, Ve = {
  fast: "0.1s",
  normal: "0.3s",
  slow: "0.5s"
}, Je = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100
}, Dr = {
  name: "f1",
  colors: {
    // Primary colors
    primary: b.f1Red,
    primaryDark: b.f1RedDark,
    primaryLight: b.f1RedLight,
    // Secondary colors
    secondary: b.f1Blue,
    secondaryDark: b.f1BlueDark,
    secondaryLight: b.f1BlueLight,
    // Accent colors
    accent: b.purple,
    accentDark: b.purpleDark,
    accentLight: b.purpleLight,
    // Status colors
    success: M.success,
    warning: M.warning,
    error: M.error,
    info: M.info,
    // Neutral colors
    background: M.background,
    surface: M.surface,
    cardBackground: M.surface,
    border: M.border,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: M.textPrimary,
    textSecondary: M.textSecondary,
    textDisabled: M.textDisabled,
    textInverse: M.textInverse,
    // Chart colors
    chartGrid: M.chartGrid,
    chartLine: M.chartLine,
    chartAxis: b.gray400,
    chartTooltip: M.tooltipBackground,
    // Trading specific colors
    profit: M.profit,
    loss: M.loss,
    neutral: M.neutral,
    // Tab colors
    tabActive: b.f1Red,
    tabInactive: b.gray600,
    // Component specific colors
    tooltipBackground: M.tooltipBackground,
    modalBackground: M.modalBackground,
    sidebarBackground: b.gray800,
    headerBackground: "rgba(0, 0, 0, 0.2)"
  },
  spacing: Fe,
  breakpoints: He,
  fontSizes: ze,
  fontWeights: qe,
  lineHeights: Ue,
  fontFamilies: We,
  borderRadius: Ye,
  shadows: Ge,
  transitions: Ve,
  zIndex: Je
}, gn = {
  name: "light",
  colors: {
    // Primary colors
    primary: b.f1Red,
    primaryDark: b.f1RedDark,
    primaryLight: b.f1RedLight,
    // Secondary colors
    secondary: b.f1Blue,
    secondaryDark: b.f1BlueDark,
    secondaryLight: b.f1BlueLight,
    // Accent colors
    accent: b.purple,
    accentDark: b.purpleDark,
    accentLight: b.purpleLight,
    // Status colors
    success: H.success,
    warning: H.warning,
    error: H.error,
    info: H.info,
    // Neutral colors
    background: H.background,
    surface: H.surface,
    cardBackground: H.surface,
    border: H.border,
    divider: b.blackTransparent10,
    // Text colors
    textPrimary: H.textPrimary,
    textSecondary: H.textSecondary,
    textDisabled: H.textDisabled,
    textInverse: H.textInverse,
    // Chart colors
    chartGrid: H.chartGrid,
    chartLine: H.chartLine,
    chartAxis: b.gray600,
    chartTooltip: H.tooltipBackground,
    // Trading specific colors
    profit: H.profit,
    loss: H.loss,
    neutral: H.neutral,
    // Tab colors
    tabActive: b.f1Red,
    tabInactive: b.gray400,
    // Component specific colors
    tooltipBackground: H.tooltipBackground,
    modalBackground: H.modalBackground,
    sidebarBackground: b.white,
    headerBackground: "rgba(0, 0, 0, 0.05)"
  },
  spacing: Fe,
  breakpoints: He,
  fontSizes: ze,
  fontWeights: qe,
  lineHeights: Ue,
  fontFamilies: We,
  borderRadius: Ye,
  shadows: Ge,
  transitions: Ve,
  zIndex: Je
}, hn = {
  name: "dark",
  colors: {
    // Primary colors (using blue as primary instead of red to differentiate from F1 theme)
    primary: b.f1Blue,
    primaryDark: b.f1BlueDark,
    primaryLight: b.f1BlueLight,
    // Secondary colors
    secondary: b.f1Blue,
    secondaryDark: b.f1BlueDark,
    secondaryLight: b.f1BlueLight,
    // Accent colors
    accent: b.purple,
    accentDark: b.purpleDark,
    accentLight: b.purpleLight,
    // Status colors
    success: M.success,
    warning: M.warning,
    error: M.error,
    info: M.info,
    // Neutral colors
    background: b.gray900,
    // Slightly different from F1 theme
    surface: b.gray800,
    cardBackground: b.gray800,
    border: b.gray700,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: b.white,
    textSecondary: b.gray300,
    textDisabled: b.gray500,
    textInverse: b.gray900,
    // Chart colors
    chartGrid: M.chartGrid,
    chartLine: b.f1Blue,
    // Using blue instead of red
    chartAxis: b.gray400,
    chartTooltip: M.tooltipBackground,
    // Trading specific colors
    profit: M.profit,
    loss: M.loss,
    neutral: M.neutral,
    // Tab colors
    tabActive: b.f1Blue,
    // Using blue instead of red
    tabInactive: b.gray600,
    // Component specific colors
    tooltipBackground: "rgba(26, 32, 44, 0.9)",
    // Slightly different from F1 theme
    modalBackground: "rgba(26, 32, 44, 0.8)",
    sidebarBackground: b.gray900,
    headerBackground: "rgba(0, 0, 0, 0.3)"
  },
  spacing: Fe,
  breakpoints: He,
  fontSizes: ze,
  fontWeights: qe,
  lineHeights: Ue,
  fontFamilies: We,
  borderRadius: Ye,
  shadows: Ge,
  transitions: Ve,
  zIndex: Je
}, mn = /* @__PURE__ */ nt(["@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:", ";background-color:", ";color:", ";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:", ";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:", ";}::-webkit-scrollbar-thumb{background:", ";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:", ";}:focus{outline:2px solid ", ";outline-offset:2px;}::selection{background-color:", ";color:", ";}"], ({
  theme: e
}) => e.fontFamilies.body, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.textInverse), bn = mn, xn = {
  f1: Dr,
  light: gn,
  dark: hn
}, Ke = Dr, Le = (e) => xn[e] || Ke, Pr = Oe({
  theme: Ke,
  setTheme: () => {
  }
}), Vn = () => Ae(Pr), Jn = ({
  initialTheme: e = Ke,
  persistTheme: r = !0,
  storageKey: o = "adhd-dashboard-theme",
  children: t
}) => {
  const [a, c] = O(() => {
    if (r && typeof window < "u") {
      const l = window.localStorage.getItem(o);
      if (l)
        try {
          const u = Le(l);
          return u || JSON.parse(l);
        } catch (u) {
          console.error("Failed to parse stored theme:", u);
        }
    }
    return typeof e == "string" ? Le(e) : e;
  }), i = (l) => {
    const u = typeof l == "string" ? Le(l) : l;
    c(u), r && typeof window < "u" && window.localStorage.setItem(o, u.name || JSON.stringify(u));
  }, d = ({
    children: l
  }) => /* @__PURE__ */ s.jsxs(at, { theme: a, children: [
    /* @__PURE__ */ s.jsx(bn, {}),
    l
  ] });
  return /* @__PURE__ */ s.jsx(Pr.Provider, { value: {
    theme: a,
    setTheme: i
  }, children: /* @__PURE__ */ s.jsx(d, { children: t }) });
};
function Kn(e, r, o = "StoreContext") {
  const t = Oe(void 0);
  t.displayName = o;
  const a = ({
    children: u,
    initialState: f
  }) => {
    const [x, v] = ot(e, f || r), y = ae(() => ({
      state: x,
      dispatch: v
    }), [x]);
    return /* @__PURE__ */ s.jsx(t.Provider, { value: y, children: u });
  };
  function c() {
    const u = Ae(t);
    if (u === void 0)
      throw new Error(`use${o} must be used within a ${o}Provider`);
    return u;
  }
  function i(u) {
    const {
      state: f
    } = c();
    return u(f);
  }
  function d(u) {
    const {
      dispatch: f
    } = c();
    return ae(() => (...x) => {
      f(u(...x));
    }, [f, u]);
  }
  function l(u) {
    const {
      dispatch: f
    } = c();
    return ae(() => {
      const x = {};
      for (const v in u)
        x[v] = (...y) => {
          f(u[v](...y));
        };
      return x;
    }, [f, u]);
  }
  return {
    Context: t,
    Provider: a,
    useStore: c,
    useSelector: i,
    useAction: d,
    useActions: l
  };
}
function Xn(...e) {
  const r = e.pop(), o = e;
  let t = null, a = null;
  return (c) => {
    const i = o.map((d) => d(c));
    return (t === null || i.length !== t.length || i.some((d, l) => d !== t[l])) && (a = r(...i), t = i), a;
  };
}
function Qn(e, r) {
  const {
    key: o,
    initialState: t,
    version: a = 1,
    migrate: c,
    serialize: i = JSON.stringify,
    deserialize: d = JSON.parse,
    filter: l = (w) => w,
    merge: u = (w, I) => ({
      ...I,
      ...w
    }),
    debug: f = !1
  } = r, x = () => {
    try {
      const w = localStorage.getItem(o);
      if (w === null)
        return null;
      const {
        state: I,
        version: R
      } = d(w);
      return R !== a && c ? (f && console.log(`Migrating state from version ${R} to ${a}`), c(I, R)) : I;
    } catch (w) {
      return f && console.error("Error loading state from local storage:", w), null;
    }
  }, v = (w) => {
    try {
      const I = l(w), R = i({
        state: I,
        version: a
      });
      localStorage.setItem(o, R);
    } catch (I) {
      f && console.error("Error saving state to local storage:", I);
    }
  }, y = () => {
    try {
      localStorage.removeItem(o);
    } catch (w) {
      f && console.error("Error clearing state from local storage:", w);
    }
  }, S = x(), E = S ? u(S, t) : t;
  return f && S && (console.log("Loaded persisted state:", S), console.log("Merged initial state:", E)), {
    reducer: (w, I) => {
      const R = e(w, I);
      return v(R), R;
    },
    initialState: E,
    clear: y
  };
}
function Zn(e, r = "$") {
  return `${r}${e.toFixed(2)}`;
}
function ea(e, r = 1) {
  return `${(e * 100).toFixed(r)}%`;
}
function ra(e, r = "short") {
  const o = typeof e == "string" ? new Date(e) : e;
  switch (r) {
    case "medium":
      return o.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    case "long":
      return o.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    case "short":
    default:
      return o.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      });
  }
}
function ta(e, r = 50) {
  return e.length <= r ? e : `${e.substring(0, r - 3)}...`;
}
function oa() {
  return Math.random().toString(36).substring(2, 9);
}
function na(e, r) {
  let o = null;
  return function(...t) {
    const a = () => {
      o = null, e(...t);
    };
    o && clearTimeout(o), o = setTimeout(a, r);
  };
}
function aa(e, r) {
  let o = !1;
  return function(...t) {
    o || (e(...t), o = !0, setTimeout(() => {
      o = !1;
    }, r));
  };
}
export {
  xt as ApiClient,
  jr as ApiContext,
  jn as ApiProvider,
  On as AppErrorBoundary,
  $n as Badge,
  de as Button,
  vo as Card,
  Un as DashboardTemplate,
  qn as DataCard,
  br as EmptyState,
  Bo as ErrorBoundary,
  An as FeatureErrorBoundary,
  _n as FormField,
  Bn as Input,
  Ut as LoadingPlaceholder,
  Fn as Modal,
  Nn as Select,
  Mn as StatusIndicator,
  zn as Table,
  Ln as Tag,
  Pr as ThemeContext,
  Jn as ThemeProvider,
  Er as TradingApiClient,
  Rr as UnifiedErrorBoundary,
  Pn as apiClient,
  b as baseColors,
  Ye as borderRadius,
  He as breakpoints,
  Sr as captureError,
  Xn as createSelector,
  Kn as createStoreContext,
  M as darkModeColors,
  hn as darkTheme,
  na as debounce,
  Dr as f1Theme,
  We as fontFamilies,
  ze as fontSizes,
  qe as fontWeights,
  Zn as formatCurrency,
  ra as formatDate,
  ea as formatPercentage,
  it as gasApi,
  oa as generateId,
  Cn as initMonitoring,
  H as lightModeColors,
  gn as lightTheme,
  Ue as lineHeights,
  ct as mockApi,
  Qn as persistState,
  Sn as setUser,
  Ge as shadows,
  Fe as spacing,
  En as startTransaction,
  aa as throttle,
  Ve as transitions,
  ta as truncateText,
  lt as useApi,
  dt as useApiMutation,
  Ee as useApiQuery,
  Wn as useAsyncData,
  kn as useDashboardData,
  Hn as useDebounce,
  Yn as useErrorHandler,
  kr as useLocalStorage,
  Rn as useMarketNews,
  Gn as usePagination,
  Tn as usePerformanceMetrics,
  Vn as useTheme,
  In as useTradingData,
  Dn as useUserPreferences,
  Je as zIndex
};
