/**
 * Unified Error Boundary
 *
 * A unified error boundary component that can be used at both the application level
 * and feature level, replacing the previous three-layer architecture.
 */
import React from 'react';
import { ErrorBoundaryProps } from './ErrorBoundary';
export interface UnifiedErrorBoundaryProps extends Omit<ErrorBoundaryProps, 'isFeatureBoundary'> {
    /** Whether this is an application-level boundary */
    isAppLevel?: boolean;
    /** Whether this is a feature-level boundary */
    isFeatureBoundary?: boolean;
}
/**
 * Unified Error Boundary
 *
 * A wrapper around the base ErrorBoundary component that provides a simpler API
 * for common use cases.
 */
export declare const UnifiedErrorBoundary: React.FC<UnifiedErrorBoundaryProps>;
/**
 * App Error Boundary
 *
 * A specialized error boundary for the application level.
 */
export declare const AppErrorBoundary: React.FC<Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'>>;
/**
 * Feature Error Boundary
 *
 * A specialized error boundary for feature modules.
 */
export declare const FeatureErrorBoundary: React.FC<Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'> & {
    featureName: string;
}>;
export default UnifiedErrorBoundary;
//# sourceMappingURL=UnifiedErrorBoundary.d.ts.map