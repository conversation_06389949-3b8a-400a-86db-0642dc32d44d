/**
 * Monitoring Service
 *
 * A service for monitoring errors and performance in the application.
 * This is a placeholder for integration with services like Sentry.
 */
/**
 * Error context
 */
export interface ErrorContext {
    /** The name of the component or feature where the error occurred */
    source?: string;
    /** The type of boundary that caught the error */
    boundaryType?: 'app' | 'feature' | 'component';
    /** Additional tags for the error */
    tags?: Record<string, string>;
    /** Additional data for the error */
    data?: Record<string, unknown>;
    /** User information */
    user?: {
        id?: string;
        username?: string;
        email?: string;
    };
}
/**
 * Initialize the monitoring service
 * @param options - Options for the monitoring service
 */
export declare function initMonitoring(options?: {
    dsn?: string;
    environment?: string;
    release?: string;
    debug?: boolean;
}): void;
/**
 * Capture an error
 * @param error - The error to capture
 * @param context - Additional context for the error
 */
export declare function captureError(error: Error, context?: ErrorContext): void;
/**
 * Set user information for the monitoring service
 * @param user - User information
 */
export declare function setUser(user: {
    id?: string;
    username?: string;
    email?: string;
}): void;
/**
 * Start a performance transaction
 * @param name - The name of the transaction
 * @param options - Options for the transaction
 * @returns A transaction object
 */
export declare function startTransaction(name: string, options?: {
    op?: string;
    tags?: Record<string, string>;
}): {
    name: string;
    startTime: number;
    finish: () => void;
};
declare const _default: {
    initMonitoring: typeof initMonitoring;
    captureError: typeof captureError;
    setUser: typeof setUser;
    startTransaction: typeof startTransaction;
};
export default _default;
//# sourceMappingURL=index.d.ts.map