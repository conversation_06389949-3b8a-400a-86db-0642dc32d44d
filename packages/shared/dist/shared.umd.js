(function(m,h){typeof exports=="object"&&typeof module<"u"?h(exports,require("react"),require("styled-components"),require("react-dom")):typeof define=="function"&&define.amd?define(["exports","react","styled-components","react-dom"],h):(m=typeof globalThis<"u"?globalThis:m||self,h(m.ADHDTradingDashboardShared={},m.React,m.styled,m.ReactDOM))})(this,function(m,h,a,Nr){"use strict";function se(e,...r){return new Promise((o,t)=>{if(!window.google||!window.google.script||!window.google.script.run){t(new Error("Not in a Google Apps Script environment"));return}const c=window.google.script.run.withSuccessHandler(d=>{o(d)}).withFailureHandler(d=>{console.error(`Error calling ${e}:`,d),t(d)});typeof c[e]=="function"?c[e].apply(c,r):t(new Error(`Function ${e} not found on server`))})}const Xe={getConfiguration(){return se("getConfiguration")},getDashboardData(){return se("getDashboardData")},getTradingData(e){return se("getTradingData",e)},getPerformanceMetrics(e){return se("getPerformanceMetrics",e)},getMarketNews(e){return se("getMarketNews",e)},saveUserPreferences(e){return se("saveUserPreferences",e)},getUserPreferences(){return se("getUserPreferences")},closeDialog(){window.google&&window.google.script&&window.google.script.host&&window.google.script.host.close()}},Qe={getConfiguration:async function(){return console.log("Mock: getConfiguration called"),{success:!0,theme:{colors:{primary:"#e10600",background:"#1a1f2c",cardBackground:"#252a37",text:"#ffffff",secondaryText:"#aaaaaa",positive:"#4caf50",negative:"#f44336",neutral:"#9e9e9e",border:"#333333",tabActive:"#e10600",tabInactive:"#555555",chartGrid:"#333333",chartLine:"#e10600",tooltipBackground:"rgba(37, 42, 55, 0.9)"}},features:{performanceChart:!0,newsEvents:!0,recentTrades:!0}}},getDashboardData:async function(){return console.log("Mock: getDashboardData called"),{success:!0,summary:{totalTrades:120,winRate:.65,profitFactor:2.3,netProfit:12500},recentTrades:[{id:1,date:"2025-01-15",symbol:"AAPL",result:"win",profit:350},{id:2,date:"2025-01-14",symbol:"MSFT",result:"loss",profit:-150},{id:3,date:"2025-01-13",symbol:"GOOGL",result:"win",profit:420}],performance:{daily:[{date:"2025-01-11",value:10200},{date:"2025-01-12",value:10500},{date:"2025-01-13",value:11200},{date:"2025-01-14",value:11e3},{date:"2025-01-15",value:12500}]}}},getTradingData:async function(e){return console.log("Mock: getTradingData called with options:",e),{success:!0,data:[{date:"2025-01-15",symbol:"AAPL",action:"BUY",price:185.25,quantity:10,profit:350},{date:"2025-01-14",symbol:"MSFT",action:"SELL",price:390.12,quantity:5,profit:-150},{date:"2025-01-13",symbol:"GOOGL",action:"BUY",price:142.75,quantity:8,profit:420}]}},getPerformanceMetrics:async function(e){return console.log("Mock: getPerformanceMetrics called with options:",e),{success:!0,metrics:{"Win Rate":.65,"Profit Factor":2.3,"Net Profit":12500,"Average Win":350,"Average Loss":-180},chartData:[{date:"2025-01-11",value:10200},{date:"2025-01-12",value:10500},{date:"2025-01-13",value:11200},{date:"2025-01-14",value:11e3},{date:"2025-01-15",value:12500}]}},getMarketNews:async function(e){return console.log("Mock: getMarketNews called with options:",e),{success:!0,news:[{title:"Market Update: Stocks Rally on Fed Decision",date:"2025-01-15",source:"Financial Times"},{title:"Tech Stocks Lead Market Higher",date:"2025-01-14",source:"Wall Street Journal"},{title:"Economic Data Shows Strong Growth",date:"2025-01-13",source:"Bloomberg"}]}},saveUserPreferences:async function(e){return console.log("Mock: saveUserPreferences called with preferences:",e),{success:!0}},getUserPreferences:async function(){return console.log("Mock: getUserPreferences called"),{success:!0,preferences:{theme:"f1",refreshInterval:5,showNotifications:!0}}},closeDialog:function(){console.log("Mock: closeDialog called")}};function Ze(e){const[r,o]=h.useState(null),[t,s]=h.useState(!1),[l,c]=h.useState(null),d=h.useCallback(async(...f)=>{try{s(!0),c(null);const p=await e(...f);if(!p.success&&p.error)throw new Error(p.error);return o(p),p}catch(p){const v=p instanceof Error?p.message:"An unknown error occurred";throw c(v),p}finally{s(!1)}},[e]),u=h.useCallback(()=>{o(null),s(!1),c(null)},[]);return{data:r,isLoading:t,error:l,execute:d,reset:u}}function Lr(){return Ze(Zr.getDashboardData)}function le(e,r={}){const{enabled:o=!0,params:t,refetchOnWindowFocus:s=!0,refetchOnReconnect:l=!0,refetchInterval:c,retry:d=3,retryDelay:u=1e3,onSuccess:f,onError:p,onSettled:v,initialData:w,keepPreviousData:y=!1,select:E,staleTime:j=0}=r,[$,k]=h.useState(w),[T,P]=h.useState(null),[F,q]=h.useState(o),[A,N]=h.useState(o),[D,C]=h.useState(!1),[X,Y]=h.useState(!1),[S,U]=h.useState(0),G=h.useRef(e),ee=h.useRef(t),re=h.useRef(0),J=h.useRef(null),ne=h.useRef(!0);h.useEffect(()=>{G.current=e,ee.current=t},[e,t]),h.useEffect(()=>()=>{ne.current=!1,J.current&&clearInterval(J.current)},[]);const Q=h.useCallback(async()=>{if(!ne.current)return $;N(!0),!y&&!$&&q(!0);try{const _=await G.current(ee.current);if(!ne.current)return _;if(_&&typeof _=="object"&&"success"in _){const ae=_;if(!ae.success&&ae.error)throw new Error(ae.error)}const K=E?E(_):_;return k(K),C(!0),Y(!1),P(null),U(Date.now()),f&&f(K),v&&v(K,null),re.current=0,K}catch(_){if(!ne.current)throw _;const K=_ instanceof Error?_:new Error(String(_));if(Y(!0),P(K),p&&p(K),v&&v(void 0,K),re.current<d){re.current++;const ae=typeof u=="function"?u(re.current):u;return await new Promise(qe=>setTimeout(qe,ae)),Q()}throw K}finally{ne.current&&(q(!1),N(!1))}},[$,y,p,v,f,d,u,E]),Ee=h.useCallback(async()=>Q(),[Q]);return h.useEffect(()=>o?((!S||Date.now()-S>j||$===void 0)&&Q().catch(()=>{}),c&&(J.current=setInterval(()=>{Q().catch(()=>{})},c)),()=>{J.current&&clearInterval(J.current)}):void 0,[o,Q,c,$,S,j]),h.useEffect(()=>{if(!s)return;const _=()=>{(!S||Date.now()-S>j)&&o&&Q().catch(()=>{})};return window.addEventListener("focus",_),()=>{window.removeEventListener("focus",_)}},[s,Q,o,S,j]),h.useEffect(()=>{if(!l)return;const _=()=>{(!S||Date.now()-S>j)&&o&&Q().catch(()=>{})};return window.addEventListener("online",_),()=>{window.removeEventListener("online",_)}},[l,Q,o,S,j]),{data:$,isLoading:F,isFetching:A,isSuccess:D,isError:X,error:T,refetch:Ee}}function er(e,r={}){const{onSuccess:o,onError:t,onSettled:s,onMutate:l,retry:c=0,retryDelay:d=1e3}=r,[u,f]=h.useState(void 0),[p,v]=h.useState(null),[w,y]=h.useState(!1),[E,j]=h.useState(!1),[$,k]=h.useState(!1),T=h.useRef(e),P=h.useRef(0),F=h.useRef(!0);h.useState(()=>{T.current=e}),h.useState(()=>()=>{F.current=!1});const q=h.useCallback(()=>{f(void 0),v(null),y(!1),j(!1),k(!1),P.current=0},[]),A=h.useCallback(async N=>{if(!F.current)return u;y(!0),j(!1),k(!1),v(null),l&&await l(N);try{const D=await T.current(N);if(!F.current)return D;if(D&&typeof D=="object"&&"success"in D){const C=D;if(!C.success&&C.error)throw new Error(C.error)}return f(D),j(!0),o&&o(D,N),s&&s(D,null,N),P.current=0,D}catch(D){if(!F.current)throw D;const C=D instanceof Error?D:new Error(String(D));if(k(!0),v(C),t&&t(C,N),s&&s(void 0,C,N),P.current<c){P.current++;const X=typeof d=="function"?d(P.current):d;return await new Promise(Y=>setTimeout(Y,X)),A(N)}throw C}finally{F.current&&y(!1)}},[u,t,l,s,o,c,d]);return{mutate:A,reset:q,data:u,isLoading:w,isSuccess:E,isError:$,error:p}}var i={},Or={get exports(){return i},set exports(e){i=e}},ue={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rr;function Rr(){if(rr)return ue;rr=1;var e=h,r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),t=Object.prototype.hasOwnProperty,s=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(d,u,f){var p,v={},w=null,y=null;f!==void 0&&(w=""+f),u.key!==void 0&&(w=""+u.key),u.ref!==void 0&&(y=u.ref);for(p in u)t.call(u,p)&&!l.hasOwnProperty(p)&&(v[p]=u[p]);if(d&&d.defaultProps)for(p in u=d.defaultProps,u)v[p]===void 0&&(v[p]=u[p]);return{$$typeof:r,type:d,key:w,ref:y,props:v,_owner:s.current}}return ue.Fragment=o,ue.jsx=c,ue.jsxs=c,ue}var de={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tr;function Ar(){return tr||(tr=1,process.env.NODE_ENV!=="production"&&function(){var e=h,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),t=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),d=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),v=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),y=Symbol.for("react.offscreen"),E=Symbol.iterator,j="@@iterator";function $(n){if(n===null||typeof n!="object")return null;var g=E&&n[E]||n[j];return typeof g=="function"?g:null}var k=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function T(n){{for(var g=arguments.length,b=new Array(g>1?g-1:0),I=1;I<g;I++)b[I-1]=arguments[I];P("error",n,b)}}function P(n,g,b){{var I=k.ReactDebugCurrentFrame,L=I.getStackAddendum();L!==""&&(g+="%s",b=b.concat([L]));var R=b.map(function(B){return String(B)});R.unshift("Warning: "+g),Function.prototype.apply.call(console[n],console,R)}}var F=!1,q=!1,A=!1,N=!1,D=!1,C;C=Symbol.for("react.module.reference");function X(n){return!!(typeof n=="string"||typeof n=="function"||n===t||n===l||D||n===s||n===f||n===p||N||n===y||F||q||A||typeof n=="object"&&n!==null&&(n.$$typeof===w||n.$$typeof===v||n.$$typeof===c||n.$$typeof===d||n.$$typeof===u||n.$$typeof===C||n.getModuleId!==void 0))}function Y(n,g,b){var I=n.displayName;if(I)return I;var L=g.displayName||g.name||"";return L!==""?b+"("+L+")":b}function S(n){return n.displayName||"Context"}function U(n){if(n==null)return null;if(typeof n.tag=="number"&&T("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n;switch(n){case t:return"Fragment";case o:return"Portal";case l:return"Profiler";case s:return"StrictMode";case f:return"Suspense";case p:return"SuspenseList"}if(typeof n=="object")switch(n.$$typeof){case d:var g=n;return S(g)+".Consumer";case c:var b=n;return S(b._context)+".Provider";case u:return Y(n,n.render,"ForwardRef");case v:var I=n.displayName||null;return I!==null?I:U(n.type)||"Memo";case w:{var L=n,R=L._payload,B=L._init;try{return U(B(R))}catch{return null}}}return null}var G=Object.assign,ee=0,re,J,ne,Q,Ee,_,K;function ae(){}ae.__reactDisabledLog=!0;function qe(){{if(ee===0){re=console.log,J=console.info,ne=console.warn,Q=console.error,Ee=console.group,_=console.groupCollapsed,K=console.groupEnd;var n={configurable:!0,enumerable:!0,value:ae,writable:!0};Object.defineProperties(console,{info:n,log:n,warn:n,error:n,group:n,groupCollapsed:n,groupEnd:n})}ee++}}function yn(){{if(ee--,ee===0){var n={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:G({},n,{value:re}),info:G({},n,{value:J}),warn:G({},n,{value:ne}),error:G({},n,{value:Q}),group:G({},n,{value:Ee}),groupCollapsed:G({},n,{value:_}),groupEnd:G({},n,{value:K})})}ee<0&&T("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Ue=k.ReactCurrentDispatcher,We;function je(n,g,b){{if(We===void 0)try{throw Error()}catch(L){var I=L.stack.trim().match(/\n( *(at )?)/);We=I&&I[1]||""}return`
`+We+n}}var He=!1,Ie;{var wn=typeof WeakMap=="function"?WeakMap:Map;Ie=new wn}function yr(n,g){if(!n||He)return"";{var b=Ie.get(n);if(b!==void 0)return b}var I;He=!0;var L=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var R;R=Ue.current,Ue.current=null,qe();try{if(g){var B=function(){throw Error()};if(Object.defineProperty(B.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(B,[])}catch(te){I=te}Reflect.construct(n,[],B)}else{try{B.call()}catch(te){I=te}n.call(B.prototype)}}else{try{throw Error()}catch(te){I=te}n()}}catch(te){if(te&&I&&typeof te.stack=="string"){for(var M=te.stack.split(`
`),V=I.stack.split(`
`),W=M.length-1,H=V.length-1;W>=1&&H>=0&&M[W]!==V[H];)H--;for(;W>=1&&H>=0;W--,H--)if(M[W]!==V[H]){if(W!==1||H!==1)do if(W--,H--,H<0||M[W]!==V[H]){var Z=`
`+M[W].replace(" at new "," at ");return n.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",n.displayName)),typeof n=="function"&&Ie.set(n,Z),Z}while(W>=1&&H>=0);break}}}finally{He=!1,Ue.current=R,yn(),Error.prepareStackTrace=L}var ce=n?n.displayName||n.name:"",Br=ce?je(ce):"";return typeof n=="function"&&Ie.set(n,Br),Br}function kn(n,g,b){return yr(n,!1)}function Cn(n){var g=n.prototype;return!!(g&&g.isReactComponent)}function Te(n,g,b){if(n==null)return"";if(typeof n=="function")return yr(n,Cn(n));if(typeof n=="string")return je(n);switch(n){case f:return je("Suspense");case p:return je("SuspenseList")}if(typeof n=="object")switch(n.$$typeof){case u:return kn(n.render);case v:return Te(n.type,g,b);case w:{var I=n,L=I._payload,R=I._init;try{return Te(R(L),g,b)}catch{}}}return""}var De=Object.prototype.hasOwnProperty,wr={},kr=k.ReactDebugCurrentFrame;function Pe(n){if(n){var g=n._owner,b=Te(n.type,n._source,g?g.type:null);kr.setExtraStackFrame(b)}else kr.setExtraStackFrame(null)}function Sn(n,g,b,I,L){{var R=Function.call.bind(De);for(var B in n)if(R(n,B)){var M=void 0;try{if(typeof n[B]!="function"){var V=Error((I||"React class")+": "+b+" type `"+B+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof n[B]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw V.name="Invariant Violation",V}M=n[B](g,B,I,b,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(W){M=W}M&&!(M instanceof Error)&&(Pe(L),T("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",I||"React class",b,B,typeof M),Pe(null)),M instanceof Error&&!(M.message in wr)&&(wr[M.message]=!0,Pe(L),T("Failed %s type: %s",b,M.message),Pe(null))}}}var En=Array.isArray;function Ye(n){return En(n)}function jn(n){{var g=typeof Symbol=="function"&&Symbol.toStringTag,b=g&&n[Symbol.toStringTag]||n.constructor.name||"Object";return b}}function In(n){try{return Cr(n),!1}catch{return!0}}function Cr(n){return""+n}function Sr(n){if(In(n))return T("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",jn(n)),Cr(n)}var fe=k.ReactCurrentOwner,Tn={key:!0,ref:!0,__self:!0,__source:!0},Er,jr,Ge;Ge={};function Dn(n){if(De.call(n,"ref")){var g=Object.getOwnPropertyDescriptor(n,"ref").get;if(g&&g.isReactWarning)return!1}return n.ref!==void 0}function Pn(n){if(De.call(n,"key")){var g=Object.getOwnPropertyDescriptor(n,"key").get;if(g&&g.isReactWarning)return!1}return n.key!==void 0}function Mn(n,g){if(typeof n.ref=="string"&&fe.current&&g&&fe.current.stateNode!==g){var b=U(fe.current.type);Ge[b]||(T('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',U(fe.current.type),n.ref),Ge[b]=!0)}}function $n(n,g){{var b=function(){Er||(Er=!0,T("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",g))};b.isReactWarning=!0,Object.defineProperty(n,"key",{get:b,configurable:!0})}}function Bn(n,g){{var b=function(){jr||(jr=!0,T("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",g))};b.isReactWarning=!0,Object.defineProperty(n,"ref",{get:b,configurable:!0})}}var Nn=function(n,g,b,I,L,R,B){var M={$$typeof:r,type:n,key:g,ref:b,props:B,_owner:R};return M._store={},Object.defineProperty(M._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(M,"_self",{configurable:!1,enumerable:!1,writable:!1,value:I}),Object.defineProperty(M,"_source",{configurable:!1,enumerable:!1,writable:!1,value:L}),Object.freeze&&(Object.freeze(M.props),Object.freeze(M)),M};function Ln(n,g,b,I,L){{var R,B={},M=null,V=null;b!==void 0&&(Sr(b),M=""+b),Pn(g)&&(Sr(g.key),M=""+g.key),Dn(g)&&(V=g.ref,Mn(g,L));for(R in g)De.call(g,R)&&!Tn.hasOwnProperty(R)&&(B[R]=g[R]);if(n&&n.defaultProps){var W=n.defaultProps;for(R in W)B[R]===void 0&&(B[R]=W[R])}if(M||V){var H=typeof n=="function"?n.displayName||n.name||"Unknown":n;M&&$n(B,H),V&&Bn(B,H)}return Nn(n,M,V,L,I,fe.current,B)}}var Ve=k.ReactCurrentOwner,Ir=k.ReactDebugCurrentFrame;function ie(n){if(n){var g=n._owner,b=Te(n.type,n._source,g?g.type:null);Ir.setExtraStackFrame(b)}else Ir.setExtraStackFrame(null)}var Je;Je=!1;function Ke(n){return typeof n=="object"&&n!==null&&n.$$typeof===r}function Tr(){{if(Ve.current){var n=U(Ve.current.type);if(n)return`

Check the render method of \``+n+"`."}return""}}function On(n){{if(n!==void 0){var g=n.fileName.replace(/^.*[\\\/]/,""),b=n.lineNumber;return`

Check your code at `+g+":"+b+"."}return""}}var Dr={};function Rn(n){{var g=Tr();if(!g){var b=typeof n=="string"?n:n.displayName||n.name;b&&(g=`

Check the top-level render call using <`+b+">.")}return g}}function Pr(n,g){{if(!n._store||n._store.validated||n.key!=null)return;n._store.validated=!0;var b=Rn(g);if(Dr[b])return;Dr[b]=!0;var I="";n&&n._owner&&n._owner!==Ve.current&&(I=" It was passed a child from "+U(n._owner.type)+"."),ie(n),T('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',b,I),ie(null)}}function Mr(n,g){{if(typeof n!="object")return;if(Ye(n))for(var b=0;b<n.length;b++){var I=n[b];Ke(I)&&Pr(I,g)}else if(Ke(n))n._store&&(n._store.validated=!0);else if(n){var L=$(n);if(typeof L=="function"&&L!==n.entries)for(var R=L.call(n),B;!(B=R.next()).done;)Ke(B.value)&&Pr(B.value,g)}}}function An(n){{var g=n.type;if(g==null||typeof g=="string")return;var b;if(typeof g=="function")b=g.propTypes;else if(typeof g=="object"&&(g.$$typeof===u||g.$$typeof===v))b=g.propTypes;else return;if(b){var I=U(g);Sn(b,n.props,"prop",I,n)}else if(g.PropTypes!==void 0&&!Je){Je=!0;var L=U(g);T("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",L||"Unknown")}typeof g.getDefaultProps=="function"&&!g.getDefaultProps.isReactClassApproved&&T("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function Fn(n){{for(var g=Object.keys(n.props),b=0;b<g.length;b++){var I=g[b];if(I!=="children"&&I!=="key"){ie(n),T("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",I),ie(null);break}}n.ref!==null&&(ie(n),T("Invalid attribute `ref` supplied to `React.Fragment`."),ie(null))}}function $r(n,g,b,I,L,R){{var B=X(n);if(!B){var M="";(n===void 0||typeof n=="object"&&n!==null&&Object.keys(n).length===0)&&(M+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var V=On(L);V?M+=V:M+=Tr();var W;n===null?W="null":Ye(n)?W="array":n!==void 0&&n.$$typeof===r?(W="<"+(U(n.type)||"Unknown")+" />",M=" Did you accidentally export a JSX literal instead of a component?"):W=typeof n,T("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",W,M)}var H=Ln(n,g,b,L,R);if(H==null)return H;if(B){var Z=g.children;if(Z!==void 0)if(I)if(Ye(Z)){for(var ce=0;ce<Z.length;ce++)Mr(Z[ce],n);Object.freeze&&Object.freeze(Z)}else T("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Mr(Z,n)}return n===t?Fn(H):An(H),H}}function _n(n,g,b){return $r(n,g,b,!0)}function zn(n,g,b){return $r(n,g,b,!1)}var qn=zn,Un=_n;de.Fragment=t,de.jsx=qn,de.jsxs=Un}()),de}(function(e){process.env.NODE_ENV==="production"?e.exports=Rr():e.exports=Ar()})(Or);class or{constructor(r={}){this.state="CLOSED",this.failureCount=0,this.successCount=0,this.nextAttempt=Date.now(),this.options={failureThreshold:r.failureThreshold||5,resetTimeout:r.resetTimeout||3e4,successThreshold:r.successThreshold||2,requestTimeout:r.requestTimeout||1e4,logging:r.logging!==void 0?r.logging:!0}}async execute(r){if(this.state==="OPEN"){if(Date.now()<this.nextAttempt)throw this.log(`Circuit is OPEN. Next attempt at ${new Date(this.nextAttempt).toISOString()}`),new Error("Circuit is open");this.log("Circuit is switching to HALF_OPEN"),this.state="HALF_OPEN"}try{const o=await this.executeWithTimeout(r);return this.handleSuccess(),o}catch(o){throw this.handleFailure(o),o}}async executeWithTimeout(r){return this.options.requestTimeout?Promise.race([r(),new Promise((o,t)=>{setTimeout(()=>{t(new Error(`Request timed out after ${this.options.requestTimeout}ms`))},this.options.requestTimeout)})]):r()}handleSuccess(){this.state==="HALF_OPEN"?(this.successCount++,this.successCount>=this.options.successThreshold&&(this.log("Circuit is switching to CLOSED"),this.state="CLOSED",this.failureCount=0,this.successCount=0)):this.failureCount=0}handleFailure(r){this.state==="HALF_OPEN"?(this.log(`Request failed in HALF_OPEN state: ${r.message}`),this.state="OPEN",this.nextAttempt=Date.now()+this.options.resetTimeout,this.successCount=0):this.state==="CLOSED"&&(this.failureCount++,this.log(`Failure count: ${this.failureCount}/${this.options.failureThreshold}`),this.failureCount>=this.options.failureThreshold&&(this.log("Circuit is switching to OPEN"),this.state="OPEN",this.nextAttempt=Date.now()+this.options.resetTimeout))}log(r){this.options.logging&&console.log(`[CircuitBreaker] ${r}`)}getState(){return this.state}reset(){this.state="CLOSED",this.failureCount=0,this.successCount=0,this.nextAttempt=Date.now(),this.log("Circuit has been reset")}}function Fr(e={}){console.log("Monitoring service initialized",e)}function Me(e,r){console.error("Error captured by monitoring service:",e,r)}function _r(e){console.log("User set for monitoring service:",e)}function zr(e,r){const o=performance.now();return{name:e,startTime:o,finish:()=>{const s=performance.now()-o;console.log(`Transaction "${e}" finished in ${s.toFixed(2)}ms`,r)}}}function qr(e,r={},o){return o.has(e)||o.set(e,new or(r)),o.get(e)}function Ur(e,r,o){if(o){const t=r*Math.pow(2,e),s=.5+Math.random()*.5;return t*s}return r}function Wr(e){return e>=500||e===429}async function Hr(e,r,o,t){try{return await t.execute(()=>r())}catch(s){const c=s;throw c.isCircuitOpen=!0,c}}function nr(e,r,o,t){var s;Me(e,{source:"ApiClient",tags:{method:r,url:o,status:((s=e.status)==null?void 0:s.toString())||"unknown"},data:{retries:t,isTimeout:e.isTimeout,isCircuitOpen:e.isCircuitOpen}})}class ar{constructor(r={}){this.requestInterceptors=[],this.responseInterceptors=[],this.cache=new Map,this.abortControllers=new Map,this.endpointCircuitBreakers=new Map,this.baseUrl=r.baseUrl??"",this.defaultHeaders=r.defaultHeaders||{"Content-Type":"application/json"},this.defaultTimeout=r.defaultTimeout||3e4,this.defaultMaxRetries=r.defaultMaxRetries||3,this.defaultRetryDelay=r.defaultRetryDelay||1e3,this.useExponentialBackoff=r.useExponentialBackoff||!1,this.enableCache=r.enableCache||!1,this.defaultCacheExpiration=r.defaultCacheExpiration||5*60*1e3,this.useCircuitBreaker=r.useCircuitBreaker||!1,this.reportErrorsToMonitoring=r.reportErrorsToMonitoring||!1,this.circuitBreaker=new or(r.circuitBreakerOptions)}addRequestInterceptor(r){return this.requestInterceptors.push(r),()=>{const o=this.requestInterceptors.indexOf(r);o!==-1&&this.requestInterceptors.splice(o,1)}}addResponseInterceptor(r){return this.responseInterceptors.push(r),()=>{const o=this.responseInterceptors.indexOf(r);o!==-1&&this.responseInterceptors.splice(o,1)}}clearCache(){this.cache.clear()}async request(r,o,t={baseUrl:""}){let s={...t,baseUrl:t.baseUrl||this.baseUrl,headers:{...this.defaultHeaders,...t.headers},timeout:t.timeout??this.defaultTimeout,maxRetries:t.maxRetries??this.defaultMaxRetries,retryDelay:t.retryDelay??this.defaultRetryDelay,useExponentialBackoff:t.useExponentialBackoff??this.useExponentialBackoff,cache:t.cache!==void 0?t.cache:this.enableCache,cacheExpiration:t.cacheExpiration??this.defaultCacheExpiration,useCircuitBreaker:t.useCircuitBreaker??this.useCircuitBreaker,isIdempotent:t.isIdempotent??r.toUpperCase()!=="POST"};for(const d of this.requestInterceptors)try{s=await d.onRequest(s)}catch(u){if(d.onRequestError)return d.onRequestError(u);throw u}const l=this.getCacheKey(r,o,s);if(s.cache&&r.toUpperCase()==="GET"){const d=this.cache.get(l);if(d&&d.expiration>Date.now())return d.data}if(s.requestId&&s.abortPrevious){const d=this.abortControllers.get(s.requestId);d&&d.abort()}const c=new AbortController;if(s.requestId&&this.abortControllers.set(s.requestId,c),s.useCircuitBreaker)try{const d=qr(o,s.circuitBreakerOptions,this.endpointCircuitBreakers);return await Hr(o,()=>this.executeRequest(r,o,s,c),s,d)}catch(d){const f=d;throw f.isCircuitOpen=!0,this.reportErrorsToMonitoring&&nr(f,r,o,0),f}return this.executeRequest(r,o,s,c)}async executeRequest(r,o,t,s){let l=0,c=new Error("Unknown error");const d=this.getCacheKey(r,o,t);for(;l<=(t.maxRetries||0);)try{const u=this.buildUrl(o,t),f=setTimeout(()=>s.abort(),t.timeout),p=await fetch(u,{method:r,headers:t.headers,body:t.data?JSON.stringify(t.data):void 0,signal:s.signal,credentials:t.withCredentials?"include":"same-origin"});if(clearTimeout(f),!p.ok){const E=new Error(`HTTP error ${p.status}: ${p.statusText}`);throw E.status=p.status,E.isRetryable=Wr(p.status)&&t.isIdempotent,E}let v;const w=p.headers.get("content-type");w&&w.includes("application/json")?v=await p.json():v=await p.text();let y=v;for(const E of this.responseInterceptors)try{y=await E.onResponse(y)}catch(j){if(E.onResponseError)y=await E.onResponseError(j);else throw j}return t.cache&&r.toUpperCase()==="GET"&&this.cache.set(d,{data:y,expiration:Date.now()+(t.cacheExpiration||this.defaultCacheExpiration)}),t.requestId&&this.abortControllers.delete(t.requestId),y}catch(u){const f=u;if(c=f,f.name==="AbortError"){c.isTimeout=!0;break}if(c.isRetryable===!1||l>=(t.maxRetries||0))break;const p=Ur(l,t.retryDelay||this.defaultRetryDelay,t.useExponentialBackoff||this.useExponentialBackoff);await new Promise(v=>setTimeout(v,p)),l++}t.requestId&&this.abortControllers.delete(t.requestId);for(const u of this.responseInterceptors)if(u.onResponseError)try{return await u.onResponseError(c)}catch(f){c=f}throw this.reportErrorsToMonitoring&&nr(c,r,o,l),c}get(r,o={baseUrl:""}){return this.request("GET",r,o)}post(r,o,t={baseUrl:""}){return this.request("POST",r,{...t,data:o})}put(r,o,t={baseUrl:""}){return this.request("PUT",r,{...t,data:o})}delete(r,o={baseUrl:""}){return this.request("DELETE",r,o)}patch(r,o,t={baseUrl:""}){return this.request("PATCH",r,{...t,data:o})}buildUrl(r,o){const t=o.baseUrl||"",s=r.startsWith("http")?r:`${t}${r}`;if(!o.params)return s;const l=new URLSearchParams;for(const[d,u]of Object.entries(o.params))u!=null&&(Array.isArray(u)?u.forEach(f=>{l.append(`${d}[]`,String(f))}):l.append(d,String(u)));const c=l.toString();return c?`${s}${s.includes("?")?"&":"?"}${c}`:s}getCacheKey(r,o,t){const s=t.params?JSON.stringify(t.params):"";return`${r}:${o}:${s}`}}class $e extends ar{constructor(r={}){const o={...r,defaultMaxRetries:r.defaultMaxRetries||3,useExponentialBackoff:r.useExponentialBackoff!==void 0?r.useExponentialBackoff:!0,useCircuitBreaker:r.useCircuitBreaker!==void 0?r.useCircuitBreaker:!0,reportErrorsToMonitoring:r.reportErrorsToMonitoring!==void 0?r.reportErrorsToMonitoring:!0,circuitBreakerOptions:{failureThreshold:3,resetTimeout:3e4,successThreshold:2,...r.circuitBreakerOptions||{}}};super(o),this.useMockData=r.useMockData||!1,this.mockDataDelay=r.mockDataDelay||500,this.addResponseInterceptor({onResponse:t=>{if(t&&typeof t=="object"&&"success"in t&&!t.success&&t.error)throw new Error(t.error);return t},onResponseError:t=>{throw console.error("Trading API Error:",t),o.reportErrorsToMonitoring&&Me(t,{source:"TradingApiClient",tags:{type:"api_error"}}),t}})}async getConfiguration(r={baseUrl:""}){return this.useMockData?(await this.delay(),this.getMockConfiguration()):this.get("/api/configuration",{...r,cache:!0,useCircuitBreaker:!0,isIdempotent:!0,maxRetries:3})}async getDashboardData(r={baseUrl:""}){return this.useMockData?(await this.delay(),this.getMockDashboardData()):this.get("/api/dashboard",{...r,useCircuitBreaker:!0,isIdempotent:!0,maxRetries:2})}async getTradingData(r={},o={baseUrl:""}){return this.useMockData?(await this.delay(),this.getMockTradingData(r)):this.get("/api/trading",{...o,params:r,useCircuitBreaker:!0,isIdempotent:!0,maxRetries:3,useExponentialBackoff:!0,requestId:"trading-data",abortPrevious:!0})}async getPerformanceMetrics(r={},o={baseUrl:""}){return this.useMockData?(await this.delay(),this.getMockPerformanceMetrics(r)):this.get("/api/metrics",{...o,params:r,useCircuitBreaker:!0,isIdempotent:!0,maxRetries:2,cache:!0,cacheExpiration:5*60*1e3})}async getMarketNews(r={},o={baseUrl:""}){return this.useMockData?(await this.delay(),this.getMockMarketNews(r)):this.get("/api/news",{...o,params:r,useCircuitBreaker:!0,isIdempotent:!0,maxRetries:2,cache:!0,cacheExpiration:15*60*1e3})}async saveUserPreferences(r,o={baseUrl:""}){return this.useMockData?(await this.delay(),{success:!0}):this.post("/api/preferences",r,{...o,useCircuitBreaker:!0,isIdempotent:!0,maxRetries:2})}async getUserPreferences(r={baseUrl:""}){return this.useMockData?(await this.delay(),this.getMockUserPreferences()):this.get("/api/preferences",{...r,cache:!0,useCircuitBreaker:!0,isIdempotent:!0,maxRetries:2,cacheExpiration:30*60*1e3})}closeDialog(){window.google&&window.google.script&&window.google.script.host&&window.google.script.host.close()}async delay(){return new Promise(r=>setTimeout(r,this.mockDataDelay))}getMockConfiguration(){return{success:!0,theme:{colors:{primary:"#e10600",background:"#1a1f2c",cardBackground:"#252a37",text:"#ffffff",secondaryText:"#aaaaaa",positive:"#4caf50",negative:"#f44336",neutral:"#9e9e9e",border:"#333333",tabActive:"#e10600",tabInactive:"#555555",chartGrid:"#333333",chartLine:"#e10600",tooltipBackground:"rgba(37, 42, 55, 0.9)"}},features:{performanceChart:!0,newsEvents:!0,recentTrades:!0}}}getMockDashboardData(){return{success:!0,summary:{totalTrades:120,winRate:.65,profitFactor:2.3,netProfit:12500},recentTrades:[{id:1,date:"2025-01-15",symbol:"AAPL",result:"win",profit:350},{id:2,date:"2025-01-14",symbol:"MSFT",result:"loss",profit:-150},{id:3,date:"2025-01-13",symbol:"GOOGL",result:"win",profit:420}],performance:{daily:[{date:"2025-01-11",value:10200},{date:"2025-01-12",value:10500},{date:"2025-01-13",value:11200},{date:"2025-01-14",value:11e3},{date:"2025-01-15",value:12500}]}}}getMockTradingData(r){return{success:!0,data:[{date:"2025-01-15",symbol:"AAPL",action:"BUY",price:185.25,quantity:10,profit:350},{date:"2025-01-14",symbol:"MSFT",action:"SELL",price:390.12,quantity:5,profit:-150},{date:"2025-01-13",symbol:"GOOGL",action:"BUY",price:142.75,quantity:8,profit:420}]}}getMockPerformanceMetrics(r){return{success:!0,metrics:{"Win Rate":.65,"Profit Factor":2.3,"Net Profit":12500,"Average Win":350,"Average Loss":-180},chartData:[{date:"2025-01-11",value:10200},{date:"2025-01-12",value:10500},{date:"2025-01-13",value:11200},{date:"2025-01-14",value:11e3},{date:"2025-01-15",value:12500}]}}getMockMarketNews(r){return{success:!0,news:[{title:"Market Update: Stocks Rally on Fed Decision",date:"2025-01-15",source:"Financial Times"},{title:"Tech Stocks Lead Market Higher",date:"2025-01-14",source:"Wall Street Journal"},{title:"Economic Data Shows Strong Growth",date:"2025-01-13",source:"Bloomberg"}]}}getMockUserPreferences(){return{success:!0,preferences:{theme:"f1",refreshInterval:5,showNotifications:!0}}}}const Be=h.createContext(void 0),Yr=({children:e,options:r,client:o})=>{const t=h.useMemo(()=>o||new $e(r),[o,r]);return i.jsx(Be.Provider,{value:t,children:e})};function pe(){const e=h.useContext(Be);if(e===void 0)throw new Error("useApi must be used within an ApiProvider");return e}function Gr(e={}){const r=pe(),{tradingOptions:o,...t}=e,s=h.useCallback(l=>r.getTradingData(l||o),[r,o]);return le(s,{...t,params:o})}function Vr(e={}){const r=pe(),{metricsOptions:o,...t}=e,s=h.useCallback(l=>r.getPerformanceMetrics(l||o),[r,o]);return le(s,{...t,params:o})}function Jr(e={}){const r=pe(),{newsOptions:o,...t}=e,s=h.useCallback(l=>r.getMarketNews(l||o),[r,o]);return le(s,{...t,params:o})}function Kr(e={}){var d;const r=pe(),{autoFetch:o=!0,...t}=e,s=h.useCallback(()=>r.getUserPreferences(),[r]),l=le(s,{...t,enabled:o}),c=er(u=>r.saveUserPreferences(u),{onSuccess:()=>{l.refetch()}});return{preferences:(d=l.data)==null?void 0:d.preferences,isLoading:l.isLoading,isError:l.isError,error:l.error,refetch:l.refetch,savePreferences:c.mutate,isSaving:c.isLoading,saveError:c.error}}function Ne(){return!!(window.google&&window.google.script&&window.google.script.run)}const Xr=Ne()?Xe:Qe;console.log(`Using ${Ne()?"GAS":"mock"} API implementation`);const Qr=new $e({useMockData:!Ne(),enableCache:!0,defaultMaxRetries:3}),Zr=Xr,et={small:a.css(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs,({dot:e})=>e?"8px":"20px"),medium:a.css(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm,({dot:e})=>e?"10px":"24px"),large:a.css(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md,({dot:e})=>e?"12px":"32px")},rt=(e,r,o=!1)=>a.css(["",""],({theme:t})=>{let s,l,c;switch(e){case"primary":s=r?t.colors.primary:`${t.colors.primary}20`,l=r?t.colors.textInverse:t.colors.primary,c=t.colors.primary;break;case"secondary":s=r?t.colors.secondary:`${t.colors.secondary}20`,l=r?t.colors.textInverse:t.colors.secondary,c=t.colors.secondary;break;case"success":s=r?t.colors.success:`${t.colors.success}20`,l=r?t.colors.textInverse:t.colors.success,c=t.colors.success;break;case"warning":s=r?t.colors.warning:`${t.colors.warning}20`,l=r?t.colors.textInverse:t.colors.warning,c=t.colors.warning;break;case"error":s=r?t.colors.error:`${t.colors.error}20`,l=r?t.colors.textInverse:t.colors.error,c=t.colors.error;break;case"info":s=r?t.colors.info:`${t.colors.info}20`,l=r?t.colors.textInverse:t.colors.info,c=t.colors.info;break;case"neutral":s=r?t.colors.textSecondary:`${t.colors.textSecondary}10`,l=r?t.colors.textInverse:t.colors.textSecondary,c=t.colors.textSecondary;break;default:s=r?t.colors.textSecondary:`${t.colors.textSecondary}20`,l=r?t.colors.textInverse:t.colors.textSecondary,c=t.colors.textSecondary}return o?`
          background-color: transparent;
          color: ${c};
          border: 1px solid ${c};
        `:`
        background-color: ${s};
        color: ${l};
        border: 1px solid transparent;
      `}),sr=a.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),tt=a(sr).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:e})=>e.spacing.xxs),ot=a(sr).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:e})=>e.spacing.xxs),nt=a.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:e})=>e?"inline-flex":"flex",({theme:e,rounded:r,dot:o})=>o?"50%":r?"9999px":e.borderRadius.sm,({theme:e})=>e.fontWeights.medium,({size:e})=>et[e],({variant:e,solid:r,outlined:o})=>rt(e,r,o||!1),({dot:e})=>e&&a.css(["padding:0;height:8px;width:8px;"]),({counter:e})=>e&&a.css(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:e})=>e&&a.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),at=({children:e,variant:r="default",size:o="medium",solid:t=!1,className:s,onClick:l,rounded:c=!1,dot:d=!1,counter:u=!1,outlined:f=!1,startIcon:p,endIcon:v,max:w,inline:y=!0})=>{let E=e;return u&&typeof e=="number"&&w!==void 0&&e>w&&(E=`${w}+`),i.jsx(nt,{variant:r,size:o,solid:t,clickable:!!l,className:s,onClick:l,rounded:c,dot:d,counter:u,outlined:f,inline:y,children:!d&&i.jsxs(i.Fragment,{children:[p&&i.jsx(tt,{children:p}),E,v&&i.jsx(ot,{children:v})]})})},st=a.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),it=a.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],st,({theme:e})=>e.spacing.xs),ct={small:a.css(["padding:",";font-size:",";min-height:32px;"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.xs),medium:a.css(["padding:",";font-size:",";min-height:40px;"],({theme:e})=>`${e.spacing.xs} ${e.spacing.md}`,({theme:e})=>e.fontSizes.sm),large:a.css(["padding:",";font-size:",";min-height:48px;"],({theme:e})=>`${e.spacing.sm} ${e.spacing.lg}`,({theme:e})=>e.fontSizes.md)},lt={primary:a.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.primaryDark,({theme:e})=>e.colors.primaryDark),secondary:a.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.secondaryDark,({theme:e})=>e.colors.secondaryDark),outline:a.css(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),text:a.css(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs),success:a.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.success,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.success),danger:a.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.error,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.error)},ut=a.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:e})=>e.borderRadius.sm,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({size:e="medium"})=>ct[e],({variant:e="primary"})=>lt[e],({fullWidth:e})=>e&&a.css(["width:100%;"]),({$hasStartIcon:e})=>e&&a.css(["& > *:first-child{margin-right:",";}"],({theme:r})=>r.spacing.xs),({$hasEndIcon:e})=>e&&a.css(["& > *:last-child{margin-left:",";}"],({theme:r})=>r.spacing.xs)),dt=a.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),oe=({children:e,variant:r="primary",disabled:o=!1,loading:t=!1,size:s="medium",fullWidth:l=!1,startIcon:c,endIcon:d,onClick:u,className:f,type:p="button",...v})=>i.jsx(ut,{variant:r,disabled:o||t,size:s,fullWidth:l,onClick:u,className:f,type:p,$hasStartIcon:!!c&&!t,$hasEndIcon:!!d&&!t,...v,children:i.jsxs(dt,{children:[t&&i.jsx(it,{}),!t&&c,e,!t&&d]})}),ft=a.div.withConfig({displayName:"InputWrapper",componentId:"sc-uv3rzi-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),pt=a.label.withConfig({displayName:"Label",componentId:"sc-uv3rzi-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),gt=a.div.withConfig({displayName:"InputContainer",componentId:"sc-uv3rzi-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:o,isFocused:t})=>r?e.colors.error:o?e.colors.success:t?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&a.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:o,hasSuccess:t})=>e&&a.css(["box-shadow:0 0 0 2px ",";"],o?`${r.colors.error}33`:t?`${r.colors.success}33`:`${r.colors.primary}33`),({size:e})=>{switch(e){case"small":return a.css(["height:32px;"]);case"large":return a.css(["height:48px;"]);default:return a.css(["height:40px;"])}}),ir=a.div.withConfig({displayName:"IconContainer",componentId:"sc-uv3rzi-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),ht=a.input.withConfig({displayName:"StyledInput",componentId:"sc-uv3rzi-4"})(["flex:1;border:none;background:transparent;color:",";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:",";}"," "," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.textDisabled,({hasStartIcon:e})=>e&&a.css(["padding-left:0;"]),({hasEndIcon:e})=>e&&a.css(["padding-right:0;"]),({size:e,theme:r})=>e==="small"?a.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?a.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):a.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),mt=a.button.withConfig({displayName:"ClearButton",componentId:"sc-uv3rzi-5"})(["background:none;border:none;cursor:pointer;color:",";padding:0 ",";display:flex;align-items:center;justify-content:center;&:hover{color:",";}&:focus{outline:none;}"],({theme:e})=>e.colors.textDisabled,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),bt=a.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-uv3rzi-6"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:o})=>r?e.colors.error:o?e.colors.success:e.colors.textSecondary),xt=({value:e,onChange:r,placeholder:o,disabled:t=!1,error:s,type:l="text",name:c,id:d,className:u,required:f=!1,autoComplete:p,label:v,helperText:w,startIcon:y,endIcon:E,loading:j=!1,success:$=!1,clearable:k=!1,onClear:T,maxLength:P,showCharCount:F=!1,size:q="medium",fullWidth:A=!1,...N})=>{const[D,C]=h.useState(!1),X=h.useRef(null),Y=()=>{T?T():r(""),X.current&&X.current.focus()},S=J=>{C(!0),N.onFocus&&N.onFocus(J)},U=J=>{C(!1),N.onBlur&&N.onBlur(J)},G=k&&e&&!t,ee=(e==null?void 0:e.length)||0,re=F||P!==void 0&&P>0;return i.jsxs(ft,{className:u,fullWidth:A,children:[v&&i.jsxs(pt,{htmlFor:d,children:[v,f&&" *"]}),i.jsxs(gt,{hasError:!!s,hasSuccess:!!$,disabled:!!t,size:q,hasStartIcon:!!y,hasEndIcon:!!(E||G),isFocused:!!D,children:[y&&i.jsx(ir,{children:y}),i.jsx(ht,{ref:X,type:l,value:e,onChange:J=>r(J.target.value),placeholder:o,disabled:!!(t||j),name:c,id:d,required:!!f,autoComplete:p,hasStartIcon:!!y,hasEndIcon:!!(E||G),size:q,maxLength:P,onFocus:S,onBlur:U,...N}),G&&i.jsx(mt,{type:"button",onClick:Y,tabIndex:-1,children:"✕"}),E&&i.jsx(ir,{children:E})]}),(s||w||re)&&i.jsxs(bt,{hasError:!!s,hasSuccess:!!$,children:[i.jsx("div",{children:s||w}),re&&i.jsxs("div",{children:[ee,P!==void 0&&`/${P}`]})]})]})},cr={small:a.css(["height:100px;"]),medium:a.css(["height:200px;"]),large:a.css(["height:300px;"]),custom:e=>a.css(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},vt={default:a.css(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:a.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:a.css(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:a.css(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},yt=a.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),wt=a.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:r,customWidth:o})=>e==="custom"?cr.custom({customHeight:r,customWidth:o}):cr[e],({variant:e})=>vt[e]),kt=a.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,yt,({theme:e})=>e.spacing.sm),Ct=a.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),lr=({variant:e="default",size:r="medium",height:o="200px",width:t,text:s="Loading...",showSpinner:l=!0,className:c})=>i.jsxs(wt,{variant:e,size:r,customHeight:o,customWidth:t,className:c,children:[l&&i.jsx(kt,{}),s&&i.jsx(Ct,{children:s})]}),St=a.div.withConfig({displayName:"SelectWrapper",componentId:"sc-wvk2um-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),Et=a.label.withConfig({displayName:"Label",componentId:"sc-wvk2um-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),jt=a.div.withConfig({displayName:"SelectContainer",componentId:"sc-wvk2um-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:o,isFocused:t})=>r?e.colors.error:o?e.colors.success:t?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&a.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:o,hasSuccess:t})=>e&&a.css(["box-shadow:0 0 0 2px ",";"],o?`${r.colors.error}33`:t?`${r.colors.success}33`:`${r.colors.primary}33`),({size:e})=>{switch(e){case"small":return a.css(["height:32px;"]);case"large":return a.css(["height:48px;"]);default:return a.css(["height:40px;"])}}),It=a.div.withConfig({displayName:"IconContainer",componentId:"sc-wvk2um-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Tt=a.select.withConfig({displayName:"StyledSelect",componentId:"sc-wvk2um-4"})(["flex:1;border:none;background:transparent;color:",`;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `," center;background-size:16px;padding-right:",";&:disabled{cursor:not-allowed;}"," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.xl,({hasStartIcon:e})=>e&&a.css(["padding-left:0;"]),({size:e,theme:r})=>e==="small"?a.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?a.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):a.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),Dt=a.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-wvk2um-5"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:o})=>r?e.colors.error:o?e.colors.success:e.colors.textSecondary),Pt=a.optgroup.withConfig({displayName:"OptionGroup",componentId:"sc-wvk2um-6"})(["font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>e.colors.textPrimary),Mt=({options:e,value:r,onChange:o,disabled:t=!1,error:s,name:l,id:c,className:d,required:u=!1,placeholder:f,label:p,helperText:v,size:w="medium",fullWidth:y=!0,loading:E=!1,success:j=!1,startIcon:$,...k})=>{const[T,P]=h.useState(!1),F=C=>{P(!0),k.onFocus&&k.onFocus(C)},q=C=>{P(!1),k.onBlur&&k.onBlur(C)},A={},N=[];e.forEach(C=>{C.group?(A[C.group]||(A[C.group]=[]),A[C.group].push(C)):N.push(C)});const D=Object.keys(A).length>0;return i.jsxs(St,{className:d,fullWidth:y,children:[p&&i.jsxs(Et,{htmlFor:c,children:[p,u&&" *"]}),i.jsxs(jt,{hasError:!!s,hasSuccess:!!j,disabled:!!(t||E),size:w,hasStartIcon:!!$,isFocused:!!T,children:[$&&i.jsx(It,{children:$}),i.jsxs(Tt,{value:r,onChange:C=>o(C.target.value),disabled:!!(t||E),name:l,id:c,required:!!u,hasStartIcon:!!$,size:w,onFocus:F,onBlur:q,...k,children:[f&&i.jsx("option",{value:"",disabled:!0,children:f}),D?i.jsxs(i.Fragment,{children:[N.map(C=>i.jsx("option",{value:C.value,disabled:C.disabled,children:C.label},C.value)),Object.entries(A).map(([C,X])=>i.jsx(Pt,{label:C,children:X.map(Y=>i.jsx("option",{value:Y.value,disabled:Y.disabled,children:Y.label},Y.value))},C))]}):e.map(C=>i.jsx("option",{value:C.value,disabled:C.disabled,children:C.label},C.value))]})]}),(s||v)&&i.jsx(Dt,{hasError:!!s,hasSuccess:!!j,children:i.jsx("div",{children:s||v})})]})},ur={small:"8px",medium:"12px",large:"16px"},$t={small:a.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs),medium:a.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm),large:a.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.spacing.md)},Bt=a.css(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]),Nt=a.div.withConfig({displayName:"Container",componentId:"sc-gwj3m-0"})(["display:inline-flex;align-items:center;"]),Lt=a.div.withConfig({displayName:"Indicator",componentId:"sc-gwj3m-1"})(["border-radius:50%;width:",";height:",";",""],({size:e})=>ur[e],({size:e})=>ur[e],({status:e,theme:r,pulse:o})=>{let t,s;switch(e){case"success":t=r.colors.success,s="76, 175, 80";break;case"error":t=r.colors.error,s="244, 67, 54";break;case"warning":t=r.colors.warning,s="255, 152, 0";break;case"info":t=r.colors.info,s="33, 150, 243";break;default:t=r.colors.textSecondary,s="158, 158, 158"}return a.css(["background-color:",";",""],t,o&&a.css(["--pulse-color:",";",""],s,Bt))}),Ot=a.span.withConfig({displayName:"Label",componentId:"sc-gwj3m-2"})([""," ",""],({size:e})=>$t[e],({status:e,theme:r})=>{let o;switch(e){case"success":o=r.colors.success;break;case"error":o=r.colors.error;break;case"warning":o=r.colors.warning;break;case"info":o=r.colors.info;break;default:o=r.colors.textSecondary}return a.css(["color:",";font-weight:",";"],o,r.fontWeights.medium)}),Rt=({status:e,size:r="medium",pulse:o=!1,showLabel:t=!1,label:s,className:l})=>{const c=s||e.charAt(0).toUpperCase()+e.slice(1);return i.jsxs(Nt,{className:l,children:[i.jsx(Lt,{status:e,size:r,pulse:o}),t&&i.jsx(Ot,{status:e,size:r,children:c})]})},At={small:a.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:a.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:a.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},Ft=e=>a.css(["",""],({theme:r})=>{let o,t,s;switch(e){case"primary":o=`${r.colors.primary}10`,t=r.colors.primary,s=`${r.colors.primary}30`;break;case"secondary":o=`${r.colors.secondary}10`,t=r.colors.secondary,s=`${r.colors.secondary}30`;break;case"success":o=`${r.colors.success}10`,t=r.colors.success,s=`${r.colors.success}30`;break;case"warning":o=`${r.colors.warning}10`,t=r.colors.warning,s=`${r.colors.warning}30`;break;case"error":o=`${r.colors.error}10`,t=r.colors.error,s=`${r.colors.error}30`;break;case"info":o=`${r.colors.info}10`,t=r.colors.info,s=`${r.colors.info}30`;break;default:o=`${r.colors.textSecondary}10`,t=r.colors.textSecondary,s=`${r.colors.textSecondary}30`}return`
        background-color: ${o};
        color: ${t};
        border: 1px solid ${s};
      `}),_t=a.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>At[e],({variant:e})=>Ft(e),({clickable:e})=>e&&a.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),zt=a.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:r})=>{const o={small:"12px",medium:"14px",large:"16px"};return`
      width: ${o[e]};
      height: ${o[e]};
      font-size: ${r.fontSizes.xs};
    `}),qt=({children:e,variant:r="default",size:o="medium",removable:t=!1,onRemove:s,className:l,onClick:c})=>{const d=u=>{u.stopPropagation(),s==null||s()};return i.jsxs(_t,{variant:r,size:o,clickable:!!c,className:l,onClick:c,children:[e,t&&i.jsx(zt,{size:o,onClick:d,children:"×"})]})},Ut={none:a.css(["padding:0;"]),small:a.css(["padding:",";"],({theme:e})=>e.spacing.sm),medium:a.css(["padding:",";"],({theme:e})=>e.spacing.md),large:a.css(["padding:",";"],({theme:e})=>e.spacing.lg)},Wt={default:a.css(["background-color:",";"],({theme:e})=>e.colors.surface),primary:a.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),secondary:a.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.secondary),outlined:a.css(["background-color:transparent;border:1px solid ",";"],({theme:e})=>e.colors.border),elevated:a.css(["background-color:",";box-shadow:",";border:none;"],({theme:e})=>e.colors.surface,({theme:e})=>e.shadows.md)},Ht=a.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({bordered:e,theme:r})=>e&&a.css(["border:1px solid ",";"],r.colors.border),({padding:e})=>Ut[e],({variant:e})=>Wt[e],({clickable:e})=>e&&a.css(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:r})=>r.shadows.medium)),Yt=a.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:e})=>e.spacing.md),Gt=a.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),Vt=a.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Jt=a.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),Kt=a.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Xt=a.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),Qt=a.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),Zt=a.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),eo=a.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.error,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.md),ro=a.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),dr=({children:e,title:r,subtitle:o,bordered:t=!0,variant:s="default",padding:l="medium",className:c,footer:d,actions:u,isLoading:f=!1,hasError:p=!1,errorMessage:v="An error occurred",clickable:w=!1,onClick:y,...E})=>{const j=r||o||u;return i.jsxs(Ht,{bordered:t,variant:s,padding:l,clickable:w,className:c,onClick:w?y:void 0,...E,children:[f&&i.jsx(Zt,{children:i.jsx(ro,{})}),j&&i.jsxs(Yt,{children:[i.jsxs(Gt,{children:[r&&i.jsx(Vt,{children:r}),o&&i.jsx(Jt,{children:o})]}),u&&i.jsx(Kt,{children:u})]}),p&&i.jsx(eo,{children:i.jsx("p",{children:v})}),i.jsx(Xt,{children:e}),d&&i.jsx(Qt,{children:d})]})},to=a.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:r})=>{const o={small:r.fontSizes.md,medium:r.fontSizes.lg,large:r.fontSizes.xl};return a.css(["font-size:",";"],o[e])}),oo=a.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:r})=>{const o={small:r.fontSizes.sm,medium:r.fontSizes.md,large:r.fontSizes.lg};return a.css(["font-size:",";"],o[e])}),no={default:a.css(["background-color:transparent;"]),compact:a.css(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:a.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},ao=a.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>no[e],({size:e,theme:r})=>{switch(e){case"small":return a.css(["padding:",";min-height:120px;"],r.spacing.md);case"large":return a.css(["padding:",";min-height:300px;"],r.spacing.xl);default:return a.css(["padding:",";min-height:200px;"],r.spacing.lg)}}),so=a.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:r})=>{const o={small:"32px",medium:"48px",large:"64px"};return a.css(["font-size:",";svg{width:",";height:",";color:",";}"],o[e],o[e],o[e],r.colors.textSecondary)}),io=a.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),co=a.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),Le=({title:e,description:r,icon:o,actionText:t,onAction:s,variant:l="default",size:c="medium",className:d,children:u})=>i.jsxs(ao,{variant:l,size:c,className:d,children:[o&&i.jsx(so,{size:c,children:o}),e&&i.jsx(to,{size:c,children:e}),r&&i.jsx(oo,{size:c,children:r}),t&&s&&i.jsx(io,{children:i.jsx(oe,{variant:"primary",size:c==="small"?"small":"medium",onClick:s,children:t})}),u&&i.jsx(co,{children:u})]}),fr=a.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),lo=a.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),pr=a.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),ge=a.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),gr=a.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),hr=a.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),uo=a.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),mr=a.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),fo=a.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),po=a(mr).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),go=({error:e,resetError:r,isAppLevel:o,name:t,onSkip:s})=>{const l=()=>{window.location.reload()};return o?i.jsx(fr,{isAppLevel:!0,children:i.jsxs(lo,{children:[i.jsx(pr,{isAppLevel:!0,children:"Something went wrong"}),i.jsx(ge,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),i.jsxs(gr,{children:[i.jsx("summary",{children:"Technical Details"}),i.jsx(ge,{children:e.message}),e.stack&&i.jsx(hr,{children:e.stack})]}),i.jsx(po,{onClick:l,children:"Reload Application"})]})}):i.jsxs(fr,{children:[i.jsx(pr,{children:t?`Error in ${t}`:"Something went wrong"}),i.jsx(ge,{children:t?`We encountered a problem while loading ${t}. You can try again${s?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),i.jsxs(gr,{children:[i.jsx("summary",{children:"Technical Details"}),i.jsx(ge,{children:e.message}),e.stack&&i.jsx(hr,{children:e.stack})]}),i.jsxs(uo,{children:[i.jsx(mr,{onClick:r,children:"Try Again"}),s&&i.jsx(fo,{onClick:s,children:"Skip This Feature"})]})]})};class br extends h.Component{constructor(r){super(r),this.resetError=()=>{this.setState({hasError:!1,error:null})},this.state={hasError:!1,error:null}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,o){const{name:t}=this.props,s=t?`ErrorBoundary(${t})`:"ErrorBoundary";console.error(`Error caught by ${s}:`,r,o),this.props.onError&&this.props.onError(r,o)}componentDidUpdate(r){this.state.hasError&&this.props.resetOnPropsChange&&r.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:r,error:o}=this.state,{children:t,fallback:s,name:l,isFeatureBoundary:c,onSkip:d}=this.props;return r&&o?typeof s=="function"?s({error:o,resetError:this.resetError}):s||i.jsx(go,{error:o,resetError:this.resetError,isAppLevel:!c,name:l,onSkip:d}):t}}const Oe=({isAppLevel:e,isFeatureBoundary:r,...o})=>{const t=e?"app":r?"feature":"component",s={resetOnPropsChange:t!=="app",resetOnUnmount:t!=="app",isFeatureBoundary:t==="feature"};return i.jsx(br,{...s,...o})},ho=e=>i.jsx(Oe,{isAppLevel:!0,...e}),mo=({featureName:e,...r})=>i.jsx(Oe,{isFeatureBoundary:!0,name:e,...r}),bo=a.div.withConfig({displayName:"FieldContainer",componentId:"sc-i922jg-0"})(["display:flex;flex-direction:column;margin-bottom:",";"],({theme:e})=>e.spacing.md),xo=a.label.withConfig({displayName:"Label",componentId:"sc-i922jg-1"})(["font-size:",";font-weight:500;margin-bottom:",";color:",";.required-indicator{color:",";margin-left:",";}"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xxs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textPrimary,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.xxs),vo=a.div.withConfig({displayName:"HelperText",componentId:"sc-i922jg-2"})(["font-size:",";color:",";margin-top:",";"],({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textSecondary,({theme:e})=>e.spacing.xxs),yo=({children:e,label:r,helperText:o,required:t=!1,error:s,className:l,id:c,...d})=>{const u=c||`field-${Math.random().toString(36).substr(2,9)}`,f=h.Children.map(e,p=>h.isValidElement(p)?h.cloneElement(p,{id:u,required:t,error:s,...p.props}):p);return i.jsxs(bo,{className:l,...d,children:[i.jsxs(xo,{htmlFor:u,hasError:!!s,children:[r,t&&i.jsx("span",{className:"required-indicator",children:"*"})]}),f,(o||s)&&i.jsx(vo,{hasError:!!s,children:s||o})]})},wo=a.keyframes(["from{opacity:0;}to{opacity:1;}"]),ko=a.keyframes(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]),Co=a.div.withConfig({displayName:"Backdrop",componentId:"sc-1cuqxtr-0"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:",";animation:"," 0.2s ease-out;"],({zIndex:e})=>e||1e3,wo),So=a.div.withConfig({displayName:"ModalContainer",componentId:"sc-1cuqxtr-1"})(["background-color:",";border-radius:",";box-shadow:",";display:flex;flex-direction:column;max-height:",";width:",";max-width:95vw;animation:"," 0.2s ease-out;position:relative;"," ",""],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.lg,({size:e})=>e==="fullscreen"?"100vh":"90vh",({size:e})=>{switch(e){case"small":return"400px";case"medium":return"600px";case"large":return"800px";case"fullscreen":return"100vw";default:return"600px"}},ko,({size:e})=>e==="fullscreen"&&a.css(["height:100vh;border-radius:0;"]),({centered:e})=>e&&a.css(["margin:auto;"])),Eo=a.div.withConfig({displayName:"ModalHeader",componentId:"sc-1cuqxtr-2"})(["display:flex;justify-content:space-between;align-items:center;padding:",";border-bottom:1px solid ",";"],({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),jo=a.h3.withConfig({displayName:"ModalTitle",componentId:"sc-1cuqxtr-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Io=a.button.withConfig({displayName:"CloseButton",componentId:"sc-1cuqxtr-4"})(["background:none;border:none;cursor:pointer;font-size:",";color:",";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",";&:hover{background-color:",";}&:focus{outline:none;box-shadow:0 0 0 2px ","33;}"],({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),To=a.div.withConfig({displayName:"ModalContent",componentId:"sc-1cuqxtr-5"})(["padding:",";",""],({theme:e})=>e.spacing.lg,({scrollable:e})=>e&&a.css(["overflow-y:auto;flex:1;"])),Do=a.div.withConfig({displayName:"ModalFooter",componentId:"sc-1cuqxtr-6"})(["display:flex;justify-content:flex-end;gap:",";padding:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),Po=({isOpen:e,title:r,children:o,onClose:t,size:s="medium",closeOnOutsideClick:l=!0,showCloseButton:c=!0,footer:d,hasFooter:u=!0,primaryActionText:f,onPrimaryAction:p,primaryActionDisabled:v=!1,primaryActionLoading:w=!1,secondaryActionText:y,onSecondaryAction:E,secondaryActionDisabled:j=!1,className:$,zIndex:k=1e3,centered:T=!0,scrollable:P=!0})=>{const F=h.useRef(null);h.useEffect(()=>{const D=C=>{C.key==="Escape"&&e&&l&&t()};return document.addEventListener("keydown",D),()=>{document.removeEventListener("keydown",D)}},[e,t,l]);const q=D=>{F.current&&!F.current.contains(D.target)&&l&&t()};h.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[e]);const A=i.jsxs(i.Fragment,{children:[y&&i.jsx(oe,{variant:"outline",onClick:E,disabled:j,children:y}),f&&i.jsx(oe,{onClick:p,disabled:v,loading:w,children:f})]});if(!e)return null;const N=i.jsx(Co,{onClick:q,zIndex:k,children:i.jsxs(So,{ref:F,size:s,className:$,centered:T,scrollable:P,onClick:D=>D.stopPropagation(),children:[(r||c)&&i.jsxs(Eo,{children:[r&&i.jsx(jo,{children:r}),c&&i.jsx(Io,{onClick:t,"aria-label":"Close",children:"×"})]}),i.jsx(To,{scrollable:P,children:o}),u&&(d||f||y)&&i.jsx(Do,{children:d||A})]})});return Nr.createPortal(N,document.body)},Mo=a.div.withConfig({displayName:"TableContainer",componentId:"sc-4as3uq-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),$o=a.table.withConfig({displayName:"StyledTable",componentId:"sc-4as3uq-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>e.fontSizes.sm,({bordered:e,theme:r})=>e&&a.css(["border:1px solid ",";border-radius:",";"],r.colors.border,r.borderRadius.sm),({compact:e,theme:r})=>e?a.css(["th,td{padding:"," ",";}"],r.spacing.xs,r.spacing.sm):a.css(["th,td{padding:"," ",";}"],r.spacing.sm,r.spacing.md)),Bo=a.thead.withConfig({displayName:"TableHeader",componentId:"sc-4as3uq-2"})(["",""],({stickyHeader:e})=>e&&a.css(["position:sticky;top:0;z-index:1;"])),No=a.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-4as3uq-3"})(["background-color:",";"],({theme:e})=>e.colors.background),Lo=a.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-4as3uq-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.colors.border,({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&a.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:r})=>r.colors.background),({isSorted:e,theme:r})=>e&&a.css(["color:",";"],r.colors.primary)),Oo=a.span.withConfig({displayName:"SortIcon",componentId:"sc-4as3uq-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),Ro=a.tbody.withConfig({displayName:"TableBody",componentId:"sc-4as3uq-6"})([""]),Ao=a.tr.withConfig({displayName:"TableRow",componentId:"sc-4as3uq-7"})([""," "," "," ",""],({striped:e,theme:r,isSelected:o})=>e&&!o&&a.css(["&:nth-child(even){background-color:","50;}"],r.colors.background),({hoverable:e,theme:r,isSelected:o})=>e&&!o&&a.css(["&:hover{background-color:","aa;}"],r.colors.background),({isSelected:e,theme:r})=>e&&a.css(["background-color:","15;"],r.colors.primary),({isClickable:e})=>e&&a.css(["cursor:pointer;"])),Fo=a.td.withConfig({displayName:"TableCell",componentId:"sc-4as3uq-8"})(["text-align:",";border-bottom:1px solid ",";color:",";"],({align:e})=>e||"left",({theme:e})=>e.colors.border,({theme:e})=>e.colors.textPrimary),_o=a.div.withConfig({displayName:"EmptyState",componentId:"sc-4as3uq-9"})(["padding:",";text-align:center;color:",";"],({theme:e})=>e.spacing.xl,({theme:e})=>e.colors.textSecondary),zo=a.div.withConfig({displayName:"PaginationContainer",componentId:"sc-4as3uq-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm),qo=a.div.withConfig({displayName:"PageInfo",componentId:"sc-4as3uq-11"})(["color:",";"],({theme:e})=>e.colors.textSecondary),Uo=a.div.withConfig({displayName:"PaginationControls",componentId:"sc-4as3uq-12"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.xs),Wo=a.div.withConfig({displayName:"PageSizeSelector",componentId:"sc-4as3uq-13"})(["display:flex;align-items:center;gap:",";margin-right:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),Ho=a.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-4as3uq-14"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),Yo=a.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-4as3uq-15"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary);function Go({columns:e,data:r,isLoading:o=!1,bordered:t=!0,striped:s=!0,hoverable:l=!0,compact:c=!1,stickyHeader:d=!1,height:u,onRowClick:f,isRowSelected:p,onSort:v,sortColumn:w,sortDirection:y,pagination:E=!1,currentPage:j=1,pageSize:$=10,totalRows:k=0,onPageChange:T,onPageSizeChange:P,className:F,emptyMessage:q="No data available",scrollable:A=!0}){const N=h.useMemo(()=>e.filter(S=>!S.hidden),[e]),D=h.useMemo(()=>Math.ceil(k/$),[k,$]),C=h.useMemo(()=>{if(!E)return r;const S=(j-1)*$,U=S+$;return k>0&&r.length<=$?r:r.slice(S,U)},[r,E,j,$,k]),X=S=>{if(!v)return;v(S,w===S&&y==="asc"?"desc":"asc")},Y=S=>{S<1||S>D||!T||T(S)};return i.jsxs("div",{style:{position:"relative"},children:[o&&i.jsx(Ho,{children:i.jsx(Yo,{})}),i.jsx(Mo,{height:u,scrollable:A,children:i.jsxs($o,{bordered:t,striped:s,compact:c,className:F,children:[i.jsx(Bo,{stickyHeader:d,children:i.jsx(No,{children:N.map(S=>i.jsxs(Lo,{sortable:S.sortable,isSorted:w===S.id,align:S.align,width:S.width,onClick:()=>S.sortable&&X(S.id),children:[S.header,S.sortable&&i.jsx(Oo,{direction:w===S.id?y:void 0})]},S.id))})}),i.jsx(Ro,{children:C.length>0?C.map((S,U)=>i.jsx(Ao,{hoverable:l,striped:s,isSelected:p?p(S,U):!1,isClickable:!!f,onClick:()=>f&&f(S,U),children:N.map(G=>i.jsx(Fo,{align:G.align,children:G.cell(S,U)},G.id))},U)):i.jsx("tr",{children:i.jsx("td",{colSpan:N.length,children:i.jsx(_o,{children:q})})})})]})}),E&&D>0&&i.jsxs(zo,{children:[i.jsxs(qo,{children:["Showing ",Math.min((j-1)*$+1,k)," to"," ",Math.min(j*$,k)," of ",k," entries"]}),i.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[P&&i.jsxs(Wo,{children:[i.jsx("span",{children:"Show"}),i.jsx("select",{value:$,onChange:S=>P(Number(S.target.value)),style:{padding:"4px 8px",borderRadius:"4px",border:"1px solid #ccc"},children:[10,25,50,100].map(S=>i.jsx("option",{value:S,children:S},S))}),i.jsx("span",{children:"entries"})]}),i.jsxs(Uo,{children:[i.jsx(oe,{size:"small",variant:"outline",onClick:()=>Y(1),disabled:j===1,children:"First"}),i.jsx(oe,{size:"small",variant:"outline",onClick:()=>Y(j-1),disabled:j===1,children:"Prev"}),i.jsx(oe,{size:"small",variant:"outline",onClick:()=>Y(j+1),disabled:j===D,children:"Next"}),i.jsx(oe,{size:"small",variant:"outline",onClick:()=>Y(D),disabled:j===D,children:"Last"})]})]})]})]})}const Vo=a.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Jo=({title:e,children:r,isLoading:o=!1,hasError:t=!1,errorMessage:s="An error occurred while loading data",showRetry:l=!0,onRetry:c,isEmpty:d=!1,emptyMessage:u="No data available",emptyActionText:f,onEmptyAction:p,actionButton:v,className:w,...y})=>{const E=i.jsx(Vo,{children:v});let j;return o?j=i.jsx(lr,{variant:"card",text:"Loading data..."}):t?j=i.jsx(Le,{title:"Error",description:s,variant:"compact",actionText:l?"Retry":void 0,onAction:l?c:void 0}):d?j=i.jsx(Le,{title:"No Data",description:u,variant:"compact",actionText:f,onAction:p}):j=r,i.jsx(dr,{title:e,actions:E,className:w,...y,children:j})},Ko=a.div.withConfig({displayName:"Container",componentId:"sc-djltr5-0"})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:",";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns "," ease;"],({sidebarCollapsed:e})=>e?"auto 1fr":"240px 1fr",({theme:e})=>e.transitions.normal),Xo=a.header.withConfig({displayName:"HeaderContainer",componentId:"sc-djltr5-1"})(["grid-area:header;background-color:",";border-bottom:1px solid ",";padding:",";z-index:",";"],({theme:e})=>e.colors.headerBackground,({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md,({theme:e})=>e.zIndex.fixed),Qo=a.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-djltr5-2"})(["grid-area:sidebar;background-color:",";border-right:1px solid ",";overflow-y:auto;transition:width "," ease;width:",";"],({theme:e})=>e.colors.sidebarBackground,({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal,({collapsed:e})=>e?"60px":"240px"),Zo=a.main.withConfig({displayName:"ContentContainer",componentId:"sc-djltr5-3"})(["grid-area:content;overflow-y:auto;padding:",";background-color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background),en=({header:e,sidebar:r,children:o,sidebarCollapsed:t=!1,className:s})=>i.jsxs(Ko,{sidebarCollapsed:t,className:s,children:[i.jsx(Xo,{children:e}),i.jsx(Qo,{collapsed:t,children:r}),i.jsx(Zo,{children:o})]});function rn(e,r={}){const{fetchOnMount:o=!0,dependencies:t=[]}=r,[s,l]=h.useState({data:null,isLoading:!1,error:null,isInitialized:!1}),c=h.useCallback(async(...d)=>{l(u=>({...u,isLoading:!0,error:null}));try{const u=await e(...d);return l({data:u,isLoading:!1,error:null,isInitialized:!0}),u}catch(u){const f=u instanceof Error?u:new Error(String(u));throw l(p=>({...p,isLoading:!1,error:f,isInitialized:!0})),f}},[e]);return h.useEffect(()=>{o&&c()},[o,c,...t]),{...s,fetchData:c,refetch:()=>c()}}function tn(e,r){const[o,t]=h.useState(e);return h.useEffect(()=>{const s=setTimeout(()=>{t(e)},r);return()=>{clearTimeout(s)}},[e,r]),o}function on(e={}){const{componentName:r,logToConsole:o=!0,reportToMonitoring:t=!0,onError:s}=e,[l,c]=h.useState(null),[d,u]=h.useState(!1),f=h.useCallback(w=>{if(c(w),u(!0),o){const y=r?`[${r}]`:"";console.error(`Error caught by useErrorHandler${y}:`,w)}s&&s(w)},[r,o,t,s]),p=h.useCallback(()=>{c(null),u(!1)},[]),v=h.useCallback(async w=>{try{return await w()}catch(y){f(y);return}},[f]);return h.useEffect(()=>()=>{c(null),u(!1)},[]),{error:l,hasError:d,handleError:f,resetError:p,tryExecute:v}}function Re(e,r){const o=()=>{if(typeof window>"u")return r;try{const c=window.localStorage.getItem(e);return c?JSON.parse(c):r}catch(c){return console.warn(`Error reading localStorage key "${e}":`,c),r}},[t,s]=h.useState(o),l=c=>{try{const d=c instanceof Function?c(t):c;s(d),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(d))}catch(d){console.warn(`Error setting localStorage key "${e}":`,d)}};return h.useEffect(()=>{const c=d=>{d.key===e&&d.newValue&&s(JSON.parse(d.newValue))};return window.addEventListener("storage",c),()=>window.removeEventListener("storage",c)},[e]),[t,l]}function nn(e){const{totalItems:r,itemsPerPage:o=10,initialPage:t=1,persistKey:s}=e,[l,c]=s?Re(`${s}_page`,t):h.useState(t),[d,u]=s?Re(`${s}_itemsPerPage`,o):h.useState(o),f=h.useMemo(()=>Math.max(1,Math.ceil(r/d)),[r,d]),p=h.useMemo(()=>Math.min(Math.max(1,l),f),[l,f]);p!==l&&c(p);const v=(p-1)*d,w=Math.min(v+d-1,r-1),y=p>1,E=p<f,j=h.useMemo(()=>{const q=[];if(f<=5)for(let A=1;A<=f;A++)q.push(A);else{let A=Math.max(1,p-Math.floor(2.5));const N=Math.min(f,A+5-1);N===f&&(A=Math.max(1,N-5+1));for(let D=A;D<=N;D++)q.push(D)}return q},[p,f]),$=h.useCallback(()=>{E&&c(p+1)},[E,p,c]),k=h.useCallback(()=>{y&&c(p-1)},[y,p,c]),T=h.useCallback(F=>{const q=Math.min(Math.max(1,F),f);c(q)},[f,c]),P=h.useCallback(F=>{u(F),c(1)},[u,c]);return{currentPage:p,itemsPerPage:d,totalPages:f,hasPreviousPage:y,hasNextPage:E,startIndex:v,endIndex:w,pageRange:j,nextPage:$,previousPage:k,goToPage:T,setItemsPerPage:P}}const x={f1Red:"#e10600",f1RedDark:"#c10500",f1RedLight:"#ff3b36",f1Blue:"#1e5bc6",f1BlueDark:"#1a4da8",f1BlueLight:"#4a7dd8",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},O={background:"#1a1f2c",surface:"#252a37",cardBackground:"#252a37",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:x.green,warning:x.yellow,error:x.red,info:x.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:x.f1Red,profit:x.green,loss:x.red,neutral:x.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},z={background:"#f5f5f5",surface:"#ffffff",cardBackground:"#ffffff",border:"#e0e0e0",divider:"rgba(0, 0, 0, 0.1)",textPrimary:"#333333",textSecondary:"#666666",textDisabled:"#999999",textInverse:"#ffffff",success:x.green,warning:x.yellow,error:x.red,info:x.blue,chartGrid:"rgba(0, 0, 0, 0.1)",chartLine:x.f1Red,profit:x.green,loss:x.red,neutral:x.gray400,tooltipBackground:"rgba(255, 255, 255, 0.9)",modalBackground:"rgba(255, 255, 255, 0.8)"},he={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},me={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},be={light:300,regular:400,medium:500,semibold:600,bold:700},xe={tight:1.25,normal:1.5,relaxed:1.75},ve={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},ye={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},we={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},ke={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},Ce={fast:"0.1s",normal:"0.3s",slow:"0.5s"},Se={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},Ae={name:"f1",colors:{primary:x.f1Red,primaryDark:x.f1RedDark,primaryLight:x.f1RedLight,secondary:x.f1Blue,secondaryDark:x.f1BlueDark,secondaryLight:x.f1BlueLight,accent:x.purple,accentDark:x.purpleDark,accentLight:x.purpleLight,success:O.success,warning:O.warning,error:O.error,info:O.info,background:O.background,surface:O.surface,cardBackground:O.surface,border:O.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:O.textPrimary,textSecondary:O.textSecondary,textDisabled:O.textDisabled,textInverse:O.textInverse,chartGrid:O.chartGrid,chartLine:O.chartLine,chartAxis:x.gray400,chartTooltip:O.tooltipBackground,profit:O.profit,loss:O.loss,neutral:O.neutral,tabActive:x.f1Red,tabInactive:x.gray600,tooltipBackground:O.tooltipBackground,modalBackground:O.modalBackground,sidebarBackground:x.gray800,headerBackground:"rgba(0, 0, 0, 0.2)"},spacing:he,breakpoints:ye,fontSizes:me,fontWeights:be,lineHeights:xe,fontFamilies:ve,borderRadius:we,shadows:ke,transitions:Ce,zIndex:Se},xr={name:"light",colors:{primary:x.f1Red,primaryDark:x.f1RedDark,primaryLight:x.f1RedLight,secondary:x.f1Blue,secondaryDark:x.f1BlueDark,secondaryLight:x.f1BlueLight,accent:x.purple,accentDark:x.purpleDark,accentLight:x.purpleLight,success:z.success,warning:z.warning,error:z.error,info:z.info,background:z.background,surface:z.surface,cardBackground:z.surface,border:z.border,divider:x.blackTransparent10,textPrimary:z.textPrimary,textSecondary:z.textSecondary,textDisabled:z.textDisabled,textInverse:z.textInverse,chartGrid:z.chartGrid,chartLine:z.chartLine,chartAxis:x.gray600,chartTooltip:z.tooltipBackground,profit:z.profit,loss:z.loss,neutral:z.neutral,tabActive:x.f1Red,tabInactive:x.gray400,tooltipBackground:z.tooltipBackground,modalBackground:z.modalBackground,sidebarBackground:x.white,headerBackground:"rgba(0, 0, 0, 0.05)"},spacing:he,breakpoints:ye,fontSizes:me,fontWeights:be,lineHeights:xe,fontFamilies:ve,borderRadius:we,shadows:ke,transitions:Ce,zIndex:Se},vr={name:"dark",colors:{primary:x.f1Blue,primaryDark:x.f1BlueDark,primaryLight:x.f1BlueLight,secondary:x.f1Blue,secondaryDark:x.f1BlueDark,secondaryLight:x.f1BlueLight,accent:x.purple,accentDark:x.purpleDark,accentLight:x.purpleLight,success:O.success,warning:O.warning,error:O.error,info:O.info,background:x.gray900,surface:x.gray800,cardBackground:x.gray800,border:x.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:x.white,textSecondary:x.gray300,textDisabled:x.gray500,textInverse:x.gray900,chartGrid:O.chartGrid,chartLine:x.f1Blue,chartAxis:x.gray400,chartTooltip:O.tooltipBackground,profit:O.profit,loss:O.loss,neutral:O.neutral,tabActive:x.f1Blue,tabInactive:x.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:x.gray900,headerBackground:"rgba(0, 0, 0, 0.3)"},spacing:he,breakpoints:ye,fontSizes:me,fontWeights:be,lineHeights:xe,fontFamilies:ve,borderRadius:we,shadows:ke,transitions:Ce,zIndex:Se},an=a.createGlobalStyle(["@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),sn={f1:Ae,light:xr,dark:vr},Fe=Ae,_e=e=>sn[e]||Fe,ze=h.createContext({theme:Fe,setTheme:()=>{}}),cn=()=>h.useContext(ze),ln=({initialTheme:e=Fe,persistTheme:r=!0,storageKey:o="adhd-dashboard-theme",children:t})=>{const[s,l]=h.useState(()=>{if(r&&typeof window<"u"){const u=window.localStorage.getItem(o);if(u)try{const f=_e(u);return f||JSON.parse(u)}catch(f){console.error("Failed to parse stored theme:",f)}}return typeof e=="string"?_e(e):e}),c=u=>{const f=typeof u=="string"?_e(u):u;l(f),r&&typeof window<"u"&&window.localStorage.setItem(o,f.name||JSON.stringify(f))},d=({children:u})=>i.jsxs(a.ThemeProvider,{theme:s,children:[i.jsx(an,{}),u]});return i.jsx(ze.Provider,{value:{theme:s,setTheme:c},children:i.jsx(d,{children:t})})};function un(e,r,o="StoreContext"){const t=h.createContext(void 0);t.displayName=o;const s=({children:f,initialState:p})=>{const[v,w]=h.useReducer(e,p||r),y=h.useMemo(()=>({state:v,dispatch:w}),[v]);return i.jsx(t.Provider,{value:y,children:f})};function l(){const f=h.useContext(t);if(f===void 0)throw new Error(`use${o} must be used within a ${o}Provider`);return f}function c(f){const{state:p}=l();return f(p)}function d(f){const{dispatch:p}=l();return h.useMemo(()=>(...v)=>{p(f(...v))},[p,f])}function u(f){const{dispatch:p}=l();return h.useMemo(()=>{const v={};for(const w in f)v[w]=(...y)=>{p(f[w](...y))};return v},[p,f])}return{Context:t,Provider:s,useStore:l,useSelector:c,useAction:d,useActions:u}}function dn(...e){const r=e.pop(),o=e;let t=null,s=null;return l=>{const c=o.map(d=>d(l));return(t===null||c.length!==t.length||c.some((d,u)=>d!==t[u]))&&(s=r(...c),t=c),s}}function fn(e,r){const{key:o,initialState:t,version:s=1,migrate:l,serialize:c=JSON.stringify,deserialize:d=JSON.parse,filter:u=k=>k,merge:f=(k,T)=>({...T,...k}),debug:p=!1}=r,v=()=>{try{const k=localStorage.getItem(o);if(k===null)return null;const{state:T,version:P}=d(k);return P!==s&&l?(p&&console.log(`Migrating state from version ${P} to ${s}`),l(T,P)):T}catch(k){return p&&console.error("Error loading state from local storage:",k),null}},w=k=>{try{const T=u(k),P=c({state:T,version:s});localStorage.setItem(o,P)}catch(T){p&&console.error("Error saving state to local storage:",T)}},y=()=>{try{localStorage.removeItem(o)}catch(k){p&&console.error("Error clearing state from local storage:",k)}},E=v(),j=E?f(E,t):t;return p&&E&&(console.log("Loaded persisted state:",E),console.log("Merged initial state:",j)),{reducer:(k,T)=>{const P=e(k,T);return w(P),P},initialState:j,clear:y}}function pn(e,r="$"){return`${r}${e.toFixed(2)}`}function gn(e,r=1){return`${(e*100).toFixed(r)}%`}function hn(e,r="short"){const o=typeof e=="string"?new Date(e):e;switch(r){case"medium":return o.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});case"long":return o.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});case"short":default:return o.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"})}}function mn(e,r=50){return e.length<=r?e:`${e.substring(0,r-3)}...`}function bn(){return Math.random().toString(36).substring(2,9)}function xn(e,r){let o=null;return function(...t){const s=()=>{o=null,e(...t)};o&&clearTimeout(o),o=setTimeout(s,r)}}function vn(e,r){let o=!1;return function(...t){o||(e(...t),o=!0,setTimeout(()=>{o=!1},r))}}m.ApiClient=ar,m.ApiContext=Be,m.ApiProvider=Yr,m.AppErrorBoundary=ho,m.Badge=at,m.Button=oe,m.Card=dr,m.DashboardTemplate=en,m.DataCard=Jo,m.EmptyState=Le,m.ErrorBoundary=br,m.FeatureErrorBoundary=mo,m.FormField=yo,m.Input=xt,m.LoadingPlaceholder=lr,m.Modal=Po,m.Select=Mt,m.StatusIndicator=Rt,m.Table=Go,m.Tag=qt,m.ThemeContext=ze,m.ThemeProvider=ln,m.TradingApiClient=$e,m.UnifiedErrorBoundary=Oe,m.apiClient=Qr,m.baseColors=x,m.borderRadius=we,m.breakpoints=ye,m.captureError=Me,m.createSelector=dn,m.createStoreContext=un,m.darkModeColors=O,m.darkTheme=vr,m.debounce=xn,m.f1Theme=Ae,m.fontFamilies=ve,m.fontSizes=me,m.fontWeights=be,m.formatCurrency=pn,m.formatDate=hn,m.formatPercentage=gn,m.gasApi=Xe,m.generateId=bn,m.initMonitoring=Fr,m.lightModeColors=z,m.lightTheme=xr,m.lineHeights=xe,m.mockApi=Qe,m.persistState=fn,m.setUser=_r,m.shadows=ke,m.spacing=he,m.startTransaction=zr,m.throttle=vn,m.transitions=Ce,m.truncateText=mn,m.useApi=Ze,m.useApiMutation=er,m.useApiQuery=le,m.useAsyncData=rn,m.useDashboardData=Lr,m.useDebounce=tn,m.useErrorHandler=on,m.useLocalStorage=Re,m.useMarketNews=Jr,m.usePagination=nn,m.usePerformanceMetrics=Vr,m.useTheme=cn,m.useTradingData=Gr,m.useUserPreferences=Kr,m.zIndex=Se,Object.defineProperty(m,Symbol.toStringTag,{value:"Module"})});
