import '@testing-library/jest-dom';
import { vi } from 'vitest';
import React from 'react';
import { f1Theme } from './packages/shared/src/theme/f1Theme';

// Create a mock theme provider that actually provides the theme
vi.mock('styled-components', async () => {
  const actual = await vi.importActual('styled-components');
  return {
    ...actual,
    ThemeProvider: ({ children, theme = f1Theme }: { children: React.ReactNode; theme?: any }) => {
      return React.createElement(actual.ThemeProvider, { theme }, children);
    },
  };
});

// Global test setup
beforeAll(() => {
  // Setup global test environment
  console.log('Setting up test environment');
});

afterAll(() => {
  // Clean up global test environment
  console.log('Cleaning up test environment');
});

// Add custom matchers
expect.extend({
  toHaveStyleRule(received, property, value) {
    try {
      const styles = window.getComputedStyle(received);
      const pass = styles[property as any] === value;
      if (pass) {
        return {
          message: () => `expected ${received} not to have CSS property "${property}: ${value}"`,
          pass: true,
        };
      } else {
        return {
          message: () =>
            `expected ${received} to have CSS property "${property}: ${value}", instead received "${
              styles[property as any]
            }"`,
          pass: false,
        };
      }
    } catch (err) {
      return {
        message: () =>
          `expected ${received} to have CSS property "${property}: ${value}", but failed with error: ${err}`,
        pass: false,
      };
    }
  },
});
