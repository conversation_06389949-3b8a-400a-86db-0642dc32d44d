<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ADHD Trading Dashboard</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Roboto', Arial, sans-serif;
      background-color: #1a1f2c;
      color: #ffffff;
    }
  </style>
  <script>
    // Mock Google Apps Script environment for local development
    window.google = {
      script: {
        host: {
          origin: 'https://docs.google.com',
          closeDialog: function() {
            console.log('Dialog closed');
          }
        },
        run: {
          withSuccessHandler: function(callback) {
            this.successCallback = callback;
            return this;
          },
          withFailureHandler: function(callback) {
            this.failureCallback = callback;
            return this;
          },
          // Mock API methods
          getConfiguration: function() {
            console.log('Mock: getConfiguration called');
            setTimeout(() => {
              this.successCallback({
                theme: {
                  colors: {
                    primary: '#e10600', // F1 red
                    background: '#1a1f2c',
                    cardBackground: '#252a37',
                    text: '#ffffff',
                    secondaryText: '#aaaaaa',
                    positive: '#4caf50',
                    negative: '#f44336',
                    neutral: '#9e9e9e',
                    border: '#333333',
                    tabActive: '#e10600',
                    tabInactive: '#555555',
                    chartGrid: '#333333',
                    chartLine: '#e10600',
                    tooltipBackground: 'rgba(37, 42, 55, 0.9)'
                  }
                },
                features: {
                  performanceChart: true,
                  newsEvents: true,
                  recentTrades: true
                }
              });
            }, 500);
          },
          getDashboardData: function() {
            console.log('Mock: getDashboardData called');
            setTimeout(() => {
              this.successCallback({
                summary: {
                  totalTrades: 120,
                  winRate: 0.65,
                  profitFactor: 2.3,
                  netProfit: 12500
                },
                recentTrades: [
                  { id: 1, date: '2025-01-15', symbol: 'AAPL', result: 'win', profit: 350 },
                  { id: 2, date: '2025-01-14', symbol: 'MSFT', result: 'loss', profit: -150 },
                  { id: 3, date: '2025-01-13', symbol: 'GOOGL', result: 'win', profit: 420 }
                ],
                performance: {
                  daily: [
                    { date: '2025-01-11', value: 10200 },
                    { date: '2025-01-12', value: 10500 },
                    { date: '2025-01-13', value: 11200 },
                    { date: '2025-01-14', value: 11000 },
                    { date: '2025-01-15', value: 12500 }
                  ]
                },
                news: [
                  { id: 1, time: '09:30', event: 'Market Open', impact: 'Low' },
                  { id: 2, time: '10:00', event: 'CPI Data Release', impact: 'High' },
                  { id: 3, time: '14:00', event: 'FOMC Minutes', impact: 'High' }
                ]
              });
            }, 800);
          }
        }
      }
    };

    // Initialize dashboard configuration
    window.DASHBOARD_CONFIG = {
      theme: {
        colors: {
          primary: '#e10600', // F1 red
          background: '#1a1f2c',
          cardBackground: '#252a37',
          text: '#ffffff'
        }
      }
    };

    // Set the public path for chunk loading
    window.publicPath = '/';

    // Initialize ADHDTradingDashboard namespace
    window.ADHDTradingDashboard = {
      version: '1.0.0',
      initialized: false,
      config: window.DASHBOARD_CONFIG,
      log: function() {
        console.log('[ADHDTradingDashboard]', ...arguments);
      },
      error: function() {
        console.error('[ADHDTradingDashboard]', ...arguments);
      }
    };
  </script>
</head>
<body>
  <div id="root"></div>
</body>
</html>
