import { test, expect } from '@playwright/test';

test.describe('Trade Analysis Feature', () => {
  test('should display trade analysis page with all sections', async ({ page }) => {
    // Navigate to the trade analysis page
    await page.goto('/trade-analysis');
    
    // Check that the page title is displayed
    await expect(page.locator('h1')).toContainText('Trade Analysis');
    
    // Check that the filter panel is displayed
    await expect(page.getByText('Filters')).toBeVisible();
    
    // Check that the summary view is displayed by default
    await expect(page.getByText('Performance Summary')).toBeVisible();
    await expect(page.getByText('Performance by Time of Day')).toBeVisible();
    await expect(page.getByText('Performance by Day of Week')).toBeVisible();
    
    // Navigate to different views
    await page.getByRole('button', { name: 'Trades' }).click();
    await expect(page.getByText('Symbol')).toBeVisible();
    await expect(page.getByText('Direction')).toBeVisible();
    
    await page.getByRole('button', { name: 'Symbols' }).click();
    await expect(page.getByText('Performance by Symbol')).toBeVisible();
    
    await page.getByRole('button', { name: 'Strategies' }).click();
    await expect(page.getByText('Performance by Strategy')).toBeVisible();
    
    await page.getByRole('button', { name: 'Timeframes' }).click();
    await expect(page.getByText('Performance by Timeframe')).toBeVisible();
    await expect(page.getByText('Performance by Session')).toBeVisible();
    
    await page.getByRole('button', { name: 'Time Analysis' }).click();
    await expect(page.getByText('Performance by Time of Day')).toBeVisible();
    await expect(page.getByText('Performance by Day of Week')).toBeVisible();
  });
  
  test('should allow filtering of trades', async ({ page }) => {
    // Navigate to the trade analysis page
    await page.goto('/trade-analysis');
    
    // Open the date picker and select a date range
    const startDateInput = page.locator('input[type="date"]').first();
    const endDateInput = page.locator('input[type="date"]').nth(1);
    
    // Set date range to last month
    const today = new Date();
    const lastMonth = new Date(today);
    lastMonth.setMonth(today.getMonth() - 1);
    
    const startDate = lastMonth.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const endDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    
    await startDateInput.fill(startDate);
    await endDateInput.fill(endDate);
    
    // Apply filters
    await page.getByRole('button', { name: 'Apply Filters' }).click();
    
    // Wait for data to reload
    await page.waitForTimeout(1000);
    
    // Reset filters
    await page.getByRole('button', { name: 'Reset' }).click();
    
    // Wait for data to reload
    await page.waitForTimeout(1000);
  });
  
  test('should display trade details when a trade is selected', async ({ page }) => {
    // Navigate to the trade analysis page
    await page.goto('/trade-analysis');
    
    // Switch to trades view
    await page.getByRole('button', { name: 'Trades' }).click();
    
    // Wait for trades to load
    await page.waitForTimeout(1000);
    
    // Click on the first trade row
    await page.locator('tr').nth(1).click();
    
    // Check that trade details are displayed
    await expect(page.getByText('Trade Details')).toBeVisible();
    
    // Click on the same trade row again to deselect
    await page.locator('tr').nth(1).click();
    
    // Check that trade details are no longer displayed
    await expect(page.getByText('Trade Details')).not.toBeVisible();
  });
});
