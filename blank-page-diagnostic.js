#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class BlankPageDiagnostic {
  constructor(rootDir = '.') {
    this.rootDir = path.resolve(rootDir);
    this.issues = [];
    this.warnings = [];
    this.fixes = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix =
      {
        error: '🚨 ERROR',
        warning: '⚠️  WARNING',
        success: '✅ SUCCESS',
        info: '🔍 INFO',
        fix: '🔧 FIX',
      }[type] || '📋';

    console.log(`[${timestamp}] ${prefix}: ${message}`);
  }

  // 1. Check for JavaScript/TypeScript compilation errors
  async checkCompilationErrors() {
    this.log('Checking for compilation errors...', 'info');

    try {
      // Check for TypeScript config
      const tsConfigPath = path.join(this.rootDir, 'tsconfig.json');
      if (fs.existsSync(tsConfigPath)) {
        const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));

        // Check for strict mode issues
        if (tsConfig.compilerOptions?.strict) {
          this.warnings.push('TypeScript strict mode enabled - check for type errors');
        }
      }

      // Look for common TypeScript errors in recent files
      const recentFiles = await this.findRecentlyModifiedFiles();
      for (const file of recentFiles) {
        if (file.endsWith('.ts') || file.endsWith('.tsx')) {
          await this.checkFileForTSErrors(file);
        }
      }
    } catch (error) {
      this.issues.push(`TypeScript config error: ${error.message}`);
    }
  }

  // 2. Check for import/export issues
  async checkImportExportIssues() {
    this.log('Scanning for import/export issues...', 'info');

    const sourceFiles = await this.findSourceFiles();
    const importMap = new Map();
    const exportMap = new Map();

    for (const file of sourceFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');

        // Find imports
        const imports = this.extractImports(content, file);
        imports.forEach((imp) => {
          if (!importMap.has(imp.from)) importMap.set(imp.from, []);
          importMap.get(imp.from).push({ file, import: imp });
        });

        // Find exports
        const exports = this.extractExports(content, file);
        exports.forEach((exp) => exportMap.set(file, exp));

        // Check for specific patterns that cause blank pages
        await this.checkFileForBlankPagePatterns(file, content);
      } catch (error) {
        this.issues.push(`Error reading file ${file}: ${error.message}`);
      }
    }

    // Validate imports against exports
    this.validateImportsAgainstExports(importMap, exportMap);
  }

  // 3. Check for React-specific issues
  async checkReactIssues() {
    this.log('Checking React-specific issues...', 'info');

    // Check main entry points
    await this.checkMainEntryPoint();
    await this.checkRootComponent();
    await this.checkRouterSetup();
    await this.checkProviderSetup();
  }

  // 4. Check for console errors and runtime issues
  async checkRuntimeIssues() {
    this.log('Checking for runtime issue patterns...', 'info');

    const sourceFiles = await this.findSourceFiles();

    for (const file of sourceFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');

        // Check for problematic patterns
        this.checkForProblematicPatterns(file, content);

        // Check for missing dependencies
        await this.checkMissingDependencies(file, content);
      } catch (error) {
        this.issues.push(`Error analyzing ${file}: ${error.message}`);
      }
    }
  }

  // 5. Check build configuration
  async checkBuildConfig() {
    this.log('Checking build configuration...', 'info');

    // Check package.json
    await this.checkPackageJson();

    // Check for Vite config (since you're using Vite)
    await this.checkViteConfig();

    // Check for environment variables
    await this.checkEnvironmentVariables();
  }

  // Helper methods
  async findRecentlyModifiedFiles() {
    const files = await this.findSourceFiles();
    const now = Date.now();
    const hourAgo = now - 2 * 60 * 60 * 1000; // 2 hours ago

    return files.filter((file) => {
      try {
        const stats = fs.statSync(file);
        return stats.mtime.getTime() > hourAgo;
      } catch {
        return false;
      }
    });
  }

  async findSourceFiles() {
    const files = [];
    const extensions = ['.js', '.jsx', '.ts', '.tsx'];

    const walk = (dir) => {
      if (
        dir.includes('node_modules') ||
        dir.includes('.git') ||
        dir.includes('dist') ||
        dir.includes('build')
      ) {
        return;
      }

      try {
        const items = fs.readdirSync(dir);
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory()) {
            walk(fullPath);
          } else if (extensions.some((ext) => item.endsWith(ext))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip inaccessible directories
      }
    };

    walk(this.rootDir);
    return files;
  }

  extractImports(content, file) {
    const imports = [];

    // ES6 imports
    const importRegex =
      /import\s+(?:(?:(?:[\w*\s{},]*)\s+from\s+)?["']([^"']+)["']|["']([^"']+)["'])/g;
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1] || match[2];
      if (importPath) {
        imports.push({
          from: importPath,
          line: content.substring(0, match.index).split('\n').length,
          raw: match[0],
        });
      }
    }

    // Dynamic imports
    const dynamicImportRegex = /import\s*\(\s*["']([^"']+)["']\s*\)/g;
    while ((match = dynamicImportRegex.exec(content)) !== null) {
      imports.push({
        from: match[1],
        line: content.substring(0, match.index).split('\n').length,
        raw: match[0],
        dynamic: true,
      });
    }

    return imports;
  }

  extractExports(content, file) {
    const exports = {
      default: null,
      named: [],
    };

    // Default exports
    const defaultExportRegex = /export\s+default\s+(.*)/g;
    const defaultMatch = defaultExportRegex.exec(content);
    if (defaultMatch) {
      exports.default = defaultMatch[1].trim();
    }

    // Named exports
    const namedExportRegex = /export\s+(?:const|let|var|function|class)\s+(\w+)/g;
    let match;
    while ((match = namedExportRegex.exec(content)) !== null) {
      exports.named.push(match[1]);
    }

    // Export declarations
    const exportDeclRegex = /export\s*\{\s*([^}]+)\s*\}/g;
    while ((match = exportDeclRegex.exec(content)) !== null) {
      const exportNames = match[1].split(',').map((name) => name.trim().split(' as ')[0]);
      exports.named.push(...exportNames);
    }

    return exports;
  }

  async checkFileForBlankPagePatterns(file, content) {
    // Check for missing React import in JSX files
    if ((file.endsWith('.jsx') || file.endsWith('.tsx')) && content.includes('<')) {
      if (!content.includes('import React') && !content.includes('import * as React')) {
        this.issues.push(`Missing React import in ${file} - may cause blank page`);
        this.fixes.push(`Add: import React from 'react'; to ${file}`);
      }
    }

    // Check for missing default export in component files
    if (file.includes('component') || file.includes('Component')) {
      if (!content.includes('export default')) {
        this.warnings.push(`Component file ${file} missing default export`);
      }
    }

    // Check for circular imports
    const imports = this.extractImports(content, file);
    for (const imp of imports) {
      if (imp.from.includes(path.basename(file, path.extname(file)))) {
        this.issues.push(`Potential circular import in ${file}: ${imp.from}`);
      }
    }

    // Check for unhandled promise rejections
    if (content.includes('await ') && !content.includes('try') && !content.includes('catch')) {
      this.warnings.push(`Unhandled async operation in ${file} - could cause crashes`);
    }
  }

  checkForProblematicPatterns(file, content) {
    // Check for console.error or throw statements
    if (content.includes('throw new Error') || content.includes('console.error')) {
      this.warnings.push(`Error throwing code found in ${file}`);
    }

    // Check for incorrect hook usage
    if (content.includes('useState') || content.includes('useEffect')) {
      const lines = content.split('\n');
      lines.forEach((line, index) => {
        if (
          line.trim().startsWith('if') &&
          (line.includes('useState') || line.includes('useEffect'))
        ) {
          this.issues.push(`Conditional hook usage at ${file}:${index + 1} - will cause crashes`);
        }
      });
    }

    // Check for missing error boundaries
    if (content.includes('componentDidCatch') || content.includes('ErrorBoundary')) {
      this.log(`Error boundary found in ${file}`, 'success');
    }
  }

  async checkMissingDependencies(file, content) {
    try {
      const packageJsonPath = path.join(this.rootDir, 'package.json');
      if (!fs.existsSync(packageJsonPath)) return;

      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
      };

      const imports = this.extractImports(content, file);

      for (const imp of imports) {
        if (!imp.from.startsWith('.') && !imp.from.startsWith('/')) {
          const packageName = imp.from.startsWith('@')
            ? imp.from.split('/').slice(0, 2).join('/')
            : imp.from.split('/')[0];

          if (!allDeps[packageName]) {
            this.issues.push(`Missing dependency: ${packageName} (imported in ${file})`);
            this.fixes.push(`Run: yarn add ${packageName}`);
          }
        }
      }
    } catch (error) {
      this.warnings.push(`Could not check dependencies: ${error.message}`);
    }
  }

  async checkMainEntryPoint() {
    // Check for main.tsx or index.tsx
    const possibleEntries = [
      'main.tsx',
      'index.tsx',
      'src/main.tsx',
      'src/index.tsx',
      'packages/dashboard/src/main.tsx',
      'packages/dashboard/src/index.tsx',
    ];

    let foundEntry = false;
    for (const entry of possibleEntries) {
      const entryPath = path.join(this.rootDir, entry);
      if (fs.existsSync(entryPath)) {
        foundEntry = true;
        const content = fs.readFileSync(entryPath, 'utf8');

        // Check if it renders something
        if (!content.includes('render') && !content.includes('createRoot')) {
          this.issues.push(`Entry point ${entry} doesn't render anything`);
        }

        // Check for StrictMode or proper React setup
        if (!content.includes('React') && !content.includes('createRoot')) {
          this.issues.push(`Entry point ${entry} missing React setup`);
        }

        // Check for App component import
        if (!content.includes('App') && !content.includes('./App')) {
          this.issues.push(`Entry point ${entry} doesn't import App component`);
        }

        this.log(`Found entry point: ${entry}`, 'success');
        break;
      }
    }

    if (!foundEntry) {
      this.issues.push('No main entry point found (main.tsx, index.tsx)');
      this.fixes.push(
        'Create src/main.tsx or packages/dashboard/src/main.tsx with React.render() call'
      );
    }
  }

  async checkRootComponent() {
    // Look for App.tsx or similar root component
    const possibleRoots = [
      'App.tsx',
      'src/App.tsx',
      'packages/dashboard/src/App.tsx',
      'App.jsx',
      'src/App.jsx',
      'packages/dashboard/src/App.jsx',
    ];

    let foundRoot = false;
    for (const rootPath of possibleRoots) {
      const fullPath = path.join(this.rootDir, rootPath);
      if (fs.existsSync(fullPath)) {
        foundRoot = true;
        const content = fs.readFileSync(fullPath, 'utf8');

        // Check if it returns JSX
        if (!content.includes('return') || !content.includes('<')) {
          this.issues.push(`Root component ${rootPath} doesn't return JSX`);
        }

        // Check for empty component
        if (content.includes('return </>') || content.includes('return null')) {
          this.issues.push(
            `Root component ${rootPath} returns empty content - THIS IS LIKELY YOUR BLANK PAGE CAUSE`
          );
        }

        // Check for missing function/component definition
        if (!content.includes('function') && !content.includes('const') && !content.includes('=')) {
          this.issues.push(`Root component ${rootPath} has no component definition`);
        }

        this.log(`Found root component: ${rootPath}`, 'success');
        break;
      }
    }

    if (!foundRoot) {
      this.issues.push('No root App component found - THIS IS LIKELY YOUR BLANK PAGE CAUSE');
      this.fixes.push('Create App.tsx with a proper React component that returns JSX');
    }
  }

  async checkRouterSetup() {
    const sourceFiles = await this.findSourceFiles();

    for (const file of sourceFiles) {
      const content = fs.readFileSync(file, 'utf8');

      if (content.includes('BrowserRouter') || content.includes('Router')) {
        // Check if routes are defined
        if (!content.includes('Route') && !content.includes('Routes')) {
          this.warnings.push(`Router found in ${file} but no routes defined`);
        }

        // Check for empty route paths
        if (content.includes('path=""') || content.includes("path=''")) {
          this.warnings.push(`Empty route path in ${file}`);
        }

        this.log(`Router setup found in ${file}`, 'success');
      }
    }
  }

  async checkProviderSetup() {
    const sourceFiles = await this.findSourceFiles();

    for (const file of sourceFiles) {
      const content = fs.readFileSync(file, 'utf8');

      if (content.includes('Provider') || content.includes('Context')) {
        // Check for provider wrapping
        if (content.includes('Provider') && !content.includes('<') && !content.includes('value=')) {
          this.warnings.push(`Provider in ${file} may not be properly configured`);
        }
      }
    }
  }

  validateImportsAgainstExports(importMap, exportMap) {
    for (const [importPath, importUsages] of importMap) {
      if (importPath.startsWith('.')) {
        // Local import - try to resolve the file
        for (const usage of importUsages) {
          const resolvedPath = this.resolveLocalImport(usage.file, importPath);
          if (resolvedPath && !exportMap.has(resolvedPath)) {
            this.issues.push(
              `Import "${importPath}" in ${usage.file} - target file not found or has no exports`
            );
          }
        }
      }
    }
  }

  resolveLocalImport(fromFile, importPath) {
    const dir = path.dirname(fromFile);
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];

    for (const ext of extensions) {
      const fullPath = path.resolve(dir, importPath + ext);
      if (fs.existsSync(fullPath)) {
        return fullPath;
      }
    }

    // Check for index files
    for (const ext of extensions) {
      const indexPath = path.resolve(dir, importPath, 'index' + ext);
      if (fs.existsSync(indexPath)) {
        return indexPath;
      }
    }

    return null;
  }

  async checkPackageJson() {
    const packageJsonPath = path.join(this.rootDir, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      this.issues.push('package.json not found');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

    // Check scripts
    if (!packageJson.scripts || !packageJson.scripts.dev) {
      this.warnings.push('No dev script found in package.json');
    }

    // Check for React
    if (!packageJson.dependencies?.react) {
      this.issues.push('React not found in dependencies');
    }

    // Check for React DOM
    if (!packageJson.dependencies?.['react-dom']) {
      this.issues.push('React DOM not found in dependencies');
    }

    this.log('package.json dependencies look good', 'success');
  }

  async checkViteConfig() {
    const viteConfigPaths = [
      'vite.config.ts',
      'vite.config.js',
      'packages/dashboard/vite.config.ts',
    ];

    for (const configPath of viteConfigPaths) {
      const fullPath = path.join(this.rootDir, configPath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');

        // Check for proper React plugin
        if (!content.includes('@vitejs/plugin-react')) {
          this.warnings.push('Vite React plugin not found in config');
        }

        this.log(`Vite config found: ${configPath}`, 'success');
        return;
      }
    }

    this.warnings.push('No Vite config found - may be causing build issues');
  }

  async checkEnvironmentVariables() {
    const envFiles = ['.env', '.env.local', '.env.development'];

    for (const envFile of envFiles) {
      const envPath = path.join(this.rootDir, envFile);
      if (fs.existsSync(envPath)) {
        this.log(`Environment file found: ${envFile}`, 'success');
      }
    }
  }

  async checkFileForTSErrors(file) {
    try {
      const content = fs.readFileSync(file, 'utf8');

      // Look for common TypeScript patterns that cause issues
      if (content.includes('any') && content.includes('strict')) {
        this.warnings.push(`File ${file} uses 'any' type with strict mode - may cause issues`);
      }

      // Check for missing type imports
      if (content.includes(': React.') && !content.includes('import React')) {
        this.issues.push(`File ${file} uses React types without importing React`);
      }
    } catch (error) {
      this.issues.push(`Error checking TypeScript in ${file}: ${error.message}`);
    }
  }

  // Main diagnostic runner
  async runDiagnostics() {
    this.log('🚀 Starting React Blank Page Diagnostics...', 'info');
    this.log(`📁 Analyzing: ${this.rootDir}`, 'info');
    this.log('='.repeat(60), 'info');

    await this.checkCompilationErrors();
    await this.checkImportExportIssues();
    await this.checkReactIssues();
    await this.checkRuntimeIssues();
    await this.checkBuildConfig();

    this.generateReport();
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    this.log('📊 DIAGNOSTIC REPORT', 'info');
    console.log('='.repeat(60));

    if (this.issues.length === 0) {
      this.log('🎉 No critical issues found!', 'success');
    } else {
      this.log(`Found ${this.issues.length} critical issues:`, 'error');
      this.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning}`);
      });
    }

    if (this.fixes.length > 0) {
      console.log('\n🔧 SUGGESTED FIXES:');
      this.fixes.forEach((fix, index) => {
        console.log(`   ${index + 1}. ${fix}`);
      });
    }

    console.log('\n' + '='.repeat(60));
    this.log('🎯 PRIORITY ACTIONS:', 'info');

    if (this.issues.length > 0) {
      console.log('1. Fix critical issues first (❌ errors)');
      console.log('2. Address warnings (⚠️  warnings)');
      console.log('3. Test the application');
    }

    console.log('\n🔍 Additional debugging:');
    console.log('   • Check browser console for JavaScript errors');
    console.log('   • Verify dev server is running without errors');
    console.log('   • Test with browser dev tools open');
    console.log('   • Check network tab for failed requests');

    console.log('\n📝 Quick fixes to try:');
    console.log('   • yarn install (reinstall dependencies)');
    console.log('   • rm -rf node_modules && yarn install (clean install)');
    console.log('   • yarn dev --force (force rebuild)');
    console.log('   • Check if running on correct port');
  }
}

// CLI Usage
const targetDir = process.argv[2] || '.';
const diagnostic = new BlankPageDiagnostic(targetDir);

diagnostic.runDiagnostics().catch((error) => {
  console.error('❌ Diagnostic failed:', error.message);
  process.exit(1);
});

export default BlankPageDiagnostic;
