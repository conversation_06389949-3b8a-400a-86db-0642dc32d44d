import { Anthropic } from '@anthropic-ai/sdk'
const client = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY })

async function main() {
  const systemMsg = {
    role: 'system',
    content: `
You are an expert TypeScript developer and compiler engineer.
Your task is to design and implement AST-based parsing for accurate TypeScript analysis.
Focus on leveraging the TypeScript compiler API to traverse and analyze the AST effectively.
Ensure the solution handles complex language features and edge cases.
    `.trim(),
    cache_control: 'user:ast-parser-system',
  }

  const userMsg = {
    role: 'user',
    content: `
Please provide a detailed plan and code examples for implementing AST-based parsing in TypeScript.
Include steps for setting up the parser, traversing the AST, and extracting relevant information.
Highlight how this approach improves analysis accuracy compared to previous regex-based methods.
    `.trim(),
  }

  const resp = await client.completions.create({
    model: 'claude-opus-4-20250514',
    messages: [ systemMsg, userMsg ],
    max_tokens_to_sample: 500,
    headers: {
      'x-anthropic-token-efficient-tools': 'token-efficient-tools-2025-02-19'
    },
  })

  console.log(resp.completion)
}

main()
