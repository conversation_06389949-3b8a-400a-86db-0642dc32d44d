#!/usr/bin/env node

/**
 * Test script to verify improvements tracking
 *
 * This script creates a fake previous analysis file with inflated numbers
 * and then runs the analyzer to see if it correctly detects improvements
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

async function testImprovementsTracking() {
  console.log('🧪 Testing improvements tracking...');

  // Create a fake previous analysis with inflated numbers
  const fakeAnalysis = {
    duplicates: Array(20)
      .fill()
      .map((_, i) => ({
        type: 'duplicate_function',
        scripts: [`script${i}.js`, `script${i + 1}.js`],
        similarity: 0.9,
        suggestion: `Consider merging script${i}.js and script${i + 1}.js`,
      })),
    mergeOpportunities: Array(15)
      .fill()
      .map((_, i) => ({
        category: 'utils',
        scripts: [`util${i}.js`, `util${i + 1}.js`],
        reason: 'Multiple small scripts with similar purpose',
        suggestion: `Merge into a single utils-${i}.js with exported functions`,
      })),
    splitOpportunities: Array(10)
      .fill()
      .map((_, i) => ({
        script: `large${i}.js`,
        reason: 'Script is too large',
        suggestion: `Split into multiple smaller modules`,
      })),
    obsoleteScripts: Array(8)
      .fill()
      .map((_, i) => ({
        script: `obsolete${i}.js`,
        reason: 'Not used anywhere',
        confidence: 'high',
        suggestion: `Remove obsolete${i}.js`,
      })),
    namingIssues: Array(12)
      .fill()
      .map((_, i) => ({
        script: `bad_name-${i}.js`,
        issue: 'Mixed naming convention',
        suggestion: 'Use consistent kebab-case naming',
      })),
    organizationSuggestions: [],
    dependencyIssues: Array(5)
      .fill()
      .map((_, i) => ({
        script: `tight${i}.js`,
        issue: 'Multiple local imports',
        imports: ['./a.js', './b.js', './c.js'],
        suggestion: 'Consider if this indicates tight coupling',
      })),
    timestamp: new Date().toISOString(),
  };

  // Save the fake analysis
  await fs.writeFile('scripts-analysis-previous.json', JSON.stringify(fakeAnalysis, null, 2));
  console.log('✅ Created fake previous analysis with inflated numbers');

  // Run the analyzer
  console.log('\n🚀 Running analyzer to test improvements detection...');
  execSync('node code-health/enhanced-refactor-analyzer.js ./scripts', { stdio: 'inherit' });

  console.log('\n✅ Test complete. Check the output above to see if improvements were detected.');
}

testImprovementsTracking().catch(console.error);
