#!/usr/bin/env node

/**
 * Dynamic Scripts Cleanup and Restructuring Tool
 * Automatically reorganizes scripts based on analysis results
 *
 * Features:
 * - Creates organized directory structure
 * - Merges related small scripts
 * - Generates unified CLI interface
 * - Removes obsolete scripts (with backup)
 * - Creates index files for categories
 *
 * Usage: node cleanup-scripts.js [--dry-run] [--no-backup]
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import ScriptAnalyzer from './enhanced-refactor-analyzer.js';

class ScriptRestructurer {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.createBackup = options.backup !== false;
    this.scriptsDir = options.scriptsDir || './scripts';
    this.backupDir = options.backupDir || './scripts-backup';
    this.newStructure = {
      build: [],
      deploy: [],
      analysis: [],
      maintenance: [],
      diagnostics: [],
      utils: [],
    };
  }

  async restructure() {
    console.log('\n🔧 Starting Scripts Restructuring Process\n');

    // Track changes for reporting
    const changes = {
      directoriesCreated: 0,
      scriptsMerged: 0,
      scriptsMoved: 0,
      scriptsDeleted: 0,
      newFilesCreated: 0,
    };

    // Step 1: Analyze current structure
    console.log('📊 Step 1: Analyzing current structure...');
    const analyzer = new ScriptAnalyzer();
    const analysis = await analyzer.analyzeDirectory(this.scriptsDir);

    // Step 2: Create backup
    if (this.createBackup && !this.dryRun) {
      console.log('\n💾 Step 2: Creating backup...');
      await this.backupScripts();
    }

    // Step 3: Plan new structure
    console.log('\n📐 Step 3: Planning new structure...');
    const plan = await this.createRestructuringPlan(analysis, analyzer.scripts);

    // Step 4: Execute restructuring
    console.log('\n🚀 Step 4: Executing restructuring...');
    await this.executeRestructuring(plan, changes);

    // Step 5: Generate unified CLI
    console.log('\n🎯 Step 5: Generating unified CLI interface...');
    await this.generateUnifiedCLI(plan);

    // Step 6: Create documentation
    console.log('\n📚 Step 6: Updating documentation...');
    await this.updateDocumentation(plan);

    // Save changes report
    await fs.writeFile('scripts-restructure-changes.json', JSON.stringify(changes, null, 2));

    console.log('\n✅ Restructuring complete!');
    console.log('\n📊 Changes made:');
    console.log(`  Directories created: ${changes.directoriesCreated}`);
    console.log(`  Scripts merged: ${changes.scriptsMerged}`);
    console.log(`  Scripts moved: ${changes.scriptsMoved}`);
    console.log(`  Scripts deleted: ${changes.scriptsDeleted}`);
    console.log(`  New files created: ${changes.newFilesCreated}`);
  }

  async backupScripts() {
    if (!this.dryRun) {
      await fs.rm(this.backupDir, { recursive: true, force: true });
      await fs.cp(this.scriptsDir, this.backupDir, { recursive: true });
      console.log(`  ✓ Backup created at ${this.backupDir}`);
    }
  }

  async createRestructuringPlan(analysis, scripts) {
    const plan = {
      directories: new Map(),
      merges: [],
      moves: [],
      deletions: [],
      newFiles: [],
    };

    // Plan directory structure
    plan.directories.set('build', {
      description: 'Build and compilation scripts',
      scripts: [],
    });
    plan.directories.set('deploy', {
      description: 'Deployment and publishing scripts',
      scripts: [],
    });
    plan.directories.set('analysis', {
      description: 'Code analysis and reporting tools',
      scripts: [],
    });
    plan.directories.set('maintenance', {
      description: 'Cleanup, fixing, and maintenance utilities',
      scripts: [],
    });
    plan.directories.set('utils', {
      description: 'Shared utilities and helpers',
      scripts: [],
    });

    // Categorize existing scripts
    for (const [filePath, scriptInfo] of scripts) {
      const category = this.determineBestCategory(scriptInfo);
      // Make sure the category exists in the plan
      if (plan.directories.has(category)) {
        plan.directories.get(category).scripts.push(scriptInfo);
      } else {
        console.log(`  ⚠️ Unknown category: ${category}, placing script in utils`);
        plan.directories.get('utils').scripts.push(scriptInfo);
      }
    }

    // Plan merges based on analysis
    if (analysis && analysis.mergeOpportunities) {
      for (const opportunity of analysis.mergeOpportunities) {
        // Skip if opportunity is null or undefined
        if (!opportunity || !opportunity.scripts) {
          console.log('  ⚠️ Skipping invalid merge opportunity');
          continue;
        }

        // Check if all scripts in the merge opportunity still exist
        const allScriptsExist = opportunity.scripts.every((scriptName) => {
          return Array.from(scripts.values()).some((s) => s.fileName === scriptName);
        });

        // Check if the target category exists
        const categoryExists = !opportunity.category || plan.directories.has(opportunity.category);

        if (allScriptsExist && opportunity.scripts.length <= 4 && categoryExists) {
          const targetName = this.generateMergedName(opportunity);

          // Check if the target file already exists
          const targetExists = Array.from(scripts.values()).some((s) => s.fileName === targetName);

          if (!targetExists) {
            plan.merges.push({
              scripts: opportunity.scripts,
              targetName,
              category: opportunity.category || 'utils',
            });
          } else {
            console.log(`  ⚠️ Skipping merge as target file already exists: ${targetName}`);
          }
        } else if (!allScriptsExist) {
          console.log(
            `  ⚠️ Skipping merge opportunity as some scripts no longer exist: ${opportunity.scripts.join(
              ', '
            )}`
          );
        } else if (!categoryExists) {
          console.log(
            `  ⚠️ Skipping merge opportunity as category does not exist: ${opportunity.category}`
          );
        }
      }
    }

    // Plan deletions for obsolete scripts
    if (analysis && analysis.obsoleteScripts) {
      for (const obsolete of analysis.obsoleteScripts) {
        if (obsolete && obsolete.confidence === 'high') {
          // Check if the script still exists
          const scriptExists = Array.from(scripts.values()).some(
            (s) => s.fileName === obsolete.script
          );
          if (scriptExists) {
            plan.deletions.push(obsolete.script);
          } else {
            console.log(`  ⚠️ Skipping deletion as script no longer exists: ${obsolete.script}`);
          }
        }
      }
    }

    // Plan new utility files
    plan.newFiles.push({
      name: 'cli.js',
      type: 'unified-cli',
      description: 'Unified command-line interface for all scripts',
    });

    plan.newFiles.push({
      name: 'scripts.config.js',
      type: 'configuration',
      description: 'Central configuration for all scripts',
    });

    return plan;
  }

  determineBestCategory(scriptInfo) {
    const name = scriptInfo.fileName.toLowerCase();

    if (
      name.includes('build') ||
      name.includes('compile') ||
      name.includes('webpack') ||
      name.includes('rollup')
    ) {
      return 'build';
    }
    if (name.includes('deploy') || name.includes('publish') || name.includes('release')) {
      return 'deploy';
    }
    if (name.includes('analyze') || name.includes('report') || name.includes('check')) {
      return 'analysis';
    }
    if (
      name.includes('clean') ||
      name.includes('fix') ||
      name.includes('manage') ||
      name.includes('version')
    ) {
      return 'maintenance';
    }

    return 'utils';
  }

  generateMergedName(opportunity) {
    if (opportunity.category) {
      return `${opportunity.category}-utils.js`;
    }

    // Extract common prefix from script names
    const names = opportunity.scripts;
    const commonPrefix = this.findCommonPrefix(names);

    if (commonPrefix.length > 3) {
      return `${commonPrefix}-utils.js`;
    }

    return 'merged-utils.js';
  }

  findCommonPrefix(strings) {
    if (!strings.length) return '';

    let prefix = '';
    const firstString = strings[0];

    for (let i = 0; i < firstString.length; i++) {
      const char = firstString[i];
      if (strings.every((str) => str[i] === char)) {
        prefix += char;
      } else {
        break;
      }
    }

    return prefix.replace(/-$/, '');
  }

  async executeRestructuring(plan, changes) {
    // Create new directory structure
    for (const [dir, info] of plan.directories) {
      const dirPath = path.join(this.scriptsDir, dir);

      if (!this.dryRun) {
        await fs.mkdir(dirPath, { recursive: true });
        console.log(`  ✓ Created directory: ${dir}/`);
        changes.directoriesCreated++;
      } else {
        console.log(`  [DRY RUN] Would create directory: ${dir}/`);
      }
    }

    // Execute merges
    for (const merge of plan.merges) {
      await this.mergeScripts(merge);
      changes.scriptsMerged++;
    }

    // Move scripts to appropriate directories
    for (const [dir, info] of plan.directories) {
      for (const script of info.scripts) {
        if (!plan.deletions.includes(script.fileName)) {
          await this.moveScript(script, dir);
          changes.scriptsMoved++;
        }
      }
    }

    // Handle deletions
    for (const scriptName of plan.deletions) {
      await this.archiveScript(scriptName);
      changes.scriptsDeleted++;
    }

    // Create new utility files
    for (const newFile of plan.newFiles) {
      await this.createNewFile(newFile);
      changes.newFilesCreated++;
    }
  }

  async mergeScripts(merge) {
    const categoryDir = path.join(this.scriptsDir, merge.category);
    const outputPath = path.join(categoryDir, merge.targetName);

    console.log(
      `  📦 Merging ${merge.scripts.length} scripts into ${merge.category}/${merge.targetName}`
    );

    if (!this.dryRun) {
      // Ensure the target directory exists before writing the file
      try {
        await fs.mkdir(categoryDir, { recursive: true });

        // Check if all script files exist before attempting to merge
        const existingScripts = [];
        for (const scriptName of merge.scripts) {
          const scriptPath = path.join(this.scriptsDir, scriptName);
          try {
            await fs.access(scriptPath);
            existingScripts.push(scriptName);
          } catch (error) {
            console.log(`    ⚠️  Script does not exist: ${scriptName}, skipping`);
          }
        }

        if (existingScripts.length === 0) {
          console.log(`  ⚠️ No scripts exist for merge, skipping: ${merge.targetName}`);
          return;
        }

        const mergedContent = await this.generateMergedContent(existingScripts);
        await fs.writeFile(outputPath, mergedContent);
        console.log(`  ✓ Created merged file: ${merge.category}/${merge.targetName}`);
      } catch (error) {
        console.error(`  ❌ Error creating merged file: ${error.message}`);
      }
    }
  }

  async generateMergedContent(scriptNames) {
    const sections = [];

    sections.push(`#!/usr/bin/env node

/**
 * Merged utility functions from multiple scripts
 * Auto-generated by cleanup-scripts.js
 *
 * Original scripts:
${scriptNames.map((s) => ` * - ${s}`).join('\n')}
 */

`);

    // Add shared imports
    sections.push(`import fs from 'fs/promises';
import path from 'path';

`);

    // Extract and merge functions from each script
    for (const scriptName of scriptNames) {
      const scriptPath = path.join(this.scriptsDir, scriptName);
      try {
        // Check if the file exists before trying to read it
        try {
          await fs.access(scriptPath);
        } catch (error) {
          console.log(`    ⚠️  Could not access ${scriptName}: ${error.message}`);
          continue;
        }

        const content = await fs.readFile(scriptPath, 'utf-8');
        const functions = this.extractExportableFunctions(content);

        if (functions.length > 0) {
          sections.push(`// Functions from ${scriptName}\n`);
          sections.push(functions.join('\n\n'));
          sections.push('\n');
        }
      } catch (error) {
        console.log(`    ⚠️  Could not read ${scriptName}: ${error.message}`);
      }
    }

    // Add exports
    sections.push(`
// Export functions
export {
  // Add function exports here
};`);

    return sections.join('');
  }

  extractExportableFunctions(content) {
    // Simple regex-based extraction (would be better with AST)
    const functionRegex = /^(?:async\s+)?function\s+(\w+)\s*\([^)]*\)\s*{[^}]+}/gm;
    const functions = [];
    let match;

    while ((match = functionRegex.exec(content)) !== null) {
      functions.push(match[0]);
    }

    return functions;
  }

  async moveScript(script, targetDir) {
    const sourcePath = script.path;
    const targetDirPath = path.join(this.scriptsDir, targetDir);
    const targetPath = path.join(targetDirPath, script.fileName);

    if (!this.dryRun) {
      try {
        // Ensure the target directory exists before moving the file
        await fs.mkdir(targetDirPath, { recursive: true });
        await fs.rename(sourcePath, targetPath);
        console.log(`  ✓ Moved ${script.fileName} to ${targetDir}/`);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          console.log(`  ⚠️  Could not move ${script.fileName}: ${error.message}`);
        }
      }
    } else {
      console.log(`  [DRY RUN] Would move ${script.fileName} to ${targetDir}/`);
    }
  }

  async archiveScript(scriptName) {
    const archiveDir = path.join(this.scriptsDir, '.archived');

    if (!this.dryRun) {
      await fs.mkdir(archiveDir, { recursive: true });

      try {
        const sourcePath = path.join(this.scriptsDir, scriptName);
        const targetPath = path.join(archiveDir, scriptName);
        await fs.rename(sourcePath, targetPath);
        console.log(`  🗄️  Archived ${scriptName}`);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          console.log(`  ⚠️  Could not archive ${scriptName}: ${error.message}`);
        }
      }
    } else {
      console.log(`  [DRY RUN] Would archive ${scriptName}`);
    }
  }

  async createNewFile(fileInfo) {
    const filePath = path.join(this.scriptsDir, fileInfo.name);
    const dirPath = path.dirname(filePath);

    if (!this.dryRun) {
      try {
        // Ensure the directory exists before creating the file
        await fs.mkdir(dirPath, { recursive: true });

        let content;
        switch (fileInfo.type) {
          case 'unified-cli':
            content = await this.generateCLIContent();
            break;
          case 'configuration':
            content = this.generateConfigContent();
            break;
          default:
            content = `// ${fileInfo.description}\n`;
        }

        await fs.writeFile(filePath, content);
        console.log(`  ✓ Created ${fileInfo.name}`);
      } catch (error) {
        console.error(`  ❌ Error creating file ${fileInfo.name}: ${error.message}`);
      }
    } else {
      console.log(`  [DRY RUN] Would create ${fileInfo.name}`);
    }
  }

  async generateCLIContent() {
    return `#!/usr/bin/env node

/**
 * Unified CLI for all project scripts
 * Auto-generated by cleanup-scripts.js
 */

import { program } from 'commander';
import path from 'path';

program
  .name('project-scripts')
  .description('Unified interface for all project scripts')
  .version('1.0.0');

// Build commands
program
  .command('build [target]')
  .description('Build the project')
  .option('-w, --watch', 'Watch mode')
  .option('-p, --production', 'Production build')
  .action(async (target, options) => {
    const buildUtilsModule = await import('./build/build-utils.js');
    await buildUtilsModule.default.build(target, options);
  });

// Deploy commands
program
  .command('deploy [environment]')
  .description('Deploy the project')
  .option('-d, --dry-run', 'Dry run')
  .action(async (environment, options) => {
    const deployUtilsModule = await import('./deploy/deploy-utils.js');
    await deployUtilsModule.default.deploy(environment, options);
  });

// Analysis commands
program
  .command('analyze [type]')
  .description('Run code analysis')
  .option('-o, --output <file>', 'Output file')
  .action(async (type, options) => {
    const analysisUtilsModule = await import('./analysis/analysis-utils.js');
    await analysisUtilsModule.default.analyze(type, options);
  });

// Maintenance commands
program
  .command('clean [target]')
  .description('Clean build artifacts and temporary files')
  .action(async (target) => {
    const maintenanceUtilsModule = await import('./maintenance/maintenance-utils.js');
    await maintenanceUtilsModule.default.clean(target);
  });

program.parse(process.argv);
`;
  }

  generateConfigContent() {
    return `/**
 * Central configuration for all scripts
 * Auto-generated by cleanup-scripts.js
 */

export default {
  paths: {
    src: './src',
    dist: './dist',
    build: './build',
    temp: './temp'
  },

  build: {
    minify: true,
    sourceMaps: true,
    target: 'es2020'
  },

  deploy: {
    environments: {
      development: {
        url: 'http://localhost:3000',
        branch: 'develop'
      },
      production: {
        url: 'https://app.example.com',
        branch: 'main'
      }
    }
  },

  analysis: {
    eslint: true,
    prettier: true,
    complexity: true,
    duplicates: true
  }
};
`;
  }

  async generateUnifiedCLI(plan) {
    const cliPath = path.join(this.scriptsDir, 'README-CLI.md');

    const content = `# Unified Scripts CLI

After restructuring, all scripts are now organized into categories and accessible through a unified interface.

## New Structure

\`\`\`
scripts/
├── cli.js              # Main CLI interface
├── scripts.config.js   # Central configuration
├── build/              # Build and compilation scripts
├── deploy/             # Deployment scripts
├── analysis/           # Code analysis tools
├── maintenance/        # Cleanup and maintenance
└── utils/              # Shared utilities
\`\`\`

## Usage

### Using the unified CLI:

\`\`\`bash
# Build commands
./scripts/cli.js build              # Default build
./scripts/cli.js build --production # Production build
./scripts/cli.js build --watch      # Watch mode

# Deploy commands
./scripts/cli.js deploy             # Deploy to default
./scripts/cli.js deploy production  # Deploy to production
./scripts/cli.js deploy --dry-run   # Test deployment

# Analysis commands
./scripts/cli.js analyze            # Run all analyses
./scripts/cli.js analyze bundle     # Analyze bundle size
./scripts/cli.js analyze deps       # Analyze dependencies

# Maintenance commands
./scripts/cli.js clean              # Clean all
./scripts/cli.js clean dist         # Clean dist only
\`\`\`

### Direct script access:

Scripts can still be run directly from their category folders:
\`\`\`bash
node scripts/build/build-utils.js
node scripts/analysis/code-analyzer.js
\`\`\`

## Migrated Scripts

${this.generateMigrationTable(plan)}

## Archived Scripts

Scripts that were obsolete or redundant have been moved to \`scripts/.archived/\`.
`;

    if (!this.dryRun) {
      await fs.writeFile(cliPath, content);
      console.log('  ✓ Created CLI documentation');
    }
  }

  generateMigrationTable(plan) {
    const lines = [
      '| Original Script | New Location | Notes |',
      '|-----------------|--------------|-------|',
    ];

    for (const [dir, info] of plan.directories) {
      for (const script of info.scripts) {
        lines.push(`| ${script.fileName} | ${dir}/${script.fileName} | Moved |`);
      }
    }

    for (const merge of plan.merges) {
      lines.push(
        `| ${merge.scripts.join(', ')} | ${merge.category}/${merge.targetName} | Merged |`
      );
    }

    return lines.join('\n');
  }

  async updateDocumentation(plan) {
    const readmePath = path.join(this.scriptsDir, 'README.md');

    const content = `# Scripts Directory

This directory contains all build, deployment, and maintenance scripts for the project.

## Organization

Scripts are organized into categories for better maintainability:

### 📦 Build Scripts (\`/build\`)
Build and compilation related scripts.

### 🚀 Deploy Scripts (\`/deploy\`)
Deployment and publishing scripts.

### 📊 Analysis Scripts (\`/analysis\`)
Code analysis and quality checking tools.

### 🔧 Maintenance Scripts (\`/maintenance\`)
Cleanup, fixing, and general maintenance utilities.

### 🛠️ Utilities (\`/utils\`)
Shared utilities and helper functions.

## Quick Start

Use the unified CLI for common tasks:

\`\`\`bash
# Run any script through the CLI
./scripts/cli.js [command] [options]

# Get help
./scripts/cli.js --help
\`\`\`

## Configuration

All scripts share a common configuration file at \`scripts.config.js\`.

## Development

When adding new scripts:
1. Place them in the appropriate category directory
2. Follow the naming convention: \`category-action.js\`
3. Export reusable functions for potential merging
4. Update the CLI if adding new commands

---
*Last restructured: ${new Date().toISOString()}*
`;

    if (!this.dryRun) {
      await fs.writeFile(readmePath, content);
      console.log('  ✓ Updated main README');
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: args.includes('--dry-run'),
    backup: !args.includes('--no-backup'),
  };

  if (options.dryRun) {
    console.log('🔍 Running in DRY RUN mode - no changes will be made\n');
  }

  const restructurer = new ScriptRestructurer(options);
  await restructurer.restructure();

  if (options.dryRun) {
    console.log('\n💡 To execute the restructuring, run without --dry-run flag');
  }
}

// Run if called directly
if (import.meta.url.endsWith(process.argv[1])) {
  main().catch(console.error);
}

export default ScriptRestructurer;
