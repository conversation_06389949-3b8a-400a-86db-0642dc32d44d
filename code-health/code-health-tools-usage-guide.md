# Code Health Tools - Usage Guide

## Overview

This enhanced code-health toolkit provides comprehensive analysis and automated refactoring capabilities for your codebase, with special focus on organizing and cleaning up script directories.

> **IMPORTANT**: This project uses Yarn as its package manager. All code health tools should be run using Yarn commands (e.g., `yarn health`, `yarn health:analyze`) rather than direct Node.js commands for proper monorepo compatibility. The examples below show the underlying Node.js commands, but you should use the corresponding Yarn scripts defined in package.json.

## Tools Included

### 1. Enhanced Refactor Analyzer (`enhanced-refactor-analyzer.js`)

Specialized tool for analyzing script directories and finding refactoring opportunities.

**Features:**

- Detects duplicate functionality across scripts
- Identifies scripts that should be merged or split
- Finds obsolete or redundant scripts
- Suggests better organizational structure
- Analyzes dependencies and usage patterns

**Usage:**

```bash
# Analyze scripts directory
node code-health/enhanced-refactor-analyzer.js ./scripts

# Output: scripts-analysis.json with detailed findings
```

### 2. Dynamic Scripts Cleanup (`dynamic-scripts-cleanup.js`)

Automated tool that restructures script directories based on analysis results.

**Features:**

- Creates organized directory structure
- Merges related small scripts
- Generates unified CLI interface
- Archives obsolete scripts
- Updates documentation

**Usage:**

```bash
# Dry run to see what would change
node code-health/dynamic-scripts-cleanup.js --dry-run

# Execute cleanup (creates backup first)
node code-health/dynamic-scripts-cleanup.js

# Execute without backup
node code-health/dynamic-scripts-cleanup.js --no-backup
```

### 3. Enhanced Codebase Analyzer (`enhanced-analyze-codebase.js`)

Comprehensive health analysis for entire codebase.

**Features:**

- Multi-dimensional health scoring
- Complexity analysis
- Duplication detection
- Dependency analysis
- Test coverage metrics
- Documentation coverage
- Security vulnerability detection

**Usage:**

```bash
# Analyze current directory
node code-health/enhanced-analyze-codebase.js

# Analyze specific directory with JSON output
node code-health/enhanced-analyze-codebase.js ./src --output=json

# Generate HTML report
node code-health/enhanced-analyze-codebase.js . --output=html
```

### 4. Code Health Orchestrator (`orchestrate-health.js`)

Master script that coordinates all tools for comprehensive analysis and refactoring.

**Usage:**

```bash
# Full analysis with recommendations
node code-health/orchestrate-health.js

# Focus on scripts directory
node code-health/orchestrate-health.js --target scripts

# Execute automated refactoring
node code-health/orchestrate-health.js --target scripts --execute
```

## Workflow Example

### Step 1: Initial Health Check

```bash
# Get baseline health score
yarn health:analyze
```

### Step 2: Focused Analysis

```bash
# Analyze problem areas (e.g., scripts directory)
yarn health:scripts
```

### Step 3: Review Findings

Check the generated reports:

- `code-health-report-*.md` - Overall health report
- `scripts-analysis.json` - Detailed scripts analysis

### Step 4: Plan Refactoring

```bash
# Generate comprehensive plan
yarn health
```

### Step 5: Execute Cleanup

```bash
# Dry run first
yarn health:cleanup:dry

# Execute if satisfied
yarn health:cleanup
```

### Step 6: Verify Improvements

```bash
# Re-run health check
yarn health:analyze
```

## Integration with Existing Scripts

The new code-health tools work alongside your existing scripts in `scripts/` and `scripts/diagnostics/`:

1. **Complementary Analysis**: While your existing diagnostic scripts focus on specific aspects (bundle size, dependencies, etc.), the code-health tools provide holistic analysis.

2. **Automated Organization**: The cleanup tool can reorganize your existing scripts without breaking functionality.

3. **Unified Interface**: After cleanup, all scripts are accessible through a single CLI interface.

## Best Practices

1. **Regular Health Checks**: Run the analyzer weekly or before major releases

   ```bash
   # Add to your CI/CD pipeline
   yarn health:analyze
   ```

2. **Incremental Refactoring**: Use the priority recommendations to tackle issues gradually

3. **Track Progress**: Save reports to track improvement over time

   ```bash
   # Create reports directory
   mkdir code-health-reports

   # Save timestamped reports
   yarn health:analyze
   mv code-health-report-*.json code-health-reports/
   ```

4. **Customize Thresholds**: Modify the analyzer thresholds based on your project needs

## Customization

### Adding New Metrics

Edit `enhanced-analyze-codebase.js` to add custom metrics:

```javascript
async analyzeCustomMetric() {
  console.log('  - Analyzing custom metric...');
  // Your analysis logic
  this.metrics.custom = { /* results */ };
}
```

### Modifying Cleanup Rules

Edit `dynamic-scripts-cleanup.js` to customize organization:

```javascript
determineBestCategory(scriptInfo) {
  // Add your custom categorization logic
}
```

## Troubleshooting

### Common Issues

1. **Parse Errors**: Some files may not parse correctly

   - Solution: The tools skip unparseable files and continue

2. **Permission Errors**: Cannot read/write certain directories

   - Solution: Check file permissions or run with appropriate privileges

3. **Large Codebases**: Analysis takes too long
   - Solution: Focus on specific directories or increase sample sizes

### Debug Mode

Run with verbose output:

```bash
yarn health -- --verbose
```

## Performance Comparison

To compare Opus 4 vs Sonnet 4 performance:

1. Run the same analysis with each model
2. Compare:
   - Quality of recommendations
   - Accuracy of issue detection
   - Usefulness of refactoring suggestions
   - Execution time

The tools are designed to handle complex codebases efficiently, saving significant time and cost compared to manual analysis.

## Future Enhancements

Consider adding:

- Git integration for historical analysis
- IDE plugins for real-time feedback
- Custom rule configuration files
- Team-specific coding standards
- Automated PR comments with health metrics

---

## Yarn Commands Reference

For convenience, the following Yarn scripts are available in package.json:

| Yarn Command              | Description                                    |
| ------------------------- | ---------------------------------------------- |
| `yarn health`             | Run the full orchestration script              |
| `yarn health:analyze`     | Run the codebase analyzer                      |
| `yarn health:scripts`     | Analyze the scripts directory                  |
| `yarn health:cleanup`     | Execute cleanup operations                     |
| `yarn health:cleanup:dry` | Preview cleanup changes without executing them |
| `yarn health:setup`       | Set up the code health tools environment       |

Always use these Yarn commands instead of direct Node.js commands to ensure proper monorepo compatibility.

---

**Remember**: These tools are meant to assist, not replace, human judgment. Always review recommendations and test thoroughly after refactoring.
