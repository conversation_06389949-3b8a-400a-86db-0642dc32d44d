#!/usr/bin/env node

/**
 * Enhanced Refactoring Opportunity & Script Organization Analyzer
 * Specifically designed to analyze and suggest reorganization for scripts directories
 *
 * Features:
 * - Detects duplicate functionality across scripts
 * - Identifies scripts that should be merged or split
 * - Finds obsolete or redundant scripts
 * - Suggests better organizational structure
 * - Analyzes dependencies and usage patterns
 *
 * Usage: node enhanced-refactor-analyzer.js [directory]
 */

import fs from 'fs/promises';
import path from 'path';
// We'll use a simpler approach without relying on @babel/parser and @babel/traverse
// This avoids ESM/CommonJS compatibility issues

class ScriptAnalyzer {
  constructor() {
    this.scripts = new Map();
    this.functionSignatures = new Map();
    this.dependencies = new Map();
    this.categories = {
      build: [],
      deploy: [],
      analyze: [],
      manage: [],
      clean: [],
      diagnostic: [],
      utility: [],
    };
  }

  async analyzeDirectory(dir) {
    console.log(`\n🔍 Analyzing scripts in: ${dir}\n`);

    // Get absolute path for analysis file
    const analysisFilePath = path.resolve(process.cwd(), 'scripts-analysis-previous.json');
    console.log(`Looking for previous analysis at: ${analysisFilePath}`);

    // Try to load previous analysis results
    let previousAnalysis = null;
    try {
      const previousData = await fs.readFile(analysisFilePath, 'utf-8');
      previousAnalysis = JSON.parse(previousData);
      console.log('📊 Found previous analysis results for comparison');
      console.log(`  Previous analysis had ${previousAnalysis.scriptCount || 'unknown'} scripts`);
    } catch (error) {
      console.log('📊 No previous analysis found, creating baseline');
    }

    const files = await this.getAllScripts(dir);
    console.log(`\n📂 Found ${files.length} script files to analyze:`);

    // Log the first 10 files and total count
    files.slice(0, 10).forEach((file) => {
      console.log(`  - ${file}`);
    });
    if (files.length > 10) {
      console.log(`  ... and ${files.length - 10} more files`);
    }

    // Phase 1: Parse and categorize all scripts
    for (const file of files) {
      await this.analyzeScript(file);
    }

    // Phase 2: Find patterns and opportunities
    const analysis = {
      scriptCount: files.length,
      duplicates: this.findDuplicateFunctionality(),
      mergeOpportunities: this.findMergeOpportunities(),
      splitOpportunities: this.findSplitOpportunities(),
      obsoleteScripts: this.findObsoleteScripts(),
      namingIssues: this.findNamingIssues(),
      organizationSuggestions: this.suggestOrganization(),
      dependencyIssues: this.analyzeDependencies(),
      timestamp: new Date().toISOString(),
      analyzedFiles: files,
    };

    // Compare with previous if available
    if (previousAnalysis) {
      analysis.improvements = this.calculateImprovements(previousAnalysis, analysis);
    }

    // Save current analysis for future comparison
    console.log(`Saving current analysis to: ${analysisFilePath}`);
    await fs.writeFile(analysisFilePath, JSON.stringify(analysis, null, 2));

    return analysis;
  }

  async getAllScripts(dir, fileList = []) {
    const files = await fs.readdir(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = await fs.stat(filePath);

      if (stat.isDirectory() && !file.startsWith('.')) {
        // Skip directories that are likely backups or generated content
        if (
          file === 'scripts-backup' ||
          file === 'node_modules' ||
          file.includes('backup') ||
          file.includes('generated')
        ) {
          console.log(`  Skipping directory: ${filePath}`);
          continue;
        }
        await this.getAllScripts(filePath, fileList);
      } else if (file.endsWith('.js')) {
        // Skip analysis files, backup files, and temporary files
        if (
          file.includes('analysis') ||
          file.includes('backup') ||
          file.includes('temp') ||
          file.startsWith('.') ||
          file === 'scripts-analysis.json' ||
          file === 'scripts-analysis-previous.json'
        ) {
          console.log(`  Skipping file: ${filePath}`);
          continue;
        }
        fileList.push(filePath);
      }
    }

    return fileList;
  }

  async analyzeScript(filePath) {
    const content = await fs.readFile(filePath, 'utf-8');
    const fileName = path.basename(filePath);
    const category = this.categorizeScript(fileName);

    const scriptInfo = {
      path: filePath,
      fileName,
      category,
      size: content.length,
      functions: [],
      imports: [],
      exports: [],
      patterns: [],
      purpose: this.extractPurpose(content),
    };

    try {
      // Simple regex-based analysis instead of AST parsing
      // Extract imports
      const importRegex = /import\s+.*?from\s+['"]([^'"]+)['"]/g;
      let match;
      while ((match = importRegex.exec(content)) !== null) {
        scriptInfo.imports.push(match[1]);
      }

      // Extract functions
      const functionRegex = /function\s+(\w+)\s*\(([^)]*)\)/g;
      while ((match = functionRegex.exec(content)) !== null) {
        const funcInfo = {
          name: match[1],
          params: match[2].split(',').map((p) => p.trim()),
          loc: this.getLineNumber(content, match.index),
        };
        scriptInfo.functions.push(funcInfo);
        this.addFunctionSignature(funcInfo, filePath);
      }

      // Extract arrow functions
      const arrowFuncRegex = /const\s+(\w+)\s*=\s*(?:async\s*)?\(([^)]*)\)\s*=>/g;
      while ((match = arrowFuncRegex.exec(content)) !== null) {
        const funcInfo = {
          name: match[1],
          params: match[2].split(',').map((p) => p.trim()),
          loc: this.getLineNumber(content, match.index),
        };
        scriptInfo.functions.push(funcInfo);
        this.addFunctionSignature(funcInfo, filePath);
      }

      // Extract function calls
      const callRegex = /\b(\w+)\s*\(/g;
      while ((match = callRegex.exec(content)) !== null) {
        if (
          match[1] !== 'if' &&
          match[1] !== 'for' &&
          match[1] !== 'while' &&
          match[1] !== 'switch'
        ) {
          scriptInfo.patterns.push({
            type: 'call',
            name: match[1],
          });
        }
      }
    } catch (e) {
      console.log(`⚠️  Parse error in ${fileName}: ${e.message}`);
    }

    this.scripts.set(filePath, scriptInfo);
    this.categories[category].push(scriptInfo);
  }

  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  categorizeScript(fileName) {
    const name = fileName.toLowerCase();
    if (name.includes('build')) return 'build';
    if (name.includes('deploy')) return 'deploy';
    if (name.includes('analyze') || name.includes('analysis')) return 'analyze';
    if (name.includes('manage') || name.includes('version')) return 'manage';
    if (name.includes('clean') || name.includes('cleanup')) return 'clean';
    if (name.includes('diagnostic') || name.includes('check')) return 'diagnostic';
    return 'utility';
  }

  extractPurpose(content) {
    const lines = content.split('\n');
    const purposeLines = [];
    let inComment = false;

    for (const line of lines.slice(0, 20)) {
      if (line.includes('/**')) inComment = true;
      if (inComment && line.includes('*/')) break;
      if (inComment && line.includes('*') && !line.includes('/**')) {
        purposeLines.push(line.replace(/^\s*\*\s?/, '').trim());
      }
    }

    return purposeLines.join(' ').trim();
  }

  extractFunctionInfo(node) {
    return {
      name: node.id ? node.id.name : 'anonymous',
      params: node.params.map((p) => p.name || 'param'),
      loc: node.loc ? node.loc.start.line : 0,
    };
  }

  addFunctionSignature(funcInfo, filePath) {
    const signature = `${funcInfo.name}(${funcInfo.params.join(',')})`;
    if (!this.functionSignatures.has(signature)) {
      this.functionSignatures.set(signature, []);
    }
    this.functionSignatures.get(signature).push(filePath);
  }

  findDuplicateFunctionality() {
    const duplicates = [];
    console.log('  Finding duplicate functionality...');

    // Find scripts with similar names
    const scriptNames = Array.from(this.scripts.values()).map((s) => s.fileName);
    for (let i = 0; i < scriptNames.length; i++) {
      for (let j = i + 1; j < scriptNames.length; j++) {
        const similarity = this.calculateSimilarity(scriptNames[i], scriptNames[j]);
        if (similarity > 0.7) {
          duplicates.push({
            type: 'similar_names',
            scripts: [scriptNames[i], scriptNames[j]],
            similarity,
            suggestion: `Consider merging ${scriptNames[i]} and ${scriptNames[j]}`,
          });
        }
      }
    }

    console.log(`  Found ${duplicates.length} potential duplicates`);
    return duplicates;
  }

  findMergeOpportunities() {
    const opportunities = [];
    console.log('  Finding merge opportunities...');

    // Small scripts in the same category
    for (const [category, scripts] of Object.entries(this.categories)) {
      const smallScripts = scripts.filter((s) => s.size < 3000);
      if (smallScripts.length > 2) {
        opportunities.push({
          category,
          scripts: smallScripts.map((s) => s.fileName),
          reason: 'Multiple small scripts with similar purpose',
          suggestion: `Merge into a single ${category}-utils.js with exported functions`,
        });
      }
    }

    console.log(`  Found ${opportunities.length} merge opportunities`);
    return opportunities;
  }

  findSplitOpportunities() {
    const opportunities = [];

    for (const script of this.scripts.values()) {
      // Large scripts
      if (script.size > 10000) {
        opportunities.push({
          script: script.fileName,
          size: script.size,
          functionCount: script.functions.length,
          reason: 'Script is too large',
          suggestion: 'Split into smaller, focused modules',
        });
      }

      // Scripts with many functions
      if (script.functions.length > 10) {
        opportunities.push({
          script: script.fileName,
          functionCount: script.functions.length,
          reason: 'Too many functions in one file',
          suggestion: 'Group related functions into separate modules',
        });
      }
    }

    return opportunities;
  }

  findObsoleteScripts() {
    const obsolete = [];

    for (const script of this.scripts.values()) {
      // Scripts with "old", "deprecated", "legacy" in name
      if (/old|deprecated|legacy|backup/i.test(script.fileName)) {
        obsolete.push({
          script: script.fileName,
          reason: 'Name suggests obsolescence',
          confidence: 'high',
        });
      }

      // Very small scripts (might be empty or minimal)
      if (script.size < 500 && script.functions.length === 0) {
        obsolete.push({
          script: script.fileName,
          reason: 'Minimal content',
          confidence: 'medium',
        });
      }
    }

    return obsolete;
  }

  findNamingIssues() {
    const issues = [];

    for (const script of this.scripts.values()) {
      // Inconsistent naming conventions
      if (script.fileName.includes('_') && script.fileName.includes('-')) {
        issues.push({
          script: script.fileName,
          issue: 'Mixed naming convention (both _ and -)',
          suggestion: 'Use consistent kebab-case naming',
        });
      }

      // Vague names
      if (/^(script|util|helper|misc|temp)\d*\.js$/i.test(script.fileName)) {
        issues.push({
          script: script.fileName,
          issue: 'Vague or generic name',
          suggestion: 'Use descriptive names that indicate purpose',
        });
      }
    }

    return issues;
  }

  suggestOrganization() {
    const suggestions = [];

    // Suggest subdirectory structure
    const categoryCounts = {};
    for (const [category, scripts] of Object.entries(this.categories)) {
      categoryCounts[category] = scripts.length;
    }

    suggestions.push({
      type: 'directory_structure',
      current: 'Flat structure with many scripts',
      suggested: {
        'build/': ['build-*.js', 'webpack-*.js', 'rollup-*.js'],
        'deploy/': ['deploy-*.js', 'publish-*.js'],
        'analysis/': ['analyze-*.js', 'report-*.js'],
        'maintenance/': ['clean-*.js', 'fix-*.js', 'manage-*.js'],
        'utils/': ['Shared utilities and helpers'],
      },
    });

    // Suggest consolidation
    if (this.scripts.size > 20) {
      suggestions.push({
        type: 'consolidation',
        reason: `Too many scripts (${this.scripts.size}) in one directory`,
        suggestion: 'Create a CLI tool with subcommands instead of individual scripts',
      });
    }

    return suggestions;
  }

  analyzeDependencies() {
    const issues = [];

    // Check for scripts that might be importing each other
    for (const script of this.scripts.values()) {
      const localImports = script.imports.filter((imp) => imp.startsWith('./'));
      if (localImports.length > 2) {
        issues.push({
          script: script.fileName,
          issue: 'Multiple local imports',
          imports: localImports,
          suggestion: 'Consider if this indicates tight coupling',
        });
      }
    }

    return issues;
  }

  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.getEditDistance(longer, shorter);
    return (longer.length - editDistance) / parseFloat(longer.length);
  }

  getEditDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  async generateReport(analysis) {
    console.log('\n📊 Script Analysis Report\n');
    console.log('='.repeat(60));

    // Summary
    console.log('\n📈 Summary:');
    console.log(`Total scripts analyzed: ${this.scripts.size}`);
    console.log(
      `Duplicate functions found: ${
        analysis.duplicates.filter((d) => d.type === 'duplicate_function').length
      }`
    );
    console.log(`Merge opportunities: ${analysis.mergeOpportunities.length}`);
    console.log(`Split opportunities: ${analysis.splitOpportunities.length}`);
    console.log(`Potentially obsolete: ${analysis.obsoleteScripts.length}`);

    // Add improvements section if available
    if (analysis.improvements) {
      console.log('\n🚀 Improvements Since Last Run:');
      console.log(`Duplicates fixed: ${analysis.improvements.duplicatesFixed}`);
      console.log(
        `Merge opportunities addressed: ${analysis.improvements.mergeOpportunitiesAddressed}`
      );
      console.log(`Obsolete scripts removed: ${analysis.improvements.obsoleteScriptsRemoved}`);
      console.log(`Naming issues fixed: ${analysis.improvements.namingIssuesFixed}`);
      console.log(`Dependency issues fixed: ${analysis.improvements.dependencyIssuesFixed}`);
      console.log(`Total improvements: ${analysis.improvements.totalImprovements}`);
    }

    // Detailed findings
    if (analysis.duplicates.length > 0) {
      console.log('\n🔄 Duplicate Functionality:');
      analysis.duplicates.forEach((dup) => {
        console.log(`  - ${dup.suggestion}`);
      });
    }

    if (analysis.mergeOpportunities.length > 0) {
      console.log('\n🔗 Merge Opportunities:');
      analysis.mergeOpportunities.forEach((opp) => {
        console.log(`  - ${opp.suggestion}`);
        console.log(
          `    Scripts: ${opp.scripts.slice(0, 3).join(', ')}${opp.scripts.length > 3 ? '...' : ''}`
        );
      });
    }

    if (analysis.organizationSuggestions.length > 0) {
      console.log('\n📁 Organization Suggestions:');
      analysis.organizationSuggestions.forEach((sug) => {
        if (sug.type === 'directory_structure') {
          console.log('  Suggested directory structure:');
          Object.entries(sug.suggested).forEach(([dir, desc]) => {
            console.log(`    ${dir} - ${Array.isArray(desc) ? desc[0] : desc}`);
          });
        } else {
          console.log(`  - ${sug.suggestion}`);
        }
      });
    }

    return analysis;
  }

  calculateImprovements(previous, current) {
    console.log('\n🔄 Calculating improvements since last run...');

    // Debug output
    console.log(
      `Previous: ${previous.scriptCount || 'unknown'} scripts, ${
        previous.duplicates.length
      } duplicates, ${previous.mergeOpportunities.length} merge opportunities`
    );
    console.log(
      `Current: ${current.scriptCount} scripts, ${current.duplicates.length} duplicates, ${current.mergeOpportunities.length} merge opportunities`
    );

    // Check if file count has changed
    if (previous.scriptCount && previous.scriptCount !== current.scriptCount) {
      console.log(
        `⚠️ Warning: Script count changed from ${previous.scriptCount} to ${current.scriptCount}`
      );
      console.log('This may affect improvement calculations');

      // If we have the list of previously analyzed files, we can do a more accurate comparison
      if (previous.analyzedFiles && current.analyzedFiles) {
        const previousSet = new Set(previous.analyzedFiles);
        const currentSet = new Set(current.analyzedFiles);

        // Find new files
        const newFiles = current.analyzedFiles.filter((file) => !previousSet.has(file));
        if (newFiles.length > 0) {
          console.log(`New files found (${newFiles.length}):`);
          newFiles.slice(0, 5).forEach((file) => console.log(`  + ${file}`));
          if (newFiles.length > 5) console.log(`  ... and ${newFiles.length - 5} more`);
        }

        // Find removed files
        const removedFiles = previous.analyzedFiles.filter((file) => !currentSet.has(file));
        if (removedFiles.length > 0) {
          console.log(`Removed files (${removedFiles.length}):`);
          removedFiles.slice(0, 5).forEach((file) => console.log(`  - ${file}`));
          if (removedFiles.length > 5) console.log(`  ... and ${removedFiles.length - 5} more`);
        }
      }
    }

    // Calculate improvements, accounting for file count changes
    const improvements = {
      duplicatesFixed: Math.max(0, previous.duplicates.length - current.duplicates.length),
      mergeOpportunitiesAddressed: Math.max(
        0,
        previous.mergeOpportunities.length - current.mergeOpportunities.length
      ),
      obsoleteScriptsRemoved: Math.max(
        0,
        previous.obsoleteScripts.length - current.obsoleteScripts.length
      ),
      namingIssuesFixed: Math.max(0, previous.namingIssues.length - current.namingIssues.length),
      dependencyIssuesFixed: Math.max(
        0,
        previous.dependencyIssues.length - current.dependencyIssues.length
      ),
    };

    improvements.totalImprovements = Object.values(improvements).reduce((sum, val) => sum + val, 0);

    return improvements;
  }

  async cleanupTemporaryFiles() {
    console.log('\n🧹 Cleaning up temporary files...');

    const tempPatterns = ['temp-*.js', '*-temp.js', '*.js.bak', '*.backup.js'];

    for (const pattern of tempPatterns) {
      try {
        const files = await this.findFiles(pattern);
        for (const file of files) {
          console.log(`  Removing temporary file: ${file}`);
          await fs.unlink(file);
        }
      } catch (error) {
        console.log(`  Error cleaning up ${pattern}: ${error.message}`);
      }
    }
  }

  async findFiles(pattern) {
    // Simple glob implementation
    const parts = pattern.split('/');
    const baseDir = parts.length > 1 ? parts.slice(0, -1).join('/') : '.';
    const filePattern = parts[parts.length - 1];

    const files = await fs.readdir(baseDir);
    return files
      .filter((file) => {
        // Convert glob pattern to regex
        const regexPattern = filePattern.replace(/\./g, '\\.').replace(/\*/g, '.*');
        return new RegExp(`^${regexPattern}$`).test(file);
      })
      .map((file) => path.join(baseDir, file));
  }
}

// Main execution
async function main() {
  const targetDir = process.argv[2] || './scripts';

  const analyzer = new ScriptAnalyzer();

  // Clean up any temporary files before starting
  await analyzer.cleanupTemporaryFiles();

  const analysis = await analyzer.analyzeDirectory(targetDir);
  await analyzer.generateReport(analysis);

  // Save detailed analysis to file
  const outputPath = path.join(process.cwd(), 'scripts-analysis.json');
  await fs.writeFile(outputPath, JSON.stringify(analysis, null, 2));
  console.log(`\n💾 Detailed analysis saved to: ${outputPath}`);

  // Clean up again after analysis
  await analyzer.cleanupTemporaryFiles();
}

// Run if called directly
if (import.meta.url.endsWith(process.argv[1])) {
  main().catch(console.error);
}

export default ScriptAnalyzer;
