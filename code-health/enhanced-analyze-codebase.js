#!/usr/bin/env node

/**
 * Enhanced Codebase Health Analyzer
 * Comprehensive analysis tool for code quality, organization, and maintenance needs
 *
 * Features:
 * - Multi-dimensional code health scoring
 * - Automated issue detection and prioritization
 * - Actionable refactoring recommendations
 * - Integration with existing analysis tools
 * - Trend tracking over time
 *
 * Usage: node code-health/analyze-codebase.js [directory] [--output json|html|markdown]
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import crypto from 'crypto';

class CodebaseAnalyzer {
  constructor(options = {}) {
    this.rootDir = options.rootDir || process.cwd();
    this.outputFormat = options.outputFormat || 'markdown';
    this.metrics = {
      complexity: [],
      duplication: [],
      dependencies: [],
      testCoverage: [],
      documentation: [],
      organization: [],
    };
    this.issues = [];
    this.recommendations = [];
  }

  async analyze() {
    console.log('🏥 Starting Comprehensive Codebase Health Analysis\n');

    const startTime = Date.now();

    // Phase 1: Gather metrics
    console.log('📊 Phase 1: Gathering metrics...');
    await this.gatherMetrics();

    // Phase 2: Analyze patterns
    console.log('\n🔍 Phase 2: Analyzing patterns...');
    await this.analyzePatterns();

    // Phase 3: Check integration points
    console.log('\n🔗 Phase 3: Checking integration points...');
    await this.checkIntegrations();

    // Phase 4: Generate health score
    console.log('\n💊 Phase 4: Calculating health score...');
    const healthScore = await this.calculateHealthScore();

    // Phase 5: Generate recommendations
    console.log('\n💡 Phase 5: Generating recommendations...');
    await this.generateRecommendations(healthScore);

    // Phase 6: Create report
    console.log('\n📝 Phase 6: Creating report...');
    const report = await this.createReport(healthScore);

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`\n✅ Analysis complete in ${duration}s`);

    return report;
  }

  async gatherMetrics() {
    await Promise.all([
      this.analyzeComplexity(),
      this.analyzeDuplication(),
      this.analyzeDependencies(),
      this.analyzeTestCoverage(),
      this.analyzeDocumentation(),
      this.analyzeOrganization(),
    ]);
  }

  async analyzeComplexity() {
    console.log('  - Analyzing code complexity...');

    const files = await this.findFiles('**/*.{js,jsx,ts,tsx}');

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        const complexity = this.calculateComplexity(content);

        this.metrics.complexity.push({
          file: path.relative(this.rootDir, file),
          complexity,
          lines: content.split('\n').length,
          functions: this.countFunctions(content),
        });

        if (complexity > 20) {
          this.issues.push({
            type: 'high_complexity',
            severity: 'high',
            file: path.relative(this.rootDir, file),
            message: `Cyclomatic complexity of ${complexity} exceeds threshold`,
            complexity,
          });
        }
      } catch (error) {
        // Skip files that can't be analyzed
      }
    }
  }

  calculateComplexity(content) {
    // Simplified cyclomatic complexity calculation
    let complexity = 1;

    const patterns = [
      /\bif\b/g,
      /\belse\s+if\b/g,
      /\bfor\b/g,
      /\bwhile\b/g,
      /\bcase\b/g,
      /\bcatch\b/g,
      /\?\s*:/g, // ternary
      /&&/g, // logical AND
      /\|\|/g, // logical OR
    ];

    patterns.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) complexity += matches.length;
    });

    return complexity;
  }

  countFunctions(content) {
    const functionPatterns = [
      /function\s+\w+\s*\(/g,
      /\w+\s*:\s*function\s*\(/g,
      /\w+\s*=\s*function\s*\(/g,
      /\w+\s*=\s*\([^)]*\)\s*=>/g,
      /\w+\s*:\s*\([^)]*\)\s*=>/g,
    ];

    let count = 0;
    functionPatterns.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) count += matches.length;
    });

    return count;
  }

  async analyzeDuplication() {
    console.log('  - Analyzing code duplication...');

    const files = await this.findFiles('**/*.{js,jsx,ts,tsx}');
    const hashes = new Map();

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        const blocks = this.extractCodeBlocks(content);

        blocks.forEach((block) => {
          const hash = crypto.createHash('md5').update(block.normalized).digest('hex');

          if (!hashes.has(hash)) {
            hashes.set(hash, []);
          }

          hashes.get(hash).push({
            file: path.relative(this.rootDir, file),
            line: block.line,
            content: block.content,
          });
        });
      } catch (error) {
        // Skip files that can't be analyzed
      }
    }

    // Find duplicates
    for (const [hash, locations] of hashes) {
      if (locations.length > 1) {
        this.metrics.duplication.push({
          hash,
          locations,
          size: locations[0].content.split('\n').length,
        });

        if (locations.length > 3) {
          this.issues.push({
            type: 'code_duplication',
            severity: 'medium',
            message: `Code block duplicated in ${locations.length} locations`,
            locations: locations.map((l) => `${l.file}:${l.line}`),
          });
        }
      }
    }
  }

  extractCodeBlocks(content) {
    const lines = content.split('\n');
    const blocks = [];
    const blockSize = 10; // Minimum lines for a "block"

    for (let i = 0; i <= lines.length - blockSize; i++) {
      const block = lines.slice(i, i + blockSize).join('\n');
      const normalized = block.replace(/\s+/g, ' ').replace(/['"`]/g, '').trim();

      if (normalized.length > 50) {
        // Minimum characters
        blocks.push({
          line: i + 1,
          content: block,
          normalized,
        });
      }
    }

    return blocks;
  }

  async analyzeDependencies() {
    console.log('  - Analyzing dependencies...');

    try {
      const packagePath = path.join(this.rootDir, 'package.json');
      const packageJson = JSON.parse(await fs.readFile(packagePath, 'utf-8'));

      const deps = Object.keys(packageJson.dependencies || {});
      const devDeps = Object.keys(packageJson.devDependencies || {});

      this.metrics.dependencies = {
        total: deps.length + devDeps.length,
        production: deps.length,
        development: devDeps.length,
        outdated: await this.checkOutdatedDeps(),
      };

      // Check for security issues
      try {
        const auditOutput = execSync('npm audit --json', {
          cwd: this.rootDir,
          stdio: 'pipe',
        }).toString();

        const audit = JSON.parse(auditOutput);
        if (audit.metadata.vulnerabilities.total > 0) {
          this.issues.push({
            type: 'security_vulnerabilities',
            severity: 'critical',
            message: `Found ${audit.metadata.vulnerabilities.total} security vulnerabilities`,
            details: audit.metadata.vulnerabilities,
          });
        }
      } catch (error) {
        // npm audit might fail in some environments
      }
    } catch (error) {
      console.log('    ⚠️  Could not analyze dependencies');
    }
  }

  async checkOutdatedDeps() {
    try {
      const output = execSync('npm outdated --json', {
        cwd: this.rootDir,
        stdio: 'pipe',
      }).toString();

      const outdated = JSON.parse(output);
      return Object.keys(outdated).length;
    } catch (error) {
      return 0;
    }
  }

  async analyzeTestCoverage() {
    console.log('  - Analyzing test coverage...');

    const testFiles = await this.findFiles('**/*.{test,spec}.{js,jsx,ts,tsx}');
    const srcFiles = await this.findFiles('src/**/*.{js,jsx,ts,tsx}');

    this.metrics.testCoverage = {
      testFiles: testFiles.length,
      srcFiles: srcFiles.length,
      ratio: srcFiles.length > 0 ? testFiles.length / srcFiles.length : 0,
    };

    if (this.metrics.testCoverage.ratio < 0.5) {
      this.issues.push({
        type: 'low_test_coverage',
        severity: 'medium',
        message: `Only ${(this.metrics.testCoverage.ratio * 100).toFixed(
          1
        )}% of source files have tests`,
      });
    }
  }

  async analyzeDocumentation() {
    console.log('  - Analyzing documentation...');

    const files = await this.findFiles('**/*.{js,jsx,ts,tsx}');
    let documented = 0;
    let total = 0;

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        const functions = content.match(/function\s+\w+|const\s+\w+\s*=\s*(?:async\s*)?\(/g) || [];
        const jsdocs = content.match(/\/\*\*[\s\S]*?\*\//g) || [];

        total += functions.length;
        documented += Math.min(functions.length, jsdocs.length);
      } catch (error) {
        // Skip files that can't be analyzed
      }
    }

    this.metrics.documentation = {
      documented,
      total,
      percentage: total > 0 ? (documented / total) * 100 : 0,
    };

    if (this.metrics.documentation.percentage < 30) {
      this.issues.push({
        type: 'poor_documentation',
        severity: 'low',
        message: `Only ${this.metrics.documentation.percentage.toFixed(
          1
        )}% of functions are documented`,
      });
    }
  }

  async analyzeOrganization() {
    console.log('  - Analyzing code organization...');

    const structure = await this.analyzeDirectoryStructure(this.rootDir);

    this.metrics.organization = {
      maxDepth: structure.maxDepth,
      totalFiles: structure.totalFiles,
      avgFilesPerDir: structure.avgFilesPerDir,
      emptyDirs: structure.emptyDirs,
    };

    if (structure.maxDepth > 6) {
      this.issues.push({
        type: 'deep_nesting',
        severity: 'low',
        message: `Directory structure is too deep (${structure.maxDepth} levels)`,
      });
    }

    if (structure.avgFilesPerDir > 20) {
      this.issues.push({
        type: 'overcrowded_directories',
        severity: 'medium',
        message: `Directories contain too many files (avg: ${structure.avgFilesPerDir.toFixed(1)})`,
      });
    }
  }

  async analyzeDirectoryStructure(dir, depth = 0) {
    const result = {
      maxDepth: depth,
      totalFiles: 0,
      totalDirs: 0,
      emptyDirs: 0,
      avgFilesPerDir: 0,
    };

    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      const files = entries.filter((e) => e.isFile()).length;
      const dirs = entries.filter((e) => e.isDirectory() && !e.name.startsWith('.')).length;

      result.totalFiles = files;
      result.totalDirs = 1;

      if (files === 0 && dirs === 0) {
        result.emptyDirs = 1;
      }

      // Recurse into subdirectories
      for (const entry of entries) {
        if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
          const subResult = await this.analyzeDirectoryStructure(
            path.join(dir, entry.name),
            depth + 1
          );

          result.maxDepth = Math.max(result.maxDepth, subResult.maxDepth);
          result.totalFiles += subResult.totalFiles;
          result.totalDirs += subResult.totalDirs;
          result.emptyDirs += subResult.emptyDirs;
        }
      }

      result.avgFilesPerDir = result.totalDirs > 0 ? result.totalFiles / result.totalDirs : 0;
    } catch (error) {
      // Skip directories that can't be read
    }

    return result;
  }

  async analyzePatterns() {
    // Analyze common anti-patterns
    const antiPatterns = [
      {
        name: 'God Object',
        check: (metrics) => metrics.complexity.some((f) => f.functions > 20),
        severity: 'high',
      },
      {
        name: 'Spaghetti Code',
        check: (metrics) => metrics.complexity.some((f) => f.complexity > 30),
        severity: 'high',
      },
      {
        name: 'Copy-Paste Programming',
        check: (metrics) => metrics.duplication.length > 10,
        severity: 'medium',
      },
      {
        name: 'Dependency Hell',
        check: (metrics) => metrics.dependencies.total > 100,
        severity: 'medium',
      },
    ];

    antiPatterns.forEach((pattern) => {
      if (pattern.check(this.metrics)) {
        this.issues.push({
          type: 'anti_pattern',
          severity: pattern.severity,
          message: `Detected ${pattern.name} anti-pattern`,
          pattern: pattern.name,
        });
      }
    });
  }

  async checkIntegrations() {
    // Check for common integration issues
    const checks = [
      {
        name: 'Mixed module systems',
        test: async () => {
          const files = await this.findFiles('**/*.js');
          let hasCommonJS = false;
          let hasESModules = false;

          for (const file of files.slice(0, 20)) {
            // Sample check
            try {
              const content = await fs.readFile(file, 'utf-8');
              if (content.includes('require(') || content.includes('module.exports')) {
                hasCommonJS = true;
              }
              if (content.includes('import ') || content.includes('export ')) {
                hasESModules = true;
              }
            } catch (error) {
              // Skip
            }
          }

          return hasCommonJS && hasESModules;
        },
      },
      {
        name: 'Inconsistent async patterns',
        test: async () => {
          const files = await this.findFiles('**/*.js');
          let hasCallbacks = false;
          let hasPromises = false;
          let hasAsync = false;

          for (const file of files.slice(0, 20)) {
            // Sample check
            try {
              const content = await fs.readFile(file, 'utf-8');
              if (content.match(/\(err,\s*\w+\)\s*=>/)) hasCallbacks = true;
              if (content.includes('.then(') || content.includes('.catch(')) hasPromises = true;
              if (content.includes('async ') || content.includes('await ')) hasAsync = true;
            } catch (error) {
              // Skip
            }
          }

          return (hasCallbacks ? 1 : 0) + (hasPromises ? 1 : 0) + (hasAsync ? 1 : 0) > 1;
        },
      },
    ];

    for (const check of checks) {
      if (await check.test()) {
        this.issues.push({
          type: 'integration_issue',
          severity: 'low',
          message: check.name,
        });
      }
    }
  }

  calculateHealthScore() {
    const weights = {
      complexity: 0.25,
      duplication: 0.2,
      dependencies: 0.15,
      testing: 0.2,
      documentation: 0.1,
      organization: 0.1,
    };

    const scores = {
      complexity: this.scoreComplexity(),
      duplication: this.scoreDuplication(),
      dependencies: this.scoreDependencies(),
      testing: this.scoreTesting(),
      documentation: this.scoreDocumentation(),
      organization: this.scoreOrganization(),
    };

    let totalScore = 0;
    for (const [metric, weight] of Object.entries(weights)) {
      totalScore += scores[metric] * weight;
    }

    return {
      overall: Math.round(totalScore),
      breakdown: scores,
      grade: this.getGrade(totalScore),
    };
  }

  scoreComplexity() {
    const avgComplexity =
      this.metrics.complexity.reduce((sum, f) => sum + f.complexity, 0) /
      (this.metrics.complexity.length || 1);

    if (avgComplexity < 5) return 100;
    if (avgComplexity < 10) return 80;
    if (avgComplexity < 15) return 60;
    if (avgComplexity < 20) return 40;
    return 20;
  }

  scoreDuplication() {
    const duplicateRatio = this.metrics.duplication.length / (this.metrics.complexity.length || 1);

    if (duplicateRatio < 0.05) return 100;
    if (duplicateRatio < 0.1) return 80;
    if (duplicateRatio < 0.2) return 60;
    if (duplicateRatio < 0.3) return 40;
    return 20;
  }

  scoreDependencies() {
    const { total, outdated } = this.metrics.dependencies;
    const outdatedRatio = outdated / (total || 1);

    let score = 100;
    if (total > 50) score -= 20;
    if (total > 100) score -= 20;
    if (outdatedRatio > 0.2) score -= 20;
    if (outdatedRatio > 0.5) score -= 20;

    return Math.max(score, 20);
  }

  scoreTesting() {
    const ratio = this.metrics.testCoverage.ratio;

    if (ratio > 0.8) return 100;
    if (ratio > 0.6) return 80;
    if (ratio > 0.4) return 60;
    if (ratio > 0.2) return 40;
    return 20;
  }

  scoreDocumentation() {
    const percentage = this.metrics.documentation.percentage;

    if (percentage > 80) return 100;
    if (percentage > 60) return 80;
    if (percentage > 40) return 60;
    if (percentage > 20) return 40;
    return 20;
  }

  scoreOrganization() {
    const { maxDepth, avgFilesPerDir } = this.metrics.organization;

    let score = 100;
    if (maxDepth > 4) score -= 20;
    if (maxDepth > 6) score -= 20;
    if (avgFilesPerDir > 15) score -= 20;
    if (avgFilesPerDir > 25) score -= 20;

    return Math.max(score, 20);
  }

  getGrade(score) {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  async generateRecommendations(healthScore) {
    // Priority 1: Critical issues
    const criticalIssues = this.issues.filter((i) => i.severity === 'critical');
    if (criticalIssues.length > 0) {
      this.recommendations.push({
        priority: 1,
        category: 'Security',
        action: 'Fix security vulnerabilities immediately',
        impact: 'Prevents potential security breaches',
        effort: 'Low',
        details: criticalIssues,
      });
    }

    // Priority 2: High complexity
    if (healthScore.breakdown.complexity < 60) {
      this.recommendations.push({
        priority: 2,
        category: 'Complexity',
        action: 'Refactor high-complexity functions',
        impact: 'Improves maintainability and reduces bugs',
        effort: 'Medium',
        targets: this.metrics.complexity
          .filter((f) => f.complexity > 20)
          .sort((a, b) => b.complexity - a.complexity)
          .slice(0, 5)
          .map((f) => `${f.file} (complexity: ${f.complexity})`),
      });
    }

    // Priority 3: Code duplication
    if (healthScore.breakdown.duplication < 60) {
      this.recommendations.push({
        priority: 3,
        category: 'Duplication',
        action: 'Extract common code into shared utilities',
        impact: 'Reduces maintenance burden',
        effort: 'Medium',
        targets: this.metrics.duplication
          .filter((d) => d.locations.length > 2)
          .slice(0, 5)
          .map((d) => `Duplicated in ${d.locations.length} places (${d.size} lines)`),
      });
    }

    // Priority 4: Testing
    if (healthScore.breakdown.testing < 60) {
      this.recommendations.push({
        priority: 4,
        category: 'Testing',
        action: 'Increase test coverage',
        impact: 'Improves reliability and confidence in changes',
        effort: 'High',
        current: `${(this.metrics.testCoverage.ratio * 100).toFixed(1)}%`,
        target: '80%',
      });
    }

    // Priority 5: Dependencies
    if (this.metrics.dependencies.outdated > 10) {
      this.recommendations.push({
        priority: 5,
        category: 'Dependencies',
        action: 'Update outdated dependencies',
        impact: 'Improves security and performance',
        effort: 'Low',
        count: this.metrics.dependencies.outdated,
      });
    }
  }

  async createReport(healthScore) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        healthScore: healthScore.overall,
        grade: healthScore.grade,
        totalIssues: this.issues.length,
        criticalIssues: this.issues.filter((i) => i.severity === 'critical').length,
        filesAnalyzed: this.metrics.complexity.length,
      },
      metrics: this.metrics,
      issues: this.issues,
      recommendations: this.recommendations.sort((a, b) => a.priority - b.priority),
      breakdown: healthScore.breakdown,
    };

    // Save report
    const reportPath = path.join(
      this.rootDir,
      `code-health-report-${Date.now()}.${this.outputFormat}`
    );

    switch (this.outputFormat) {
      case 'json':
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
        break;
      case 'markdown':
        await fs.writeFile(reportPath, this.generateMarkdownReport(report));
        break;
      case 'html':
        await fs.writeFile(reportPath, this.generateHTMLReport(report));
        break;
    }

    console.log(`\n📄 Report saved to: ${reportPath}`);

    return report;
  }

  generateMarkdownReport(report) {
    const sections = [];

    sections.push(`# Code Health Report

Generated: ${new Date(report.timestamp).toLocaleString()}

## Executive Summary

**Overall Health Score: ${report.summary.healthScore}/100 (Grade: ${report.summary.grade})**

- Files Analyzed: ${report.summary.filesAnalyzed}
- Total Issues: ${report.summary.totalIssues}
- Critical Issues: ${report.summary.criticalIssues}

## Health Breakdown

| Metric | Score | Status |
|--------|-------|--------|
${Object.entries(report.breakdown)
  .map(
    ([metric, score]) =>
      `| ${metric.charAt(0).toUpperCase() + metric.slice(1)} | ${score}/100 | ${this.getStatusEmoji(
        score
      )} |`
  )
  .join('\n')}

## Top Recommendations

${report.recommendations
  .slice(0, 5)
  .map(
    (rec, i) => `
### ${i + 1}. ${rec.action}

- **Category:** ${rec.category}
- **Priority:** ${rec.priority}
- **Effort:** ${rec.effort}
- **Impact:** ${rec.impact}
${rec.targets ? `\n**Targets:**\n${rec.targets.map((t) => `- ${t}`).join('\n')}` : ''}
`
  )
  .join('\n')}

## Detailed Metrics

### Code Complexity
- Average Complexity: ${(
      report.metrics.complexity.reduce((s, f) => s + f.complexity, 0) /
      report.metrics.complexity.length
    ).toFixed(1)}
- High Complexity Files: ${report.metrics.complexity.filter((f) => f.complexity > 20).length}

### Code Duplication
- Duplicate Blocks: ${report.metrics.duplication.length}
- Most Duplicated: ${
      report.metrics.duplication.length > 0
        ? `${report.metrics.duplication[0].locations.length} instances`
        : 'None'
    }

### Dependencies
- Total: ${report.metrics.dependencies.total}
- Outdated: ${report.metrics.dependencies.outdated}

### Test Coverage
- Test Files: ${report.metrics.testCoverage.testFiles}
- Source Files: ${report.metrics.testCoverage.srcFiles}
- Coverage Ratio: ${(report.metrics.testCoverage.ratio * 100).toFixed(1)}%

### Documentation
- Documented Functions: ${report.metrics.documentation.documented}/${
      report.metrics.documentation.total
    }
- Documentation Rate: ${report.metrics.documentation.percentage.toFixed(1)}%

## Issues by Severity

${['critical', 'high', 'medium', 'low']
  .map((severity) => {
    const issues = report.issues.filter((i) => i.severity === severity);
    if (issues.length === 0) return '';

    return `### ${severity.charAt(0).toUpperCase() + severity.slice(1)} (${issues.length})

${issues
  .slice(0, 5)
  .map((issue) => `- **${issue.type}:** ${issue.message}`)
  .join('\n')}
${issues.length > 5 ? `\n_...and ${issues.length - 5} more_` : ''}`;
  })
  .filter((s) => s)
  .join('\n\n')}

---
_Use this report to prioritize refactoring efforts and improve code quality._
`);

    return sections.join('\n');
  }

  generateHTMLReport(report) {
    return `<!DOCTYPE html>
<html>
<head>
  <title>Code Health Report</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }
    .header { background: #f0f0f0; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
    .score { font-size: 48px; font-weight: bold; color: ${this.getScoreColor(
      report.summary.healthScore
    )}; }
    .grade { font-size: 24px; margin-left: 20px; }
    .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
    .metric { background: #f8f8f8; padding: 20px; border-radius: 8px; text-align: center; }
    .metric-score { font-size: 32px; font-weight: bold; margin: 10px 0; }
    .recommendations { margin: 30px 0; }
    .recommendation { background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #ffc107; }
    .issues { margin: 30px 0; }
    .issue { padding: 10px; margin: 5px 0; border-radius: 4px; }
    .critical { background: #f8d7da; border-left: 4px solid #dc3545; }
    .high { background: #fff3cd; border-left: 4px solid #ffc107; }
    .medium { background: #cfe2ff; border-left: 4px solid #0d6efd; }
    .low { background: #d1ecf1; border-left: 4px solid #0dcaf0; }
  </style>
</head>
<body>
  <div class="header">
    <h1>Code Health Report</h1>
    <p>Generated: ${new Date(report.timestamp).toLocaleString()}</p>
    <div>
      <span class="score">${report.summary.healthScore}</span>
      <span class="grade">Grade: ${report.summary.grade}</span>
    </div>
  </div>

  <div class="metrics">
    ${Object.entries(report.breakdown)
      .map(
        ([metric, score]) => `
      <div class="metric">
        <h3>${metric.charAt(0).toUpperCase() + metric.slice(1)}</h3>
        <div class="metric-score" style="color: ${this.getScoreColor(score)}">${score}</div>
      </div>
    `
      )
      .join('')}
  </div>

  <div class="recommendations">
    <h2>Top Recommendations</h2>
    ${report.recommendations
      .slice(0, 5)
      .map(
        (rec) => `
      <div class="recommendation">
        <h4>${rec.action}</h4>
        <p><strong>Impact:</strong> ${rec.impact}</p>
        <p><strong>Effort:</strong> ${rec.effort}</p>
      </div>
    `
      )
      .join('')}
  </div>

  <div class="issues">
    <h2>Issues</h2>
    ${report.issues
      .slice(0, 10)
      .map(
        (issue) => `
      <div class="issue ${issue.severity}">
        <strong>${issue.type}:</strong> ${issue.message}
      </div>
    `
      )
      .join('')}
  </div>
</body>
</html>`;
  }

  getStatusEmoji(score) {
    if (score >= 80) return '✅';
    if (score >= 60) return '⚠️';
    return '❌';
  }

  getScoreColor(score) {
    if (score >= 80) return '#28a745';
    if (score >= 60) return '#ffc107';
    return '#dc3545';
  }

  async findFiles(pattern) {
    // Simple file finder - in production, use glob or similar
    const files = [];
    const extensions = pattern.match(/\{([^}]+)\}/)?.[1].split(',') || ['js'];

    async function walk(dir) {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
            await walk(fullPath);
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name).slice(1);
            if (extensions.includes(ext)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    }

    await walk(this.rootDir);
    return files;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const targetDir = args[0] || process.cwd();
  const outputFormat = args.find((a) => a.startsWith('--output='))?.split('=')[1] || 'markdown';

  const analyzer = new CodebaseAnalyzer({
    rootDir: targetDir,
    outputFormat,
  });

  await analyzer.analyze();
}

// Run if called directly
if (import.meta.url.endsWith(process.argv[1])) {
  main().catch(console.error);
}

export default CodebaseAnalyzer;
