#!/usr/bin/env node

/**
 * Test script for the HealthOrchestrator class
 *
 * This script tests the functionality of the HealthOrchestrator class,
 * particularly focusing on the issues mentioned in the previous thread:
 *
 * 1. Missing loadPreviousReport method
 * 2. Issues with generateFinalReport method not saving data for comparison
 * 3. Problems with the script trying to create manage-utils.js even though it was removed
 * 4. Tracking improvements between runs
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import HealthOrchestrator from './orchestrate-health.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function createTestEnvironment() {
  console.log('🔧 Setting up test environment...');

  // Create a fake previous report
  const fakeReport = {
    timestamp: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    currentHealth: {
      score: 30,
      grade: 'F',
      issues: 1000,
    },
    refactoringPlan: {
      phases: 4,
      estimatedEffort: '15 days',
      expectedImprovement: {
        healthScore: 45,
      },
    },
    healthScore: 30,
    totalIssues: 1000,
    criticalIssues: 20,
    scriptAnalysis: {
      duplicates: Array(30)
        .fill()
        .map((_, i) => ({
          type: 'duplicate_function',
          scripts: [`script${i}.js`, `script${i + 1}.js`],
        })),
      mergeOpportunities: Array(20)
        .fill()
        .map((_, i) => ({
          category: 'utils',
          scripts: [`util${i}.js`, `util${i + 1}.js`],
        })),
      obsoleteScripts: Array(15)
        .fill()
        .map((_, i) => ({
          script: `obsolete${i}.js`,
          confidence: 'high',
        })),
    },
    improvements: [],
  };

  try {
    await fs.writeFile('code-health-previous-report.json', JSON.stringify(fakeReport, null, 2));
    console.log('✅ Created fake previous report');
  } catch (error) {
    console.error('❌ Failed to create fake previous report:', error);
  }

  // Create a fake scripts directory structure for testing
  const scriptsDir = path.join(process.cwd(), 'scripts');
  try {
    await fs.mkdir(scriptsDir, { recursive: true });

    // Create some test script files
    for (let i = 1; i <= 5; i++) {
      await fs.writeFile(
        path.join(scriptsDir, `test-script-${i}.js`),
        `#!/usr/bin/env node\n\n/**\n * Test script ${i}\n */\n\nconsole.log('Test script ${i}');\n`
      );
    }

    console.log('✅ Created test scripts directory');
  } catch (error) {
    console.error('❌ Failed to create test scripts directory:', error);
  }
}

async function cleanupTestEnvironment() {
  console.log('🧹 Cleaning up test environment...');

  try {
    // Remove the fake previous report
    await fs.unlink('code-health-previous-report.json');
    console.log('✅ Removed fake previous report');
  } catch (error) {
    console.log('⚠️ Could not remove fake previous report:', error.message);
  }

  try {
    // Remove the test scripts directory
    await fs.rm(path.join(process.cwd(), 'scripts'), { recursive: true, force: true });
    console.log('✅ Removed test scripts directory');
  } catch (error) {
    console.log('⚠️ Could not remove test scripts directory:', error.message);
  }

  // Remove any generated reports
  try {
    await fs.unlink('code-health-orchestration-report.md');
    console.log('✅ Removed orchestration report');
  } catch (error) {
    console.log('⚠️ Could not remove orchestration report:', error.message);
  }

  try {
    await fs.unlink('scripts-detailed-analysis.json');
    console.log('✅ Removed scripts analysis');
  } catch (error) {
    console.log('⚠️ Could not remove scripts analysis:', error.message);
  }
}

async function runTest() {
  console.log('\n🧪 Running HealthOrchestrator test...');

  try {
    // Create a new instance of HealthOrchestrator
    const orchestrator = new HealthOrchestrator({
      target: 'scripts',
      execute: true, // Set to true to test the execute functionality
      verbose: true,
    });

    // Run the orchestrator
    await orchestrator.run();
    console.log('✅ HealthOrchestrator run completed');

    // Check if a new report was generated
    try {
      await fs.access('code-health-orchestration-report.md');
      console.log('✅ Report was generated successfully');
    } catch (error) {
      console.error('❌ Report was not generated:', error);
    }

    // Check if the previous report was updated
    try {
      await fs.access('code-health-previous-report.json');
      console.log('✅ Previous report was updated');

      // Read the updated report to check its content
      const updatedReport = JSON.parse(
        await fs.readFile('code-health-previous-report.json', 'utf-8')
      );
      console.log('📊 Updated report timestamp:', updatedReport.timestamp);
      console.log('📊 Updated health score:', updatedReport.healthScore);
      console.log('📊 Improvements tracked:', updatedReport.improvements?.length || 0);
    } catch (error) {
      console.error('❌ Previous report was not updated:', error);
    }
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

async function main() {
  console.log('🚀 Starting HealthOrchestrator test');

  // Setup test environment
  await createTestEnvironment();

  // Run the test
  await runTest();

  // Cleanup test environment
  await cleanupTestEnvironment();

  console.log('\n🏁 Test completed');
}

main().catch(console.error);
