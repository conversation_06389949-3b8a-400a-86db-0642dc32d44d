#!/usr/bin/env node

/* eslint-disable no-console */
/**
 * Code Health Orchestrator
 * Master script that coordinates all code health tools
 *
 * This script demonstrates how to use the code-health tools to:
 * 1. Analyze the codebase health
 * 2. Focus on specific problem areas (like scripts/)
 * 3. Generate actionable refactoring plans
 * 4. Execute automated cleanup
 *
 * Usage: node code-health/orchestrate-health.js [--target scripts] [--execute]
 */

import path from 'path';
import fs from 'fs/promises';

// Import the analysis tools
import CodebaseAnalyzer from './enhanced-analyze-codebase.js';
import ScriptAnalyzer from './enhanced-refactor-analyzer.js';
import ScriptRestructurer from './dynamic-scripts-cleanup.js';

class HealthOrchestrator {
  constructor(options = {}) {
    this.targetDir = options.target || '.';
    this.execute = options.execute || false;
    this.verbose = options.verbose || false;
    this.options = options;
    this.improvements = [];
  }

  async run() {
    console.log(`
╔═══════════════════════════════════════════════╗
║          Code Health Orchestrator             ║
║   Comprehensive Analysis & Refactoring Tool   ║
╚═══════════════════════════════════════════════╝
`);

    // Load previous report for comparison
    const previousReport = await this.loadPreviousReport();

    // Phase 1: Overall codebase health analysis
    console.log('\n📊 PHASE 1: Overall Codebase Health Analysis');
    const healthReport = await this.analyzeCodebaseHealth();
    this.healthScore = healthReport.summary.healthScore;
    this.issues = healthReport.issues || [];
    this.criticalIssues = this.issues.filter((i) => i.severity === 'critical');

    // Phase 2: Identify problem areas
    console.log('\n🔍 PHASE 2: Deep Analysis of Problem Areas');
    let problemAreas = await this.identifyProblemAreas(healthReport);
    // Make sure problemAreas is an array before calling join
    if (Array.isArray(problemAreas)) {
      console.log(`Identified problem areas: ${problemAreas.join(', ')}`);
    } else {
      console.log('No specific problem areas identified');
      // Ensure problemAreas is an array for subsequent operations
      problemAreas = ['organization'];
    }

    // Phase 3: Focused analysis on scripts directory
    console.log('\n🎯 PHASE 3: Focused Scripts Directory Analysis\n');
    this.scriptAnalysis = await this.analyzeScriptsDirectory();

    // Phase 4: Generate refactoring plan
    console.log('\n📋 PHASE 4: Generating Refactoring Plan\n');
    const plan = await this.generateRefactoringPlan(healthReport, problemAreas);

    // Track improvements if we have a previous report
    if (previousReport) {
      this.improvements = this.trackImprovements(previousReport);
      console.log('\n🚀 PHASE 5: Tracking Improvements');
      if (this.improvements.length > 0) {
        console.log('Improvements since last run:');
        this.improvements.forEach((improvement) => console.log(`- ${improvement}`));
      } else {
        console.log('No improvements detected since last run');
      }
    }

    // Phase 5: Execute refactoring if requested
    if (this.options.execute) {
      console.log('\n🛠️ PHASE 5: Executing Refactoring Plan');
      await this.executeRefactoring(plan);
    } else {
      console.log('\n💡 To execute the refactoring plan, run with --execute flag');
    }

    // Phase 6: Generate final report
    await this.generateFinalReport(healthReport, plan);
  }

  async analyzeCodebaseHealth() {
    const analyzer = new CodebaseAnalyzer({
      rootDir: process.cwd(),
      outputFormat: 'json',
    });

    const report = await analyzer.analyze();

    console.log(`
Health Score: ${report.summary.healthScore}/100 (${report.summary.grade})
Critical Issues: ${report.summary.criticalIssues}
Total Issues: ${report.summary.totalIssues}
`);

    return report;
  }

  async identifyProblemAreas(healthReport) {
    const problemAreas = [];

    // Check if healthReport has the expected structure
    if (!healthReport || !healthReport.breakdown) {
      console.log('⚠️ Warning: Health report is missing breakdown metrics');
      return ['organization']; // Default to organization as a problem area
    }

    // Check each metric for problem areas
    if (healthReport.breakdown.complexity < 60) {
      problemAreas.push('high-complexity');
    }

    if (healthReport.breakdown.duplication < 60) {
      problemAreas.push('duplication');
    }

    if (healthReport.breakdown.organization < 60) {
      problemAreas.push('organization');
    }

    // Check for specific directories with issues
    if (healthReport.metrics && healthReport.metrics.complexity) {
      const complexFiles = healthReport.metrics.complexity.filter((f) => f.complexity > 20);
      const directoryCounts = {};

      complexFiles.forEach((file) => {
        if (file && file.file) {
          const dir = path.dirname(file.file).split('/')[0];
          directoryCounts[dir] = (directoryCounts[dir] || 0) + 1;
        }
      });

      Object.entries(directoryCounts).forEach(([dir, count]) => {
        if (count > 3) {
          problemAreas.push(dir);
        }
      });
    } else {
      console.log('⚠️ Warning: Health report is missing complexity metrics');
    }

    // Ensure we always have at least one problem area
    if (problemAreas.length === 0) {
      problemAreas.push('organization');
    }

    console.log('Identified problem areas:', problemAreas.join(', '));

    return problemAreas;
  }

  async analyzeScriptsDirectory() {
    const analyzer = new ScriptAnalyzer();
    const analysis = await analyzer.analyzeDirectory('./scripts');

    console.log(`
Scripts Analysis Summary:
- Total scripts: ${analyzer.scripts.size}
- Duplicate functions: ${analysis.duplicates.filter((d) => d.type === 'duplicate_function').length}
- Merge opportunities: ${analysis.mergeOpportunities.length}
- Obsolete scripts: ${analysis.obsoleteScripts.length}
`);

    // Save detailed scripts analysis
    await fs.writeFile('scripts-detailed-analysis.json', JSON.stringify(analysis, null, 2));

    return analysis;
  }

  async generateRefactoringPlan(healthReport, problemAreas) {
    const plan = {
      phases: [],
      estimatedEffort: 0,
      expectedImprovement: {},
    };

    // Phase 1: Critical fixes
    if (healthReport.summary.criticalIssues > 0) {
      plan.phases.push({
        phase: 1,
        name: 'Critical Security Fixes',
        tasks: healthReport.issues
          .filter((i) => i.severity === 'critical')
          .map((i) => ({
            type: i.type,
            action: `Fix ${i.message}`,
            effort: 'Low',
            priority: 'Critical',
          })),
        estimatedDays: 1,
      });
    }

    // Phase 2: Scripts reorganization
    if (problemAreas.includes('scripts') || problemAreas.includes('organization')) {
      plan.phases.push({
        phase: 2,
        name: 'Scripts Directory Reorganization',
        tasks: [
          {
            type: 'restructure',
            action: 'Reorganize scripts into categorical subdirectories',
            effort: 'Medium',
            priority: 'High',
          },
          {
            type: 'merge',
            action: 'Merge related small scripts into utilities',
            effort: 'Medium',
            priority: 'Medium',
          },
          {
            type: 'cleanup',
            action: 'Archive obsolete scripts',
            effort: 'Low',
            priority: 'Low',
          },
        ],
        estimatedDays: 3,
      });
    }

    // Phase 3: Complexity reduction
    if (problemAreas.includes('high-complexity')) {
      const complexFiles = healthReport.metrics.complexity
        .filter((f) => f.complexity > 20)
        .sort((a, b) => b.complexity - a.complexity)
        .slice(0, 10);

      plan.phases.push({
        phase: 3,
        name: 'Complexity Reduction',
        tasks: complexFiles.map((f) => ({
          type: 'refactor',
          action: `Refactor ${f.file} (complexity: ${f.complexity})`,
          effort: 'High',
          priority: 'Medium',
        })),
        estimatedDays: 5,
      });
    }

    // Phase 4: Duplication removal
    if (problemAreas.includes('duplication')) {
      plan.phases.push({
        phase: 4,
        name: 'Duplication Removal',
        tasks: [
          {
            type: 'extract',
            action: 'Extract common code into shared utilities',
            effort: 'Medium',
            priority: 'Medium',
          },
          {
            type: 'consolidate',
            action: 'Consolidate duplicate implementations',
            effort: 'Medium',
            priority: 'Medium',
          },
        ],
        estimatedDays: 3,
      });
    }

    // Calculate total effort
    plan.estimatedEffort = plan.phases.reduce((sum, phase) => sum + phase.estimatedDays, 0);

    // Calculate expected improvements
    plan.expectedImprovement = {
      healthScore: Math.min(100, healthReport.summary.healthScore + plan.phases.length * 5),
      complexityReduction: '30-40%',
      duplicationReduction: '50-60%',
      maintainabilityIncrease: '40-50%',
    };

    console.log(`
Refactoring Plan Generated:
- Phases: ${plan.phases.length}
- Total Effort: ${plan.estimatedEffort} days
- Expected Health Score: ${healthReport.summary.healthScore} → ${plan.expectedImprovement.healthScore}
`);

    return plan;
  }

  async executeRefactoring(plan) {
    console.log('Starting automated refactoring...\n');

    for (const phase of plan.phases) {
      console.log(`\n=== Phase ${phase.phase}: ${phase.name} ===\n`);

      if (phase.name === 'Scripts Directory Reorganization') {
        try {
          // Execute scripts cleanup
          const restructurer = new ScriptRestructurer({
            dryRun: false,
            backup: true,
            scriptsDir: './scripts', // Ensure we're using the correct directory
          });

          await restructurer.restructure();
        } catch (error) {
          console.error(`Error during scripts restructuring: ${error.message}`);
          console.log('Continuing with other phases...');
        }
      } else {
        // For other phases, just log what would be done
        console.log(`Manual intervention required for: ${phase.name}`);
        if (phase.tasks && Array.isArray(phase.tasks)) {
          phase.tasks.forEach((task) => {
            console.log(`  - ${task.action} [${task.priority}]`);
          });
        } else {
          console.log('  No specific tasks defined for this phase');
        }
      }
    }
  }

  async generateFinalReport(healthReport, refactoringPlan) {
    const report = {
      timestamp: new Date().toISOString(),
      currentHealth: {
        score: healthReport.summary.healthScore,
        grade: healthReport.summary.grade,
        issues: healthReport.summary.totalIssues,
      },
      refactoringPlan: {
        phases: refactoringPlan.phases.length,
        estimatedEffort: `${refactoringPlan.estimatedEffort} days`,
        expectedImprovement: refactoringPlan.expectedImprovement,
      },
      recommendations: [
        'Start with critical security fixes immediately',
        'Use automated tools for scripts reorganization',
        'Tackle high-complexity files incrementally',
        'Set up CI/CD checks to maintain improvements',
        'Schedule regular code health reviews',
      ],
      healthScore: healthReport.summary.healthScore,
      totalIssues: healthReport.summary.totalIssues,
      criticalIssues: healthReport.issues.filter((i) => i.severity === 'critical').length,
      scriptAnalysis: this.scriptAnalysis,
      improvements: this.improvements,
    };

    const reportContent = `# Code Health Orchestration Report

Generated: ${new Date(report.timestamp).toLocaleString()}

## Current State

- **Health Score:** ${report.currentHealth.score}/100 (${report.currentHealth.grade})
- **Total Issues:** ${report.currentHealth.issues}

## Refactoring Plan

- **Phases:** ${report.refactoringPlan.phases}
- **Estimated Effort:** ${report.refactoringPlan.estimatedEffort}
- **Expected Improvement:** ${report.refactoringPlan.expectedImprovement.healthScore}/100

${
  this.improvements && this.improvements.length > 0
    ? `
## Improvements Since Last Run

${this.improvements.map((imp) => `- ${imp}`).join('\n')}
`
    : ''
}

## Key Recommendations

${report.recommendations.map((r, i) => `${i + 1}. ${r}`).join('\n')}

## Next Steps

1. Review the detailed analysis files:
   - \`code-health-report-*.json\` - Overall health report
   - \`scripts-analysis.json\` - Scripts directory analysis
   - \`scripts-detailed-analysis.json\` - Detailed scripts issues

2. Execute automated refactoring:
   \`\`\`bash
   node orchestrate-health.js --target scripts --execute
   \`\`\`

3. Manual refactoring tasks:
   - Review high-complexity files
   - Extract common utilities
   - Update documentation

## Monitoring

After refactoring, run the health check regularly:

\`\`\`bash
# Quick health check
node code-health/analyze-codebase.js

# Detailed analysis
node orchestrate-health.js
\`\`\`

---

*Healthy code is maintainable code. Invest in quality today for productivity tomorrow.*
`;

    await fs.writeFile('code-health-orchestration-report.md', reportContent);
    console.log('📄 Final report saved to: code-health-orchestration-report.md');

    // Save the report for future comparison
    await fs.writeFile('code-health-previous-report.json', JSON.stringify(report, null, 2));
  }

  // Load previous report at startup
  async loadPreviousReport() {
    try {
      const data = await fs.readFile('code-health-previous-report.json', 'utf-8');
      const previousReport = JSON.parse(data);
      this.previousHealthScore = previousReport.healthScore;
      console.log(`Previous health score: ${this.previousHealthScore}/100`);
      return previousReport;
    } catch (error) {
      console.log('No previous report found, creating baseline');
      return null;
    }
  }

  // Track improvements between runs
  trackImprovements(previousReport) {
    const improvements = [];

    if (!previousReport) {
      console.log('⚠️ Warning: No previous report provided to trackImprovements');
      return improvements;
    }

    // Compare health scores
    if (
      previousReport.healthScore !== undefined &&
      this.healthScore !== undefined &&
      this.healthScore > previousReport.healthScore
    ) {
      improvements.push(
        `Health score improved from ${previousReport.healthScore} to ${this.healthScore} (+${
          this.healthScore - previousReport.healthScore
        } points)`
      );
    }

    // Compare issue counts
    if (
      previousReport.totalIssues !== undefined &&
      this.issues &&
      this.issues.length !== undefined &&
      this.issues.length < previousReport.totalIssues
    ) {
      improvements.push(
        `Reduced total issues from ${previousReport.totalIssues} to ${this.issues.length} (-${
          previousReport.totalIssues - this.issues.length
        } issues)`
      );
    }

    if (
      previousReport.criticalIssues !== undefined &&
      this.criticalIssues &&
      this.criticalIssues.length !== undefined &&
      this.criticalIssues.length < previousReport.criticalIssues
    ) {
      improvements.push(
        `Reduced critical issues from ${previousReport.criticalIssues} to ${
          this.criticalIssues.length
        } (-${previousReport.criticalIssues - this.criticalIssues.length} issues)`
      );
    }

    // Check script analysis improvements
    if (this.scriptAnalysis && previousReport.scriptAnalysis) {
      // Check duplicates
      if (
        this.scriptAnalysis.duplicates &&
        previousReport.scriptAnalysis.duplicates &&
        this.scriptAnalysis.duplicates.length < previousReport.scriptAnalysis.duplicates.length
      ) {
        improvements.push(
          `Reduced script duplicates from ${previousReport.scriptAnalysis.duplicates.length} to ${this.scriptAnalysis.duplicates.length}`
        );
      }

      // Check merge opportunities
      if (
        this.scriptAnalysis.mergeOpportunities &&
        previousReport.scriptAnalysis.mergeOpportunities &&
        this.scriptAnalysis.mergeOpportunities.length <
          previousReport.scriptAnalysis.mergeOpportunities.length
      ) {
        improvements.push(
          `Addressed merge opportunities: ${
            previousReport.scriptAnalysis.mergeOpportunities.length -
            this.scriptAnalysis.mergeOpportunities.length
          } fewer opportunities`
        );
      }

      // Check obsolete scripts
      if (
        this.scriptAnalysis.obsoleteScripts &&
        previousReport.scriptAnalysis.obsoleteScripts &&
        this.scriptAnalysis.obsoleteScripts.length <
          previousReport.scriptAnalysis.obsoleteScripts.length
      ) {
        improvements.push(
          `Removed obsolete scripts: ${
            previousReport.scriptAnalysis.obsoleteScripts.length -
            this.scriptAnalysis.obsoleteScripts.length
          } fewer obsolete scripts`
        );
      }
    }

    return improvements;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  const options = {
    target: args.find((a) => a.startsWith('--target='))?.split('=')[1] || 'scripts',
    execute: args.includes('--execute'),
    verbose: args.includes('--verbose'),
  };

  const orchestrator = new HealthOrchestrator(options);
  await orchestrator.run();
}

// Run if called directly
if (import.meta.url.endsWith(process.argv[1])) {
  main().catch(console.error);
}

export default HealthOrchestrator;
