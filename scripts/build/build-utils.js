/**
 * Build Utilities
 * 
 * Utilities for building the ADHD Trading Dashboard monorepo packages.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import config from '../scripts.config.js';

/**
 * Execute a shell command and return the output
 * @param {string} command - The command to execute
 * @param {string} cwd - The working directory
 * @returns {string} The command output
 */
function executeCommand(command, cwd = process.cwd()) {
  try {
    console.log(`Executing: ${command} in ${cwd}`);
    const output = execSync(command, { 
      cwd, 
      stdio: 'inherit',
      env: { ...process.env, FORCE_COLOR: true }
    });
    return output ? output.toString() : '';
  } catch (error) {
    console.error(`Command failed: ${command}`);
    console.error(error.message);
    throw error;
  }
}

/**
 * Check if a directory exists
 * @param {string} dir - The directory path
 * @returns {boolean} Whether the directory exists
 */
function directoryExists(dir) {
  try {
    return fs.existsSync(dir) && fs.statSync(dir).isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * Clean build artifacts
 * @param {string} target - The target to clean (all, shared, core, dashboard)
 */
function clean(target = 'all') {
  console.log(`Cleaning build artifacts for: ${target}`);
  
  if (target === 'all') {
    // Clean all packages
    executeCommand('yarn clean');
  } else {
    // Clean specific package
    const packagePath = config.monorepo.packages.find(pkg => pkg.name === target)?.path;
    if (packagePath) {
      const distPath = path.join(process.cwd(), packagePath, 'dist');
      if (directoryExists(distPath)) {
        fs.rmSync(distPath, { recursive: true, force: true });
        console.log(`Cleaned ${distPath}`);
      }
    } else {
      console.error(`Unknown target: ${target}`);
    }
  }
}

/**
 * Build a specific package
 * @param {string} packageName - The package name (shared, core, dashboard)
 * @param {Object} options - Build options
 */
async function buildPackage(packageName, options = {}) {
  const pkg = config.monorepo.packages.find(p => p.name === packageName);
  if (!pkg) {
    throw new Error(`Unknown package: ${packageName}`);
  }
  
  console.log(`Building package: ${packageName}`);
  
  const buildCommand = options.watch 
    ? 'yarn build:watch' 
    : options.production 
      ? 'yarn build:prod' 
      : 'yarn build';
  
  executeCommand(buildCommand, path.join(process.cwd(), pkg.path));
  
  console.log(`Successfully built ${packageName}`);
}

/**
 * Build all packages in the correct order
 * @param {Object} options - Build options
 */
async function buildAll(options = {}) {
  console.log('Building all packages in order');
  
  // Clean first if not in watch mode
  if (!options.watch) {
    clean('all');
  }
  
  // Build each package in order
  for (const packageName of config.monorepo.buildOrder) {
    await buildPackage(packageName, options);
  }
  
  console.log('All packages built successfully');
}

/**
 * Main build function
 * @param {string} target - The target to build (all, shared, core, dashboard)
 * @param {Object} options - Build options
 */
async function build(target = 'all', options = {}) {
  console.log(`Build target: ${target}, options:`, options);
  
  try {
    if (target === 'all') {
      await buildAll(options);
    } else {
      await buildPackage(target, options);
    }
    console.log('Build completed successfully');
  } catch (error) {
    console.error('Build failed:', error.message);
    process.exit(1);
  }
}

export default {
  build,
  clean,
  buildPackage,
  buildAll
};
