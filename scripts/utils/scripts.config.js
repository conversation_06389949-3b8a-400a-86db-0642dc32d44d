/**
 * Central configuration for all scripts
 * Auto-generated by cleanup-scripts.js
 */

export default {
  paths: {
    src: './src',
    dist: './dist',
    build: './build',
    temp: './temp'
  },

  build: {
    minify: true,
    sourceMaps: true,
    target: 'es2020'
  },

  deploy: {
    environments: {
      development: {
        url: 'http://localhost:3000',
        branch: 'develop'
      },
      production: {
        url: 'https://app.example.com',
        branch: 'main'
      }
    }
  },

  analysis: {
    eslint: true,
    prettier: true,
    complexity: true,
    duplicates: true
  }
};
