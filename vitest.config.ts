import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: [
          ['babel-plugin-styled-components', {
            displayName: true,
            fileName: false,
            pure: true
          }]
        ]
      }
    })
  ],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./vitest.setup.ts'],
    include: ['packages/**/*.{test,spec}.{ts,tsx}'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: ['**/node_modules/**', '**/dist/**'],
    },
  },
  resolve: {
    alias: {
      '@adhd-trading-dashboard/shared': resolve(__dirname, 'packages/shared/src'),
      '@adhd-trading-dashboard/core': resolve(__dirname, 'packages/core/src'),
      '@adhd-trading-dashboard/dashboard': resolve(__dirname, 'packages/dashboard/src'),
    },
  },
});
