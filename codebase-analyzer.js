#!/usr/bin/env node

/**
 * React Codebase Structure Analyzer
 *
 * Analyzes a React codebase to understand its structure, patterns, and important elements
 *
 * Usage:
 * node codebase-analyzer.js [directory]
 *
 * If no directory specified, analyzes current directory
 */

import fs from 'fs';
import path from 'path';

class CodebaseAnalyzer {
  constructor(rootDir = '.') {
    this.rootDir = path.resolve(rootDir);
    this.stats = {
      totalFiles: 0,
      components: [],
      hooks: [],
      services: [],
      types: [],
      tests: [],
      configs: [],
      styles: [],
      directories: new Map(),
      technologies: new Set(),
      patterns: {
        stateManagement: new Set(),
        styling: new Set(),
        testing: new Set(),
        bundling: new Set(),
      },
    };
  }

  analyze() {
    console.log(`🔍 ANALYZING REACT CODEBASE: ${this.rootDir}`);
    console.log('='.repeat(60));

    this.scanDirectory(this.rootDir);
    this.identifyTechnologies();
    this.generateReport();
  }

  scanDirectory(dir, depth = 0) {
    if (depth > 10) return; // Prevent infinite recursion

    let items;
    try {
      items = fs.readdirSync(dir);
    } catch (error) {
      return;
    }

    const dirName = path.basename(dir);
    const relativePath = path.relative(this.rootDir, dir);

    // Skip certain directories
    if (this.shouldSkipDirectory(dirName)) return;

    let dirStats = {
      path: relativePath || '.',
      files: 0,
      components: 0,
      hooks: 0,
      types: 0,
      tests: 0,
      subdirs: 0,
    };

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        dirStats.subdirs++;
        this.scanDirectory(fullPath, depth + 1);
      } else if (stat.isFile()) {
        dirStats.files++;
        this.stats.totalFiles++;
        this.analyzeFile(fullPath, relativePath, dirStats);
      }
    }

    this.stats.directories.set(relativePath || '.', dirStats);
  }

  shouldSkipDirectory(dirName) {
    const skipDirs = [
      'node_modules',
      '.git',
      'dist',
      'build',
      '.next',
      'coverage',
      '.nyc_output',
      'tmp',
      '.cache',
    ];
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }

  analyzeFile(filePath, relativeDirPath, dirStats) {
    const fileName = path.basename(filePath);
    const ext = path.extname(fileName);
    const baseName = path.basename(fileName, ext);

    // Read file content for analysis
    let content = '';
    try {
      content = fs.readFileSync(filePath, 'utf8');
    } catch {
      return; // Skip files we can't read
    }

    const fileInfo = {
      name: fileName,
      path: path.relative(this.rootDir, filePath),
      size: content.length,
      lines: content.split('\n').length,
      type: this.getFileType(fileName, content),
      exports: this.findExports(content),
      imports: this.findImports(content),
      complexity: this.calculateComplexity(content),
    };

    // Categorize files
    if (this.isReactComponent(fileName, content)) {
      this.stats.components.push(fileInfo);
      dirStats.components++;
    } else if (this.isHook(fileName, content)) {
      this.stats.hooks.push(fileInfo);
      dirStats.hooks++;
    } else if (this.isService(fileName, content)) {
      this.stats.services.push(fileInfo);
    } else if (this.isTypeFile(fileName, content)) {
      this.stats.types.push(fileInfo);
      dirStats.types++;
    } else if (this.isTestFile(fileName)) {
      this.stats.tests.push(fileInfo);
      dirStats.tests++;
    } else if (this.isConfigFile(fileName)) {
      this.stats.configs.push(fileInfo);
    } else if (this.isStyleFile(fileName)) {
      this.stats.styles.push(fileInfo);
    }

    // Detect patterns in content
    this.detectPatterns(content);
  }

  getFileType(fileName, content) {
    const ext = path.extname(fileName);
    if (['.tsx', '.jsx'].includes(ext)) return 'Component';
    if (['.ts', '.js'].includes(ext)) {
      if (content.includes('export default') || content.includes('export const')) {
        return 'Module';
      }
      return 'Script';
    }
    if (['.css', '.scss', '.sass', '.less'].includes(ext)) return 'Style';
    if (fileName.includes('test') || fileName.includes('spec')) return 'Test';
    if (fileName.includes('config')) return 'Config';
    return 'Other';
  }

  isReactComponent(fileName, content) {
    return (
      ((fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) &&
        (content.includes('export default') || content.includes('export const')) &&
        (content.includes('return (') || content.includes('return<') || content.includes('jsx'))) ||
      content.includes('React.') ||
      content.includes('useState') ||
      content.includes('useEffect') ||
      content.includes('</') // JSX closing tag
    );
  }

  isHook(fileName, content) {
    return (
      fileName.startsWith('use') ||
      content.includes('export const use') ||
      content.includes('export default use') ||
      (content.includes('useState') && content.includes('function use'))
    );
  }

  isService(fileName, content) {
    return (
      fileName.toLowerCase().includes('service') ||
      fileName.toLowerCase().includes('api') ||
      fileName.toLowerCase().includes('storage') ||
      (content.includes('class') && content.includes('service')) ||
      (content.includes('export') && fileName.toLowerCase().includes('client'))
    );
  }

  isTypeFile(fileName, content) {
    return (
      fileName.includes('types') ||
      fileName.includes('interfaces') ||
      fileName.endsWith('.d.ts') ||
      (content.includes('interface ') && content.includes('export')) ||
      (content.includes('type ') && content.includes('='))
    );
  }

  isTestFile(fileName) {
    return (
      fileName.includes('.test.') || fileName.includes('.spec.') || fileName.includes('__tests__')
    );
  }

  isConfigFile(fileName) {
    const configFiles = [
      'package.json',
      'tsconfig.json',
      'vite.config',
      'webpack.config',
      '.eslintrc',
      'prettier.config',
      'tailwind.config',
      'next.config',
    ];
    return configFiles.some((config) => fileName.includes(config));
  }

  isStyleFile(fileName) {
    return /\.(css|scss|sass|less|styl)$/.test(fileName);
  }

  findExports(content) {
    const exports = [];
    const patterns = [
      /export\s+default\s+(\w+)/g,
      /export\s+const\s+(\w+)/g,
      /export\s+function\s+(\w+)/g,
      /export\s+class\s+(\w+)/g,
      /export\s*{\s*([^}]+)\s*}/g,
    ];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        exports.push(match[1]);
      }
    });

    return exports;
  }

  findImports(content) {
    const imports = [];
    const importPattern = /import.*?from\s+['"]([^'"]+)['"]/g;
    let match;

    while ((match = importPattern.exec(content)) !== null) {
      imports.push(match[1]);
    }

    return imports;
  }

  calculateComplexity(content) {
    const complexityIndicators = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /&&/g,
      /\|\|/g,
      /\?.*:/g, // ternary
      /useEffect/g,
      /useState/g,
    ];

    let complexity = 1; // Base complexity

    complexityIndicators.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    });

    return complexity;
  }

  detectPatterns(content) {
    // State management patterns
    if (content.includes('useState') || content.includes('useReducer')) {
      this.stats.patterns.stateManagement.add('React Hooks');
    }
    if (content.includes('redux') || content.includes('useDispatch')) {
      this.stats.patterns.stateManagement.add('Redux');
    }
    if (content.includes('zustand')) {
      this.stats.patterns.stateManagement.add('Zustand');
    }
    if (content.includes('recoil')) {
      this.stats.patterns.stateManagement.add('Recoil');
    }

    // Styling patterns
    if (content.includes('styled-components') || content.includes('styled.')) {
      this.stats.patterns.styling.add('Styled Components');
    }
    if ((content.includes('className=') && content.includes('bg-')) || content.includes('text-')) {
      this.stats.patterns.styling.add('Tailwind CSS');
    }
    if (content.includes('@emotion')) {
      this.stats.patterns.styling.add('Emotion');
    }
    if (content.includes('makeStyles') || content.includes('@mui')) {
      this.stats.patterns.styling.add('Material-UI');
    }

    // Testing patterns
    if (content.includes('jest') || content.includes('describe(') || content.includes('it(')) {
      this.stats.patterns.testing.add('Jest');
    }
    if (content.includes('@testing-library')) {
      this.stats.patterns.testing.add('Testing Library');
    }
    if (content.includes('enzyme')) {
      this.stats.patterns.testing.add('Enzyme');
    }
  }

  identifyTechnologies() {
    // Check package.json for technologies
    const packageJsonPath = path.join(this.rootDir, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

        Object.keys(deps).forEach((dep) => {
          if (dep.includes('react')) this.stats.technologies.add('React');
          if (dep.includes('typescript')) this.stats.technologies.add('TypeScript');
          if (dep.includes('vite')) this.stats.technologies.add('Vite');
          if (dep.includes('webpack')) this.stats.technologies.add('Webpack');
          if (dep.includes('next')) this.stats.technologies.add('Next.js');
          if (dep.includes('tailwind')) this.stats.technologies.add('Tailwind CSS');
          if (dep.includes('eslint')) this.stats.technologies.add('ESLint');
          if (dep.includes('prettier')) this.stats.technologies.add('Prettier');
        });
      } catch (error) {
        console.warn('Could not parse package.json');
      }
    }
  }

  generateReport() {
    console.log('\n📊 CODEBASE ANALYSIS REPORT');
    console.log('='.repeat(60));

    // Overview
    console.log('\n🏗️  OVERVIEW');
    console.log(`Total Files: ${this.stats.totalFiles}`);
    console.log(`Technologies: ${Array.from(this.stats.technologies).join(', ')}`);

    // Components analysis
    console.log('\n⚛️  REACT COMPONENTS');
    console.log(`Total Components: ${this.stats.components.length}`);

    if (this.stats.components.length > 0) {
      const largeComponents = this.stats.components
        .filter((c) => c.lines > 200)
        .sort((a, b) => b.lines - a.lines);

      if (largeComponents.length > 0) {
        console.log('\n🚨 Large Components (>200 lines):');
        largeComponents.slice(0, 5).forEach((comp) => {
          console.log(`   ${comp.name}: ${comp.lines} lines (complexity: ${comp.complexity})`);
        });
      }

      const complexComponents = this.stats.components
        .filter((c) => c.complexity > 20)
        .sort((a, b) => b.complexity - a.complexity);

      if (complexComponents.length > 0) {
        console.log('\n🧠 Complex Components (complexity >20):');
        complexComponents.slice(0, 5).forEach((comp) => {
          console.log(`   ${comp.name}: complexity ${comp.complexity} (${comp.lines} lines)`);
        });
      }
    }

    // Hooks analysis
    console.log('\n🪝 REACT HOOKS');
    console.log(`Total Custom Hooks: ${this.stats.hooks.length}`);
    if (this.stats.hooks.length > 0) {
      this.stats.hooks.slice(0, 5).forEach((hook) => {
        console.log(`   ${hook.name}: ${hook.lines} lines`);
      });
    }

    // Services analysis
    console.log('\n🔧 SERVICES & APIs');
    console.log(`Total Services: ${this.stats.services.length}`);
    if (this.stats.services.length > 0) {
      this.stats.services.slice(0, 5).forEach((service) => {
        console.log(`   ${service.name}: ${service.lines} lines`);
      });
    }

    // Directory structure
    console.log('\n📁 DIRECTORY STRUCTURE');
    const sortedDirs = Array.from(this.stats.directories.entries())
      .filter(([path, stats]) => stats.files > 0)
      .sort((a, b) => b[1].files - a[1].files);

    sortedDirs.slice(0, 10).forEach(([path, stats]) => {
      console.log(
        `   ${path || '.'}: ${stats.files} files` +
          (stats.components > 0 ? `, ${stats.components} components` : '') +
          (stats.hooks > 0 ? `, ${stats.hooks} hooks` : '') +
          (stats.tests > 0 ? `, ${stats.tests} tests` : '')
      );
    });

    // Patterns
    console.log('\n🎨 DETECTED PATTERNS');
    if (this.stats.patterns.stateManagement.size > 0) {
      console.log(
        `State Management: ${Array.from(this.stats.patterns.stateManagement).join(', ')}`
      );
    }
    if (this.stats.patterns.styling.size > 0) {
      console.log(`Styling: ${Array.from(this.stats.patterns.styling).join(', ')}`);
    }
    if (this.stats.patterns.testing.size > 0) {
      console.log(`Testing: ${Array.from(this.stats.patterns.testing).join(', ')}`);
    }

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS');

    const largeFiles = [...this.stats.components, ...this.stats.hooks, ...this.stats.services]
      .filter((f) => f.lines > 300)
      .sort((a, b) => b.lines - a.lines);

    if (largeFiles.length > 0) {
      console.log('   🔄 Consider splitting these large files:');
      largeFiles.slice(0, 3).forEach((file) => {
        console.log(`      ${file.name} (${file.lines} lines)`);
      });
    }

    if (this.stats.tests.length === 0) {
      console.log('   ✅ Consider adding tests to improve code quality');
    }

    if (!this.stats.technologies.has('TypeScript')) {
      console.log('   🔧 Consider migrating to TypeScript for better type safety');
    }

    console.log('\n🎯 FOCUS AREAS FOR CLEANUP');
    console.log('   1. Split components >200 lines into smaller pieces');
    console.log('   2. Reduce complexity in components with complexity >20');
    console.log('   3. Add tests for critical business logic');
    console.log('   4. Consider extracting reusable hooks from large components');
  }
}

// CLI usage
const targetDir = process.argv[2] || '.';
const analyzer = new CodebaseAnalyzer(targetDir);
analyzer.analyze();
