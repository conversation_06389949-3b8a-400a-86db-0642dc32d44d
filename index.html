<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ADHD Trading Dashboard</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #1a1f2c;
      color: #ffffff;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #333333;
      padding-bottom: 10px;
    }
    .title {
      font-size: 24px;
      margin: 0;
      color: #ffffff;
    }
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }
    .metric-card {
      background-color: #252a37;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    .metric-title {
      font-size: 14px;
      color: #aaaaaa;
      margin-bottom: 8px;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: #ffffff;
    }
    .section {
      background-color: #252a37;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    .section-title {
      font-size: 18px;
      margin-top: 0;
      margin-bottom: 15px;
      color: #ffffff;
    }
    .chart-placeholder {
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #aaaaaa;
      background-color: #1a1f2c;
      border-radius: 4px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th {
      text-align: left;
      padding: 12px 8px;
      border-bottom: 1px solid #333333;
      font-weight: bold;
      color: #aaaaaa;
    }
    td {
      padding: 12px 8px;
      border-bottom: 1px solid #333333;
    }
    .positive {
      color: #4caf50;
    }
    .negative {
      color: #f44336;
    }
    .badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }
    .badge-long {
      background-color: rgba(76, 175, 80, 0.2);
      color: #4caf50;
    }
    .badge-short {
      background-color: rgba(244, 67, 54, 0.2);
      color: #f44336;
    }
    .badge-win {
      background-color: rgba(76, 175, 80, 0.2);
      color: #4caf50;
    }
    .badge-loss {
      background-color: rgba(244, 67, 54, 0.2);
      color: #f44336;
    }
    .tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #333333;
    }
    .tab {
      padding: 10px 20px;
      cursor: pointer;
      color: #aaaaaa;
      position: relative;
    }
    .tab.active {
      color: #e10600;
    }
    .tab.active:after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #e10600;
    }
    .refresh-button {
      background-color: #e10600;
      color: #ffffff;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 14px;
      cursor: pointer;
    }
    .refresh-button:hover {
      background-color: #c10500;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 class="title">ADHD Trading Dashboard</h1>
      <button class="refresh-button">Refresh Data</button>
    </div>

    <div class="tabs">
      <div class="tab active">Summary</div>
      <div class="tab">Trades</div>
      <div class="tab">Analysis</div>
    </div>

    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-title">Win Rate</div>
        <div class="metric-value">65%</div>
      </div>
      <div class="metric-card">
        <div class="metric-title">Total P&L</div>
        <div class="metric-value">$12,500</div>
      </div>
      <div class="metric-card">
        <div class="metric-title">Avg R-Multiple</div>
        <div class="metric-value">2.3</div>
      </div>
      <div class="metric-card">
        <div class="metric-title">Total Trades</div>
        <div class="metric-value">120</div>
      </div>
    </div>

    <div class="section">
      <h2 class="section-title">Performance Chart</h2>
      <div class="chart-placeholder">
        [Performance Chart - Mock Data]
        <br>
        2023-05-01 to 2023-05-15
        <br>
        Starting: $10,000 → Ending: $11,200
      </div>
    </div>

    <div class="section">
      <h2 class="section-title">Recent Trades</h2>
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Symbol</th>
            <th>Direction</th>
            <th>Result</th>
            <th>Profit/Loss</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>2023-05-15</td>
            <td>AAPL</td>
            <td><span class="badge badge-long">LONG</span></td>
            <td><span class="badge badge-win">WIN</span></td>
            <td class="positive">$350</td>
          </tr>
          <tr>
            <td>2023-05-14</td>
            <td>MSFT</td>
            <td><span class="badge badge-short">SHORT</span></td>
            <td><span class="badge badge-loss">LOSS</span></td>
            <td class="negative">-$150</td>
          </tr>
          <tr>
            <td>2023-05-13</td>
            <td>GOOGL</td>
            <td><span class="badge badge-long">LONG</span></td>
            <td><span class="badge badge-win">WIN</span></td>
            <td class="positive">$420</td>
          </tr>
          <tr>
            <td>2023-05-12</td>
            <td>TSLA</td>
            <td><span class="badge badge-short">SHORT</span></td>
            <td><span class="badge badge-win">WIN</span></td>
            <td class="positive">$280</td>
          </tr>
          <tr>
            <td>2023-05-11</td>
            <td>AMZN</td>
            <td><span class="badge badge-long">LONG</span></td>
            <td><span class="badge badge-loss">LOSS</span></td>
            <td class="negative">-$200</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <script>
    // Simple tab switching functionality
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
      });
    });

    // Refresh button functionality
    document.querySelector('.refresh-button').addEventListener('click', () => {
      alert('Data refreshed!');
    });
  </script>
</body>
</html>
