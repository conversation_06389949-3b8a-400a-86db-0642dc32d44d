const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const port = 3000;

// Check if we're in a monorepo structure
const isMonorepo = fs.existsSync(path.join(__dirname, 'packages'));

// Set up paths based on project structure
const dashboardPath = isMonorepo ? path.join(__dirname, 'packages', 'dashboard') : __dirname;

const distPath = path.join(dashboardPath, 'dist');
const publicPath = path.join(dashboardPath, 'public');

// Determine which directory to serve from
let staticPath = __dirname;
if (fs.existsSync(distPath)) {
  staticPath = distPath;
  console.log('Serving from dashboard dist directory');
} else if (fs.existsSync(publicPath)) {
  staticPath = publicPath;
  console.log('Serving from dashboard public directory');
} else {
  console.log('Serving from root directory');
}

// Serve static files
app.use(express.static(staticPath));

// Serve our standalone dashboard for the root route
app.get('/', (req, res) => {
  // First try to serve index.html from the static path
  const indexPath = path.join(staticPath, 'index.html');

  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    // Fall back to our standalone dashboard
    res.sendFile(path.join(__dirname, 'index.html'));
  }
});

// Handle all other routes for SPA
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Start the server
app.listen(port, () => {
  console.log(`Dashboard server running at http://localhost:${port}`);
  console.log(`Serving files from: ${staticPath}`);
});
