#!/usr/bin/env node

/**
 * Schema & Structure Validator
 *
 * Finds files and folders not following the established trading schema and structure patterns
 * Focuses on actual source code, ignores generated files
 *
 * Usage: node schema-validator.js [directory]
 */

import fs from 'fs';
import path from 'path';

class SchemaStructureValidator {
  constructor(rootDir = '.') {
    this.rootDir = path.resolve(rootDir);
    this.scriptName = path.basename(import.meta.url);
    this.violations = {
      oldApiPatterns: [],
      missingTypeImports: [],
      incorrectFileLocations: [],
      namingViolations: [],
      schemaInconsistencies: [],
      largeSourceFiles: [],
    };

    // Expected schema patterns
    this.expectedPatterns = {
      tradingTypes: ['Trade', 'TradeFormData', 'TradeFvgDetails', 'TradeSetup', 'TradeAnalysis'],
      newServiceMethods: ['saveTradeWithDetails', 'getTradeById', 'getPerformanceMetrics'],
      oldApiPatterns: ['ApiClient', 'TradingApiClient', 'useApiQuery', 'api.call', 'gasApi'],
    };
  }

  validate() {
    console.log(`🔍 VALIDATING SCHEMA & STRUCTURE: ${this.rootDir}`);
    console.log('='.repeat(60));

    this.scanDirectory(this.rootDir);
    this.generateReport();
  }

  scanDirectory(dir, depth = 0) {
    if (depth > 10) return;

    let items;
    try {
      items = fs.readdirSync(dir);
    } catch (error) {
      return;
    }

    const dirName = path.basename(dir);
    if (this.shouldSkipDirectory(dirName)) return;

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        this.validateDirectoryStructure(fullPath);
        this.scanDirectory(fullPath, depth + 1);
      } else if (stat.isFile()) {
        this.validateFile(fullPath);
      }
    }
  }

  shouldSkipDirectory(dirName) {
    const skipDirs = [
      'node_modules',
      '.git',
      'dist',
      'build',
      '.next',
      'coverage',
      '.nyc_output',
      'tmp',
      '.cache',
      '.vscode',
      'code-health', // Skip diagnostic/utility scripts
    ];
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }

  shouldSkipFile(fileName) {
    // Skip generated files and non-source files
    const skipFiles = [
      'package-lock.json',
      'yarn.lock',
      'pnpm-lock.yaml',
      '.gitignore',
      '.eslintrc',
      '.prettierrc',
      'workbox-',
      'sw.js',
      'registerSW.js',
      'blank-page-diagnostic', // Skip diagnostic scripts
      'codebase-analyzer', // Skip analyzer scripts
    ];

    const skipExtensions = ['.map', '.lock', '.log', '.md'];

    // Skip if it's this script or other diagnostic files
    if (
      fileName === this.scriptName ||
      fileName.includes('diagnostic') ||
      fileName.includes('analyzer') ||
      fileName.includes('code-health')
    ) {
      return true;
    }

    return (
      skipFiles.some((skip) => fileName.includes(skip)) ||
      skipExtensions.some((ext) => fileName.endsWith(ext))
    );
  }

  validateFile(filePath) {
    const fileName = path.basename(filePath);

    if (this.shouldSkipFile(fileName)) return;

    let content = '';
    try {
      content = fs.readFileSync(filePath, 'utf8');
    } catch {
      return;
    }

    const relativePath = path.relative(this.rootDir, filePath);

    // Only analyze source files
    if (!this.isSourceFile(fileName)) return;

    const fileInfo = {
      name: fileName,
      path: relativePath,
      lines: content.split('\n').length,
      content,
    };

    this.checkOldApiPatterns(fileInfo);
    this.checkMissingTypeImports(fileInfo);
    this.checkFileLocation(fileInfo);
    this.checkNamingConventions(fileInfo);
    this.checkSchemaConsistency(fileInfo);
    this.checkFileSizeViolations(fileInfo);
  }

  isSourceFile(fileName) {
    const sourceExtensions = ['.ts', '.tsx', '.js', '.jsx'];
    return sourceExtensions.some((ext) => fileName.endsWith(ext));
  }

  validateDirectoryStructure(dirPath) {
    const relativePath = path.relative(this.rootDir, dirPath);
    const dirName = path.basename(dirPath);

    // Check for incorrect directory naming in features
    if (relativePath.includes('features') && relativePath.includes('trade')) {
      if (
        !dirName.includes('trade') &&
        !['components', 'hooks', 'services', 'types'].includes(dirName)
      ) {
        this.violations.namingViolations.push({
          type: 'directory',
          path: relativePath,
          issue: 'Directory in trade features should follow naming convention',
          suggestion: 'Use trade-* naming or standard folder names (components, hooks, etc.)',
        });
      }
    }
  }

  checkOldApiPatterns(fileInfo) {
    this.expectedPatterns.oldApiPatterns.forEach((pattern) => {
      if (fileInfo.content.includes(pattern)) {
        this.violations.oldApiPatterns.push({
          file: fileInfo.path,
          pattern,
          line: this.findLineNumber(fileInfo.content, pattern),
          suggestion: 'Replace with direct tradeStorageService calls',
        });
      }
    });

    // Check for old import patterns
    const oldImportPatterns = [
      /import.*from.*['"].*api.*client.*['"]/g,
      /import.*ApiClient.*from/g,
      /import.*gasApi.*from/g,
    ];

    oldImportPatterns.forEach((pattern) => {
      if (pattern.test(fileInfo.content)) {
        this.violations.oldApiPatterns.push({
          file: fileInfo.path,
          pattern: 'Old API import',
          suggestion: 'Update to import from tradeStorageService or trading types',
        });
      }
    });
  }

  checkMissingTypeImports(fileInfo) {
    // Files that should use trading types - be more specific
    const shouldUseTradingTypes = [
      fileInfo.path.includes('trade-journal') && fileInfo.content.includes('Trade'),
      fileInfo.path.includes('trade-analysis') && fileInfo.content.includes('Trade'),
      fileInfo.path.includes('trading-dashboard') && fileInfo.content.includes('Trade'),
      fileInfo.content.includes('TradeFormData') && !fileInfo.content.includes('import'),
      (fileInfo.content.includes('entry_price') || fileInfo.content.includes('exit_price')) &&
        fileInfo.content.includes('interface') &&
        !fileInfo.content.includes('import'),
    ].some(Boolean);

    if (shouldUseTradingTypes) {
      const hasCorrectTypeImport =
        fileInfo.content.includes('from') &&
        (fileInfo.content.includes('@adhd-trading-dashboard/shared') ||
          fileInfo.content.includes('packages/shared/src/types/trading'));

      const usesTradingTypes = this.expectedPatterns.tradingTypes.some(
        (type) =>
          fileInfo.content.includes(`import.*${type}`) ||
          fileInfo.content.includes(`} from.*shared`)
      );

      if (!hasCorrectTypeImport && !usesTradingTypes) {
        this.violations.missingTypeImports.push({
          file: fileInfo.path,
          issue: 'Should import trading types from shared package',
          suggestion: 'Add: import { Trade, TradeFormData } from "@adhd-trading-dashboard/shared"',
        });
      }
    }
  }

  checkFileLocation(fileInfo) {
    // Check for duplicate Trade interface definitions (not imports)
    const hasTradeInterfaceDefinition =
      fileInfo.content.includes('interface Trade') ||
      fileInfo.content.includes('export interface Trade') ||
      fileInfo.content.includes('type Trade =');

    if (
      hasTradeInterfaceDefinition &&
      !fileInfo.path.includes('packages/shared/src/types/trading.ts') &&
      !fileInfo.path.includes('test') &&
      !fileInfo.path.includes('spec')
    ) {
      this.violations.incorrectFileLocations.push({
        file: fileInfo.path,
        issue: 'Duplicate Trade interface definition found',
        suggestion:
          'Remove local Trade interface and import from packages/shared/src/types/trading.ts',
      });
    }

    // Check if actual service files are in wrong location (be more specific)
    const isActualService =
      fileInfo.name.toLowerCase().includes('service.ts') ||
      fileInfo.name.toLowerCase().includes('service.js') ||
      (fileInfo.content.includes('IndexedDB') && fileInfo.content.includes('class')) ||
      (fileInfo.content.includes('storage') &&
        fileInfo.content.includes('export') &&
        fileInfo.content.includes('save'));

    if (
      isActualService &&
      !fileInfo.path.includes('services') &&
      !fileInfo.path.includes('test') &&
      !fileInfo.path.includes('node_modules')
    ) {
      this.violations.incorrectFileLocations.push({
        file: fileInfo.path,
        issue: 'Service file not in services directory',
        suggestion: 'Move to appropriate services/ directory',
      });
    }
  }

  checkNamingConventions(fileInfo) {
    // Check component naming
    if (
      (fileInfo.name.endsWith('.tsx') || fileInfo.name.endsWith('.jsx')) &&
      fileInfo.content.includes('export default')
    ) {
      const componentName = path.basename(fileInfo.name, path.extname(fileInfo.name));

      // Component should be PascalCase
      if (componentName !== componentName.charAt(0).toUpperCase() + componentName.slice(1)) {
        this.violations.namingViolations.push({
          file: fileInfo.path,
          issue: 'Component file should be PascalCase',
          suggestion: `Rename to ${componentName.charAt(0).toUpperCase() + componentName.slice(1)}`,
        });
      }

      // Trade-related components should include "Trade" in name
      if (
        fileInfo.content.includes('trade') &&
        fileInfo.path.includes('trade-journal') &&
        !componentName.toLowerCase().includes('trade')
      ) {
        this.violations.namingViolations.push({
          file: fileInfo.path,
          issue: 'Trade-related component should include "Trade" in name',
          suggestion: 'Consider renaming to Trade* for clarity',
        });
      }
    }

    // Check hook naming
    if (
      fileInfo.name.startsWith('use') &&
      fileInfo.content.includes('export') &&
      !fileInfo.name.startsWith('use')
    ) {
      this.violations.namingViolations.push({
        file: fileInfo.path,
        issue: 'Hook file should start with "use"',
        suggestion: 'Rename to use* convention',
      });
    }
  }

  checkSchemaConsistency(fileInfo) {
    // Check for problematic hardcoded field access patterns
    const hardcodedFields = [
      'entry_price',
      'exit_price',
      'win_loss',
      'r_multiple',
      'pattern_quality_rating',
      'model_type',
    ];

    // Only flag raw string literals that are NOT part of typed constants
    const hasTypedConstants = this.hasTypedFieldConstants(fileInfo.content);
    const hasColumnIds =
      fileInfo.content.includes('TRADE_COLUMN_IDS') ||
      fileInfo.content.includes('COLUMN_IDS') ||
      fileInfo.content.includes('FIELD_IDS');

    hardcodedFields.forEach((field) => {
      const hasRawStringLiteral =
        fileInfo.content.includes(`"${field}"`) || fileInfo.content.includes(`'${field}'`);

      if (hasRawStringLiteral) {
        // Skip if this file defines typed constants (good practice)
        if (hasTypedConstants || hasColumnIds) {
          // Check if the string literal is used OUTSIDE of constant definitions
          const isProblematicUsage = this.isProblematicStringLiteralUsage(fileInfo.content, field);

          if (isProblematicUsage) {
            this.violations.schemaInconsistencies.push({
              file: fileInfo.path,
              field,
              issue: 'Raw string literal field access found alongside typed constants',
              suggestion:
                'Use the existing typed constants (e.g., TRADE_COLUMN_IDS) instead of raw strings',
            });
          }
          // If it's just constant definitions, don't flag it
        } else {
          // No typed constants found, flag raw string usage
          this.violations.schemaInconsistencies.push({
            file: fileInfo.path,
            field,
            issue: 'Hardcoded field name should use typed constants',
            suggestion:
              'Define typed constants (e.g., TRADE_COLUMN_IDS) or use TypeScript interface properties',
          });
        }
      }
    });

    // Check for old data structures
    if (
      fileInfo.content.includes('tradeData') &&
      !fileInfo.content.includes('Trade') &&
      !fileInfo.path.includes('test')
    ) {
      this.violations.schemaInconsistencies.push({
        file: fileInfo.path,
        issue: 'Using generic tradeData instead of Trade interface',
        suggestion: 'Use Trade interface from types/trading.ts',
      });
    }
  }

  /**
   * Check if file contains typed field constants (good practice)
   */
  hasTypedFieldConstants(content) {
    const constantPatterns = [
      /const\s+\w*COLUMN_IDS?\s*=\s*{/,
      /const\s+\w*FIELD_IDS?\s*=\s*{/,
      /export\s+const\s+\w*COLUMN_IDS?\s*=\s*{/,
      /export\s+const\s+\w*FIELD_IDS?\s*=\s*{/,
      /'[^']+'\s+as\s+const/,
      /"[^"]+"\s+as\s+const/,
    ];

    return constantPatterns.some((pattern) => pattern.test(content));
  }

  /**
   * Check if string literal usage is problematic (not in constant definitions)
   */
  isProblematicStringLiteralUsage(content, field) {
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Skip if this line is defining a constant
      if (this.isConstantDefinitionLine(line)) {
        continue;
      }

      // Check for problematic patterns like: obj["field"] or obj['field']
      const problematicPatterns = [
        new RegExp(`\\w+\\["${field}"\\]`),
        new RegExp(`\\w+\\['${field}'\\]`),
        new RegExp(`\\["${field}"\\]`),
        new RegExp(`\\['${field}'\\]`),
      ];

      if (problematicPatterns.some((pattern) => pattern.test(line))) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a line is defining a constant (should not be flagged)
   */
  isConstantDefinitionLine(line) {
    const constantLinePatterns = [
      /const\s+\w+\s*=\s*{/,
      /export\s+const\s+\w+\s*=\s*{/,
      /\w+:\s*['"][^'"]+['"]\s*as\s+const/,
      /\w+:\s*['"][^'"]+['"],?\s*$/,
      /^\s*['"][^'"]+['"]:\s*['"][^'"]+['"],?\s*$/,
    ];

    return constantLinePatterns.some((pattern) => pattern.test(line.trim()));
  }

  checkFileSizeViolations(fileInfo) {
    // Only flag actual source files, not generated ones
    if (fileInfo.lines > 300 && this.isSourceFile(fileInfo.name)) {
      this.violations.largeSourceFiles.push({
        file: fileInfo.path,
        lines: fileInfo.lines,
        suggestion: 'Consider splitting into smaller focused files',
      });
    }
  }

  findLineNumber(content, searchText) {
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(searchText)) {
        return i + 1;
      }
    }
    return null;
  }

  generateReport() {
    console.log('\n📊 SCHEMA & STRUCTURE VALIDATION REPORT');
    console.log('='.repeat(60));

    let totalViolations = 0;

    // Old API Patterns
    if (this.violations.oldApiPatterns.length > 0) {
      console.log('\n🚨 OLD API PATTERNS (High Priority)');
      this.violations.oldApiPatterns.slice(0, 10).forEach((violation) => {
        console.log(`   ${violation.file}: ${violation.pattern}`);
        console.log(`      → ${violation.suggestion}`);
      });
      totalViolations += this.violations.oldApiPatterns.length;
    }

    // Missing Type Imports
    if (this.violations.missingTypeImports.length > 0) {
      console.log('\n📝 MISSING TYPE IMPORTS (Medium Priority)');
      this.violations.missingTypeImports.slice(0, 8).forEach((violation) => {
        console.log(`   ${violation.file}`);
        console.log(`      → ${violation.suggestion}`);
      });
      totalViolations += this.violations.missingTypeImports.length;
    }

    // Incorrect File Locations
    if (this.violations.incorrectFileLocations.length > 0) {
      console.log('\n📁 INCORRECT FILE LOCATIONS (Medium Priority)');
      this.violations.incorrectFileLocations.forEach((violation) => {
        console.log(`   ${violation.file}: ${violation.issue}`);
        console.log(`      → ${violation.suggestion}`);
      });
      totalViolations += this.violations.incorrectFileLocations.length;
    }

    // Schema Inconsistencies
    if (this.violations.schemaInconsistencies.length > 0) {
      console.log('\n🔧 SCHEMA INCONSISTENCIES (Medium Priority)');
      this.violations.schemaInconsistencies.slice(0, 8).forEach((violation) => {
        console.log(`   ${violation.file}: ${violation.field || violation.issue}`);
        console.log(`      → ${violation.suggestion}`);
      });
      totalViolations += this.violations.schemaInconsistencies.length;
    }

    // Large Source Files (excluding generated files)
    if (this.violations.largeSourceFiles.length > 0) {
      console.log('\n📏 LARGE SOURCE FILES (Low Priority)');
      this.violations.largeSourceFiles.slice(0, 5).forEach((violation) => {
        console.log(`   ${violation.file}: ${violation.lines} lines`);
        console.log(`      → ${violation.suggestion}`);
      });
      totalViolations += this.violations.largeSourceFiles.length;
    }

    // Naming Violations
    if (this.violations.namingViolations.length > 0) {
      console.log('\n🏷️  NAMING VIOLATIONS (Low Priority)');
      this.violations.namingViolations.slice(0, 5).forEach((violation) => {
        console.log(`   ${violation.file || violation.path}: ${violation.issue}`);
        console.log(`      → ${violation.suggestion}`);
      });
      totalViolations += this.violations.namingViolations.length;
    }

    // Summary
    console.log('\n📈 SUMMARY');
    console.log(`Total Violations: ${totalViolations}`);

    if (totalViolations === 0) {
      console.log('🎉 No schema or structure violations found!');
    } else {
      console.log('\n🎯 RECOMMENDED ACTION ORDER:');
      console.log('1. Fix old API patterns (breaks functionality)');
      console.log('2. Add missing type imports (improves type safety)');
      console.log('3. Move files to correct locations (improves organization)');
      console.log('4. Fix schema inconsistencies (prevents bugs)');
      console.log('5. Split large files (improves maintainability)');
    }

    console.log('\n✅ NOTE: Correctly ignored generated files like package-lock.json, yarn.lock');
  }
}

// CLI usage
const targetDir = process.argv[2] || '.';
const validator = new SchemaStructureValidator(targetDir);
validator.validate();
