# ADHD Trading Dashboard Monorepo

A React-based dashboard library for Google Apps Script with a Formula 1 racing theme. This library provides a modern, responsive UI for displaying trading metrics, performance charts, and market news.

## What is a Monorepo?

A **monorepo** is a single repository that contains multiple related packages or projects. In this project, all the main code for the dashboard, shared utilities, and Google Apps Script integration live together in one place for easier development and code sharing.

## What lives at the root?

- **README.md**: This file! High-level documentation and navigation for the whole project.
- **package.json**: Workspace configuration, shared dev dependencies, and scripts for the whole monorepo.
- **tsconfig.json**: TypeScript settings for the whole repo.
- **webpack.config.js**: Webpack bundling config (if used).
- **workspace.json**: (Optional) Additional workspace settings.
- **docs/**: Project documentation.
- **examples/**: Example usage or integration scripts.
- **public/**: Static files (e.g., index.html, images).
- **scripts/**: Build and deployment scripts.
- **server/**, **src/**: Legacy or migration code (most new code is in `packages/`).
- **appsscript.json**: Google Apps Script manifest (if deploying to GAS).

## Monorepo Architecture

The monorepo is organized into three primary packages (see `packages/`):

1. **core**: Google Apps Script integration layer

   - Provides bootstrap code for GAS integration
   - Handles communication between React and Google Apps Script
   - Contains server-side code for Google Apps Script
   - [See core README](./packages/core/README.md)

2. **shared**: Common utilities and components

   - Exports reusable components, hooks, theme, and utilities
   - Provides the foundation for both core and dashboard packages
   - Contains the theme system with F1-inspired design
   - [See shared README](./packages/shared/README.md)

3. **dashboard**: React application
   - User-facing frontend with pages and layouts
   - Implements feature-specific components
   - Uses React Router for navigation
   - [See dashboard README](./packages/dashboard/README.md)

## Features

- **Modern React UI**: Built with React, TypeScript, and modern JavaScript features
- **Formula 1 Racing Theme**: Sleek, high-performance design inspired by Formula 1 racing
- **Code Splitting**: Optimized for Google Apps Script's 250KB file size limit
- **Progressive Loading**: Loads code chunks as needed for better performance
- **Feature Flagging**: Controlled rollout of features with percentage-based exposure
- **Responsive Design**: Works well in Google Apps Script dialog windows of various sizes
- **Error Handling**: Comprehensive error boundaries and fallbacks
- **Atomic Design**: Components organized according to atomic design principles
- **Robust API Layer**: Type-safe API client with error handling, retries, and interceptors
- **State Management**: Context-based state management with selectors and persistence
- **Enhanced Components**: Reusable UI components with proper theming and accessibility
- **Trade Analysis**: Comprehensive trade analysis with filtering, sorting, and visualization

## How to Use This README

- **For a high-level overview**, read this file.
- **For details on a specific package**, see the README in that package's folder:
  - [`packages/core/README.md`](./packages/core/README.md)
  - [`packages/shared/README.md`](./packages/shared/README.md)
  - [`packages/dashboard/README.md`](./packages/dashboard/README.md)

## Installation

### 1. Add the Library to Your Google Apps Script Project

1. In your Google Apps Script project, go to **Libraries** in the left sidebar
2. Click the **+** button to add a library
3. Enter the library ID: `YOUR_LIBRARY_ID_HERE` (replace with your actual library ID after deployment)
4. Select the latest version
5. Set the identifier to `ADHDTradingDashboard` (or your preferred name)
6. Click **Add**

### 2. Initialize the Library

Add the following code to your Google Apps Script project:

```javascript
/**
 * Initialize the ADHD Trading Dashboard
 */
function initDashboard() {
  // Initialize the dashboard library
  ADHDTradingDashboard.init({
    theme: 'f1',
    features: {
      performanceChart: true,
      newsEvents: true,
      recentTrades: true,
    },
    cache: true,
    debug: false,
  });
}

/**
 * Show the dashboard
 */
function showDashboard() {
  // Show the dashboard in a dialog
  ADHDTradingDashboard.showDashboard({
    width: 1000,
    height: 700,
    title: 'ADHD Trading Dashboard',
    theme: 'f1',
    features: {
      performanceChart: true,
      newsEvents: true,
      recentTrades: true,
    },
  });
}

/**
 * Update dashboard data
 */
function updateDashboardData() {
  // Get data from your spreadsheet
  const data = {
    summary: {
      totalTrades: 120,
      winRate: 0.65,
      profitFactor: 2.3,
      netProfit: 12500,
    },
    recentTrades: [
      // Your trades data
    ],
    performance: {
      daily: [
        // Your performance data
      ],
    },
  };

  // Update the dashboard data
  ADHDTradingDashboard.updateData(data);
}

/**
 * Get dashboard status
 */
function getDashboardStatus() {
  // Get the dashboard status
  const status = ADHDTradingDashboard.getStatus();
  console.log(status);
}
```

## API Reference

### `init(options)`

Initialize the dashboard library.

**Parameters:**

- `options` (Object): Configuration options
  - `theme` (String): Theme name (default: 'f1')
  - `features` (Object): Feature flags
  - `cache` (Boolean): Enable caching (default: true)
  - `debug` (Boolean): Enable debug logging (default: false)
  - `persistConfig` (Boolean): Persist configuration in script properties (default: false)

**Returns:** Object - Library instance

### `showDashboard(options)`

Show the dashboard in a dialog.

**Parameters:**

- `options` (Object): Dashboard options
  - `width` (Number): Dialog width (default: 1000)
  - `height` (Number): Dialog height (default: 700)
  - `title` (String): Dialog title (default: 'ADHD Trading Dashboard')
  - `theme` (String): Theme name (default: 'f1')
  - `features` (Object): Feature flags
  - `data` (Object): Initial data (optional)

**Returns:** GoogleAppsScript.Base.UI.Dialog - The dialog instance

### `updateData(data)`

Update dashboard data.

**Parameters:**

- `data` (Object): The data to update
  - `summary` (Object): Summary metrics
  - `recentTrades` (Array): Recent trades data
  - `performance` (Object): Performance data
  - `news` (Array): Market news (optional)

**Returns:** Boolean - Success status

### `getStatus()`

Get dashboard status.

**Returns:** Object - Status information

- `initialized` (Boolean): Whether the dashboard is initialized
- `hasData` (Boolean): Whether the dashboard has data
- `lastUpdated` (String): ISO timestamp of last update (or null)

## Development

### Prerequisites

- Node.js v18 LTS (recommended)
- Yarn package manager
- Google Clasp CLI (`npm install -g @google/clasp`)

### Setup

1. Clone the repository
2. Install dependencies: `yarn install`
3. Login to Google: `clasp login`
4. Create a new Google Apps Script project: `yarn create-gas-project`

### Development Workflow

```bash
# Start the development server
yarn start

# Build all packages
yarn build

# Build individual packages
yarn build:shared
yarn build:core
yarn build:dashboard

# Build in development mode
yarn build:dev

# Type checking
yarn typecheck
yarn typecheck:watch

# Run tests
yarn test
yarn test:watch
yarn test:coverage

# Clean build artifacts
yarn clean

# Clean build artifacts and node_modules
yarn clean:all

# Deploy to Google Apps Script
yarn deploy

# Deploy to GitHub Pages
yarn deploy:gh-pages
```

### Architecture

The packages have the following dependency flow:

```
shared → core → dashboard
```

This means that:

- `shared` has no dependencies on other packages
- `core` depends on `shared`
- `dashboard` depends on both `shared` and `core`

### Technology Stack

- **TypeScript**: For type safety
- **React**: For UI components
- **styled-components**: For styling with F1-inspired theme
- **Vite**: For building and development
- **Vitest**: For testing

### Package Structure

```
packages/
  ├── core/                # Google Apps Script integration
  │   ├── server/          # Server-side code
  │   └── src/             # Client-side code
  ├── shared/              # Shared components and utilities
  │   ├── src/
  │   │   ├── api/         # API layer
  │   │   │   ├── core/    # Core API client
  │   │   │   ├── hooks/   # API hooks
  │   │   │   ├── context/ # API context
  │   │   │   └── types/   # API types
  │   │   ├── components/  # UI components
  │   │   │   ├── atoms/   # Atomic components
  │   │   │   ├── molecules/ # Molecular components
  │   │   │   └── organisms/ # Organism components
  │   │   ├── hooks/       # Custom hooks
  │   │   ├── state/       # State management
  │   │   ├── theme/       # Theme system
  │   │   └── utils/       # Utilities
  └── dashboard/           # React application
      ├── src/
      │   ├── features/    # Feature-specific components
      │   │   ├── trade-analysis/ # Trade analysis feature
      │   │   └── daily-guide/    # Daily guide feature
      │   ├── layouts/     # Layout components
      │   ├── pages/       # Page components
      │   └── routes/      # Route definitions
```

## Feature Flags

The dashboard supports feature flags for controlled rollout of features. You can enable or disable features by setting the `features` object in the configuration:

```javascript
ADHDTradingDashboard.init({
  features: {
    performanceChart: true,
    newsEvents: true,
    recentTrades: true,
    advancedMetrics: {
      enabled: true,
      percentage: 50, // Enable for 50% of users
    },
  },
});
```

## License

MIT
