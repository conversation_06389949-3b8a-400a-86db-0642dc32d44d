# ADHD Trading Dashboard Monorepo Fix Guide

This document provides a comprehensive guide to resolve the routing issues and build problems in your monorepo.

## Summary of Changes Made

The following changes have been made to fix the issues:

1. **Replaced BrowserRouter with HashRouter**:

   - This eliminates 404/"Cannot GET /" errors by handling routing client-side
   - Hash<PERSON><PERSON><PERSON> uses URL fragments (#) that don't require server routing configuration

2. **Fixed Vite Configuration**:

   - Set `base: './'` for proper relative path references
   - Updated package aliases to reference dist folders instead of src
   - Added proper server configuration
   - Configured asset handling for production builds

3. **Updated HTML Template**:

   - Replaced CRA placeholders (%PUBLIC_URL%) with relative paths
   - Added explicit module script reference for Vite

4. **Created a Reliable Build Script**:
   - Added a dedicated script for building the monorepo in the correct order
   - Includes validation and error handling

## How to Build and Run the Dashboard

### Method 1: Using the Fix Script (Recommended)

1. Install required packages:

```bash
yarn
```

2. Run the fix script to build all packages in the correct order:

```bash
node scripts/fix-monorepo-build.js
```

3. Start the development server:

```bash
yarn workspace @adhd-trading-dashboard/dashboard dev
```

4. Or preview the production build:

```bash
yarn workspace @adhd-trading-dashboard/dashboard preview
```

### Method 2: Manual Build

If you prefer to build packages manually:

1. Clean previous builds:

```bash
yarn clean
```

2. Build packages in order:

```bash
yarn workspace @adhd-trading-dashboard/shared build
yarn workspace @adhd-trading-dashboard/core build
yarn workspace @adhd-trading-dashboard/dashboard build
```

3. Start the preview server:

```bash
yarn workspace @adhd-trading-dashboard/dashboard preview
```

## Explanation of Core Issues

### 1. Routing Configuration

The original setup used `BrowserRouter` which requires server-side support for direct URL access. This was causing the "Cannot GET /" errors. The fix switches to `HashRouter` which works without server configuration.

### 2. Package Resolution

The monorepo had path alias issues where packages were referencing source files instead of built output. The fix ensures packages reference the correct dist folders.

### 3. Build Order and Dependencies

The monorepo requires packages to be built in a specific order. The new build script enforces this order and validates the output.

## Tips for Future Development

1. **Always build in the correct order**: shared → core → dashboard
2. **Use HashRouter for simple deployment**: This avoids server configuration issues
3. **Keep package.json dependencies in sync**: Ensure workspace versions match
4. **Check Vite configuration when adding new dependencies**: Update external dependencies as needed

For local development with hot reloading, use:

```bash
yarn workspace @adhd-trading-dashboard/dashboard dev
```

For testing production builds, use:

```bash
node scripts/fix-monorepo-build.js
yarn workspace @adhd-trading-dashboard/dashboard preview
```

## Troubleshooting

If you encounter issues:

1. Check that all packages have built successfully with output in their dist folders
2. Ensure you're accessing the right URL (e.g., http://localhost:3000/#/ for development)
3. Check browser console for errors related to missing modules or assets
4. Clear node_modules and reinstall if package resolution issues persist:
   ```bash
   yarn clean:deep
   yarn
   ```

The dashboard should now be accessible and functioning properly!
